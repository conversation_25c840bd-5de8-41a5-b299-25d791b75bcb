// Test client API after manual sync
const fetch = require('node-fetch');

async function testClientAPI() {
  console.log('🧪 Testing Client API After Manual Sync');
  console.log('=======================================');

  const yourWallet = '******************************************';
  const targetTokenAddress = '******************************************';
  
  try {
    const response = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(yourWallet)}`);
    
    if (!response.ok) {
      console.log('❌ Client API request failed');
      return;
    }
    
    const tokens = await response.json();
    console.log(`✅ Client API returned ${tokens.length} tokens`);
    
    // Find the specific token we're testing
    const targetToken = tokens.find(t => t.address.toLowerCase() === targetTokenAddress.toLowerCase());
    
    if (targetToken) {
      console.log('\n🎯 Target Token (Client_01):');
      console.log(`   Name: ${targetToken.name}`);
      console.log(`   Symbol: ${targetToken.symbol}`);
      console.log(`   Address: ${targetToken.address}`);
      console.log(`   Whitelisted: ${targetToken.isWhitelisted ? '✅ YES' : '❌ NO'}`);
      console.log(`   Price: ${targetToken.price} ${targetToken.currency}`);
      
      if (targetToken.isWhitelisted) {
        console.log('\n🎉 SUCCESS! Client_01 token now shows as WHITELISTED!');
        console.log('The manual database sync worked perfectly.');
      } else {
        console.log('\n❌ ISSUE: Client_01 token still shows as NOT WHITELISTED');
        console.log('The database sync may not have taken effect yet.');
      }
    } else {
      console.log('\n❌ Target token (Client_01) not found in client API response');
    }
    
    // Show summary of all whitelisted tokens
    const whitelistedTokens = tokens.filter(t => t.isWhitelisted);
    console.log(`\n📊 Summary: ${whitelistedTokens.length}/${tokens.length} tokens whitelisted`);
    
    if (whitelistedTokens.length > 0) {
      console.log('\n✅ Whitelisted tokens:');
      whitelistedTokens.forEach(token => {
        console.log(`   - ${token.symbol} (${token.name})`);
      });
    }
    
    // Check if Client_01 is in the whitelisted list
    const client01Whitelisted = whitelistedTokens.find(t => t.symbol === 'Client_01');
    if (client01Whitelisted) {
      console.log('\n🎯 CONFIRMED: Client_01 is in the whitelisted tokens list!');
    } else {
      console.log('\n⚠️  Client_01 is not in the whitelisted tokens list');
    }
    
  } catch (error) {
    console.error('Error testing client API:', error);
  }
}

async function main() {
  await testClientAPI();
  
  console.log('\n🔧 WHAT THIS MEANS:');
  console.log('===================');
  console.log('If Client_01 shows as WHITELISTED:');
  console.log('✅ The database sync functionality works perfectly');
  console.log('✅ The admin panel sync function should work the same way');
  console.log('✅ Your original issue is solved');
  console.log('');
  console.log('If Client_01 shows as NOT WHITELISTED:');
  console.log('❌ There might be a caching issue or API delay');
  console.log('❌ Try refreshing or waiting a moment');
  console.log('');
  console.log('🎯 NEXT TEST:');
  console.log('Now you can test the admin panel whitelist functionality');
  console.log('to see if it automatically syncs to the database!');
}

main().catch(console.error);
