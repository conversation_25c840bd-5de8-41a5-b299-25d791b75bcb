// <PERSON>ript to manage token operations

const { ethers } = require("hardhat");

async function main() {
  // Get the token address from environment variables
  const tokenAddress = process.env.TOKEN_ADDRESS;
  if (!tokenAddress) {
    throw new Error("TOKEN_ADDRESS environment variable not set");
  }
  
  // Check for custom gas limit
  const customGasLimit = process.env.GAS_LIMIT ? parseInt(process.env.GAS_LIMIT) : null;
  if (customGasLimit) {
    console.log(`Using custom gas limit: ${customGasLimit}`);
  }

  /**
   * Helper function to execute contract transactions with proper gas handling
   * 
   * @param {Object} contract - The ethers contract instance
   * @param {string} methodName - The name of the contract method to call
   * @param {Array} args - The arguments to pass to the contract method
   * @param {number} defaultGasLimit - The default gas limit to use if estimation fails
   * @param {string} successMessage - Message to display on success
   * @returns {Promise<Object>} - The transaction receipt
   */
  async function executeWithGas(contract, methodName, args, defaultGasLimit, successMessage) {
    try {
      // Special handling for pause/unpause which often have RPC issues
      if (methodName === "pause" || methodName === "unpause") {
        // Use a much higher gas limit for these problematic functions
        const gasLimit = customGasLimit || 300000; // Much higher default for pause/unpause
        
        // Get the function data directly
        const data = contract.interface.encodeFunctionData(methodName, args);
        
        // Prepare transaction
        const tx = {
          to: contract.target,
          data: data,
          gasLimit: gasLimit
        };
        
        console.log(`\nSending ${methodName} transaction with fixed gas limit: ${gasLimit}...`);
        const txResponse = await contract.runner.sendTransaction(tx);
        console.log(successMessage);
        return txResponse;
      } 
      
      // For other functions, use normal flow with gas estimation
      try {
        const gasEstimate = await contract[methodName].estimateGas(...args);
        console.log(`Estimated gas: ${gasEstimate.toString()}`);
        
        // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
        const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
        
        console.log(`\nSending ${methodName} transaction with gas limit: ${gasLimit}...`);
        const tx = await contract[methodName](...args, { gasLimit });
        console.log(successMessage);
        return tx;
      } catch (err) {
        console.error("\nERROR: Gas estimation failed. Using default gas limit...");
        // Use the provided default gas limit
        const gasLimit = customGasLimit || defaultGasLimit;
        console.log(`\nSending ${methodName} transaction with default gas limit: ${gasLimit}...`);
        const tx = await contract[methodName](...args, { gasLimit });
        console.log(successMessage);
        return tx;
      }
    } catch (err) {
      console.error(`\nERROR: ${err.message}`);
      throw err;
    }
  }

  // Get the action from environment variables
  const action = process.env.ACTION;
  if (!action) {
    throw new Error("ACTION environment variable not set. Valid actions: mint, burn, adminTransfer, pause, unpause, updateMetadata, updateMaxSupply, updateWhitelist, addToWhitelist, batchAddToWhitelist, removeFromWhitelist, freezeAddress, unfreezeAddress");
  }

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log(`Executor: ${deployer.address}`);

  // Connect to the token contract
  const token = await ethers.getContractAt("SecurityToken", tokenAddress);
  console.log(`Connected to SecurityToken at: ${tokenAddress}`);

  // Perform the action
  let tx;
  console.log(`Performing action: ${action}`);

  switch (action.toLowerCase()) {
    case "mint":
      // Get mint parameters
      const mintTo = process.env.TO_ADDRESS;
      if (!mintTo || !ethers.isAddress(mintTo)) {
        throw new Error("TO_ADDRESS environment variable not set or invalid");
      }
      
      const mintAmount = process.env.AMOUNT;
      if (!mintAmount) {
        throw new Error("AMOUNT environment variable not set");
      }
      
      const mintAmountWei = ethers.parseEther(mintAmount);
      
      try {
        // Check if the recipient is whitelisted
        let isRecipientWhitelisted = false;
        try {
          isRecipientWhitelisted = await token.isWhitelisted(mintTo);
          console.log(`Recipient whitelisted: ${isRecipientWhitelisted}`);
        } catch (err) {
          console.log("Could not check if recipient is whitelisted (function may not exist on contract)");
        }
        
        // Check if the executor has the AGENT_ROLE
        let hasAgentRole = false;
        try {
          const AGENT_ROLE = await token.AGENT_ROLE();
          hasAgentRole = await token.hasRole(AGENT_ROLE, deployer.address);
          console.log(`Executor has AGENT_ROLE: ${hasAgentRole}`);
          
          if (!hasAgentRole) {
            console.error("\nWARNING: You don't have the AGENT_ROLE required to mint tokens");
            console.error("You need to be granted the AGENT_ROLE by the admin first.");
          }
        } catch (err) {
          console.log("Could not check AGENT_ROLE (function may not exist on contract)");
        }
        
        // Check token max supply and total supply
        try {
          const maxSupply = await token.maxSupply();
          const totalSupply = await token.totalSupply();
          console.log(`Current supply: ${ethers.formatEther(totalSupply)} / ${ethers.formatEther(maxSupply)}`);
          
          if (totalSupply + mintAmountWei > maxSupply) {
            console.error("\nWARNING: This mint would exceed the maximum token supply");
          }
        } catch (err) {
          console.log("Could not check supply information");
        }
        
        // Use the executeWithGas helper for mint with high default gas limit
        tx = await executeWithGas(token, "mint", [mintTo, mintAmountWei], 500000, `Minted ${mintAmount} tokens to ${mintTo}`);
      } catch (err) {
        if (err.message.includes("execution reverted")) {
          console.error("\nERROR: Transaction reverted. Possible reasons:");
          console.error("1. You don't have the AGENT_ROLE required to mint tokens");
          console.error("2. The recipient is not whitelisted");
          console.error("3. The mint would exceed the maximum token supply");
          console.error("4. The token contract is paused");
        }
        // Re-throw the error to be caught by the main try/catch
        throw err;
      }
      break;
    
    case "burn":
      // Get burn parameters
      const burnAmount = process.env.AMOUNT;
      if (!burnAmount) {
        throw new Error("AMOUNT environment variable not set");
      }
      
      const burnAmountWei = ethers.parseEther(burnAmount);
      
      try {
        // Try to estimate gas first to catch errors before sending
        try {
          const gasEstimate = await token.burn.estimateGas(burnAmountWei);
          console.log(`Estimated gas: ${gasEstimate.toString()}`);
          
          // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
          const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
          
          console.log(`\nSending burn transaction with gas limit: ${gasLimit}...`);
          tx = await token.burn(burnAmountWei, { gasLimit });
          console.log(`Burned ${burnAmount} tokens from ${deployer.address}`);
        } catch (err) {
          console.error("\nERROR: Gas estimation failed. Using default gas limit...");
          // Use a conservative default gas limit
          const gasLimit = customGasLimit || 200000;
          console.log(`\nSending burn transaction with default gas limit: ${gasLimit}...`);
          tx = await token.burn(burnAmountWei, { gasLimit });
          console.log(`Burned ${burnAmount} tokens from ${deployer.address}`);
        }
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        throw err;
      }
      break;
    
    case "admintransfer":
      // Get transfer parameters
      const fromAddress = process.env.FROM_ADDRESS;
      if (!fromAddress || !ethers.isAddress(fromAddress)) {
        throw new Error("FROM_ADDRESS environment variable not set or invalid");
      }
      
      const toAddress = process.env.TO_ADDRESS;
      if (!toAddress || !ethers.isAddress(toAddress)) {
        throw new Error("TO_ADDRESS environment variable not set or invalid");
      }
      
      const transferAmount = process.env.AMOUNT;
      if (!transferAmount) {
        throw new Error("AMOUNT environment variable not set");
      }
      
      const transferAmountWei = ethers.parseEther(transferAmount);
      
      try {
        // Try to estimate gas first to catch errors before sending
        try {
          const gasEstimate = await token.adminTransfer.estimateGas(fromAddress, toAddress, transferAmountWei);
          console.log(`Estimated gas: ${gasEstimate.toString()}`);
          
          // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
          const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
          
          console.log(`\nSending adminTransfer transaction with gas limit: ${gasLimit}...`);
          tx = await token.adminTransfer(fromAddress, toAddress, transferAmountWei, { gasLimit });
          console.log(`Admin transferred ${transferAmount} tokens from ${fromAddress} to ${toAddress}`);
        } catch (err) {
          console.error("\nERROR: Gas estimation failed. Using default gas limit...");
          // Use a conservative default gas limit
          const gasLimit = customGasLimit || 250000;
          console.log(`\nSending adminTransfer transaction with default gas limit: ${gasLimit}...`);
          tx = await token.adminTransfer(fromAddress, toAddress, transferAmountWei, { gasLimit });
          console.log(`Admin transferred ${transferAmount} tokens from ${fromAddress} to ${toAddress}`);
        }
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        throw err;
      }
      break;
    
    case "pause":
      try {
        tx = await executeWithGas(token, "pause", [], 100000, "Token transfers paused");
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        // Provide specific guidance for common pause issues
        if (err.message.includes("JSON-RPC error")) {
          console.error("\nThis appears to be an RPC node issue with the Amoy testnet.");
          console.error("Possible solutions:");
          console.error("1. Try again with a higher gas limit:");
          console.error(`
# For Windows PowerShell:
$env:GAS_LIMIT="500000"
$env:TOKEN_ADDRESS="${tokenAddress}"
$env:ACTION="pause"
npx hardhat run scripts/05-manage-token.js --network amoy
          `);
          console.error("2. Try to use a different RPC endpoint by modifying the hardhat.config.js file");
          console.error("3. If the problem persists, try to wait and retry later as the testnet might be congested");
        }
        throw err;
      }
      break;
    
    case "unpause":
      try {
        tx = await executeWithGas(token, "unpause", [], 100000, "Token transfers unpaused");
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        // Provide specific guidance for common unpause issues
        if (err.message.includes("JSON-RPC error")) {
          console.error("\nThis appears to be an RPC node issue with the Amoy testnet.");
          console.error("Possible solutions:");
          console.error("1. Try again with a higher gas limit:");
          console.error(`
# For Windows PowerShell:
$env:GAS_LIMIT="500000"
$env:TOKEN_ADDRESS="${tokenAddress}"
$env:ACTION="unpause"
npx hardhat run scripts/05-manage-token.js --network amoy
          `);
          console.error("2. Try to use a different RPC endpoint by modifying the hardhat.config.js file");
          console.error("3. If the problem persists, try to wait and retry later as the testnet might be congested");
        }
        throw err;
      }
      break;
    
    case "updatemetadata":
      // Get metadata parameters
      const tokenPrice = process.env.TOKEN_PRICE;
      if (!tokenPrice) {
        throw new Error("TOKEN_PRICE environment variable not set");
      }
      
      const bonusTiers = process.env.BONUS_TIERS;
      if (!bonusTiers) {
        throw new Error("BONUS_TIERS environment variable not set");
      }
      
      try {
        // Try to estimate gas first to catch errors before sending
        try {
          const gasEstimate = await token.updateTokenMetadata.estimateGas(tokenPrice, bonusTiers);
          console.log(`Estimated gas: ${gasEstimate.toString()}`);
          
          // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
          const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
          
          console.log(`\nSending updateTokenMetadata transaction with gas limit: ${gasLimit}...`);
          tx = await token.updateTokenMetadata(tokenPrice, bonusTiers, { gasLimit });
          console.log(`Updated token metadata:`);
          console.log(`- Token price: ${tokenPrice}`);
          console.log(`- Bonus tiers: ${bonusTiers}`);
        } catch (err) {
          console.error("\nERROR: Gas estimation failed. Using default gas limit...");
          // Use a conservative default gas limit
          const gasLimit = customGasLimit || 100000;
          console.log(`\nSending updateTokenMetadata transaction with default gas limit: ${gasLimit}...`);
          tx = await token.updateTokenMetadata(tokenPrice, bonusTiers, { gasLimit });
          console.log(`Updated token metadata:`);
          console.log(`- Token price: ${tokenPrice}`);
          console.log(`- Bonus tiers: ${bonusTiers}`);
        }
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        throw err;
      }
      break;
    
    case "updatemaxsupply":
      // Get max supply parameter
      const newMaxSupply = process.env.MAX_SUPPLY;
      if (!newMaxSupply) {
        throw new Error("MAX_SUPPLY environment variable not set");
      }
      
      const newMaxSupplyWei = ethers.parseEther(newMaxSupply);
      
      try {
        // Try to estimate gas first to catch errors before sending
        try {
          const gasEstimate = await token.updateMaxSupply.estimateGas(newMaxSupplyWei);
          console.log(`Estimated gas: ${gasEstimate.toString()}`);
          
          // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
          const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
          
          console.log(`\nSending updateMaxSupply transaction with gas limit: ${gasLimit}...`);
          tx = await token.updateMaxSupply(newMaxSupplyWei, { gasLimit });
          console.log(`Updated max supply to ${newMaxSupply} tokens`);
        } catch (err) {
          console.error("\nERROR: Gas estimation failed. Using default gas limit...");
          // Use a conservative default gas limit
          const gasLimit = customGasLimit || 100000;
          console.log(`\nSending updateMaxSupply transaction with default gas limit: ${gasLimit}...`);
          tx = await token.updateMaxSupply(newMaxSupplyWei, { gasLimit });
          console.log(`Updated max supply to ${newMaxSupply} tokens`);
        }
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        throw err;
      }
      break;
    
    case "updatewhitelist":
      // Get whitelist address parameter
      const newWhitelistAddress = process.env.WHITELIST_ADDRESS;
      if (!newWhitelistAddress || !ethers.isAddress(newWhitelistAddress)) {
        throw new Error("WHITELIST_ADDRESS environment variable not set or invalid");
      }
      
      try {
        // Try to estimate gas first to catch errors before sending
        try {
          const gasEstimate = await token.updateWhitelist.estimateGas(newWhitelistAddress);
          console.log(`Estimated gas: ${gasEstimate.toString()}`);
          
          // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
          const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
          
          console.log(`\nSending updateWhitelist transaction with gas limit: ${gasLimit}...`);
          tx = await token.updateWhitelist(newWhitelistAddress, { gasLimit });
          console.log(`Updated whitelist to ${newWhitelistAddress}`);
        } catch (err) {
          console.error("\nERROR: Gas estimation failed. Using default gas limit...");
          // Use a conservative default gas limit
          const gasLimit = customGasLimit || 100000;
          console.log(`\nSending updateWhitelist transaction with default gas limit: ${gasLimit}...`);
          tx = await token.updateWhitelist(newWhitelistAddress, { gasLimit });
          console.log(`Updated whitelist to ${newWhitelistAddress}`);
        }
      } catch (err) {
        console.error(`\nERROR: ${err.message}`);
        throw err;
      }
      break;
    
    case "addtowhitelist":
      // Get address to whitelist
      const whitelistAddress = process.env.ADDRESS;
      if (!whitelistAddress || !ethers.isAddress(whitelistAddress)) {
        throw new Error(`ADDRESS environment variable not set or invalid. Please set it as follows:
        
Unix/Linux/Mac:
  export ADDRESS=0xYourAddressHere

Windows Command Prompt:
  set ADDRESS=0xYourAddressHere

Windows PowerShell:
  $env:ADDRESS="0xYourAddressHere"
        `);
      }
      
      try {
        // Try to estimate gas first to catch errors before sending
        try {
          const gasEstimate = await token.addToWhitelist.estimateGas(whitelistAddress);
          console.log(`Estimated gas: ${gasEstimate.toString()}`);
          
          // Use custom gas limit if provided, otherwise add 20% buffer to gas estimate
          const gasLimit = customGasLimit || Math.floor(Number(gasEstimate) * 1.2);
          
          console.log(`\nSending addToWhitelist transaction with gas limit: ${gasLimit}...`);
          tx = await token.addToWhitelist(whitelistAddress, { gasLimit });
          console.log(`Added ${whitelistAddress} to whitelist`);
        } catch (err) {
          console.error("\nERROR: Gas estimation failed. Using default gas limit...");
          // Use a conservative default gas limit
          const gasLimit = customGasLimit || 150000;
          console.log(`\nSending addToWhitelist transaction with default gas limit: ${gasLimit}...`);
          
          try {
            tx = await token.addToWhitelist(whitelistAddress, { gasLimit });
            console.log(`Added ${whitelistAddress} to whitelist`);
          } catch (innerErr) {
            if (innerErr.message.includes("execution reverted")) {
              // Check if the contract needs to be upgraded first
              console.error("\nERROR: The transaction was reverted by the contract.");
              console.error("\nPossible reasons:");
              console.error("1. The contract might need to be upgraded to support whitelist functions");
              console.error("2. You may not have the required role (AGENT_ROLE)");
              console.error("3. The whitelist contract address might not be set");
            } else {
              console.error(`\nERROR: ${innerErr.message}`);
            }
            throw innerErr;
          }
        }
      } catch (err) {
        if (!err.message.includes("execution reverted")) {
          console.error(`\nERROR: ${err.message}`);
        }
        throw err;
      }
      break;
    
    case "batchaddtowhitelist":
      // Get addresses to whitelist
      const addressesString = process.env.ADDRESSES;
      if (!addressesString) {
        throw new Error(`ADDRESSES environment variable not set. Please set it as follows:
        
Unix/Linux/Mac:
  export ADDRESSES=0xAddress1,0xAddress2,0xAddress3

Windows Command Prompt:
  set ADDRESSES=0xAddress1,0xAddress2,0xAddress3

Windows PowerShell:
  $env:ADDRESSES="0xAddress1,0xAddress2,0xAddress3"
        `);
      }
      
      const addresses = addressesString.split(',');
      for (const address of addresses) {
        if (!ethers.isAddress(address.trim())) {
          throw new Error(`Invalid address in list: ${address}`);
        }
      }
      
      try {
        tx = await executeWithGas(token, "batchAddToWhitelist", [addresses], 500000, `Batch added ${addresses.length} addresses to whitelist`);
      } catch (err) {
        if (err.message.includes("execution reverted")) {
          // Check if the contract needs to be upgraded first
          console.error("\nERROR: The transaction was reverted by the contract.");
          console.error("\nPossible reasons:");
          console.error("1. The contract might need to be upgraded to support whitelist functions");
          console.error("2. You might not have the AGENT_ROLE required to call this function");
          console.error("3. Some addresses might already be whitelisted");
          console.error("4. The contract at this address might not be a SecurityToken\n");
          
          console.error("Try upgrading the contract with:");
          console.error(`
# For Unix/Linux/Mac:
export CONTRACT_ADDRESS=${tokenAddress}
export CONTRACT_TYPE=token
npx hardhat run scripts/04-upgrade-contracts.js --network amoy

# For Windows PowerShell:
$env:CONTRACT_ADDRESS="${tokenAddress}"
$env:CONTRACT_TYPE="token"
npx hardhat run scripts/04-upgrade-contracts.js --network amoy
          `);
          console.error("\nAlternatively, try managing the whitelist directly through the Whitelist contract.");
        }
        throw err;
      }
      break;
    
    case "removefromwhitelist":
      // Get address to remove from whitelist
      const removeAddress = process.env.ADDRESS;
      if (!removeAddress || !ethers.isAddress(removeAddress)) {
        throw new Error("ADDRESS environment variable not set or invalid");
      }
      
      tx = await executeWithGas(token, "removeFromWhitelist", [removeAddress], 150000, `Removed ${removeAddress} from whitelist`);
      break;
    
    case "freezeaddress":
      // Get address to freeze
      const freezeAddress = process.env.ADDRESS;
      if (!freezeAddress || !ethers.isAddress(freezeAddress)) {
        throw new Error("ADDRESS environment variable not set or invalid");
      }
      
      tx = await executeWithGas(token, "freezeAddress", [freezeAddress], 150000, `Froze address ${freezeAddress}`);
      break;
    
    case "unfreezeaddress":
      // Get address to unfreeze
      const unfreezeAddress = process.env.ADDRESS;
      if (!unfreezeAddress || !ethers.isAddress(unfreezeAddress)) {
        throw new Error("ADDRESS environment variable not set or invalid");
      }
      
      tx = await executeWithGas(token, "unfreezeAddress", [unfreezeAddress], 150000, `Unfroze address ${unfreezeAddress}`);
      break;
    
    default:
      throw new Error("Invalid action. Valid actions: mint, burn, adminTransfer, pause, unpause, updateMetadata, updateMaxSupply, updateWhitelist, addToWhitelist, batchAddToWhitelist, removeFromWhitelist, freezeAddress, unfreezeAddress");
  }

  // Wait for the transaction to be mined
  await tx.wait();
  console.log("Transaction hash:", tx.hash);
  console.log("Action completed successfully!");
  
  // Log token information
  try {
    console.log("\nToken Information:");
    
    try { console.log(`- Name: ${await token.name()}`); }
    catch (err) { console.log(`- Name: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Symbol: ${await token.symbol()}`); }
    catch (err) { console.log(`- Symbol: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Total Supply: ${ethers.formatEther(await token.totalSupply())} tokens`); }
    catch (err) { console.log(`- Total Supply: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Max Supply: ${ethers.formatEther(await token.maxSupply())} tokens`); }
    catch (err) { console.log(`- Max Supply: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Paused: ${await token.paused()}`); }
    catch (err) { console.log(`- Paused: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Whitelist: ${await token.whitelistAddress()}`); }
    catch (err) { console.log(`- Whitelist: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Token Price: ${await token.tokenPrice()}`); }
    catch (err) { console.log(`- Token Price: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`- Bonus Tiers: ${await token.bonusTiers()}`); }
    catch (err) { console.log(`- Bonus Tiers: Error: ${err.shortMessage || err.message}`); }
    
    console.log("\nNote: If you see errors above, the address might not be a SecurityToken contract or might be using a different interface.");
  } catch (err) {
    console.log(`Error displaying token information: ${err.message}`);
  }
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });