import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create sample clients for testing
  const sampleClients = [
    {
      firstName: '<PERSON>',
      lastName: 'Doe',
      gender: 'MALE',
      nationality: 'United States',
      birthday: new Date('1985-06-15'),
      birthPlace: 'New York, NY',
      identificationType: 'PASSPORT',
      passportNumber: 'US123456789',
      documentExpiration: new Date('2030-06-15'),
      phoneNumber: '******-0123',
      email: '<EMAIL>',
      occupation: 'Software Engineer',
      sectorOfActivity: 'Technology',
      pepStatus: 'NOT_PEP',
      street: '123 Main Street',
      buildingNumber: '123',
      city: 'New York',
      state: 'NY',
      country: 'United States',
      zipCode: '10001',
      sourceOfWealth: 'Employment Income',
      bankAccountNumber: '******************',
      sourceOfFunds: 'Salary and Savings',
      taxIdentificationNumber: 'US123456789',
      kycStatus: 'APPROVED',
      kycCompletedAt: new Date(),
    },
    {
      firstName: 'Jane',
      lastName: 'Smith',
      gender: 'FEMALE',
      nationality: 'Canada',
      birthday: new Date('1990-03-22'),
      birthPlace: 'Toronto, ON',
      identificationType: 'ID_CARD',
      idCardNumber: 'CA987654321',
      documentExpiration: new Date('2028-03-22'),
      phoneNumber: '******-555-0456',
      email: '<EMAIL>',
      occupation: 'Financial Analyst',
      sectorOfActivity: 'Finance',
      pepStatus: 'NOT_PEP',
      street: '456 Maple Avenue',
      buildingNumber: '456',
      city: 'Toronto',
      state: 'ON',
      country: 'Canada',
      zipCode: 'M5V 3A8',
      sourceOfWealth: 'Investment Returns',
      bankAccountNumber: '******************',
      sourceOfFunds: 'Investment Portfolio',
      taxIdentificationNumber: 'CA987654321',
      kycStatus: 'IN_REVIEW',
    },
    {
      firstName: 'Ahmed',
      lastName: 'Al-Rashid',
      gender: 'MALE',
      nationality: 'United Arab Emirates',
      birthday: new Date('1978-11-08'),
      birthPlace: 'Dubai, UAE',
      identificationType: 'PASSPORT',
      passportNumber: 'AE456789123',
      documentExpiration: new Date('2029-11-08'),
      phoneNumber: '+971-50-555-0789',
      email: '<EMAIL>',
      occupation: 'Business Owner',
      sectorOfActivity: 'Real Estate',
      pepStatus: 'NOT_PEP',
      street: 'Sheikh Zayed Road',
      buildingNumber: '789',
      city: 'Dubai',
      country: 'United Arab Emirates',
      zipCode: '00000',
      sourceOfWealth: 'Business Ownership',
      bankAccountNumber: '*********************',
      sourceOfFunds: 'Business Revenue',
      taxIdentificationNumber: 'AE456789123',
      kycStatus: 'PENDING',
    }
  ];

  for (const clientData of sampleClients) {
    const client = await prisma.client.create({
      data: clientData,
    });
    console.log(`✅ Created client: ${client.firstName} ${client.lastName}`);
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
