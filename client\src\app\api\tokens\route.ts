import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { ethers } from 'ethers';

// Helper function to check if user has required claims for a token
async function checkUserClaims(walletAddress: string, requiredClaims: string[]): Promise<{ [claimType: string]: boolean }> {
  const claimResults: { [claimType: string]: boolean } = {};

  if (!walletAddress || !requiredClaims.length) {
    return claimResults;
  }

  try {
    const claimRegistryAddress = process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      console.warn('Claim registry not configured');
      return claimResults;
    }

    const provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_AMOY_RPC_URL);
    const claimRegistryABI = [
      "function hasValidClaim(address subject, uint256 claimType) external view returns (bool)"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, provider);

    // Check each required claim (now using custom claim type IDs)
    for (const claimType of requiredClaims) {
      const claimTypeId = parseInt(claimType.trim());
      if (!isNaN(claimTypeId) && claimTypeId > 0) {
        try {
          const hasValidClaim = await claimRegistry.hasValidClaim(walletAddress, claimTypeId);
          claimResults[claimType] = hasValidClaim;
          console.log(`🔍 Claim check for ${walletAddress}: Claim Type ${claimTypeId} = ${hasValidClaim ? '✅ VALID' : '❌ INVALID'}`);
        } catch (error) {
          console.warn(`Could not check claim type ${claimTypeId}:`, error);
          claimResults[claimType] = false;
        }
      } else {
        console.warn(`Invalid claim type ID: ${claimType}`);
        claimResults[claimType] = false;
      }
    }

    return claimResults;
  } catch (error) {
    console.error('Error checking user claims:', error);
    return claimResults;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check for test wallet address in query params (for testing purposes)
    const { searchParams } = new URL(request.url);
    const testWalletAddress = searchParams.get('testWallet');

    // Get user session to check wallet address
    const session = await getSession(request, NextResponse.next());
    let userWalletAddress: string | null = testWalletAddress; // Use test wallet if provided

    // If no test wallet and user is logged in, try to get their wallet address
    if (!userWalletAddress && session?.user?.email) {
      try {
        const adminPanelUrl = process.env.ADMIN_PANEL_URL!;
        const clientResponse = await fetch(`${adminPanelUrl}/api/clients?search=${encodeURIComponent(session.user.email)}&limit=1`);
        if (clientResponse.ok) {
          const clientData = await clientResponse.json();
          const client = clientData.clients?.[0];
          userWalletAddress = client?.walletAddress || null;
        }
      } catch (error) {
        console.warn('Could not fetch user wallet address:', error);
      }
    }

    // Fetch tokens from the admin panel API
    const adminPanelUrl = process.env.ADMIN_PANEL_URL!;
    const response = await fetch(`${adminPanelUrl}/api/tokens?source=database&t=${Date.now()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      // Add cache control to ensure fresh data
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tokens from admin panel: ${response.status}`);
    }

    const tokens = await response.json();

    // Get whitelist status for all tokens if user has a wallet
    let whitelistStatuses: { [tokenAddress: string]: boolean } = {};

    if (userWalletAddress && tokens.length > 0) {
      try {
        const tokenAddresses = tokens.map((token: any) => token.address);
        const whitelistResponse = await fetch(`${adminPanelUrl}/api/whitelist/check?t=${Date.now()}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          },
          body: JSON.stringify({
            walletAddress: userWalletAddress,
            tokenAddresses: tokenAddresses
          })
        });

        if (whitelistResponse.ok) {
          const whitelistData = await whitelistResponse.json();
          whitelistData.tokens?.forEach((tokenStatus: any) => {
            whitelistStatuses[tokenStatus.tokenAddress.toLowerCase()] = tokenStatus.isWhitelisted;
          });
        }
      } catch (error) {
        console.warn('Could not fetch whitelist statuses:', error);
      }
    }

    // Check user claims for all tokens if user has a wallet
    let userClaimsMap: { [tokenAddress: string]: { [claimType: string]: boolean } } = {};
    let qualifiedTokens: string[] = [];

    if (userWalletAddress && tokens.length > 0) {
      console.log(`🔍 Checking qualification for wallet: ${userWalletAddress}`);

      for (const token of tokens) {
        if (token.selectedClaims) {
          const requiredClaims = typeof token.selectedClaims === 'string'
            ? token.selectedClaims.split(',').map((c: string) => c.trim())
            : token.selectedClaims;

          const userClaims = await checkUserClaims(userWalletAddress, requiredClaims);
          userClaimsMap[token.address.toLowerCase()] = userClaims;

          // Check if user has ALL required claims
          const hasAllRequiredClaims = requiredClaims.every((claim: string) => userClaims[claim] === true);

          if (hasAllRequiredClaims) {
            qualifiedTokens.push(token.address.toLowerCase());
            console.log(`✅ User qualified for ${token.symbol}`);
          } else {
            console.log(`❌ User NOT qualified for ${token.symbol}`);
          }
        } else {
          // No claims required, user is qualified
          qualifiedTokens.push(token.address.toLowerCase());
          console.log(`✅ User qualified for ${token.symbol} - no requirements`);
        }
      }

      console.log(`🎯 Showing ${qualifiedTokens.length} out of ${tokens.length} tokens to user`);
    }

    // Get user email for qualification checks
    const userEmail = session?.user?.email;

    // Check qualification status for all tokens if user is logged in
    let qualificationStatuses: { [tokenAddress: string]: { isQualified: boolean; status: string } } = {};

    console.log('🔍 Starting qualification checks for user:', userEmail, 'with', tokens.length, 'tokens');

    if (userEmail && tokens.length > 0) {
      for (const token of tokens) {
        console.log(`🔍 Checking token ${token.name} (${token.address}) - selectedClaims:`, token.selectedClaims);
        if (token.selectedClaims) {
          try {
            const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';
            const qualificationResponse = await fetch(`${adminApiUrl}/qualification-progress?clientEmail=${encodeURIComponent(userEmail)}&tokenAddress=${token.address}`);

            if (qualificationResponse.ok) {
              const qualificationData = await qualificationResponse.json();
              const allStepsCompleted = qualificationData.completedSteps >= 5;
              const isApproved = qualificationData.qualificationStatus === 'APPROVED' || qualificationData.qualificationStatus === 'FORCE_APPROVED';
              const isQualified = allStepsCompleted && isApproved;

              qualificationStatuses[token.address.toLowerCase()] = {
                isQualified,
                status: qualificationData.qualificationStatus || 'PENDING'
              };

              console.log(`🔍 Qualification check for ${token.name} (${token.address}):`, {
                userEmail,
                completedSteps: qualificationData.completedSteps,
                qualificationStatus: qualificationData.qualificationStatus,
                allStepsCompleted,
                isApproved,
                isQualified
              });
            } else {
              qualificationStatuses[token.address.toLowerCase()] = {
                isQualified: false,
                status: 'PENDING'
              };
            }
          } catch (error) {
            console.error('Error checking qualification status for token:', token.address, error);
            qualificationStatuses[token.address.toLowerCase()] = {
              isQualified: false,
              status: 'PENDING'
            };
          }
        } else {
          // No claims required, user is automatically qualified
          qualificationStatuses[token.address.toLowerCase()] = {
            isQualified: true,
            status: 'APPROVED'
          };
        }
      }
    }

    // Transform tokens and include qualification status (show ALL tokens, not just qualified ones)
    const transformedTokens = tokens
      .map((token: any) => {
        // Extract numeric price and currency from tokenPrice field (e.g., "1.5 ETH" -> price: "1.5", currency: "ETH")
        let numericPrice = '0';
        let extractedCurrency = token.currency || 'USD';

        if (token.tokenPrice) {
          // Try to extract price and currency from tokenPrice field
          const priceWithCurrencyMatch = token.tokenPrice.match(/([\d.]+)\s*([A-Z]{3,4})/i);
          if (priceWithCurrencyMatch) {
            numericPrice = priceWithCurrencyMatch[1];
            extractedCurrency = priceWithCurrencyMatch[2].toUpperCase();
          } else {
            // Fallback to just extracting the number
            const priceMatch = token.tokenPrice.match(/[\d.]+/);
            numericPrice = priceMatch ? priceMatch[0] : '0';
          }
        }

        const tokenAddress = token.address?.toLowerCase();

        // Get qualification status for this token
        const tokenQualification = qualificationStatuses[tokenAddress];
        const isQualifiedForToken = tokenQualification?.isQualified || false;

        // Check if user has the required claims for this token
        // Use the new qualification logic if available, otherwise fall back to blockchain claims
        const hasRequiredClaims = isQualifiedForToken || qualifiedTokens.includes(tokenAddress);

        return {
          id: token.id,
          name: token.name,
          symbol: token.symbol,
          address: token.address,
          totalSupply: token.totalSupply || '0',
          maxSupply: token.maxSupply || '0',
          price: numericPrice,
          currency: extractedCurrency,
          category: token.tokenType || 'Unknown',
          description: token.deploymentNotes || '',
          imageUrl: token.tokenImageUrl || null,
          network: token.network || 'amoy',
          decimals: token.decimals || 0,
          version: '1.0.0', // Default version since not in database
          bonusTiers: token.bonusTiers || '',
          whitelistAddress: token.whitelistAddress || '',
          createdAt: token.createdAt || new Date().toISOString(),
          isWhitelisted: whitelistStatuses[tokenAddress] || false,
          // Add qualification status
          hasRequiredClaims: hasRequiredClaims,
          needsQualification: !isQualifiedForToken && token.selectedClaims, // Needs qualification if has claim requirements but not qualified
          requiredClaims: token.selectedClaims ? (typeof token.selectedClaims === 'string' ? token.selectedClaims.split(',').map((c: string) => c.trim()) : token.selectedClaims) : [],
          userClaims: userClaimsMap[tokenAddress] || {}
        };
      });

    return NextResponse.json(transformedTokens);
  } catch (error) {
    console.error('Error fetching tokens:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tokens' },
      { status: 500 }
    );
  }
}
