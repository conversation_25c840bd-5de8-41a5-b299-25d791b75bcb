// Test client tokens API with whitelist functionality
const fetch = require('node-fetch');

async function testClientTokensAPI() {
  console.log('=== Testing Client Tokens API ===');
  
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    
    if (!response.ok) {
      console.log(`❌ Client tokens API failed: ${response.status}`);
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }
    
    const tokens = await response.json();
    
    console.log(`✅ Client tokens API returned ${tokens.length} tokens`);
    
    // Check if isWhitelisted field is present
    const hasWhitelistField = tokens.length > 0 && 'isWhitelisted' in tokens[0];
    console.log(`Whitelist field present: ${hasWhitelistField ? '✅ YES' : '❌ NO'}`);
    
    if (hasWhitelistField) {
      console.log('\nToken whitelist status:');
      tokens.forEach(token => {
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${token.symbol.padEnd(8)} | ${status}`);
      });
      
      const whitelistedCount = tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nSummary: ${whitelistedCount}/${tokens.length} tokens are whitelisted`);
    } else {
      console.log('\n❌ Whitelist field is missing from token data');
    }
    
  } catch (error) {
    console.error('Error testing client tokens API:', error);
  }
}

async function testClientLookup() {
  console.log('\n=== Testing Client Lookup by Email ===');
  
  const testEmail = '<EMAIL>';
  
  try {
    const response = await fetch(`http://localhost:3000/api/clients?search=${encodeURIComponent(testEmail)}&limit=1`);
    
    if (!response.ok) {
      console.log(`❌ Client lookup failed: ${response.status}`);
      return;
    }
    
    const data = await response.json();
    const client = data.clients?.[0];
    
    if (client) {
      console.log('✅ Client found:');
      console.log(`   Email: ${client.email}`);
      console.log(`   Wallet: ${client.walletAddress}`);
      console.log(`   KYC Status: ${client.kycStatus}`);
      console.log(`   Whitelisted: ${client.isWhitelisted}`);
    } else {
      console.log('❌ Client not found');
    }
    
  } catch (error) {
    console.error('Error testing client lookup:', error);
  }
}

async function simulateAuthenticatedRequest() {
  console.log('\n=== Simulating Authenticated Request ===');
  
  // Since we can't easily simulate Auth0 session, let's test the whitelist API directly
  const testWallet = '******************************************';
  
  try {
    // Get all tokens first
    const tokensResponse = await fetch('http://localhost:3000/api/tokens?source=database');
    const tokens = await tokensResponse.json();
    const tokenAddresses = tokens.map(t => t.address);
    
    // Test whitelist check
    const whitelistResponse = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: testWallet,
        tokenAddresses: tokenAddresses
      })
    });
    
    if (whitelistResponse.ok) {
      const whitelistData = await whitelistResponse.json();
      
      console.log('✅ Whitelist check successful:');
      console.log(`   Wallet: ${whitelistData.walletAddress}`);
      console.log(`   Global Whitelisted: ${whitelistData.globalWhitelisted}`);
      console.log(`   KYC Status: ${whitelistData.kycStatus}`);
      
      console.log('\n   Token-specific whitelist:');
      whitelistData.tokens.forEach(tokenStatus => {
        const token = tokens.find(t => t.address.toLowerCase() === tokenStatus.tokenAddress.toLowerCase());
        const symbol = token?.symbol || 'UNKNOWN';
        const status = tokenStatus.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`     ${symbol.padEnd(8)} | ${status}`);
      });
      
      const whitelistedTokens = whitelistData.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\n   Summary: ${whitelistedTokens}/${whitelistData.tokens.length} tokens whitelisted for this wallet`);
      
    } else {
      console.log('❌ Whitelist check failed');
    }
    
  } catch (error) {
    console.error('Error simulating authenticated request:', error);
  }
}

async function main() {
  await testClientLookup();
  await simulateAuthenticatedRequest();
  await testClientTokensAPI();
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Login to client app with: <EMAIL>');
  console.log('2. Connect wallet: ******************************************');
  console.log('3. Visit /offers page to see WHITELISTED tags');
}

main().catch(console.error);
