const fetch = require('node-fetch');

async function testWhitelistFix() {
  console.log('🧪 Testing Whitelist Fix');
  console.log('========================');

  const YOUR_WALLET = '******************************************';
  const ADMIN_API_URL = 'http://localhost:6677';
  const CLIENT_API_URL = 'http://localhost:7788';

  try {
    // 1. Test admin panel whitelist check directly
    console.log('1️⃣ Testing admin panel whitelist check...');
    
    // First get all tokens
    const tokensResponse = await fetch(`${ADMIN_API_URL}/api/tokens?source=database`);
    if (!tokensResponse.ok) {
      console.log('❌ Could not fetch tokens from admin panel');
      return;
    }
    
    const tokens = await tokensResponse.json();
    console.log(`   Found ${tokens.length} tokens`);
    
    if (tokens.length === 0) {
      console.log('❌ No tokens found');
      return;
    }

    // Test the whitelist check API
    const tokenAddresses = tokens.map(t => t.address);
    const whitelistResponse = await fetch(`${ADMIN_API_URL}/api/whitelist/check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: YOUR_WALLET,
        tokenAddresses: tokenAddresses
      })
    });

    if (!whitelistResponse.ok) {
      console.log(`❌ Whitelist check failed: ${whitelistResponse.status}`);
      const errorText = await whitelistResponse.text();
      console.log('Error:', errorText);
      return;
    }

    const whitelistData = await whitelistResponse.json();
    console.log(`   ✅ Whitelist check successful`);
    console.log(`   Global whitelisted: ${whitelistData.globalWhitelisted}`);
    console.log(`   Tokens checked: ${whitelistData.tokens.length}`);

    // Show detailed results
    console.log('\n   Token whitelist status:');
    whitelistData.tokens.forEach(token => {
      const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
      const blockchain = token.blockchainChecked ? '(blockchain checked)' : '(database only)';
      console.log(`   ${token.tokenSymbol?.padEnd(10) || 'Unknown'.padEnd(10)} | ${status} ${blockchain}`);
    });

    const whitelistedCount = whitelistData.tokens.filter(t => t.isWhitelisted).length;
    console.log(`\n   Summary: ${whitelistedCount}/${whitelistData.tokens.length} tokens whitelisted`);

    // 2. Test client API
    console.log('\n2️⃣ Testing client API with testWallet...');
    
    const clientResponse = await fetch(`${CLIENT_API_URL}/api/tokens?testWallet=${encodeURIComponent(YOUR_WALLET)}`);
    
    if (!clientResponse.ok) {
      console.log(`❌ Client API failed: ${clientResponse.status}`);
      const errorText = await clientResponse.text();
      console.log('Error:', errorText);
      return;
    }

    const clientTokens = await clientResponse.json();
    console.log(`   ✅ Client API returned ${clientTokens.length} tokens`);

    console.log('\n   Client API token status:');
    clientTokens.forEach(token => {
      const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
      console.log(`   ${token.symbol.padEnd(10)} | ${status} | ${token.price} ${token.currency}`);
    });

    const clientWhitelistedCount = clientTokens.filter(t => t.isWhitelisted).length;
    console.log(`\n   Client API Summary: ${clientWhitelistedCount}/${clientTokens.length} tokens whitelisted`);

    // 3. Compare results
    console.log('\n3️⃣ Comparing results...');
    
    if (whitelistedCount === clientWhitelistedCount) {
      console.log('   ✅ Results match! Admin and client APIs are in sync');
    } else {
      console.log('   ❌ Results don\'t match:');
      console.log(`   Admin API: ${whitelistedCount} whitelisted`);
      console.log(`   Client API: ${clientWhitelistedCount} whitelisted`);
    }

    // 4. Test specific token that should be whitelisted
    console.log('\n4️⃣ Testing specific token...');
    
    const testToken = tokens.find(t => t.whitelistAddress && t.whitelistAddress !== '******************************************');
    if (testToken) {
      console.log(`   Testing token: ${testToken.symbol} (${testToken.address})`);
      console.log(`   Whitelist contract: ${testToken.whitelistAddress}`);
      
      const singleTokenResponse = await fetch(`${ADMIN_API_URL}/api/whitelist/check?walletAddress=${YOUR_WALLET}&tokenAddress=${testToken.address}`);
      
      if (singleTokenResponse.ok) {
        const singleTokenData = await singleTokenResponse.json();
        console.log(`   Single token check: ${singleTokenData.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED'}`);
        console.log(`   Global whitelisted: ${singleTokenData.globalWhitelisted}`);
        console.log(`   Token specific: ${singleTokenData.tokenSpecific}`);
      } else {
        console.log('   ❌ Single token check failed');
      }
    } else {
      console.log('   ⚠️  No tokens with whitelist contracts found');
    }

    console.log('\n🎯 NEXT STEPS:');
    if (clientWhitelistedCount > 0) {
      console.log('✅ Whitelist is working! You should now be able to place orders.');
      console.log('1. Refresh your browser');
      console.log('2. Try placing an order on the /offers page');
    } else {
      console.log('❌ Still no tokens whitelisted. Check:');
      console.log('1. Make sure your wallet is actually whitelisted in admin panel');
      console.log('2. Check if the whitelist contract addresses are correct');
      console.log('3. Verify the blockchain connection is working');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testWhitelistFix().catch(console.error);
