const { ethers, upgrades } = require("hardhat");

async function main() {
  const proxyAddress = "******************************************";
  
  console.log("Upgrading Whitelist contract to WhitelistWithKYC...");
  
  // Get the contract factory for the updated Whitelist contract
  const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
  
  // First, force import the proxy to register it in the local deployment history
  console.log("Registering proxy in local deployment history...");
  await upgrades.forceImport(proxyAddress, WhitelistWithKYC);
  console.log("Proxy registered successfully!");
  
  // Now upgrade the proxy to point to a new implementation
  console.log("Deploying new implementation...");
  const upgraded = await upgrades.upgradeProxy(proxyAddress, WhitelistWithKYC);
  
  try {
    // For ethers v6
    if (upgraded.deploymentTransaction) {
      await upgraded.deploymentTransaction().wait();
    } 
    // For ethers v5
    else if (upgraded.deployTransaction) {
      await upgraded.deployTransaction.wait();
    }
    // If neither is available, the transaction might have already been processed
    else {
      console.log("No deployment transaction found, continuing...");
    }
  } catch (error) {
    console.warn("Warning: Could not wait for transaction confirmation:", error.message);
    console.log("Continuing with the process...");
  }
  
  console.log("Whitelist upgraded successfully!");
  console.log("Proxy address:", proxyAddress);
  
  // Get the new implementation address
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
  console.log("New implementation address:", implementationAddress);
  console.log(`View on OKLink: https://www.oklink.com/amoy/address/${implementationAddress}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 