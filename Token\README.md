# Token - Blockchain Smart Contracts

This folder contains all the blockchain-related files for the ERC-3643 compliant security token system.

## Structure

```
Token/
├── contracts/              # Smart contract source files
│   ├── interfaces/         # Contract interfaces
│   ├── base/              # Base contract implementations
│   ├── SecurityToken.sol  # Main token contract
│   ├── SecurityTokenFactory.sol # Factory for deploying tokens
│   ├── Whitelist.sol      # Basic whitelist implementation
│   └── WhitelistWithKYC.sol # Extended whitelist with KYC
├── scripts/               # Deployment and management scripts
├── test/                  # Contract test files
├── artifacts/             # Compiled contract artifacts
├── cache/                 # Hardhat cache
├── tokens/                # Token configuration files
├── deployments/           # Deployment records
├── hardhat.config.js      # Hardhat configuration
├── package.json           # Node.js dependencies
└── tsconfig.json          # TypeScript configuration
```

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn

### Installation

```bash
cd Token
npm install
```

### Configuration

1. Copy `.env.local` and configure your environment variables:
   - `CONTRACT_ADMIN_PRIVATE_KEY`: Private key for contract administration
   - `AMOY_RPC_URL`: RPC URL for Amoy testnet
   - `POLYGON_RPC_URL`: RPC URL for Polygon mainnet

### Deployment

#### Deploy Factory

```bash
npx hardhat run scripts/01-deploy-factory.js --network amoy
```

#### Deploy Token

```bash
# Set environment variables
export TOKEN_NAME="Your Token Name"
export TOKEN_SYMBOL="YTN"
export TOKEN_DECIMALS="0"
export MAX_SUPPLY="1000000"
export ADMIN_ADDRESS="0xYourAddress"
export TOKEN_PRICE="10 USD"
export BONUS_TIERS="Tier 1: 5%, Tier 2: 10%"

# Deploy token
npx hardhat run scripts/02-deploy-token.js --network amoy
```

### Testing

```bash
npx hardhat test
```

### Contract Management

Use the scripts in the `scripts/` folder for various contract operations:

- `05-manage-token.js`: Token management operations
- `03-manage-whitelist.js`: Whitelist management
- `04-upgrade-contracts.js`: Contract upgrades

### Admin Panel Integration

The admin panel automatically extracts ABIs from this folder using:

```bash
cd Token
node scripts/extract-abi.js
```

This updates the contract ABIs in `../admin-panel/src/contracts/`.

## Networks

- **Amoy Testnet**: Primary development network
- **Polygon Mainnet**: Production network

## Security

- Keep private keys secure and never commit them to version control
- Use environment variables for sensitive configuration
- Test thoroughly on testnets before mainnet deployment
