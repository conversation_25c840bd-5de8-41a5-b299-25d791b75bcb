const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Checking Token Features...");

  // List of tokens to check
  const tokensToCheck = [
    {
      address: "******************************************",
      name: "Advanced Control Token (ACT)"
    },
    {
      address: "******************************************",
      name: "Augment_019"
    },
    {
      address: "******************************************",
      name: "Augment_01z"
    }
  ];

  const SecurityTokenABI = require("../admin-panel/src/contracts/SecurityToken.json");

  for (const token of tokensToCheck) {
    console.log(`\n📋 Checking ${token.name} (${token.address})`);
    console.log("=" + "=".repeat(50));

    try {
      const provider = new ethers.JsonRpcProvider("https://rpc-amoy.polygon.technology");
      const contract = new ethers.Contract(token.address, SecurityTokenABI.abi, provider);

      // Check basic token info
      try {
        const name = await contract.name();
        const symbol = await contract.symbol();
        const decimals = await contract.decimals();
        const totalSupply = await contract.totalSupply();
        
        console.log(`✅ Basic Info:`);
        console.log(`   Name: ${name}`);
        console.log(`   Symbol: ${symbol}`);
        console.log(`   Decimals: ${decimals}`);
        console.log(`   Total Supply: ${ethers.formatUnits(totalSupply, decimals)}`);
      } catch (error) {
        console.log(`❌ Failed to get basic info: ${error.message}`);
        continue;
      }

      // Check advanced transfer controls
      console.log(`\n🔒 Advanced Transfer Controls:`);
      
      try {
        const conditionalTransfersEnabled = await contract.conditionalTransfersEnabled();
        console.log(`   ✅ Conditional Transfers: ${conditionalTransfersEnabled ? 'Enabled' : 'Disabled'}`);
      } catch (error) {
        console.log(`   ❌ Conditional Transfers: Not supported`);
      }

      try {
        const transferWhitelistEnabled = await contract.transferWhitelistEnabled();
        console.log(`   ✅ Transfer Whitelisting: ${transferWhitelistEnabled ? 'Enabled' : 'Disabled'}`);
      } catch (error) {
        console.log(`   ❌ Transfer Whitelisting: Not supported`);
      }

      try {
        const transferFeesEnabled = await contract.transferFeesEnabled();
        const [feePercentage, feeCollector] = await contract.getTransferFeeConfig();
        console.log(`   ✅ Transfer Fees: ${transferFeesEnabled ? `Enabled (${Number(feePercentage) / 100}%)` : 'Disabled'}`);
        if (transferFeesEnabled) {
          console.log(`   ✅ Fee Collector: ${feeCollector}`);
        }
      } catch (error) {
        console.log(`   ❌ Transfer Fees: Not supported`);
      }

      // Check other features
      console.log(`\n🛡️ Other Features:`);
      
      try {
        const paused = await contract.paused();
        console.log(`   ✅ Pausable: ${paused ? 'Paused' : 'Active'}`);
      } catch (error) {
        console.log(`   ❌ Pausable: Not supported`);
      }

      try {
        const maxSupply = await contract.maxSupply();
        console.log(`   ✅ Max Supply: ${ethers.formatUnits(maxSupply, await contract.decimals())}`);
      } catch (error) {
        console.log(`   ❌ Max Supply: Not supported`);
      }

      try {
        const version = await contract.version();
        console.log(`   ✅ Version: ${version}`);
      } catch (error) {
        console.log(`   ❌ Version: Not supported`);
      }

      // Summary
      console.log(`\n📊 Summary:`);
      let hasAdvancedControls = false;
      try {
        await contract.conditionalTransfersEnabled();
        await contract.transferWhitelistEnabled();
        await contract.transferFeesEnabled();
        hasAdvancedControls = true;
      } catch (error) {
        hasAdvancedControls = false;
      }

      if (hasAdvancedControls) {
        console.log(`   🎉 This token SUPPORTS advanced transfer controls!`);
        console.log(`   🌐 Admin Panel: http://localhost:7788/transfer-controls?token=${token.address}`);
      } else {
        console.log(`   ⚠️  This token does NOT support advanced transfer controls.`);
        console.log(`   💡 Use the Advanced Control Token (ACT) for testing these features.`);
      }

    } catch (error) {
      console.log(`❌ Failed to check token: ${error.message}`);
    }
  }

  console.log(`\n🎯 RECOMMENDATION:`);
  console.log(`Use the Advanced Control Token (ACT) at address:`);
  console.log(`******************************************`);
  console.log(`\nThis token has all the advanced transfer control features enabled and ready to test!`);
  console.log(`\n🌐 Direct link: http://localhost:7788/transfer-controls?token=******************************************`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Check failed:", error);
    process.exit(1);
  });
