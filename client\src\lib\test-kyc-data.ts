/**
 * Test KYC Data for Development
 *
 * This file contains sample ID card data for testing the KYC verification process.
 * These are fictional documents that can be used during development.
 */

export interface TestIdCard {
  id: string;
  name: string;
  description: string;
  document_type: string;
  first_name: string;
  last_name: string;
  full_name: string;
  date_of_birth: string;
  nationality: string;
  issuing_state: string;
  issuing_state_name: string;
  document_number: string;
  personal_number?: string;
  expiration_date: string;
  date_of_issue: string;
  place_of_birth: string;
  gender: 'M' | 'F' | 'U';
  marital_status: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED' | 'UNKNOWN';
  address: string;
  image_url?: string;
}

/**
 * Sample test ID cards for development
 * These can be used to test the KYC comparison functionality
 */
export const TEST_ID_CARDS: TestIdCard[] = [
  {
    id: 'test-us-passport-001',
    name: 'US Passport - John <PERSON>',
    description: 'Sample US passport for testing',
    document_type: 'Passport',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    full_name: '<PERSON>',
    date_of_birth: '1990-01-15',
    nationality: 'USA',
    issuing_state: 'USA',
    issuing_state_name: 'United States',
    document_number: 'P123456789',
    expiration_date: '2030-01-15',
    date_of_issue: '2020-01-15',
    place_of_birth: 'New York, NY',
    gender: 'M',
    marital_status: 'SINGLE',
    address: '123 Main Street, New York, NY 10001',
  },
  {
    id: 'test-uk-id-001',
    name: 'UK ID Card - Jane Smith',
    description: 'Sample UK ID card for testing',
    document_type: 'Identity Card',
    first_name: 'Jane',
    last_name: 'Smith',
    full_name: 'Jane Smith',
    date_of_birth: '1985-06-20',
    nationality: 'GBR',
    issuing_state: 'GBR',
    issuing_state_name: 'United Kingdom',
    document_number: 'ID987654321',
    personal_number: '*********',
    expiration_date: '2028-06-20',
    date_of_issue: '2018-06-20',
    place_of_birth: 'London',
    gender: 'F',
    marital_status: 'MARRIED',
    address: '456 Oxford Street, London, W1A 1AB',
  },
  {
    id: 'test-de-id-001',
    name: 'German ID Card - Hans Mueller',
    description: 'Sample German ID card for testing',
    document_type: 'Identity Card',
    first_name: 'Hans',
    last_name: 'Mueller',
    full_name: 'Hans Mueller',
    date_of_birth: '1992-03-10',
    nationality: 'DEU',
    issuing_state: 'DEU',
    issuing_state_name: 'Germany',
    document_number: '*********',
    personal_number: '920310-M-001',
    expiration_date: '2032-03-10',
    date_of_issue: '2022-03-10',
    place_of_birth: 'Berlin',
    gender: 'M',
    marital_status: 'SINGLE',
    address: 'Unter den Linden 1, 10117 Berlin',
  },
  {
    id: 'test-ca-dl-001',
    name: 'Canadian Driver\'s License - Sarah Johnson',
    description: 'Sample Canadian driver\'s license for testing',
    document_type: 'Driver\'s License',
    first_name: 'Sarah',
    last_name: 'Johnson',
    full_name: 'Sarah Johnson',
    date_of_birth: '1988-11-25',
    nationality: 'CAN',
    issuing_state: 'CAN',
    issuing_state_name: 'Canada',
    document_number: 'D123-***********',
    expiration_date: '2026-11-25',
    date_of_issue: '2021-11-25',
    place_of_birth: 'Toronto, ON',
    gender: 'F',
    marital_status: 'DIVORCED',
    address: '789 Maple Avenue, Toronto, ON M5V 3A8',
  },
  {
    id: 'test-au-passport-001',
    name: 'Australian Passport - Michael Brown',
    description: 'Sample Australian passport for testing',
    document_type: 'Passport',
    first_name: 'Michael',
    last_name: 'Brown',
    full_name: 'Michael Brown',
    date_of_birth: '1975-09-08',
    nationality: 'AUS',
    issuing_state: 'AUS',
    issuing_state_name: 'Australia',
    document_number: '********',
    expiration_date: '2029-09-08',
    date_of_issue: '2019-09-08',
    place_of_birth: 'Sydney, NSW',
    gender: 'M',
    marital_status: 'MARRIED',
    address: '321 George Street, Sydney, NSW 2000',
  }
];

/**
 * Get a test ID card by ID
 */
export function getTestIdCard(id: string): TestIdCard | undefined {
  return TEST_ID_CARDS.find(card => card.id === id);
}

/**
 * Get all test ID cards
 */
export function getAllTestIdCards(): TestIdCard[] {
  return TEST_ID_CARDS;
}

/**
 * Get test ID cards by document type
 */
export function getTestIdCardsByType(documentType: string): TestIdCard[] {
  return TEST_ID_CARDS.filter(card =>
    card.document_type.toLowerCase() === documentType.toLowerCase()
  );
}

/**
 * Get test ID cards by nationality
 */
export function getTestIdCardsByNationality(nationality: string): TestIdCard[] {
  return TEST_ID_CARDS.filter(card =>
    card.nationality.toLowerCase() === nationality.toLowerCase()
  );
}

/**
 * Create a mock KYC response for testing
 */
export function createMockKycResponse(testCard: TestIdCard) {
  return {
    status: 'Approved',
    ocr_status: 'Approved',
    epassport_status: 'Not Applicable',
    document_type: testCard.document_type,
    document_number: testCard.document_number,
    personal_number: testCard.personal_number || '',
    issuing_state: testCard.issuing_state,
    issuing_state_name: testCard.issuing_state_name,
    first_name: testCard.first_name,
    last_name: testCard.last_name,
    full_name: testCard.full_name,
    gender: testCard.gender,
    date_of_birth: testCard.date_of_birth,
    expiration_date: testCard.expiration_date,
    date_of_issue: testCard.date_of_issue,
    place_of_birth: testCard.place_of_birth,
    marital_status: testCard.marital_status,
    nationality: testCard.nationality,
    address: testCard.address,
    formatted_address: testCard.address,
    is_nfc_verified: false,
    created_at: new Date().toISOString(),
    // Mock image URLs (these would be temporary URLs in real KYC responses)
    portrait_image: 'https://example.com/mock-portrait.jpg',
    front_image: 'https://example.com/mock-front.jpg',
    back_image: 'https://example.com/mock-back.jpg',
    front_video: 'https://example.com/mock-front.mp4',
    back_video: 'https://example.com/mock-back.mp4',
    full_front_image: 'https://example.com/mock-full-front.jpg',
    full_back_image: 'https://example.com/mock-full-back.jpg',
  };
}

/**
 * Instructions for using test data with KYC systems
 */
export const TEST_INSTRUCTIONS = {
  title: 'Using Test Data with KYC Systems',
  description: 'You can use these approaches for testing KYC functionality:',
  methods: [
    {
      name: 'Mock Testing',
      description: 'Use the mock KYC responses provided in this file to test your comparison logic without actual document scanning.',
      steps: [
        'Create a test user profile with data matching one of the test ID cards',
        'Use createMockKycResponse() to generate a mock KYC response',
        'Test your comparison logic with the mock data'
      ]
    },
    {
      name: 'Real Document Testing',
      description: 'Use real documents for testing, but be aware of privacy and security considerations.',
      steps: [
        'Use your own ID documents for testing (ensure you\'re comfortable with this)',
        'Create test profiles with slightly different data to test mismatch scenarios',
        'Always use development/staging environments, never production'
      ]
    },
    {
      name: 'Synthetic Document Testing',
      description: 'Create synthetic/fake documents for testing purposes only.',
      steps: [
        'Use image editing tools to create fake ID documents',
        'Ensure these are clearly marked as test documents',
        'Never use these for any real verification purposes'
      ]
    }
  ],
  notes: [
    'Always test in development/staging environments',
    'Be mindful of privacy when using real documents',
    'Test both matching and non-matching scenarios',
    'Verify that your comparison logic handles edge cases properly'
  ]
};

/**
 * Development helper to log test instructions
 */
export function logTestInstructions() {
  if (process.env.NODE_ENV === 'development') {
    console.log('=== KYC Test Data Instructions ===');
    console.log(TEST_INSTRUCTIONS.description);
    console.log('\nAvailable test ID cards:');
    TEST_ID_CARDS.forEach(card => {
      console.log(`- ${card.name}: ${card.description}`);
    });
    console.log('\nFor detailed testing instructions, see TEST_INSTRUCTIONS in test-kyc-data.ts');
  }
}
