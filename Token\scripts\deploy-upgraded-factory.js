require("dotenv").config();
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    console.log("🚀 Deploying Upgraded Security Token Factory with Image Support...");
    console.log("=" .repeat(60));

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);

    // Check balance
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");

    // Get network information
    const network = await deployer.provider.getNetwork();
    const networkName = network.name === "unknown" ? "amoy" : network.name;
    console.log("Network:", networkName, "Chain ID:", network.chainId.toString());

    // Gas settings for Amoy testnet
    const gasOptions = {
      gasLimit: 8000000,
      gasPrice: ethers.parseUnits("50", "gwei")
    };

    console.log("Gas settings:");
    console.log("- Gas Limit:", gasOptions.gasLimit.toString());
    console.log("- Gas Price:", ethers.formatUnits(gasOptions.gasPrice, "gwei"), "gwei");

    console.log("\n📋 Step 1: Deploying SecurityTokenFactory...");

    // Deploy the factory contract
    const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    const factory = await SecurityTokenFactory.deploy(
      deployer.address // admin address
    );

    await factory.waitForDeployment();
    const factoryAddress = await factory.getAddress();

    console.log("✅ SecurityTokenFactory deployed to:", factoryAddress);

    // Get implementation addresses from the factory
    const tokenImplementation = await factory.securityTokenImplementation();
    const whitelistImplementation = await factory.whitelistImplementation();
    const whitelistWithKYCImplementation = await factory.whitelistWithKYCImplementation();

    console.log("\n📋 Implementation Addresses:");
    console.log("- SecurityToken Implementation:", tokenImplementation);
    console.log("- Whitelist Implementation:", whitelistImplementation);
    console.log("- WhitelistWithKYC Implementation:", whitelistWithKYCImplementation);

    // Test enumeration functionality
    console.log("\n🧪 Testing Factory Enumeration...");
    try {
      const tokenCount = await factory.getTokenCount();
      console.log("✅ getTokenCount() works:", tokenCount.toString());

      const allTokens = await factory.getAllDeployedTokens();
      console.log("✅ getAllDeployedTokens() works:", allTokens.length, "tokens");
    } catch (error) {
      console.log("❌ Enumeration test failed:", error.message);
    }

    // Save deployment information
    const deploymentInfo = {
      network: networkName,
      chainId: network.chainId.toString(),
      factory: factoryAddress,
      implementations: {
        securityToken: tokenImplementation,
        whitelist: whitelistImplementation,
        whitelistWithKYC: whitelistWithKYCImplementation
      },
      deployer: deployer.address,
      timestamp: new Date().toISOString(),
      features: [
        "Token Enumeration (getTokenCount, getAllDeployedTokens)",
        "Image/Logo URL Support",
        "Configurable Decimals (0-18)",
        "KYC Support",
        "Role-based Access Control"
      ],
      gasUsed: {
        gasLimit: gasOptions.gasLimit.toString(),
        gasPrice: ethers.formatUnits(gasOptions.gasPrice, "gwei") + " gwei"
      }
    };

    // Create deployments directory if it doesn't exist
    const deploymentsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    // Save deployment file
    const deploymentFile = path.join(deploymentsDir, `${networkName}-upgraded.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

    console.log("\n💾 Deployment information saved to:", deploymentFile);

    // Update admin panel configuration
    console.log("\n🔧 Updating Admin Panel Configuration...");
    const configPath = path.join(__dirname, "../admin-panel/src/config.ts");

    if (fs.existsSync(configPath)) {
      let configContent = fs.readFileSync(configPath, 'utf8');

      // Update the factory address for the current network
      const factoryRegex = new RegExp(`(${networkName}:\\s*{[^}]*factory:\\s*")[^"]*(")`);
      if (configContent.match(factoryRegex)) {
        configContent = configContent.replace(factoryRegex, `$1${factoryAddress}$2`);
        fs.writeFileSync(configPath, configContent);
        console.log("✅ Updated admin panel config with new factory address");
      } else {
        console.log("⚠️  Could not automatically update config - please update manually");
      }
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!");
    console.log("=" .repeat(60));
    console.log("📋 Summary:");
    console.log("- Factory Address:", factoryAddress);
    console.log("- Network:", networkName);
    console.log("- Features: Enumeration ✅, Image Support ✅, KYC ✅");
    console.log("- Admin Panel: Ready to use upgraded factory");
    console.log("\n🔗 Next Steps:");
    console.log("1. Update your admin panel to use the new factory address");
    console.log("2. Test token creation with image URL support");
    console.log("3. Verify enumeration works in the dashboard");

  } catch (error) {
    console.error("💥 Deployment failed:", error);
    process.exitCode = 1;
  }
}

// Run the deployment
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
