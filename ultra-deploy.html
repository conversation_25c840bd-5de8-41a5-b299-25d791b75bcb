<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra Token Deployment Tool</title>
    <!-- Using Web3.js instead of Ethers.js -->
    <script src="https://cdn.jsdelivr.net/npm/web3@1.8.2/dist/web3.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .container { margin-top: 20px; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 16px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card-header { font-weight: bold; margin-bottom: 12px; font-size: 18px; border-bottom: 1px solid #eee; padding-bottom: 8px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="number"] { width: 100%; padding: 10px; font-size: 16px; border: 1px solid #ccc; border-radius: 4px; }
        button { background-color: #4CAF50; color: white; padding: 12px 24px; border: none; cursor: pointer; font-size: 16px; border-radius: 4px; }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .btn-warning { background-color: #ff9800; }
        .btn-primary { background-color: #2196F3; }
        .result { margin-top: 15px; padding: 15px; border-radius: 4px; }
        .success { background-color: #dff0d8; color: #3c763d; }
        .error { background-color: #f2dede; color: #a94442; }
        .info { background-color: #d9edf7; color: #31708f; }
        pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 4px; }
        .loader { border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; width: 20px; height: 20px; animation: spin 2s linear infinite; display: inline-block; margin-right: 10px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .hidden { display: none; }
        .section-title { font-weight: bold; margin-top: 10px; }
        .gas-presets { display: flex; gap: 10px; margin-top: 10px; }
        .gas-preset-btn { padding: 8px 12px; background: #f0f0f0; border: 1px solid #ccc; cursor: pointer; border-radius: 4px; }
        .gas-preset-btn:hover { background: #e0e0e0; }
        .network-select { margin-bottom: 15px; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert-warning { background-color: #fff3cd; color: #856404; }
        .progress-indicator { margin-top: 15px; }
    </style>
</head>
<body>
    <h1>Ultra Token Deployment Tool for Amoy</h1>
    <p>This tool uses Web3.js for more robust RPC handling on the Amoy testnet.</p>

    <div class="card">
        <div class="card-header">Connection Settings</div>
        <div class="network-select">
            <label for="rpc-endpoint">RPC Endpoint:</label>
            <select id="rpc-endpoint" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                <option value="https://rpc-amoy.polygon.technology">Default - rpc-amoy.polygon.technology</option>
                <option value="https://polygon-amoy.blockpi.network/v1/rpc/public" selected>BlockPI - polygon-amoy.blockpi.network</option>
                <option value="https://polygon-amoy-rpc.publicnode.com">PublicNode - polygon-amoy-rpc.publicnode.com</option>
                <option value="https://polygon-amoy.drpc.org">DRPC - polygon-amoy.drpc.org</option>
            </select>
        </div>
        <button id="btn-connect" class="btn-warning">Connect MetaMask</button>
        <div id="connection-status" class="result info">Please connect your wallet</div>
    </div>

    <div class="card">
        <div class="card-header">Factory Settings</div>
        <div class="form-group">
            <label for="factory-address">Factory Address:</label>
            <input type="text" id="factory-address" value="******************************************">
        </div>
    </div>

    <div class="card">
        <div class="card-header">Token Settings</div>
        <div class="form-group">
            <label for="token-name">Token Name:</label>
            <input type="text" id="token-name" value="Nova">
        </div>
        <div class="form-group">
            <label for="token-symbol">Token Symbol:</label>
            <input type="text" id="token-symbol" value="NNN">
        </div>
        <div class="form-group">
            <label for="max-supply">Max Supply:</label>
            <input type="number" id="max-supply" value="1500000">
        </div>
        <div class="form-group">
            <label for="token-price">Token Price (Metadata):</label>
            <input type="text" id="token-price" value="3">
        </div>
        <div class="form-group">
            <label for="bonus-tiers">Bonus Tiers (Metadata):</label>
            <input type="text" id="bonus-tiers" value="Tier 1: 5%, Tier 2: 10%, Tier 3: 15%">
        </div>
    </div>

    <div class="card">
        <div class="card-header">Gas Settings</div>
        <div class="section-title">Gas Presets:</div>
        <div class="gas-presets">
            <button class="gas-preset-btn" onclick="setGasPreset(3000000, 100)">High</button>
            <button class="gas-preset-btn" onclick="setGasPreset(8000000, 300)">Very High</button>
            <button class="gas-preset-btn" onclick="setGasPreset(20000000, 500)">ULTRA</button>
        </div>
        <div class="form-group">
            <label for="gas-limit">Gas Limit:</label>
            <input type="number" id="gas-limit" value="8000000">
        </div>
        <div class="form-group">
            <label for="gas-price">Gas Price (gwei):</label>
            <input type="number" id="gas-price" value="300">
        </div>
    </div>

    <div class="card">
        <div class="card-header">Advanced Options</div>
        <div class="form-group">
            <label for="nonce">Manual Nonce (leave empty for auto):</label>
            <input type="number" id="nonce" placeholder="Auto">
        </div>
        <div class="form-group">
            <input type="checkbox" id="skip-estimation" checked>
            <label for="skip-estimation" style="display:inline"> Skip gas estimation (recommended)</label>
        </div>
    </div>

    <div class="card">
        <div class="card-header">Deployment Actions</div>
        <button id="btn-deploy" class="btn-primary" disabled>Deploy Token</button>
        <div id="operation-status" class="result hidden"></div>
        <div class="progress-indicator hidden" id="progress-indicator">
            <div class="loader"></div> Processing...
        </div>
    </div>

    <div class="card hidden" id="tx-card">
        <div class="card-header">Transaction Details</div>
        <pre id="tx-details"></pre>
    </div>

    <div class="card hidden" id="token-card">
        <div class="card-header">Deployed Token Information</div>
        <pre id="token-details"></pre>
    </div>

    <script>
        // Factory ABI - just the method we need
        const FACTORY_ABI = [
            {
                "inputs": [
                    {"internalType": "string", "name": "name", "type": "string"},
                    {"internalType": "string", "name": "symbol", "type": "string"},
                    {"internalType": "uint256", "name": "maxSupply", "type": "uint256"},
                    {"internalType": "address", "name": "admin", "type": "address"},
                    {"internalType": "string", "name": "tokenPrice", "type": "string"},
                    {"internalType": "string", "name": "bonusTiers", "type": "string"}
                ],
                "name": "deploySecurityToken",
                "outputs": [
                    {"internalType": "address", "name": "", "type": "address"},
                    {"internalType": "address", "name": "", "type": "address"}
                ],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}],
                "name": "getTokenAddressBySymbol",
                "outputs": [{"internalType": "address", "name": "", "type": "address"}],
                "stateMutability": "view",
                "type": "function"
            }
        ];

        let web3, userAccount;
        const CHAIN_ID = 80002; // Amoy testnet

        // Set gas preset values
        function setGasPreset(gasLimit, gasPrice) {
            document.getElementById('gas-limit').value = gasLimit;
            document.getElementById('gas-price').value = gasPrice;
        }

        // Connect to MetaMask
        async function connectWallet() {
            try {
                document.getElementById('btn-connect').disabled = true;
                updateStatus('connection-status', 'Connecting to wallet...', 'info');
                
                // Improved MetaMask detection with troubleshooting
                if (typeof window.ethereum === 'undefined') {
                    const errorMsg = `
                        MetaMask not detected! This could be due to one of the following reasons:
                        
                        1. MetaMask is not installed - Install from metamask.io
                        2. You're opening this file directly from your filesystem (file://) - Try hosting it on a web server
                        3. MetaMask is disabled for this page - Check your MetaMask settings
                        
                        SOLUTION: Try one of these options:
                        - Open this file using a local web server (like http://localhost)
                        - Use the direct-deploy.bat script from PowerShell instead
                        - Try another browser with MetaMask installed
                    `;
                    updateStatus('connection-status', errorMsg, 'error');
                    
                    // Enable deploy button for testing without MetaMask
                    document.getElementById('btn-deploy').disabled = true;
                    
                    // Show a button to try again
                    const statusElem = document.getElementById('connection-status');
                    statusElem.innerHTML += '<br><br><button onclick="tryAgainConnect()" class="btn-warning">Try Again</button> <button onclick="useAltMethod()" class="btn-warning">Try Alternative Method</button>';
                    
                    document.getElementById('btn-connect').disabled = false;
                    return;
                }

                // Set up Web3 with custom RPC
                const rpcUrl = document.getElementById('rpc-endpoint').value;
                web3 = new Web3(window.ethereum);
                
                // Request accounts
                const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                userAccount = accounts[0];
                
                // Check network
                try {
                    const chainId = await web3.eth.getChainId();
                    
                    if (chainId !== CHAIN_ID) {
                        updateStatus('connection-status', 
                            `WARNING: Not connected to Amoy testnet (chainId 80002)! Current chainId: ${chainId}
                            
                            Please switch to Amoy testnet in your MetaMask:
                            Network Name: Polygon Amoy Testnet
                            RPC URL: ${rpcUrl}
                            Chain ID: 80002
                            Currency Symbol: MATIC`, 'error');
                    } else {
                        updateStatus('connection-status', `Connected to Amoy testnet as ${userAccount}`, 'success');
                        document.getElementById('btn-deploy').disabled = false;
                    }
                } catch (error) {
                    console.error("Error checking chain:", error);
                    updateStatus('connection-status', 
                        `WARNING: Could not detect network. Please ensure you're connected to Amoy testnet (chainId 80002).
                        
                        Network Name: Polygon Amoy Testnet
                        RPC URL: ${rpcUrl}
                        Chain ID: 80002
                        Currency Symbol: MATIC`, 'error');
                    
                    // Still enable deploy since we'll force the right chainId
                    document.getElementById('btn-deploy').disabled = false;
                }
                
                // Listen for account changes
                window.ethereum.on('accountsChanged', function (accounts) {
                    window.location.reload();
                });
                
                // Listen for network changes
                window.ethereum.on('chainChanged', function (chainId) {
                    window.location.reload();
                });
                
                document.getElementById('btn-connect').disabled = false;
            } catch (error) {
                console.error("Connection error:", error);
                updateStatus('connection-status', `Connection error: ${error.message}`, 'error');
                document.getElementById('btn-connect').disabled = false;
            }
        }

        // Try again function for MetaMask connection
        function tryAgainConnect() {
            // Force reload page to retry MetaMask detection
            window.location.reload();
        }
        
        // Show alternative method instructions
        function useAltMethod() {
            updateStatus('connection-status', `
                Alternative Method: Use PowerShell to deploy your token
                
                1. Open PowerShell
                2. Navigate to your project directory
                3. Run the following command:
                
                $env:FACTORY_ADDRESS="******************************************"; $env:TOKEN_NAME="Nova"; $env:TOKEN_SYMBOL="NNN"; $env:MAX_SUPPLY="1500000"; $env:TOKEN_PRICE="3"; $env:BONUS_TIERS="Tier 1: 5%, Tier 2: 10%, Tier 3: 15%"; $env:GAS_LIMIT="8000000"; $env:GAS_PRICE="300"; npx hardhat run scripts/direct-deploy-token.js --network amoy
            `, 'info');
        }

        // Deploy token
        async function deployToken() {
            try {
                // Show progress indicator
                document.getElementById('progress-indicator').classList.remove('hidden');
                document.getElementById('btn-deploy').disabled = true;
                
                // Update status
                updateStatus('operation-status', 'Preparing to deploy token...', 'info');
                document.getElementById('operation-status').classList.remove('hidden');
                
                // Get form values
                const factoryAddress = document.getElementById('factory-address').value.trim();
                const tokenName = document.getElementById('token-name').value.trim();
                const tokenSymbol = document.getElementById('token-symbol').value.trim();
                const maxSupply = document.getElementById('max-supply').value;
                const tokenPrice = document.getElementById('token-price').value.trim();
                const bonusTiers = document.getElementById('bonus-tiers').value.trim();
                const gasLimit = parseInt(document.getElementById('gas-limit').value);
                const gasPriceGwei = parseInt(document.getElementById('gas-price').value);
                const skipEstimation = document.getElementById('skip-estimation').checked;
                const manualNonce = document.getElementById('nonce').value.trim();
                
                // Validate inputs
                if (!web3.utils.isAddress(factoryAddress)) {
                    throw new Error("Invalid factory address");
                }
                
                if (!tokenName || tokenName.length < 1) {
                    throw new Error("Token name is required");
                }
                
                if (!tokenSymbol || tokenSymbol.length < 1) {
                    throw new Error("Token symbol is required");
                }
                
                if (!maxSupply || parseFloat(maxSupply) <= 0) {
                    throw new Error("Max supply must be a positive number");
                }
                
                // Create factory contract instance
                const factory = new web3.eth.Contract(FACTORY_ABI, factoryAddress);
                
                // Convert maxSupply to wei (tokens have 18 decimals)
                const maxSupplyWei = web3.utils.toWei(maxSupply.toString(), 'ether');
                
                // Get nonce - either manual or from blockchain
                let nonce;
                if (manualNonce && manualNonce !== '') {
                    nonce = parseInt(manualNonce);
                    console.log(`Using manual nonce: ${nonce}`);
                } else {
                    try {
                        // Try multiple ways to get the nonce
                        try {
                            nonce = await web3.eth.getTransactionCount(userAccount, 'pending');
                        } catch (e) {
                            console.log("Error getting pending nonce, trying latest");
                            nonce = await web3.eth.getTransactionCount(userAccount, 'latest');
                        }
                        console.log(`Got nonce from blockchain: ${nonce}`);
                    } catch (error) {
                        console.error("Error getting nonce:", error);
                        throw new Error(`Failed to get nonce: ${error.message}`);
                    }
                }
                
                // Prepare function data
                const data = factory.methods.deploySecurityToken(
                    tokenName,
                    tokenSymbol,
                    maxSupplyWei,
                    userAccount,
                    tokenPrice,
                    bonusTiers
                ).encodeABI();
                
                // Create transaction object
                const txObject = {
                    from: userAccount,
                    to: factoryAddress,
                    data: data,
                    nonce: web3.utils.toHex(nonce),
                    chainId: CHAIN_ID,
                    value: '0x0'
                };
                
                // Set explicit gas price in wei
                const gasPriceWei = web3.utils.toWei(gasPriceGwei.toString(), 'gwei');
                txObject.gasPrice = web3.utils.toHex(gasPriceWei);
                
                // Either estimate gas or use provided limit
                if (!skipEstimation) {
                    try {
                        updateStatus('operation-status', 'Estimating gas (this might fail on Amoy)...', 'info');
                        const estimatedGas = await web3.eth.estimateGas(txObject);
                        console.log(`Estimated gas: ${estimatedGas}`);
                        
                        // Use 150% of estimated gas to be safe
                        txObject.gas = web3.utils.toHex(Math.floor(estimatedGas * 1.5));
                        console.log(`Using 150% of estimated gas: ${Math.floor(estimatedGas * 1.5)}`);
                    } catch (error) {
                        console.warn("Gas estimation failed:", error);
                        updateStatus('operation-status', 'Gas estimation failed, using provided gas limit', 'info');
                        txObject.gas = web3.utils.toHex(gasLimit);
                    }
                } else {
                    // Skip estimation and use provided gas limit
                    txObject.gas = web3.utils.toHex(gasLimit);
                    console.log(`Skipped estimation, using provided gas: ${gasLimit}`);
                }
                
                // Log the transaction object
                document.getElementById('tx-card').classList.remove('hidden');
                document.getElementById('tx-details').textContent = JSON.stringify(txObject, null, 2);
                
                // Send the transaction
                updateStatus('operation-status', 'Sending transaction... Please confirm in MetaMask', 'info');
                
                const txHash = await new Promise((resolve, reject) => {
                    window.ethereum.request({
                        method: 'eth_sendTransaction',
                        params: [txObject]
                    })
                    .then(hash => resolve(hash))
                    .catch(error => reject(error));
                });
                
                // Display the transaction hash
                document.getElementById('tx-details').textContent += "\n\nTransaction Hash: " + txHash;
                updateStatus('operation-status', `Transaction sent! Hash: ${txHash}`, 'info');
                
                // Add the block explorer link
                const explorerUrl = `https://amoy.polygonscan.com/tx/${txHash}`;
                document.getElementById('tx-details').textContent += `\n\nView on explorer: ${explorerUrl}`;
                
                // Wait for transaction receipt with timeout logic
                updateStatus('operation-status', 
                    `Transaction submitted! You can check the status at: 
                    <a href="${explorerUrl}" target="_blank">${explorerUrl}</a>
                    
                    Waiting for confirmation (may take several minutes)...`, 'info');
                
                // Set a timeout flag
                let isTimedOut = false;
                const timeoutId = setTimeout(() => {
                    isTimedOut = true;
                    updateStatus('operation-status', 
                        `Waiting for confirmation timed out, but your transaction has been submitted!
                        
                        Check status at: <a href="${explorerUrl}" target="_blank">${explorerUrl}</a>
                        
                        The Amoy testnet is processing your transaction. This could take a few minutes.`, 'info');
                        
                    showDeploymentInfo(txHash, tokenSymbol, tokenName, maxSupply);
                }, 60000); // 60-second timeout
                
                // Poll for receipt to avoid potential receipt waiting issues
                let receipt = null;
                let attempts = 0;
                const maxAttempts = 20;
                
                while (!receipt && !isTimedOut && attempts < maxAttempts) {
                    try {
                        receipt = await web3.eth.getTransactionReceipt(txHash);
                        if (receipt) {
                            // Clear timeout since we got the receipt
                            clearTimeout(timeoutId);
                            
                            if (receipt.status) {
                                updateStatus('operation-status', `Success! Transaction confirmed in block ${receipt.blockNumber}`, 'success');
                                await showTokenInfo(factoryAddress, tokenSymbol, tokenName, maxSupply);
                            } else {
                                updateStatus('operation-status', `Transaction failed! See explorer for details: <a href="${explorerUrl}" target="_blank">${explorerUrl}</a>`, 'error');
                            }
                            break;
                        }
                    } catch (error) {
                        console.warn(`Receipt polling attempt ${attempts + 1} failed:`, error);
                    }
                    
                    // Wait 5 seconds before next attempt
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    attempts++;
                    
                    // Update status every few attempts
                    if (attempts % 2 === 0) {
                        updateStatus('operation-status', 
                            `Still waiting for confirmation (attempt ${attempts}/${maxAttempts})... 
                            Check status: <a href="${explorerUrl}" target="_blank">${explorerUrl}</a>`, 'info');
                    }
                }
                
                // If we run out of attempts but haven't timed out
                if (!receipt && !isTimedOut) {
                    clearTimeout(timeoutId);
                    updateStatus('operation-status', 
                        `Transaction submitted but confirmation is taking longer than expected.
                        Check status manually: <a href="${explorerUrl}" target="_blank">${explorerUrl}</a>`, 'info');
                    
                    showDeploymentInfo(txHash, tokenSymbol, tokenName, maxSupply);
                }
                
            } catch (error) {
                console.error("Deployment error:", error);
                
                let errorMsg = error.message;
                
                // Special handling for common MetaMask errors
                if (errorMsg.includes("user denied") || errorMsg.includes("User rejected") || errorMsg.includes("user rejected")) {
                    errorMsg = "Transaction was rejected in MetaMask. Please try again.";
                } else if (errorMsg.includes("insufficient funds")) {
                    errorMsg = "Insufficient funds for gas * price + value. Make sure you have enough MATIC in your wallet.";
                } else if (errorMsg.includes("underpriced")) {
                    errorMsg = "Transaction underpriced. Try increasing the gas price with the 'Very High' or 'ULTRA' preset.";
                } else if (errorMsg.includes("Internal JSON-RPC error")) {
                    errorMsg = "RPC Error: This is common on Amoy testnet. Try:\n1. Using the 'ULTRA' preset for gas settings\n2. Selecting a different RPC endpoint\n3. Setting a manual nonce and trying again";
                }
                
                updateStatus('operation-status', `Error: ${errorMsg}`, 'error');
            } finally {
                document.getElementById('btn-deploy').disabled = false;
                document.getElementById('progress-indicator').classList.add('hidden');
            }
        }

        // Show token information after deployment
        async function showTokenInfo(factoryAddress, tokenSymbol, tokenName, maxSupply) {
            try {
                // Create factory contract instance
                const factory = new web3.eth.Contract(FACTORY_ABI, factoryAddress);
                
                // Try to get the token address by symbol
                const tokenAddress = await factory.methods.getTokenAddressBySymbol(tokenSymbol).call();
                
                if (tokenAddress && tokenAddress !== '******************************************') {
                    // Show token details
                    document.getElementById('token-card').classList.remove('hidden');
                    document.getElementById('token-details').textContent = `
Token Address: ${tokenAddress}

To interact with this token, use the following information:
- Token Address: ${tokenAddress}
- Token Name: ${tokenName}
- Token Symbol: ${tokenSymbol}
- Max Supply: ${maxSupply}

You can now manage this token using the direct-tx.bat script or the admin panel.
`;
                } else {
                    document.getElementById('token-card').classList.remove('hidden');
                    document.getElementById('token-details').textContent = `
Transaction was successful, but couldn't find token address by symbol "${tokenSymbol}".
This might happen if the token symbol was already used or if there's a delay in the factory's state update.

Please check the transaction on the block explorer and look for the 'TokenDeployed' event in the logs.
`;
                }
            } catch (error) {
                console.error("Error getting token info:", error);
                
                document.getElementById('token-card').classList.remove('hidden');
                document.getElementById('token-details').textContent = `
Transaction may have been successful, but encountered an error while retrieving token address:
${error.message}

Please wait a few minutes and then:
1. Check the transaction on the block explorer
2. Look for the 'TokenDeployed' event in the logs
3. Or try getting the token address using the symbol "${tokenSymbol}" in your contract later
`;
            }
        }

        // Show generic deployment info when we don't have a receipt yet
        function showDeploymentInfo(txHash, tokenSymbol, tokenName, maxSupply) {
            document.getElementById('token-card').classList.remove('hidden');
            document.getElementById('token-details').textContent = `
Transaction Hash: ${txHash}
View on explorer: https://amoy.polygonscan.com/tx/${txHash}

After the transaction is confirmed, you can find your token address:
1. Check the transaction on the block explorer
2. Look for the 'TokenDeployed' event in the logs
3. Or use the token symbol "${tokenSymbol}" in the factory's getTokenAddressBySymbol method

Token details (for future reference):
- Token Name: ${tokenName}
- Token Symbol: ${tokenSymbol}
- Max Supply: ${maxSupply}
`;
        }

        // Helper function to update status
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message; // Use innerHTML to allow links
            element.className = `result ${type}`;
        }

        // Add event listeners
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('btn-connect').addEventListener('click', connectWallet);
            document.getElementById('btn-deploy').addEventListener('click', deployToken);
            
            // Delayed auto-connect to give MetaMask time to inject provider
            setTimeout(() => {
                if (window.ethereum) {
                    connectWallet();
                }
            }, 500); // 500ms delay to allow MetaMask to initialize
        });
    </script>
</body>
</html> 