const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Testing Simplified Agreement Features...");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  // Use the newly deployed token
  const tokenAddress = "******************************************";
  console.log("Testing token:", tokenAddress);

  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  const token = SecurityToken.attach(tokenAddress);

  console.log("\n🔍 Testing simplified agreement functions...");

  try {
    // Test hasAcceptedAgreement (should be false initially)
    const hasAcceptedInitial = await token.hasAcceptedAgreement(deployer.address);
    console.log("✅ hasAcceptedAgreement (initial):", hasAcceptedInitial);

    if (hasAcceptedInitial) {
      console.log("❌ Initial agreement status should be false");
    } else {
      console.log("✅ Initial agreement status is correctly false");
    }

    // Test getAgreementAcceptanceTimestamp (should be 0 initially)
    const timestampInitial = await token.getAgreementAcceptanceTimestamp(deployer.address);
    console.log("✅ Initial timestamp:", timestampInitial.toString());

    if (timestampInitial == 0) {
      console.log("✅ Initial timestamp is correctly 0");
    } else {
      console.log("❌ Initial timestamp should be 0");
    }

    // Test acceptAgreement
    console.log("\n📝 Accepting agreement...");
    const acceptTx = await token.acceptAgreement();
    console.log("Accept transaction:", acceptTx.hash);
    const receipt = await acceptTx.wait();
    console.log("✅ Agreement accepted successfully");

    // Check for AgreementAccepted event
    const agreementAcceptedEvent = receipt.logs.find(log => {
      try {
        const parsed = token.interface.parseLog(log);
        return parsed.name === 'AgreementAccepted';
      } catch {
        return false;
      }
    });

    if (agreementAcceptedEvent) {
      const parsed = token.interface.parseLog(agreementAcceptedEvent);
      console.log("✅ AgreementAccepted event found:");
      console.log("  - Account:", parsed.args.account);
      console.log("  - Timestamp:", parsed.args.timestamp.toString());
    } else {
      console.log("❌ AgreementAccepted event not found");
    }

    // Verify acceptance
    const hasAcceptedAfter = await token.hasAcceptedAgreement(deployer.address);
    console.log("✅ Agreement status after acceptance:", hasAcceptedAfter);

    if (hasAcceptedAfter) {
      console.log("✅ Agreement acceptance verified");
    } else {
      console.log("❌ Agreement acceptance not recorded");
    }

    // Get acceptance timestamp
    const timestampAfter = await token.getAgreementAcceptanceTimestamp(deployer.address);
    console.log("✅ Agreement acceptance timestamp:", timestampAfter.toString());

    if (timestampAfter > 0) {
      console.log("✅ Timestamp recorded correctly");
    } else {
      console.log("❌ Timestamp not recorded");
    }

    // Test trying to accept again (should fail)
    try {
      console.log("\n🔄 Testing duplicate acceptance (should fail)...");
      await token.acceptAgreement();
      console.log("❌ Duplicate acceptance should have failed");
    } catch (error) {
      if (error.message.includes("agreement already accepted")) {
        console.log("✅ Duplicate acceptance correctly prevented");
      } else {
        console.log("❌ Unexpected error:", error.message);
      }
    }

    console.log("\n🎉 SIMPLIFIED AGREEMENT FEATURES TEST SUMMARY");
    console.log("=============================================");
    console.log("Token Address:", tokenAddress);
    console.log("✅ All simplified agreement functions working correctly!");
    console.log("");
    console.log("Key Features:");
    console.log("- ✅ Boolean agreement acceptance tracking");
    console.log("- ✅ Timestamp recording");
    console.log("- ✅ Duplicate acceptance prevention");
    console.log("- ✅ Event emission");
    console.log("- ✅ No URL dependency (off-chain document management)");
    console.log("");
    console.log("🌐 Test this in admin panel:");
    console.log(`http://localhost:3000/clients/`);
    console.log("");
    console.log("🌐 Test client portal with simplified agreements:");
    console.log(`http://localhost:7788/`);

  } catch (error) {
    console.log("❌ Agreement function test failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
