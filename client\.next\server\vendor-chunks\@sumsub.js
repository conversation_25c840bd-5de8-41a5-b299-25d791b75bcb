"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@sumsub";
exports.ids = ["vendor-chunks/@sumsub"];
exports.modules = {

/***/ "(ssr)/./node_modules/@sumsub/websdk/dist/index.esm.js":
/*!*******************************************************!*\
  !*** ./node_modules/@sumsub/websdk/dist/index.esm.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ s)\n/* harmony export */ });\nvar t=function(){return t=Object.assign||function(t){for(var i,e=1,o=arguments.length;e<o;e++)for(var n in i=arguments[e])Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n]);return t},t.apply(this,arguments)};function i(t){return t.endsWith(\"-v2\")?2:1}var e=function(){function e(t,i,e,o){var n=this;this.iframe=null,this.iframeId=null,this.sessionId=\"\",this.$container=null,this.initialized=!1,this.scrollPosition=-1,this.scrollElement=null,this.onMessage=function(t){n.onSdkMessage(t)},this.baseUrl=t||this.getCurrentBaseUrl(\"https://api.sumsub.com\",i.accessToken),this.config=i,this.config.packageVersion=this.getVersion(),this.callbacks=e,this.options=o}return e.prototype.getVersion=function(){return\"2.3.16\"},e.prototype.getCurrentBaseUrl=function(t,i){var e=[\"_act-sbx-jwt-\",\"_act-jwt-\"].find((function(t){return i.startsWith(t)}));if(e)for(var o=0,n=i.replace(e,\"\").replace(\"-v2\",\"\").split(\".\").map((function(t){try{return atob(t)}catch(i){return t}})).map((function(t){try{return JSON.parse(t)}catch(i){return t}}));o<n.length;o++){var s=n[o];if(null==s?void 0:s.url)return s.url}return t},e.prototype.getIframeId=function(){if(2===this.config.version)try{return btoa(this.config.accessToken)}catch(t){return this.config.accessToken}return String(Math.floor(1e8*Math.random()))},e.prototype.launch=function(t){this.options.addViewportTag&&this.addViewportTag(),this.iframe=this.createIframe(t),this.sessionId=\"\",this.iframeId=\"id_\"+this.getIframeId(),this.iframe&&this.config?(this.registerEventListener(),this.iframe.src=this.getIframeSrc()):console.error(\"Idensic was not initialized since either provided element was not found in the DOM or invalid config\")},e.prototype.addViewportTag=function(){var t=document.getElementsByName(\"viewport\");if(!t||0===t.length){var i=document.createElement(\"meta\");i.setAttribute(\"name\",\"viewport\"),i.setAttribute(\"content\",\"width=device-width,user-scalable=yes\"),document.head.appendChild(i)}},e.prototype.createIframe=function(t){var i=\"string\"==typeof t?document.querySelector(t):t;if(!i)return console.error(\"Provide a valid selector for the iframe container\"),null;this.$container=i;var e=document.createElement(\"iframe\");for(e.width=\"100%\",e.scrolling=\"no\",e.allow=\"camera; microphone; geolocation; clipboard-write\",e.setAttribute(\"frameborder\",\"0\");i.firstChild;)i.removeChild(i.firstChild);return i.appendChild(e),e},e.prototype.getIframeSrc=function(){var t=\"?_=\"+this.iframeId,i=2===this.config.version?\"/websdk\":\"/idensic\",e=this.baseUrl+i+\"/websdk.html\";return 2===this.config.version&&this.config.theme&&(t+=\"&theme=\"+this.config.theme),2===this.config.version&&this.config.customizationName&&(t+=\"&customizationName=\"+encodeURIComponent(this.config.customizationName)),2===this.config.version&&this.config.translationName&&(t+=\"&translationName=\"+encodeURIComponent(this.config.translationName)),e+t},e.prototype.registerEventListener=function(){window.addEventListener(\"message\",this.onMessage)},e.prototype.onSdkMessage=function(i){var e;if(this.baseUrl===i.origin){var o=i.data;if(o.method&&~o.method.indexOf(\"idCheck\")&&(!this.sessionId||this.sessionId===o.sessionId)&&(null===(e=this.iframe)||void 0===e?void 0:e.contentWindow)){if(\"idCheck.onReady\"==o.method&&o.frameId===this.iframeId){this.sessionId=o.sessionId;var n={options:{adaptIframeHeight:this.options.adaptIframeHeight,enableScrollIntoView:this.options.enableScrollIntoView}};this.iframe.contentWindow.postMessage(t(t({method:\"idCheck.init\"},this.config),n),\"*\")}if(\"idCheck.onInitialized\"==o.method&&(this.initialized=!0),\"idCheck.onResize\"==o.method&&this.options.adaptIframeHeight&&(this.iframe.style.height=o.height+\"px\"),\"idCheck.scrollTo\"==o.method&&this.options.adaptIframeHeight)this.scrollTo(o.top);else if(\"idCheck.getScrollPosition\"!=o.method)if(\"idCheck.restoreScrollPosition\"!=o.method){var s=o.method;delete o.method,delete o.frameId,delete o.sessionId,\"idCheck.onError\"===s&&\"invalid-token\"===o.code?this.callExpirationHandler():\"idCheck.onError\"===s&&\"function\"==typeof this.callbacks.onError?this.callbacks.onError(o):\"function\"==typeof this.callbacks.onMessage&&this.callbacks.onMessage(s,o)}else this.restoreScrollPosition();else this.getScrollPosition()}}},e.prototype.callExpirationHandler=function(){var t=this,i=this.callbacks.expirationHandler;i.legacy?i.handler((function(i){return t.updateAccessTokenOrReinitialize(i)})):i.handler().then((function(i){return t.updateAccessTokenOrReinitialize(i)}),(function(i){null!=i||(i=\"Failed to update access token\"),i.message&&(i=i.message),\"string\"!=typeof i&&(i=String(i)),t.updateAccessToken(null,i)}))},e.prototype.updateAccessTokenOrReinitialize=function(t){var e=t?i(t):this.config.version;if(t&&this.iframe&&!this.initialized&&this.config.version!=e)return this.sessionId=\"\",this.config.accessToken=t,this.config.version=e,this.baseUrl=this.getCurrentBaseUrl(this.baseUrl,t),this.iframeId=\"id_\"+this.getIframeId(),void(this.iframe.src=this.getIframeSrc());this.updateAccessToken(t)},e.prototype.getScrolledElement=function(){for(var t,i=null===(t=this.iframe)||void 0===t?void 0:t.parentElement;0===(null==i?void 0:i.scrollTop)&&\"BODY\"!==(null==i?void 0:i.tagName);)i=null==i?void 0:i.parentElement;return i},e.prototype.getScrollPosition=function(){var t=this.getScrolledElement();this.scrollPosition=(null==t?void 0:t.scrollTop)||-1,this.scrollElement=this.scrollPosition>0&&t?t:null},e.prototype.restoreScrollPosition=function(){this.scrollElement&&this.scrollPosition>=0&&(this.scrollElement.scrollTop=this.scrollPosition),this.scrollPosition=-1,this.scrollElement=null},e.prototype.scrollTo=function(t){var i;if(this.options.enableScrollIntoView){var e=this.getScrolledElement();if(0===(null==e?void 0:e.scrollTop)&&\"BODY\"===(null==e?void 0:e.tagName)){var o=(null===(i=this.iframe)||void 0===i?void 0:i.getBoundingClientRect().top)||0;window.scrollTo({top:o+t,behavior:\"smooth\"})}else null==e||e.scrollTo({top:t,behavior:\"smooth\"})}},e.prototype.updateAccessToken=function(t,i){var e,o;null===(o=null===(e=this.iframe)||void 0===e?void 0:e.contentWindow)||void 0===o||o.postMessage({method:\"idCheck.updateAccessToken\",accessToken:t,error:i},\"*\")},e.prototype.destroy=function(){for(window.removeEventListener(\"message\",this.onMessage);this.$container&&this.$container.firstChild;)this.$container.removeChild(this.$container.firstChild);this.$container=null},e.prototype.navigateBack=function(){var t,i;null===(i=null===(t=this.iframe)||void 0===t?void 0:t.contentWindow)||void 0===i||i.postMessage({method:\"idCheck.callNavigationBack\"},\"*\")},e.prototype.singleStepNext=function(t){var i,e;null===(e=null===(i=this.iframe)||void 0===i?void 0:i.contentWindow)||void 0===e||e.postMessage({method:\"idCheck.callSingleStepNext\",step:t},\"*\")},e.prototype.setLanguage=function(t){var i,e;null===(e=null===(i=this.iframe)||void 0===i?void 0:i.contentWindow)||void 0===e||e.postMessage({method:\"idCheck.callSetLanguage\",language:t},\"*\")},e.prototype.setTheme=function(t){var i,e;null===(e=null===(i=this.iframe)||void 0===i?void 0:i.contentWindow)||void 0===e||e.postMessage({method:\"idCheck.callSetTheme\",theme:t},\"*\")},e}(),o=function(){function t(t,i){if(this.config=null,this.reusableConfig=null,this.eventHandlers={},this.anyEventHandler=null,this.options={adaptIframeHeight:!0,addViewportTag:!0,enableScrollIntoView:!0},\"string\"!=typeof t)throw new Error(\"Access token must be a string\");if(\"function\"!=typeof i)throw new Error(\"updateAccessToken callback is required\");this.accessToken=t,this.updateAccessToken=i}return t.prototype.onTestEnv=function(){return this},t.prototype.withBaseUrl=function(t){return this.baseUrl=t,this},t.prototype.withConf=function(t){return this.config=t,this},t.prototype.withReusableKycConf=function(t){return this.reusableConfig=t,this},t.prototype.withOptions=function(t){var i;return t.hasOwnProperty(\"adaptIframeHeight\")&&(this.options.adaptIframeHeight=t.adaptIframeHeight),t.hasOwnProperty(\"addViewportTag\")&&(this.options.addViewportTag=t.addViewportTag),this.options.enableScrollIntoView=null===(i=t.enableScrollIntoView)||void 0===i||i,this},t.prototype.on=function(t,i){return this.eventHandlers[t]=i,this},t.prototype.onMessage=function(t){return this.anyEventHandler=t,this},t.prototype.onNavigationUiControlsStateChanged=function(t){return this.eventHandlers[\"idCheck.onNavigationUiControlsStateChanged\"]=t,this},t.prototype.build=function(){var t,o,n,s,r,a,l,c,h,d,u,f,p,g,v=this,m=i(this.accessToken);return new e(this.baseUrl,{version:m,theme:null===(t=this.config)||void 0===t?void 0:t.theme,customizationName:null===(o=this.config)||void 0===o?void 0:o.customizationName,translationName:null===(n=this.config)||void 0===n?void 0:n.translationName,accessToken:this.accessToken,lang:null===(s=this.config)||void 0===s?void 0:s.lang,email:null===(r=this.config)||void 0===r?void 0:r.email,phone:null===(a=this.config)||void 0===a?void 0:a.phone,country:null===(l=this.config)||void 0===l?void 0:l.country,uiConf:null===(c=this.config)||void 0===c?void 0:c.uiConf,i18n:null===(h=this.config)||void 0===h?void 0:h.i18n,documentsByCountries:null===(d=this.config)||void 0===d?void 0:d.documentsByCountries,documentDefinitions:null===(u=this.config)||void 0===u?void 0:u.documentDefinitions,autoSelectDocumentDefinitions:null===(f=this.config)||void 0===f?void 0:f.autoSelectDocumentDefinitions,controlledNavigationBack:null===(p=this.config)||void 0===p?void 0:p.controlledNavigationBack,singleStep:null===(g=this.config)||void 0===g?void 0:g.singleStep,reusableConfig:this.reusableConfig},{expirationHandler:{legacy:!1,handler:this.updateAccessToken},onMessage:function(t,i){var e,o=v.eventHandlers[t];o?o(i):null===(e=v.anyEventHandler)||void 0===e||e.call(v,t,i)}},this.options)},t}(),n=function(){function t(t,i){this.debugEnabled=!1,this.options={adaptIframeHeight:!0,addViewportTag:!0,enableScrollIntoView:!0},this.config=null,this.reusableConfig=null,this.accessToken=null,this.expirationHandler=null,this.baseUrl=t,this.flowName=i}return t.prototype.withAccessToken=function(t,i){if(this.accessToken=t,!i||\"function\"!=typeof i)throw new Error('Invalid parameter, \"expirationHandler\" must be a function');return this.expirationHandler=i,this},t.prototype.debug=function(t){return this.debugEnabled=t,this},t.prototype.withOptions=function(t){var i;return t.hasOwnProperty(\"adaptIframeHeight\")&&(this.options.adaptIframeHeight=t.adaptIframeHeight),t.hasOwnProperty(\"addViewportTag\")&&(this.options.addViewportTag=t.addViewportTag),this.options.enableScrollIntoView=null===(i=t.enableScrollIntoView)||void 0===i||i,this},t.prototype.withConf=function(t){return this.config=t,this},t.prototype.withReusableKycConf=function(t){return this.reusableConfig=t,this},t.prototype.build=function(){var t,o,n,s,r,a,l,c,h,d,u,f,p,g,v;if(!this.accessToken||!this.expirationHandler)throw new Error(\"Configure access token end the expiration handler before\");var m=i(this.accessToken);return new e(this.baseUrl,{version:m,theme:null===(t=this.config)||void 0===t?void 0:t.theme,customizationName:null===(o=this.config)||void 0===o?void 0:o.customizationName,translationName:null===(n=this.config)||void 0===n?void 0:n.translationName,accessToken:this.accessToken,flowName:this.flowName,lang:null===(s=this.config)||void 0===s?void 0:s.lang,email:null===(r=this.config)||void 0===r?void 0:r.email,phone:null===(a=this.config)||void 0===a?void 0:a.phone,country:null===(l=this.config)||void 0===l?void 0:l.country,uiConf:null===(c=this.config)||void 0===c?void 0:c.uiConf,i18n:null===(h=this.config)||void 0===h?void 0:h.i18n,documentsByCountries:null===(d=this.config)||void 0===d?void 0:d.documentsByCountries,documentDefinitions:null===(u=this.config)||void 0===u?void 0:u.documentDefinitions,autoSelectDocumentDefinitions:null===(f=this.config)||void 0===f?void 0:f.autoSelectDocumentDefinitions,singleStep:null===(p=this.config)||void 0===p?void 0:p.singleStep,reusableConfig:this.reusableConfig},{expirationHandler:{legacy:!0,handler:this.expirationHandler},onMessage:null===(g=this.config)||void 0===g?void 0:g.onMessage,onError:null===(v=this.config)||void 0===v?void 0:v.onError},{adaptIframeHeight:this.options.adaptIframeHeight,addViewportTag:this.options.addViewportTag,enableScrollIntoView:this.options.enableScrollIntoView,debug:this.debugEnabled})},t}(),s={Builder:function(t,i){return new n(t,i)},init:function(t,i){return new o(t,i)}};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sumsub/websdk/dist/index.esm.js\n");

/***/ })

};
;