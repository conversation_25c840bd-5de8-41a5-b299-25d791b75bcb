# Gas Optimization for Amoy Testnet

## Overview

This document summarizes the gas optimizations applied to the token deployment scripts for the Amoy testnet.

## Optimized Gas Settings

Based on successful deployment testing, we've identified these optimal gas parameters:

- **Gas Limit: 5,000,000** (5M gas)
- **Gas Price: 50 gwei**

These settings provide a good balance between:
- Ensuring transaction success on the Amoy testnet
- Minimizing unnecessary gas costs
- Improving deployment speed

## Applied Changes

These optimized settings have been applied to:

1. `scripts/api-deploy-token.js`
2. `scripts/extreme-deploy-token.js`
3. `API_INTEGRATION_GUIDE.md`

## Previous Settings

For reference, the previous settings were:
- API script: 3M gas limit, 200 gwei gas price
- Extreme script: 10M gas limit, 500 gwei gas price

## Usage

When deploying tokens via the API or batch files, these optimized values will be used by default.

You can still override them if needed using environment variables:
```
$env:GAS_LIMIT="8000000" 
$env:GAS_PRICE="100"
```

## Monitoring

Continue to monitor deployment success rates with these new settings. If the Amoy testnet conditions change, you may need to adjust these values again. 