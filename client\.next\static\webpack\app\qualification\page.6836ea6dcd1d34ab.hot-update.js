"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/app/qualification/page.tsx":
/*!****************************************!*\
  !*** ./src/app/qualification/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_QualificationForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction QualificationPage() {\n    _s();\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const mockAuth = useMockAuth ? (0,_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_3__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationPage.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"QualificationPage.useQuery\"],\n        enabled: !!user\n    });\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null // AppLayout handles authentication\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"My Profile\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"View and edit your qualification details and personal information\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QualificationForm__WEBPACK_IMPORTED_MODULE_2__.QualificationForm, {\n                        onComplete: ()=>{\n                            // Refresh the page to show updated data\n                            window.location.reload();\n                        },\n                        existingProfile: clientProfile\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationPage, \"g9UdGXQdl+ZM2TAOEEoj93tDYHE=\", false, function() {\n    return [\n        _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser,\n        _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_3__.useMockUser,\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery\n    ];\n});\n_c = QualificationPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QualificationPage);\nvar _c;\n$RefreshReg$(_c, \"QualificationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/qualification/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/MockAuthProvider.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockAuthProvider: () => (/* binding */ MockAuthProvider),\n/* harmony export */   useMockUser: () => (/* binding */ useMockUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MockAuthProvider,useMockUser auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst MockAuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MockAuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const login = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setUser({\n                sub: 'mock-user-123',\n                name: 'Demo User',\n                email: '<EMAIL>',\n                picture: 'https://via.placeholder.com/40'\n            });\n            setIsLoading(false);\n        }, 1000);\n    };\n    const logout = ()=>{\n        setUser(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MockAuthContext.Provider, {\n        value: {\n            user,\n            error: undefined,\n            isLoading,\n            login,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\MockAuthProvider.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(MockAuthProvider, \"V1EbB17x1VpGLzcimGc1EdTTHTc=\");\n_c = MockAuthProvider;\nfunction useMockUser() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MockAuthContext);\n    if (context === undefined) {\n        throw new Error('useMockUser must be used within a MockAuthProvider');\n    }\n    return context;\n}\n_s1(useMockUser, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"MockAuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\n"));

/***/ })

});