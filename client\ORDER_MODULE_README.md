# Client Order/Subscription Module

## Overview

The Client Order/Subscription Module allows authenticated users to browse available tokens, place orders, and track their order status through a comprehensive interface that integrates with the admin panel for order management.

## Features

### 🛒 **Order Placement**
- Browse available tokens on the `/offers` page
- Enhanced order modal with detailed token information
- Real-time total calculation
- Comprehensive validation (amount, whitelist status, max supply)
- Success notifications with order details

### 📋 **Order Management**
- Dedicated `/orders` page for viewing order history
- Card-based layout with expandable details
- Status-based filtering (All, Pending, Confirmed, Minted, Cancelled)
- Pagination support for large order lists
- Real-time order status updates

### 🔄 **Order Status Tracking**
- **PENDING_APPROVAL**: Order submitted, awaiting admin approval
- **CONFIRMED**: Order approved, payment processing
- **MINTED**: Tokens successfully minted and transferred
- **CANCELLED**: Order cancelled by admin or system

### 🎨 **User Interface**
- Responsive design for mobile and desktop
- Intuitive card-based order display
- Status indicators with icons and colors
- Expandable order details
- Quick actions (View Token, Refresh)

## Architecture

### **Client-Side Components**

#### Pages
- `/offers` - Token browsing and order placement
- `/orders` - Order history and management

#### Components
- `OrderCard.tsx` - Reusable order display component
- `OrderModal.tsx` - Standalone order placement modal
- `Navbar.tsx` - Updated with "My Orders" navigation link

#### API Routes
- `GET /api/client-orders` - Fetch user's orders with filtering
- `POST /api/client-orders` - Submit new orders

### **Integration with Admin Panel**

The client application communicates with the admin panel through:
- Order creation: `POST /api/orders` (admin panel)
- Order retrieval: `GET /api/orders?clientId=...` (admin panel)
- Order status updates: `PUT /api/orders/[id]` (admin panel)

## Usage

### **Placing an Order**

1. Navigate to `/offers` page
2. Browse available tokens
3. Click "Order Token" on desired token
4. Enter the number of tokens to order
5. Review total amount and submit

**Prerequisites:**
- User must be authenticated
- KYC status must be APPROVED
- User must be whitelisted for the specific token

### **Viewing Orders**

1. Navigate to `/orders` page via navbar "My Orders" link
2. View all orders in card format
3. Use status filter to narrow results
4. Click expand button on any order for detailed information
5. Use pagination to navigate through multiple pages

### **Order Details**

Each order card displays:
- **Summary**: Token name, status, amount, total cost, date
- **Expanded View**: 
  - Token price and confirmed amounts
  - Payment reference and order ID
  - Token contract address
  - Status-specific information and next steps

## API Integration

### **Environment Variables**

```env
# Admin Panel Integration
ADMIN_API_BASE_URL="http://localhost:6677/api"
NEXT_PUBLIC_API_BASE_URL="http://localhost:7788/api"
```

### **Order Data Flow**

1. **Client Order Submission**:
   ```
   Client UI → /api/client-orders → Admin Panel /api/orders → Database
   ```

2. **Order Retrieval**:
   ```
   Client UI → /api/client-orders?status=... → Admin Panel /api/orders → Database
   ```

3. **Order Status Updates**:
   ```
   Admin Panel → Database → Client UI (via refresh/polling)
   ```

## Validation Rules

### **Order Placement Validation**
- Amount must be a positive number
- Amount cannot exceed token's max supply
- User must be whitelisted for the token
- User must have APPROVED KYC status
- All required fields must be provided

### **Security Considerations**
- All API calls require authentication
- Client ID is verified against session
- Order amounts are validated server-side
- Token prices are fetched from database (not client)

## Error Handling

### **Common Error Scenarios**
- **Unauthorized**: User not logged in
- **Invalid Amount**: Non-positive or non-numeric values
- **Exceeds Supply**: Order amount > max supply
- **Not Whitelisted**: User not approved for token
- **KYC Required**: User KYC status not approved
- **Server Error**: Admin panel communication issues

### **Error Display**
- Inline error messages in order modal
- Toast notifications for success/failure
- Fallback error states in order list
- Retry mechanisms for failed requests

## Testing

### **Manual Testing Checklist**
- [ ] Order placement with valid data
- [ ] Order placement with invalid amounts
- [ ] Order placement without whitelist approval
- [ ] Order history display and filtering
- [ ] Order detail expansion/collapse
- [ ] Pagination functionality
- [ ] Mobile responsiveness
- [ ] Error handling scenarios

### **Integration Testing**
- [ ] Client-to-admin API communication
- [ ] Order status synchronization
- [ ] Authentication flow
- [ ] Database consistency

## Future Enhancements

### **Planned Features**
- Real-time order status updates via WebSocket
- Order cancellation from client side
- Bulk order operations
- Order export functionality
- Email notifications for status changes
- Payment integration
- Order analytics and reporting

### **Performance Optimizations**
- Order caching and pagination
- Optimistic UI updates
- Background data synchronization
- Image lazy loading for token icons

## Troubleshooting

### **Common Issues**

1. **Orders not loading**
   - Check ADMIN_API_BASE_URL configuration
   - Verify admin panel is running on port 6677
   - Check network connectivity

2. **Order submission fails**
   - Verify user authentication
   - Check KYC and whitelist status
   - Validate order amount

3. **Status not updating**
   - Use refresh button in orders page
   - Check admin panel order management
   - Verify database connectivity

### **Debug Information**
- Check browser console for API errors
- Monitor network tab for failed requests
- Review admin panel logs for order processing
- Verify database order records

## Dependencies

### **Client Application**
- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- TanStack Query
- Auth0 Next.js SDK

### **Admin Panel Integration**
- Prisma ORM
- PostgreSQL database
- Order management APIs
- Client management system

---

For additional support or feature requests, please refer to the main project documentation or contact the development team.
