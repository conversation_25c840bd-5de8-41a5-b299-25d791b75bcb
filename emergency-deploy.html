<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token Deployment Tool</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { margin-top: 20px; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 16px; margin-bottom: 20px; }
        .card-header { font-weight: bold; margin-bottom: 12px; font-size: 18px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"], input[type="number"] { width: 100%; padding: 8px; font-size: 16px; }
        button { background-color: #4CAF50; color: white; padding: 10px 20px; border: none; cursor: pointer; font-size: 16px; }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .btn-warning { background-color: #ff9800; }
        .btn-primary { background-color: #2196F3; }
        .result { margin-top: 15px; padding: 10px; border-radius: 4px; }
        .success { background-color: #dff0d8; color: #3c763d; }
        .error { background-color: #f2dede; color: #a94442; }
        .info { background-color: #d9edf7; color: #31708f; }
        pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; }
        .loader { border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; width: 20px; height: 20px; animation: spin 2s linear infinite; display: inline-block; margin-right: 10px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .hidden { display: none; }
        .section-title { font-weight: bold; margin-top: 10px; }
        .gas-presets { display: flex; gap: 10px; margin-top: 10px; }
        .gas-preset-btn { padding: 5px 10px; background: #f0f0f0; border: 1px solid #ccc; cursor: pointer; }
        .gas-preset-btn:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>Token Deployment Tool for Amoy Testnet</h1>
    <p>This tool provides a direct way to deploy tokens on the Amoy testnet, bypassing normal RPC methods that might be failing.</p>

    <div class="card">
        <div class="card-header">Connection Status</div>
        <div id="connection-status" class="result info">Checking connection...</div>
    </div>

    <div class="card">
        <div class="card-header">Factory Settings</div>
        <div class="form-group">
            <label for="factory-address">Factory Address:</label>
            <input type="text" id="factory-address" value="******************************************">
        </div>
    </div>

    <div class="card">
        <div class="card-header">Token Settings</div>
        <div class="form-group">
            <label for="token-name">Token Name:</label>
            <input type="text" id="token-name" value="Nova">
        </div>
        <div class="form-group">
            <label for="token-symbol">Token Symbol:</label>
            <input type="text" id="token-symbol" value="NNN">
        </div>
        <div class="form-group">
            <label for="max-supply">Max Supply:</label>
            <input type="number" id="max-supply" value="1500000">
        </div>
        <div class="form-group">
            <label for="token-price">Token Price (Metadata):</label>
            <input type="text" id="token-price" value="3">
        </div>
        <div class="form-group">
            <label for="bonus-tiers">Bonus Tiers (Metadata):</label>
            <input type="text" id="bonus-tiers" value="Tier 1: 5%, Tier 2: 10%, Tier 3: 15%">
        </div>
    </div>

    <div class="card">
        <div class="card-header">Gas Settings</div>
        <div class="section-title">Gas Presets:</div>
        <div class="gas-presets">
            <button class="gas-preset-btn" onclick="setGasPreset(3000000, 100)">High</button>
            <button class="gas-preset-btn" onclick="setGasPreset(5000000, 200)">Very High</button>
            <button class="gas-preset-btn" onclick="setGasPreset(8000000, 300)">Extreme</button>
        </div>
        <div class="form-group">
            <label for="gas-limit">Gas Limit:</label>
            <input type="number" id="gas-limit" value="5000000">
        </div>
        <div class="form-group">
            <label for="gas-price">Gas Price (gwei):</label>
            <input type="number" id="gas-price" value="200">
        </div>
    </div>

    <div class="card">
        <div class="card-header">Deployment Actions</div>
        <button id="btn-connect" class="btn-warning">Connect Wallet</button>
        <button id="btn-deploy" class="btn-primary" disabled>Deploy Token</button>
        <div id="operation-status" class="result hidden"></div>
    </div>

    <div class="card hidden" id="tx-card">
        <div class="card-header">Transaction Details</div>
        <pre id="tx-details"></pre>
    </div>

    <div class="card hidden" id="token-card">
        <div class="card-header">Deployed Token Information</div>
        <pre id="token-details"></pre>
    </div>

    <script>
        let provider, signer, network, userAddress;

        // Set gas preset values
        function setGasPreset(gasLimit, gasPrice) {
            document.getElementById('gas-limit').value = gasLimit;
            document.getElementById('gas-price').value = gasPrice;
        }

        // Connect wallet
        async function connectWallet() {
            try {
                // Check if MetaMask is installed
                if (!window.ethereum) {
                    updateStatus('connection-status', 'No Web3 wallet found! Please install MetaMask.', 'error');
                    return;
                }

                // Request account access
                const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                userAddress = accounts[0];

                // Get provider and signer
                provider = new ethers.providers.Web3Provider(window.ethereum);
                signer = provider.getSigner();
                
                // Add timeout to network call
                const networkPromise = provider.getNetwork();
                const timeoutPromise = new Promise((_, reject) => 
                    setTimeout(() => reject(new Error("Network detection timed out")), 5000)
                );
                
                try {
                    network = await Promise.race([networkPromise, timeoutPromise]);
                    
                    // Check if connected to Amoy testnet
                    if (network.chainId !== 80002) {
                        updateStatus('connection-status', 
                            `WARNING: Not connected to Amoy testnet (chainId 80002)! Current network: ${network.name} (chainId: ${network.chainId})
                            
                            Please switch to Amoy testnet in your MetaMask:
                            Network Name: Polygon Amoy Testnet
                            RPC URL: https://polygon-amoy.blockpi.network/v1/rpc/public
                            Chain ID: 80002
                            Currency Symbol: MATIC
                            Block Explorer: https://amoy.polygonscan.com`, 'error');
                    } else {
                        updateStatus('connection-status', `Connected to Amoy testnet as ${userAddress}`, 'success');
                        // Enable deploy button
                        document.getElementById('btn-deploy').disabled = false;
                    }
                } catch (timeoutError) {
                    // If network detection times out, allow user to proceed anyway
                    updateStatus('connection-status', 
                        `WARNING: Network detection timed out. Please ensure you're connected to Amoy testnet (chainId 80002) in your MetaMask.
                        
                        Network settings:
                        Network Name: Polygon Amoy Testnet
                        RPC URL: https://polygon-amoy.blockpi.network/v1/rpc/public
                        Chain ID: 80002
                        Currency Symbol: MATIC
                        Block Explorer: https://amoy.polygonscan.com`, 'error');
                        
                    // Still enable the deploy button, but warn user
                    document.getElementById('btn-deploy').disabled = false;
                }

                // Listen for network changes
                window.ethereum.on('chainChanged', () => {
                    window.location.reload();
                });
            } catch (error) {
                updateStatus('connection-status', `Connection error: ${error.message}`, 'error');
                console.error("Connection error:", error);
            }
        }

        // Deploy token
        async function deployToken() {
            try {
                updateStatus('operation-status', 'Preparing to deploy token...', 'info');
                document.getElementById('operation-status').classList.remove('hidden');
                document.getElementById('btn-deploy').disabled = true;

                // Get form values
                const factoryAddress = document.getElementById('factory-address').value.trim();
                const tokenName = document.getElementById('token-name').value.trim();
                const tokenSymbol = document.getElementById('token-symbol').value.trim();
                const maxSupply = document.getElementById('max-supply').value;
                const tokenPrice = document.getElementById('token-price').value.trim();
                const bonusTiers = document.getElementById('bonus-tiers').value.trim();
                const gasLimit = BigInt(document.getElementById('gas-limit').value);
                const gasPriceGwei = document.getElementById('gas-price').value;
                const gasPrice = ethers.utils.parseUnits(gasPriceGwei, "gwei");

                // Validate inputs
                if (!ethers.utils.isAddress(factoryAddress)) {
                    throw new Error("Invalid factory address");
                }
                
                if (!tokenName || tokenName.length < 1) {
                    throw new Error("Token name is required");
                }
                
                if (!tokenSymbol || tokenSymbol.length < 1) {
                    throw new Error("Token symbol is required");
                }
                
                if (!maxSupply || parseFloat(maxSupply) <= 0) {
                    throw new Error("Max supply must be a positive number");
                }

                // Create interface for encoding
                const factoryInterface = new ethers.utils.Interface([
                    "function deploySecurityToken(string name, string symbol, uint256 maxSupply, address admin, string tokenPrice, string bonusTiers) returns (address, address)"
                ]);

                // Encode parameters
                const maxSupplyWei = ethers.utils.parseEther(maxSupply);
                const data = factoryInterface.encodeFunctionData("deploySecurityToken", [
                    tokenName,
                    tokenSymbol,
                    maxSupplyWei,
                    userAddress, // Admin is the current user
                    tokenPrice,
                    bonusTiers
                ]);

                // Get nonce
                let nonce;
                try {
                    nonce = await signer.getTransactionCount();
                } catch (error) {
                    // If nonce retrieval fails, use explicit "latest" parameter
                    nonce = await signer.getTransactionCount("latest");
                }

                // Create transaction object with EXPLICITLY set gas parameters
                const tx = {
                    to: factoryAddress,
                    data: data,
                    gasLimit: ethers.utils.hexlify(gasLimit),
                    gasPrice: gasPrice,
                    nonce: nonce,
                    value: ethers.utils.parseEther("0") // Explicitly set 0 ETH
                };
                
                // Only set chainId if we were able to get the network
                if (network && network.chainId) {
                    tx.chainId = network.chainId;
                }

                // Display transaction details
                document.getElementById('tx-card').classList.remove('hidden');
                document.getElementById('tx-details').textContent = JSON.stringify(tx, null, 2);

                updateStatus('operation-status', 'Sending deployment transaction... Please confirm in MetaMask', 'info');

                // Send transaction
                const txResponse = await signer.sendTransaction(tx);
                document.getElementById('tx-details').textContent += "\n\nTransaction Hash: " + txResponse.hash;
                
                updateStatus('operation-status', `Transaction sent! Hash: ${txResponse.hash}`, 'info');
                updateStatus('operation-status', 'Waiting for confirmation (this may take a while)... You can check progress on https://amoy.polygonscan.com/tx/' + txResponse.hash, 'info');

                try {
                    // Wait for confirmation with timeout
                    const waitPromise = provider.waitForTransaction(txResponse.hash);
                    const timeoutPromise = new Promise((_, reject) => 
                        setTimeout(() => reject(new Error("Transaction confirmation timed out after 2 minutes")), 120000)
                    );
                    
                    const receipt = await Promise.race([waitPromise, timeoutPromise]);

                    if (receipt.status === 1) {
                        updateStatus('operation-status', `Success! Token deployed in block ${receipt.blockNumber}`, 'success');
                        
                        try {
                            // Try to get the token address from the factory
                            const factory = new ethers.Contract(
                                factoryAddress, 
                                ["function getTokenAddressBySymbol(string symbol) view returns (address)"],
                                provider
                            );
                            
                            const tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
                            
                            // Display token information
                            document.getElementById('token-card').classList.remove('hidden');
                            
                            if (tokenAddress && tokenAddress !== ethers.constants.AddressZero) {
                                document.getElementById('token-details').textContent = `
Token Address: ${tokenAddress}

To interact with this token, use the following information:
- Token Address: ${tokenAddress}
- Token Name: ${tokenName}
- Token Symbol: ${tokenSymbol}
- Max Supply: ${maxSupply}

You can now manage this token using the direct-tx.bat script or the admin panel.
`;
                            } else {
                                document.getElementById('token-details').textContent = `
Token deployment transaction was successful, but couldn't find token address.
This may happen if the token symbol was already used.

Please check the transaction on the block explorer:
https://amoy.polygonscan.com/tx/${txResponse.hash}
`;
                            }
                        } catch (error) {
                            document.getElementById('token-details').textContent = `
Token deployment transaction was successful, but encountered an error while retrieving token address:
${error.message}

Please check the transaction on the block explorer:
https://amoy.polygonscan.com/tx/${txResponse.hash}
`;
                        }
                    } else {
                        updateStatus('operation-status', 'Transaction was mined but may have failed', 'error');
                    }
                } catch (timeoutError) {
                    // If we timeout, we still provide the transaction hash for manual checking
                    updateStatus('operation-status', `Waiting for confirmation timed out, but your transaction has been submitted. Check status at: https://amoy.polygonscan.com/tx/${txResponse.hash}`, 'info');
                    
                    document.getElementById('token-card').classList.remove('hidden');
                    document.getElementById('token-details').textContent = `
Transaction has been submitted but confirmation is taking longer than expected.

Transaction Hash: ${txResponse.hash}
Check status: https://amoy.polygonscan.com/tx/${txResponse.hash}

If the transaction succeeds, you can find your token address by:
1. Checking the transaction on the block explorer
2. Looking for the 'Token Deployed' event in the logs
3. Or using the token symbol "${tokenSymbol}" in your factory methods later
`;
                }

                // Re-enable button
                document.getElementById('btn-deploy').disabled = false;
            } catch (error) {
                let errorMsg = error.message;
                
                // Special handling for common MetaMask errors
                if (errorMsg.includes("user rejected") || errorMsg.includes("User denied")) {
                    errorMsg = "Transaction was rejected in MetaMask. Please try again.";
                } else if (errorMsg.includes("insufficient funds")) {
                    errorMsg = "Insufficient funds for gas * price + value. Make sure you have enough MATIC in your wallet.";
                } else if (errorMsg.includes("gas required exceeds")) {
                    errorMsg = "Gas limit too low. Try increasing the gas limit with the 'Very High' or 'Extreme' preset.";
                } else if (errorMsg.includes("replacement fee too low")) {
                    errorMsg = "Gas price too low for replacement. Try increasing the gas price or wait for previous transactions to confirm.";
                } else if (errorMsg.includes("nonce too low")) {
                    errorMsg = "Transaction nonce is too low. Try refreshing the page and reconnecting your wallet.";
                } else if (errorMsg.includes("Internal JSON-RPC error")) {
                    errorMsg = "RPC Error: This is common on Amoy testnet. Try:  \n1. Using a higher gas limit and price (try the 'Extreme' preset)\n2. Refreshing the page and trying again\n3. Making sure your wallet has enough MATIC";
                }
                
                updateStatus('operation-status', `Error: ${errorMsg}`, 'error');
                document.getElementById('btn-deploy').disabled = false;
                console.error("Deployment error:", error);
            }
        }

        // Helper function to update status
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // Add event listeners
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('btn-connect').addEventListener('click', connectWallet);
            document.getElementById('btn-deploy').addEventListener('click', deployToken);
        });
    </script>
</body>
</html> 