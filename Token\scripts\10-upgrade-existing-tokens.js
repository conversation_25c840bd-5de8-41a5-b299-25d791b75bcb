const { ethers, upgrades } = require("hardhat");

async function main() {
    console.log("🔄 Upgrading Existing Tokens to ERC-3643...");
    console.log("============================================");

    const [deployer] = await ethers.getSigners();
    console.log("Upgrading with account:", deployer.address);

    // Get existing token addresses
    const existingTokens = [
        process.env.AUGMENT_019_ADDRESS || "******************************************",
        process.env.AUGMENT_01Z_ADDRESS || "******************************************",
        // Add more token addresses as needed
    ].filter(addr => addr && addr !== "");

    if (existingTokens.length === 0) {
        console.log("⚠️ No existing token addresses found.");
        console.log("Set AUGMENT_019_ADDRESS and/or AUGMENT_01Z_ADDRESS in .env.local");
        return;
    }

    console.log(`Found ${existingTokens.length} existing tokens to upgrade:`);
    existingTokens.forEach((addr, i) => console.log(`  ${i + 1}. ${addr}`));

    // Get new contract addresses
    const identityRegistryAddress = process.env.IDENTITY_REGISTRY_ADDRESS;
    const complianceAddress = process.env.COMPLIANCE_ADDRESS;

    if (!identityRegistryAddress || !complianceAddress) {
        console.log("❌ Missing new contract addresses.");
        console.log("Deploy IdentityRegistry and Compliance first using script 04.");
        return;
    }

    console.log("\nUsing new contracts:");
    console.log("IdentityRegistry:", identityRegistryAddress);
    console.log("Compliance:", complianceAddress);

    // Upgrade each token
    for (let i = 0; i < existingTokens.length; i++) {
        const tokenAddress = existingTokens[i];
        console.log(`\n${i + 1}️⃣ Upgrading token ${tokenAddress}...`);
        console.log("=".repeat(50));

        try {
            // Get current token info
            const SecurityToken = await ethers.getContractFactory("SecurityToken");
            const currentToken = SecurityToken.attach(tokenAddress);

            const name = await currentToken.name();
            const symbol = await currentToken.symbol();
            const currentVersion = await currentToken.version();
            console.log(`Token: ${name} (${symbol}) v${currentVersion}`);

            // Check if already upgraded
            try {
                const complianceAddr = await currentToken.compliance();
                if (complianceAddr && complianceAddr !== ethers.ZeroAddress) {
                    console.log("✅ Token already upgraded to ERC-3643");
                    continue;
                }
            } catch (error) {
                // compliance() function doesn't exist, needs upgrade
                console.log("📦 Token needs upgrade to ERC-3643");
            }

            // Perform upgrade
            console.log("🔄 Upgrading contract implementation...");
            
            const upgradedToken = await upgrades.upgradeProxy(
                tokenAddress,
                SecurityToken,
                {
                    kind: "uups"
                }
            );

            await upgradedToken.waitForDeployment();
            console.log("✅ Contract implementation upgraded");

            // Update contract references
            console.log("🔗 Updating contract references...");
            
            try {
                // Update identity registry
                await upgradedToken.updateIdentityRegistry(identityRegistryAddress);
                console.log("✅ Identity registry updated");

                // Update compliance (new function)
                await upgradedToken.updateCompliance(complianceAddress);
                console.log("✅ Compliance contract updated");

            } catch (error) {
                console.log("❌ Failed to update contract references:", error.message);
                console.log("💡 You may need to call these functions manually:");
                console.log(`   updateIdentityRegistry("${identityRegistryAddress}")`);
                console.log(`   updateCompliance("${complianceAddress}")`);
            }

            // Verify upgrade
            console.log("🔍 Verifying upgrade...");
            
            const newVersion = await upgradedToken.version();
            const identityReg = await upgradedToken.identityRegistry();
            const compliance = await upgradedToken.compliance();

            console.log("✅ Upgrade verification:");
            console.log(`  Version: ${currentVersion} → ${newVersion}`);
            console.log(`  Identity Registry: ${identityReg}`);
            console.log(`  Compliance: ${compliance}`);

            // Test new functionality
            console.log("🧪 Testing new functionality...");
            
            try {
                const canTransfer = await upgradedToken.canTransfer(deployer.address, deployer.address, 1);
                console.log(`✅ canTransfer test: ${canTransfer}`);

                const isVerified = await upgradedToken.isVerified(deployer.address);
                console.log(`✅ isVerified test: ${isVerified}`);

            } catch (error) {
                console.log("⚠️ New functionality test failed:", error.message);
                console.log("💡 This is expected if deployer is not registered in IdentityRegistry");
            }

        } catch (error) {
            console.log(`❌ Failed to upgrade token ${tokenAddress}:`, error.message);
        }
    }

    // Migration recommendations
    console.log("\n📋 Post-Upgrade Migration Steps");
    console.log("===============================");
    console.log("1. Migrate existing whitelist data:");
    console.log("   npx hardhat run scripts/05-migrate-whitelist-data.js --network amoy");
    console.log("");
    console.log("2. Register existing token holders in IdentityRegistry");
    console.log("3. Update admin panel to use new contract addresses");
    console.log("4. Test token transfers with new compliance system");
    console.log("5. Update any external integrations");

    // Admin panel integration
    console.log("\n🔧 Admin Panel Updates Needed");
    console.log("=============================");
    console.log("Update these contract addresses in admin panel:");
    existingTokens.forEach((addr, i) => {
        console.log(`Token ${i + 1}: ${addr}`);
    });
    console.log(`IdentityRegistry: ${identityRegistryAddress}`);
    console.log(`Compliance: ${complianceAddress}`);

    return {
        upgradedTokens: existingTokens,
        identityRegistry: identityRegistryAddress,
        compliance: complianceAddress
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ Upgrade failed:", error);
            process.exit(1);
        });
}

module.exports = main;
