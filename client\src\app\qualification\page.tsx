'use client'

import { withPageAuthRequired, useUser } from '@auth0/nextjs-auth0/client'
import { useQuery } from '@tanstack/react-query'
import { QualificationModule } from '@/components/QualificationModule'
import { Navbar } from '@/components/Navbar'

function QualificationPage() {
  const { user, isLoading: userLoading } = useUser()

  // Fetch client profile
  const { data: clientProfile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: async () => {
      const response = await fetch('/api/client/profile')
      if (response.ok) {
        return response.json()
      }
      return null
    },
    enabled: !!user,
  })

  if (userLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null // This shouldn't happen due to withPageAuthRequired
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar
        user={user}
        clientProfile={clientProfile}
        onGetQualified={() => {
          // Navigate to qualification page (we're already here)
          window.location.reload()
        }}
      />
      <main className="container mx-auto px-4 py-8">
        <QualificationModule />
      </main>
    </div>
  )
}

export default withPageAuthRequired(QualificationPage)
