'use client'

import { useUser } from '@auth0/nextjs-auth0/client'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { QualificationForm } from '@/components/QualificationForm'
import { QualificationFlow } from '@/components/qualification/QualificationFlow'
import { useMockUser } from '@/components/providers/MockAuthProvider'
import { useApiClient } from '@/lib/api-client'

function QualificationPage() {
  const useMockAuth = process.env.NEXT_PUBLIC_USE_MOCK_AUTH === 'true'
  const searchParams = useSearchParams()

  // Use mock auth or real Auth0 based on environment
  const auth0User = useUser()
  const mockAuth = useMockAuth ? useMockUser() : { user: undefined, isLoading: false }

  const user = useMockAuth ? mockAuth.user : auth0User.user
  const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading

  const apiClient = useApiClient()

  // Get token information from URL parameters
  const tokenAddress = searchParams.get('token')
  const tokenName = searchParams.get('tokenName')
  const tokenSymbol = searchParams.get('tokenSymbol')

  // Fetch client profile
  const { data: clientProfile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: () => apiClient.getClientProfile(),
    enabled: !!user,
  })

  // Fetch token information if token address is provided
  const { data: tokenInfo } = useQuery({
    queryKey: ['token-info', tokenAddress],
    queryFn: async () => {
      if (!tokenAddress) return null;
      const response = await fetch(`/api/tokens/${tokenAddress}`);
      if (response.ok) {
        return response.json();
      }
      return null;
    },
    enabled: !!tokenAddress,
  })

  if (userLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null // AppLayout handles authentication
  }

  // If token-specific qualification is requested, show the qualification flow
  if (tokenAddress || tokenName || tokenSymbol) {
    const finalTokenName = tokenInfo?.name || tokenName || 'Token'
    const finalTokenSymbol = tokenInfo?.symbol || tokenSymbol || 'TOKEN'

    return (
      <div className="px-4 sm:px-6 lg:px-8">
        <QualificationFlow
          tokenAddress={tokenAddress || undefined}
          tokenName={finalTokenName}
          tokenSymbol={finalTokenSymbol}
        />
      </div>
    )
  }

  // Otherwise, show the profile view (existing functionality)
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Profile</h1>
          <p className="text-gray-600">
            View and edit your qualification details and personal information
          </p>
        </div>

        {/* Profile Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <QualificationForm
            onComplete={() => {
              // Refresh the page to show updated data
              window.location.reload()
            }}
            existingProfile={clientProfile}
          />
        </div>
      </div>
    </div>
  )
}

export default QualificationPage
