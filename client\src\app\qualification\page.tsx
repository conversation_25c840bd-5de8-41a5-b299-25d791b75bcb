'use client'

import { useUser } from '@auth0/nextjs-auth0/client'
import { useQuery } from '@tanstack/react-query'
import { QualificationForm } from '@/components/QualificationForm'
import { useMockUser } from '@/components/providers/MockAuthProvider'
import { useApiClient } from '@/lib/api-client'

function QualificationPage() {
  const useMockAuth = process.env.NEXT_PUBLIC_USE_MOCK_AUTH === 'true'

  // Use mock auth or real Auth0 based on environment
  const auth0User = useUser()
  const mockAuth = useMockAuth ? useMockUser() : { user: undefined, isLoading: false }

  const user = useMockAuth ? mockAuth.user : auth0User.user
  const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading

  const apiClient = useApiClient()

  // Fetch client profile
  const { data: clientProfile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: () => apiClient.getClientProfile(),
    enabled: !!user,
  })

  if (userLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null // AppLayout handles authentication
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Profile</h1>
          <p className="text-gray-600">
            View and edit your qualification details and personal information
          </p>
        </div>

        {/* Profile Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <QualificationForm
            onComplete={() => {
              // Refresh the page to show updated data
              window.location.reload()
            }}
            existingProfile={clientProfile}
          />
        </div>
      </div>
    </div>
  )
}

export default QualificationPage
