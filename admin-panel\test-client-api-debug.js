// Debug client API whitelist issue
const fetch = require('node-fetch');

async function debugClientAPI() {
  console.log('=== Debugging Client API ===');
  
  const yourWallet = '******************************************';
  
  try {
    // Test client API with test wallet parameter
    console.log('1. Testing client API with testWallet parameter...');
    const response = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(yourWallet)}`);
    
    if (!response.ok) {
      console.log(`❌ Client API failed: ${response.status}`);
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }
    
    const tokens = await response.json();
    console.log(`✅ Client API returned ${tokens.length} tokens`);
    
    // Check if any tokens have whitelist status
    const whitelistedTokens = tokens.filter(t => t.isWhitelisted);
    console.log(`Whitelisted tokens: ${whitelistedTokens.length}`);
    
    if (whitelistedTokens.length === 0) {
      console.log('\n🔍 Debugging why no tokens are whitelisted...');
      
      // Check if the API is calling the whitelist check
      console.log('\n2. Testing admin whitelist API directly...');
      
      // Get token addresses from client response
      const tokenAddresses = tokens.map(t => t.address);
      console.log(`Token addresses: ${tokenAddresses.length}`);
      
      const whitelistResponse = await fetch('http://localhost:3000/api/whitelist/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: yourWallet,
          tokenAddresses: tokenAddresses
        })
      });
      
      if (whitelistResponse.ok) {
        const whitelistData = await whitelistResponse.json();
        console.log(`✅ Admin whitelist API: ${whitelistData.tokens.filter(t => t.isWhitelisted).length}/${whitelistData.tokens.length} whitelisted`);
        
        console.log('\n3. Comparing token addresses...');
        whitelistData.tokens.forEach(wToken => {
          const clientToken = tokens.find(cToken => 
            cToken.address.toLowerCase() === wToken.tokenAddress.toLowerCase()
          );
          
          if (clientToken) {
            console.log(`${clientToken.symbol}: Admin=${wToken.isWhitelisted}, Client=${clientToken.isWhitelisted}`);
          } else {
            console.log(`Token ${wToken.tokenAddress} not found in client response`);
          }
        });
        
      } else {
        console.log('❌ Admin whitelist API failed');
      }
    }
    
    console.log('\n4. Token details from client API:');
    tokens.forEach(token => {
      const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
      console.log(`  ${token.symbol.padEnd(10)} | ${status} | ${token.address}`);
    });
    
  } catch (error) {
    console.error('Error debugging client API:', error);
  }
}

async function checkClientServerLogs() {
  console.log('\n=== Check Client Server Logs ===');
  console.log('Look at the client server terminal for:');
  console.log('- API call logs for /api/tokens');
  console.log('- Any whitelist API calls');
  console.log('- Error messages or warnings');
  console.log('- User wallet address detection');
}

async function testWithoutTestWallet() {
  console.log('\n=== Testing Without Test Wallet Parameter ===');
  
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    
    if (response.ok) {
      const tokens = await response.json();
      const whitelistedCount = tokens.filter(t => t.isWhitelisted).length;
      console.log(`Without testWallet: ${whitelistedCount}/${tokens.length} tokens whitelisted`);
      console.log('(Should be 0 since no session/login)');
    } else {
      console.log('❌ Client API without testWallet failed');
    }
  } catch (error) {
    console.error('Error testing without testWallet:', error);
  }
}

async function main() {
  await debugClientAPI();
  await testWithoutTestWallet();
  await checkClientServerLogs();
  
  console.log('\n🔍 POSSIBLE ISSUES:');
  console.log('1. Client API not calling admin whitelist API');
  console.log('2. Token address mismatch between APIs');
  console.log('3. Client API not processing whitelist response correctly');
  console.log('4. Network/port connectivity issue between client and admin');
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Check client server logs for API calls');
  console.log('2. Verify client API is calling admin whitelist endpoint');
  console.log('3. Check if token addresses match between APIs');
  console.log('4. Test the offers page in browser to see actual behavior');
}

main().catch(console.error);
