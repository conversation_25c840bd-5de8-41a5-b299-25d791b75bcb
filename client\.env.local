# Auth0 Configuration
AUTH0_SECRET='59fc730a1cd16eee88cb824fcc06d5cf'
AUTH0_ISSUER_BASE_URL='https://treesury.eu.auth0.com'
AUTH0_CLIENT_ID='y77wBjzV5oaeIFaiCOKPHHqf5jGA4ZAD'
AUTH0_CLIENT_SECRET='****************************************************************'
AUTH0_BASE_URL='http://localhost:7788'
AUTH0_POST_LOGOUT_REDIRECT='http://localhost:7788'
NEXT_PUBLIC_AUTH0_RETURN_TO_URL=http://localhost:7788

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:7788/api
ADMIN_API_BASE_URL=http://localhost:6677/api

# JWT Configuration
JWT_SECRET='your-jwt-secret-key-here'

# Wallet Configuration
NEXT_PUBLIC_REOWN_PROJECT_ID='5b68ddabca3cc662e86f74a3044a95d4'

# Network Configuration
NEXT_PUBLIC_DEFAULT_NETWORK='amoy'
NEXT_PUBLIC_AMOY_RPC_URL='https://rpc-amoy.polygon.technology'
NEXT_PUBLIC_POLYGON_RPC_URL='https://polygon-rpc.com'

# Claim Registry Configuration (Fixed Tokeny-Style)
NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS=******************************************
CLAIM_REGISTRY_ADMIN=******************************************

# Database Configuration (shared with admin panel)
DATABASE_URL="postgresql://postgres:l1nruleZ@localhost:5432/tokendev_clients?schema=public"



# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_NAME='TokenDev KYC Portal'
NEXT_PUBLIC_USE_MOCK_AUTH=false



# Sumsub KYC Integration
SUMSUB_TOKEN="sbx:EYtvMFpu4wcggjfDAVKAFJES.Ec1S4CLuFl37qR20EmIGBfxlY1MqQavA"
SUMSUB_SECRET="qKGuARkxYRMnNuRaDINm23EcuIvw8bT2"
SUMSUB_BASE_URL="https://api.sumsub.com"
NEXT_PUBLIC_SUMSUB_BASE_URL="https://cockpit.sumsub.com"
# Set this to the name of a verification level that exists in your Sumsub account
# You can create levels at https://cockpit.sumsub.com
SUMSUB_LEVEL_NAME="basic-kyc-level"

# Admin Panel Integration
ADMIN_API_BASE_URL="http://localhost:6677/api"
ADMIN_PANEL_URL="http://localhost:6677"
