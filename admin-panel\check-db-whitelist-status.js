// Check database whitelist status for specific token
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabaseWhitelistStatus() {
  console.log('🔍 Checking Database Whitelist Status');
  console.log('====================================');

  const yourWallet = '******************************************';
  const tokenAddress = '******************************************';
  
  try {
    // 1. Check if token exists in database
    console.log('\n1. Token Information:');
    const token = await prisma.token.findFirst({
      where: {
        address: {
          equals: tokenAddress,
          mode: 'insensitive'
        }
      },
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        network: true,
        adminAddress: true,
        whitelistAddress: true
      }
    });
    
    if (token) {
      console.log(`✅ Token found in database:`);
      console.log(`   Name: ${token.name}`);
      console.log(`   Symbol: ${token.symbol}`);
      console.log(`   Address: ${token.address}`);
      console.log(`   Network: ${token.network}`);
      console.log(`   Admin Address: ${token.adminAddress || 'Not set'}`);
      console.log(`   Whitelist Address: ${token.whitelistAddress || 'Not set'}`);
    } else {
      console.log(`❌ Token ${tokenAddress} not found in database`);
      console.log('   This token needs to be added to the database first');
      return;
    }
    
    // 2. Check your user
    console.log('\n2. Your User Information:');
    const yourUser = await prisma.client.findFirst({
      where: {
        walletAddress: {
          equals: yourWallet,
          mode: 'insensitive'
        }
      },
      select: {
        id: true,
        email: true,
        walletAddress: true,
        kycStatus: true,
        isWhitelisted: true
      }
    });
    
    if (yourUser) {
      console.log(`✅ Your user found:`);
      console.log(`   Email: ${yourUser.email}`);
      console.log(`   Wallet: ${yourUser.walletAddress}`);
      console.log(`   KYC Status: ${yourUser.kycStatus}`);
      console.log(`   Global Whitelisted: ${yourUser.isWhitelisted}`);
    } else {
      console.log(`❌ Your wallet ${yourWallet} not found in database`);
      return;
    }
    
    // 3. Check token approval for this specific token
    console.log('\n3. Token Approval Status:');
    const tokenApproval = await prisma.tokenClientApproval.findFirst({
      where: {
        tokenId: token.id,
        clientId: yourUser.id
      },
      select: {
        id: true,
        approvalStatus: true,
        kycApproved: true,
        whitelistApproved: true,
        approvedBy: true,
        approvedAt: true,
        notes: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    if (tokenApproval) {
      console.log(`✅ Token approval record found:`);
      console.log(`   Approval Status: ${tokenApproval.approvalStatus}`);
      console.log(`   KYC Approved: ${tokenApproval.kycApproved}`);
      console.log(`   Whitelist Approved: ${tokenApproval.whitelistApproved}`);
      console.log(`   Approved By: ${tokenApproval.approvedBy || 'Not set'}`);
      console.log(`   Approved At: ${tokenApproval.approvedAt || 'Not set'}`);
      console.log(`   Notes: ${tokenApproval.notes || 'No notes'}`);
      console.log(`   Created: ${tokenApproval.createdAt}`);
      console.log(`   Updated: ${tokenApproval.updatedAt}`);
      
      if (tokenApproval.whitelistApproved) {
        console.log(`\n   🎉 WHITELISTED: Your wallet is whitelisted for this token in the database!`);
      } else {
        console.log(`\n   ⚠️  NOT WHITELISTED: Your wallet is NOT whitelisted for this token in the database`);
        console.log(`   This means the blockchain whitelist is not synced to database`);
      }
    } else {
      console.log(`❌ No token approval record found`);
      console.log(`   This means no approval record exists for your wallet + this token`);
      console.log(`   The blockchain whitelist is definitely not synced to database`);
    }
    
    // 4. Test admin whitelist API for this specific token
    console.log('\n4. Admin API Test:');
    const fetch = require('node-fetch');
    
    const adminResponse = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        walletAddress: yourWallet,
        tokenAddresses: [tokenAddress]
      })
    });
    
    if (adminResponse.ok) {
      const adminData = await adminResponse.json();
      const tokenStatus = adminData.tokens[0];
      
      console.log(`✅ Admin API response:`);
      console.log(`   Wallet: ${adminData.walletAddress}`);
      console.log(`   Global Whitelisted: ${adminData.globalWhitelisted}`);
      console.log(`   KYC Status: ${adminData.kycStatus}`);
      console.log(`   Token ${tokenAddress}: ${tokenStatus.isWhitelisted ? 'WHITELISTED' : 'NOT WHITELISTED'}`);
      
      if (tokenStatus.isWhitelisted && (!tokenApproval || !tokenApproval.whitelistApproved)) {
        console.log(`\n   🔥 MISMATCH DETECTED:`);
        console.log(`   - Admin API (database): WHITELISTED`);
        console.log(`   - Token approval record: ${tokenApproval ? 'NOT WHITELISTED' : 'MISSING'}`);
        console.log(`   This suggests the database sync is working for the admin API but not for token approvals`);
      }
    } else {
      console.log(`❌ Admin API test failed`);
    }
    
    // 5. Test client tokens API
    console.log('\n5. Client API Test:');
    const clientResponse = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(yourWallet)}`);
    
    if (clientResponse.ok) {
      const clientTokens = await clientResponse.json();
      const thisToken = clientTokens.find(t => t.address.toLowerCase() === tokenAddress.toLowerCase());
      
      if (thisToken) {
        console.log(`✅ Client API response for this token:`);
        console.log(`   Name: ${thisToken.name}`);
        console.log(`   Symbol: ${thisToken.symbol}`);
        console.log(`   Address: ${thisToken.address}`);
        console.log(`   Whitelisted: ${thisToken.isWhitelisted ? 'YES' : 'NO'}`);
        console.log(`   Price: ${thisToken.price} ${thisToken.currency}`);
        
        if (!thisToken.isWhitelisted) {
          console.log(`\n   ❌ CLIENT API ISSUE: Token shows as NOT WHITELISTED`);
          console.log(`   This confirms the blockchain whitelist is not synced to database`);
        } else {
          console.log(`\n   ✅ CLIENT API SUCCESS: Token shows as WHITELISTED`);
        }
      } else {
        console.log(`❌ Token not found in client API response`);
      }
    } else {
      console.log(`❌ Client API test failed`);
    }
    
  } catch (error) {
    console.error('Error checking database whitelist status:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  await checkDatabaseWhitelistStatus();
  
  console.log('\n🎯 SUMMARY:');
  console.log('===========');
  console.log('This script checks:');
  console.log('1. If the token exists in database');
  console.log('2. If your user exists in database');
  console.log('3. If there\'s a token approval record');
  console.log('4. What the admin API says (reads from database)');
  console.log('5. What the client API says (reads from database)');
  console.log('');
  console.log('If blockchain shows whitelisted but database shows not whitelisted,');
  console.log('then the sync functionality needs to be used to update the database.');
}

main().catch(console.error);
