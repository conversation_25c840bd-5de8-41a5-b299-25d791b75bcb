-- CreateEnum
CREATE TYPE "Gender" AS ENUM ('MALE', 'FEMALE', 'OTHER', 'PREFER_NOT_TO_SAY');

-- Create<PERSON>num
CREATE TYPE "IdentificationType" AS ENUM ('PASSPORT', 'ID_CARD', 'DRIVERS_LICENSE', 'OTHER');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "PEPStatus" AS ENUM ('NOT_PEP', 'DOMESTIC_PEP', 'FOREIGN_PEP', 'INTERNATIONAL_ORG_PEP', 'FAMILY_MEMBER', 'CLOSE_ASSOCIATE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "KYCStatus" AS ENUM ('PENDING', 'IN_REVIEW', 'APPROVED', 'REJECTED', 'EXPIRED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "DocumentType" AS ENUM ('PASSPORT', 'ID_CARD', 'DRIVERS_LICENSE', 'PROOF_OF_ADDRESS', 'BANK_STATEMENT', 'INCOME_PROOF', 'OTHER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DocumentStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "TransactionType" AS ENUM ('MINT', 'TRANSFER', 'BURN', 'FREEZE', 'UNFREEZE', 'WHITELIST_ADD', 'WHITELIST_REMOVE');

-- CreateEnum
CREATE TYPE "TransactionStatus" AS ENUM ('PENDING', 'CONFIRMED', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ApprovalStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "clients" (
    "id" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "gender" "Gender" NOT NULL,
    "nationality" TEXT NOT NULL,
    "birthday" TIMESTAMP(3) NOT NULL,
    "birthPlace" TEXT NOT NULL,
    "identificationType" "IdentificationType" NOT NULL,
    "passportNumber" TEXT,
    "idCardNumber" TEXT,
    "documentExpiration" TIMESTAMP(3) NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "email" TEXT,
    "occupation" TEXT NOT NULL,
    "sectorOfActivity" TEXT NOT NULL,
    "pepStatus" "PEPStatus" NOT NULL,
    "pepDetails" TEXT,
    "street" TEXT NOT NULL,
    "buildingNumber" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT,
    "country" TEXT NOT NULL,
    "zipCode" TEXT NOT NULL,
    "sourceOfWealth" TEXT NOT NULL,
    "bankAccountNumber" TEXT NOT NULL,
    "sourceOfFunds" TEXT NOT NULL,
    "taxIdentificationNumber" TEXT NOT NULL,
    "kycStatus" "KYCStatus" NOT NULL DEFAULT 'PENDING',
    "kycCompletedAt" TIMESTAMP(3),
    "kycNotes" TEXT,
    "walletAddress" TEXT,
    "walletSignature" TEXT,
    "walletVerifiedAt" TIMESTAMP(3),
    "isWhitelisted" BOOLEAN NOT NULL DEFAULT false,
    "whitelistedAt" TIMESTAMP(3),
    "agreementAccepted" BOOLEAN NOT NULL DEFAULT false,
    "agreementAcceptedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "clients_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "client_documents" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "documentType" "DocumentType" NOT NULL,
    "fileName" TEXT NOT NULL,
    "originalFileName" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "mimeType" TEXT NOT NULL,
    "filePath" TEXT NOT NULL,
    "status" "DocumentStatus" NOT NULL DEFAULT 'PENDING',
    "verifiedAt" TIMESTAMP(3),
    "verifiedBy" TEXT,
    "rejectionReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "client_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "client_transactions" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "transactionHash" TEXT NOT NULL,
    "tokenAddress" TEXT NOT NULL,
    "transactionType" "TransactionType" NOT NULL,
    "amount" TEXT NOT NULL,
    "fromAddress" TEXT,
    "toAddress" TEXT,
    "status" "TransactionStatus" NOT NULL DEFAULT 'PENDING',
    "blockNumber" TEXT,
    "gasUsed" TEXT,
    "gasPrice" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "client_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tokens" (
    "id" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "transactionHash" TEXT,
    "blockNumber" TEXT,
    "network" TEXT NOT NULL DEFAULT 'amoy',
    "name" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "decimals" INTEGER NOT NULL DEFAULT 18,
    "maxSupply" TEXT NOT NULL DEFAULT '1000000',
    "totalSupply" TEXT NOT NULL DEFAULT '0',
    "tokenType" TEXT NOT NULL DEFAULT 'equity',
    "tokenPrice" TEXT NOT NULL DEFAULT '10 USD',
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "bonusTiers" TEXT,
    "tokenImageUrl" TEXT,
    "whitelistAddress" TEXT,
    "adminAddress" TEXT,
    "hasKYC" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deployedBy" TEXT,
    "deploymentNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "token_client_approvals" (
    "id" TEXT NOT NULL,
    "tokenId" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "approvalStatus" "ApprovalStatus" NOT NULL DEFAULT 'PENDING',
    "kycApproved" BOOLEAN NOT NULL DEFAULT false,
    "whitelistApproved" BOOLEAN NOT NULL DEFAULT false,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "rejectedReason" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "token_client_approvals_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "clients_email_key" ON "clients"("email");

-- CreateIndex
CREATE UNIQUE INDEX "clients_taxIdentificationNumber_key" ON "clients"("taxIdentificationNumber");

-- CreateIndex
CREATE UNIQUE INDEX "clients_walletAddress_key" ON "clients"("walletAddress");

-- CreateIndex
CREATE UNIQUE INDEX "client_transactions_transactionHash_key" ON "client_transactions"("transactionHash");

-- CreateIndex
CREATE UNIQUE INDEX "tokens_address_key" ON "tokens"("address");

-- CreateIndex
CREATE UNIQUE INDEX "tokens_transactionHash_key" ON "tokens"("transactionHash");

-- CreateIndex
CREATE UNIQUE INDEX "tokens_symbol_key" ON "tokens"("symbol");

-- CreateIndex
CREATE UNIQUE INDEX "token_client_approvals_tokenId_clientId_key" ON "token_client_approvals"("tokenId", "clientId");

-- AddForeignKey
ALTER TABLE "client_documents" ADD CONSTRAINT "client_documents_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "client_transactions" ADD CONSTRAINT "client_transactions_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "client_transactions" ADD CONSTRAINT "client_transactions_tokenAddress_fkey" FOREIGN KEY ("tokenAddress") REFERENCES "tokens"("address") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "token_client_approvals" ADD CONSTRAINT "token_client_approvals_tokenId_fkey" FOREIGN KEY ("tokenId") REFERENCES "tokens"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "token_client_approvals" ADD CONSTRAINT "token_client_approvals_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE CASCADE;
