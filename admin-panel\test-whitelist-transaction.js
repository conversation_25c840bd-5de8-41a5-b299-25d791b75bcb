const { ethers } = require("ethers");

async function testWhitelistTransaction() {
  console.log("🧪 Testing Whitelist Transaction");
  console.log("=================================");

  // Configuration
  const WHITELIST_ADDRESS = "******************************************";
  const CLIENT_ADDRESS = "******************************************";
  const RPC_URL = process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology/";
  const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

  if (!PRIVATE_KEY) {
    console.log("❌ No private key found in environment variables");
    console.log("💡 Make sure CONTRACT_ADMIN_PRIVATE_KEY is set in your .env file");
    return;
  }

  try {
    // Connect to the network
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    
    console.log(`📡 Connected to: ${RPC_URL}`);
    console.log(`🔑 Using wallet: ${wallet.address}`);
    console.log(`📋 Whitelist Contract: ${WHITELIST_ADDRESS}`);
    console.log(`👤 Target Address: ${CLIENT_ADDRESS}`);
    console.log("");

    // Load the Whitelist ABI
    const WhitelistABI = require("./src/contracts/Whitelist.json");
    
    // Connect to the whitelist contract
    const whitelistContract = new ethers.Contract(
      WHITELIST_ADDRESS,
      WhitelistABI.abi,
      wallet
    );

    // Step 1: Check current status
    console.log("1️⃣ Checking current whitelist status...");
    const isWhitelisted = await whitelistContract.isWhitelisted(CLIENT_ADDRESS);
    console.log(`   Current status: ${isWhitelisted ? "✅ WHITELISTED" : "❌ NOT WHITELISTED"}`);

    if (isWhitelisted) {
      console.log("   ✅ Address is already whitelisted - no action needed!");
      return;
    }

    // Step 2: Estimate gas
    console.log("\n2️⃣ Estimating gas...");
    try {
      const gasEstimate = await whitelistContract.addToWhitelist.estimateGas(CLIENT_ADDRESS);
      console.log(`   Estimated gas: ${gasEstimate.toString()}`);
    } catch (error) {
      console.log(`   ❌ Gas estimation failed: ${error.message}`);
      console.log("   🚨 This indicates the transaction will fail!");
      
      // Try to get more details about the error
      if (error.data) {
        console.log(`   Error data: ${error.data}`);
      }
      if (error.reason) {
        console.log(`   Error reason: ${error.reason}`);
      }
      return;
    }

    // Step 3: Try different gas configurations
    const gasConfigs = [
      { gasLimit: 200000, gasPrice: "50" },   // Original settings
      { gasLimit: 300000, gasPrice: "100" },  // Updated settings
      { gasLimit: 500000, gasPrice: "150" },  // High settings
    ];

    for (let i = 0; i < gasConfigs.length; i++) {
      const config = gasConfigs[i];
      console.log(`\n${i + 3}️⃣ Attempting transaction with gas limit: ${config.gasLimit}, gas price: ${config.gasPrice} gwei`);
      
      try {
        const tx = await whitelistContract.addToWhitelist(CLIENT_ADDRESS, {
          gasLimit: BigInt(config.gasLimit),
          gasPrice: ethers.parseUnits(config.gasPrice, "gwei")
        });

        console.log(`   ✅ Transaction submitted: ${tx.hash}`);
        console.log("   ⏳ Waiting for confirmation...");

        const receipt = await tx.wait();
        console.log(`   ✅ Transaction confirmed in block: ${receipt.blockNumber}`);
        console.log(`   ⛽ Gas used: ${receipt.gasUsed.toString()}`);
        
        // Verify the whitelist status
        const newStatus = await whitelistContract.isWhitelisted(CLIENT_ADDRESS);
        console.log(`   📊 New whitelist status: ${newStatus ? "✅ WHITELISTED" : "❌ FAILED"}`);
        
        if (newStatus) {
          console.log("\n🎉 SUCCESS! Address has been whitelisted!");
        } else {
          console.log("\n❌ Transaction succeeded but address is still not whitelisted");
        }
        return;

      } catch (error) {
        console.log(`   ❌ Transaction failed: ${error.message}`);
        
        if (error.data) {
          console.log(`   Error data: ${error.data}`);
        }
        if (error.reason) {
          console.log(`   Error reason: ${error.reason}`);
        }
        
        // If this is the last attempt, show final recommendations
        if (i === gasConfigs.length - 1) {
          console.log("\n🚨 All attempts failed!");
          console.log("💡 Possible solutions:");
          console.log("1. Check if the contract has been upgraded");
          console.log("2. Verify the contract address is correct");
          console.log("3. Check if there are additional access controls");
          console.log("4. Try using a different RPC endpoint");
          console.log("5. Check the transaction on Polygonscan for more details");
        }
      }
    }

  } catch (error) {
    console.error("❌ Script failed:", error.message);
  }
}

// Run the test
testWhitelistTransaction().catch(console.error);
