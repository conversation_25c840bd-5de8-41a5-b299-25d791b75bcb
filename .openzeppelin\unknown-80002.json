{"manifestVersion": "3.2", "proxies": [{"address": "0x324AB4526d55630bf8AfA86F479697B23c6792A4", "kind": "uups"}, {"address": "0xed6F9Ac095b73C903383f4E9C4aC05b1aEfD0457", "txHash": "0x4560ec82de34326d66865cafedb48324c7ae91f81a380ce2603d72b0970ebe2e", "kind": "uups"}, {"address": "0xE8E7cE6cf7C6F15fe71B773F289782b61467Cc2b", "kind": "uups"}, {"address": "0x431ee288E464b6FefbBa93daAE774C35FD184498", "txHash": "0xd0299a9032e52fd37fa871d9aaefa5cc3354cd923c71cfd3be96ab9abdcb6ad0", "kind": "uups"}, {"address": "0xF1b4215213f78D5eF3E60919090D42349fE024A6", "txHash": "0x39f38705ca784a7006667a402ce31945575bc1f25e9fe5e0bae5dc47463a933d", "kind": "uups"}, {"address": "0x4d0B8567dc0dC37Ff85F8637b352f86823E3741E", "txHash": "0x4b63b67123ab57d139958aef82fd5515d42fad388febe5d2fd046d9c6c1e4899", "kind": "uups"}, {"address": "0xf286B6db24008Ff690C6f2C7e7Fe62cbd05603b4", "txHash": "0x0ae60787b0ca871269f318ae35366935152fb5fbecc51b816569784921ab9157", "kind": "uups"}, {"address": "0xfebBeec9B76fCa264E788EB4FB34Cd4e14E283Db", "txHash": "0x652b919189b3c7ea7b72c2f0c795dfb2ff6ca3c77aae8bfce4acee64c2b6bb9c", "kind": "uups"}, {"address": "0x7544A3072FAA793e3f89048C31b794f171779544", "txHash": "0x10abcd33241547ca28e5d6e7b1b947f9873bef3695464f9b90881f2ade0d681d", "kind": "uups"}], "impls": {"5495aa78b302abbf3f53a253097bee61f8831ea4fb9711edab2769066baee086": {"address": "0xb03e461E6eE68194918209aA0b20B7f067a30CD5", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:27"}, {"label": "_tokenPrice", "offset": 0, "slot": "1", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:30"}, {"label": "_bonusTiers", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:31"}, {"label": "_whitelist", "offset": 0, "slot": "3", "type": "t_contract(IWhitelist)4973", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:34"}, {"label": "_adminTransferInProgress", "offset": 20, "slot": "3", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:112"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWhitelist)4973": {"label": "contract IWhitelist", "numberOfBytes": "20"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "allAddresses": ["0xb03e461E6eE68194918209aA0b20B7f067a30CD5"]}, "544f48dc99fd3d760df82cae36e59df23f7dfb2d55da545b257d3a88d47cba33": {"address": "0x15A5Fe1C7fa488939A5b43379E029e1fBCd3C82F", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_whitelisted", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_bool)", "contract": "WhitelistWithKYC", "src": "contracts\\WhitelistWithKYC.sol:22"}, {"label": "_frozen", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_bool)", "contract": "WhitelistWithKYC", "src": "contracts\\WhitelistWithKYC.sol:25"}, {"label": "_kycApproved", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_bool)", "contract": "WhitelistWithKYC", "src": "contracts\\WhitelistWithKYC.sol:28"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "allAddresses": ["0x15A5Fe1C7fa488939A5b43379E029e1fBCd3C82F"]}, "937dc2dbccff42cfc2f487474d428dc81c0529c92657b67aa9ea18d970f6ff91": {"address": "0x7fbdfE0883856a1BD3d335A46Ec73FAC6965250D", "txHash": "0x3d84b7becf91c5e888ea3cf9a37d13f989f8d27e7f8333eddfa759a4c8880ef8", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_whitelisted", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_bool)", "contract": "BaseIdentityRegistry", "src": "contracts\\base\\BaseIdentityRegistry.sol:25"}, {"label": "_frozen", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_bool)", "contract": "BaseIdentityRegistry", "src": "contracts\\base\\BaseIdentityRegistry.sol:28"}, {"label": "_kycApproved", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_bool)", "contract": "Whitelist", "src": "contracts\\Whitelist.sol:20"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "f704b312a243df7bb7370b44b805fbc8735802c966c5661b6d75ba22910fda4d": {"address": "0x83e424bcA10182f28AD0d91572e91E3536cDE944", "txHash": "0xacba75b526a41663faa2b6df213ca28ba3f7042bff9894d42bf739bd3bb604e6", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:34"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:37"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:40"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:41"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:42"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:43"}, {"label": "_identityRegistry", "offset": 0, "slot": "6", "type": "t_contract(ICompleteWhitelist)7178", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:46"}, {"label": "_forcedTransferInProgress", "offset": 20, "slot": "6", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:49"}, {"label": "_approvedTransferInProgress", "offset": 21, "slot": "6", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:52"}, {"label": "_agentList", "offset": 0, "slot": "7", "type": "t_array(t_address)dyn_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:58"}, {"label": "_agentListIndex", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:60"}, {"label": "_conditionalTransfersEnabled", "offset": 0, "slot": "9", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:65"}, {"label": "_transferApprovals", "offset": 0, "slot": "10", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:66"}, {"label": "_transferNonces", "offset": 0, "slot": "11", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:67"}, {"label": "_transfer<PERSON><PERSON><PERSON>stEnabled", "offset": 0, "slot": "12", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:70"}, {"label": "_transfer<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "13", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:71"}, {"label": "_transferFeesEnabled", "offset": 0, "slot": "14", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:74"}, {"label": "_transferFeePercentage", "offset": 0, "slot": "15", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:75"}, {"label": "_feeCollector", "offset": 0, "slot": "16", "type": "t_address", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:76"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_contract(ICompleteWhitelist)7178": {"label": "contract ICompleteWhitelist", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}