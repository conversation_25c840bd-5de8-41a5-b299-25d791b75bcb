# Amoy Testnet Troubleshooting Guide

This guide provides solutions for common issues when interacting with contracts on the Polygon Amoy testnet.

## Simplest Windows Solution: Direct Transaction Batch File

For Windows users, we've created a convenient batch file that makes it easy to execute transactions:

```
# Pause a token:
direct-tx.bat pause 0xYourTokenAddress

# Unpause a token:
direct-tx.bat unpause 0xYourTokenAddress

# Mint tokens:
direct-tx.bat mint 0xYourTokenAddress 0xRecipientAddress 1000

# With custom gas settings (optional):
direct-tx.bat pause 0xYourTokenAddress 2000000 200
                                       ^ Gas Limit  ^ Gas Price (gwei)
```

This batch file automatically sets the proper environment variables and executes the direct-transaction.js script.

## Token Deployment Issues

If you're encountering RPC errors when deploying tokens via the factory contract, we've created a dedicated solution:

```powershell
# Basic usage:
direct-deploy.bat 0xFactoryAddress "My Token" MTK 1000000

# Full parameters:
direct-deploy.bat 0xFactoryAddress "My Token" MTK 1000000 "20 USD" "Tier 1: 10%" 3000000 100 0xAdminAddress
                  ^ Factory       ^ Name    ^ Symbol ^ Max Supply ^ Price  ^ Bonus Tiers ^ Gas    ^ Gas  ^ Admin
                                                                                         Limit    Price
```

Alternatively, you can use environment variables:

```powershell
# For Windows PowerShell:
$env:FACTORY_ADDRESS="******************************************"
$env:TOKEN_NAME="My Security Token"
$env:TOKEN_SYMBOL="MST"
$env:MAX_SUPPLY="1000000"
$env:TOKEN_PRICE="10 USD"
$env:BONUS_TIERS="Tier 1: 5%, Tier 2: 10%, Tier 3: 15%"
$env:GAS_LIMIT="2000000"  # 2M gas limit
$env:GAS_PRICE="100"      # 100 gwei
npx hardhat run scripts/direct-deploy-token.js --network amoy
```

This script:
- Uses ethers.js Interface to properly encode parameters
- Sets extremely high gas limits (2M default)
- Bypasses standard gas estimation
- Logs deployment details for reference
- Automatically retrieves the deployed token and whitelist addresses

## Most Reliable Solution: Direct Transaction Script

For persistent RPC and gas issues, we've created a special script that completely bypasses standard transaction handling and sends raw transactions with extremely high gas parameters. This is our most reliable solution for Amoy testnet issues:

```powershell
# For Windows PowerShell:
$env:TOKEN_ADDRESS="your_token_address"
$env:OPERATION="pause"  # Options: pause, unpause, mint
$env:GAS_LIMIT="1000000"  # Optional: Default is 1,000,000
$env:GAS_PRICE="100"  # Optional: Default is 100 gwei
npx hardhat run scripts/direct-transaction.js --network amoy

# For minting tokens:
$env:TOKEN_ADDRESS="your_token_address"
$env:OPERATION="mint"
$env:TO_ADDRESS="recipient_address"
$env:AMOUNT="1000"  # Amount in tokens
npx hardhat run scripts/direct-transaction.js --network amoy
```

```bash
# For Linux/Mac:
export TOKEN_ADDRESS="your_token_address"
export OPERATION="pause"  # Options: pause, unpause, mint
export GAS_LIMIT="1000000"  # Optional: Default is 1,000,000
export GAS_PRICE="100"  # Optional: Default is 100 gwei
npx hardhat run scripts/direct-transaction.js --network amoy

# For minting tokens:
export TOKEN_ADDRESS="your_token_address"
export OPERATION="mint"
export TO_ADDRESS="recipient_address"
export AMOUNT="1000"  # Amount in tokens
npx hardhat run scripts/direct-transaction.js --network amoy
```

This script:
- Directly creates and signs the raw transaction
- Uses extremely high gas limit (1M) and gas price (100 gwei) by default
- Logs transaction details for later reference
- Handles errors with detailed troubleshooting suggestions

## Alternative Scripts for Pause/Unpause

If the direct transaction script still encounters issues, you can also try our dedicated pause/unpause scripts:

### Pausing a Token

```powershell
# For Windows PowerShell:
$env:TOKEN_ADDRESS="your_token_address"
$env:GAS_LIMIT="500000"  # Optional, defaults to 500,000
npx hardhat run scripts/pause-token.js --network amoy
```

### Unpausing a Token

```powershell
# For Windows PowerShell:
$env:TOKEN_ADDRESS="your_token_address"
$env:GAS_LIMIT="500000"  # Optional, defaults to 500,000
npx hardhat run scripts/unpause-token.js --network amoy
```

## Alternative RPC Endpoints

If you're still having issues, you can try using an alternative RPC endpoint:

```powershell
# For Windows PowerShell:
$env:AMOY_RPC_URL="https://polygon-amoy.blockpi.network/v1/rpc/public"
npx hardhat run scripts/direct-transaction.js --network amoy
```

Here are some alternative Amoy RPC endpoints you can try:

- `https://rpc-amoy.polygon.technology` (default)
- `https://polygon-amoy.blockpi.network/v1/rpc/public`
- `https://polygon-amoy-rpc.publicnode.com`
- `https://polygon-amoy.drpc.org`

## Extreme Gas Settings

For extremely difficult transactions, try with very high gas settings:

```powershell
# For Windows PowerShell:
$env:GAS_LIMIT="2000000"
$env:GAS_PRICE="200"
npx hardhat run scripts/direct-transaction.js --network amoy
```

## General Token Management

For other token operations, use the main token management script:

```powershell
# For Windows PowerShell:
$env:TOKEN_ADDRESS="your_token_address"
$env:ACTION="mint"
$env:TO_ADDRESS="recipient_address"
$env:AMOUNT="1000"
$env:GAS_LIMIT="1000000"
npx hardhat run scripts/05-manage-token.js --network amoy
```

## Network Congestion

Amoy testnet can experience periods of high congestion or instability. If you continue to encounter issues despite trying the solutions above, wait some time and try again later.

## Transaction Logs

The direct-transaction.js script saves transaction details to a `transaction-logs.json` file in the project root directory. This can be useful for tracking and troubleshooting.

## Additional Resources

- [Polygon Amoy Documentation](https://wiki.polygon.technology/docs/amoy/)
- [Hardhat Troubleshooting](https://hardhat.org/hardhat-runner/docs/troubleshooting)
- [Ethers.js Documentation](https://docs.ethers.org/) 