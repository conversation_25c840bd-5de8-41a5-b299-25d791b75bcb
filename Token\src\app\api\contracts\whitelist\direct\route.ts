import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import WhitelistABI from '../../../../../contracts/Whitelist.json';

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology', // Default to Amoy for unknown networks
};

// Rest of the file remains the same 