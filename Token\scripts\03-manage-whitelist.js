// We require the Hardhat Runtime Environment explicitly here. This is optional
// but useful for running the script in a standalone fashion through `node <script>`.
//
// When running the script with `npx hardhat run <script>` you'll find the Hardhat
// Runtime Environment's members available in the global scope.
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    // Get signers and network information
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;
    
    console.log("Managing whitelist with the account:", deployer.address);
    console.log("Network:", networkName);
    
    // Load command line arguments
    const args = process.argv.slice(2);
    
    // Define operations
    const OPERATIONS = {
      ADD: "add",
      REMOVE: "remove",
      FREEZE: "freeze",
      UNFREEZE: "unfreeze",
      APPROVE_KYC: "approveKyc",
      REVOKE_KYC: "revokeKyc",
      LIST: "list",
      STATUS: "status",
    };
    
    // Parse command line arguments
    let operation = args[0] || OPERATIONS.LIST;
    let tokenSymbol = args[1];
    let addresses = args.slice(2);
    
    // For batch operations, addresses can be loaded from a file
    if (addresses.length === 1 && addresses[0].endsWith(".txt")) {
      const addressFile = addresses[0];
      if (fs.existsSync(addressFile)) {
        const addressesFromFile = fs.readFileSync(addressFile, "utf8")
          .split("\n")
          .map(line => line.trim())
          .filter(line => line && line.startsWith("0x"));
        
        addresses = addressesFromFile;
      }
    }
    
    // Validate arguments
    if (!Object.values(OPERATIONS).includes(operation)) {
      console.error(`Invalid operation: ${operation}`);
      console.error(`Valid operations: ${Object.values(OPERATIONS).join(", ")}`);
      process.exit(1);
    }
    
    if (!tokenSymbol && operation !== OPERATIONS.LIST) {
      console.error("Token symbol is required");
      process.exit(1);
    }
    
    // Load token information
    const tokensDir = path.join(__dirname, "../tokens");
    const indexFile = path.join(tokensDir, "index.json");
    
    if (!fs.existsSync(indexFile)) {
      console.error("Token index file not found");
      console.error("Please deploy a token first");
      process.exit(1);
    }
    
    const tokenIndex = JSON.parse(fs.readFileSync(indexFile, "utf8"));
    
    // List tokens if no token symbol is provided or operation is LIST
    if (!tokenSymbol || operation === OPERATIONS.LIST) {
      console.log("Available tokens:");
      Object.entries(tokenIndex).forEach(([symbol, info]) => {
        console.log(`${symbol}: ${info.name} (${info.address} on ${info.network})`);
      });
      
      if (operation === OPERATIONS.LIST) {
        process.exit(0);
      } else {
        console.error("Please specify a token symbol");
        process.exit(1);
      }
    }
    
    // Check if token exists
    if (!tokenIndex[tokenSymbol]) {
      console.error(`Token with symbol ${tokenSymbol} not found`);
      console.error("Available tokens:", Object.keys(tokenIndex).join(", "));
      process.exit(1);
    }
    
    const tokenInfo = tokenIndex[tokenSymbol];
    const tokenAddress = tokenInfo.address;
    const identityRegistryAddress = tokenInfo.identityRegistry;
    
    console.log(`Using token: ${tokenSymbol} (${tokenAddress})`);
    console.log(`Identity Registry: ${identityRegistryAddress}`);
    
    // Load contracts with ethers v6
    const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);
    
    // Determine which whitelist contract to use
    let whitelistContract;
    if (tokenInfo.withKYC) {
      whitelistContract = await hre.ethers.getContractAt("WhitelistWithKYC", identityRegistryAddress);
      console.log("Using WhitelistWithKYC implementation");
    } else {
      whitelistContract = await hre.ethers.getContractAt("Whitelist", identityRegistryAddress);
      console.log("Using basic Whitelist implementation");
    }
    
    // Check status
    if (operation === OPERATIONS.STATUS) {
      if (addresses.length === 0) {
        console.error("Address is required for status check");
        process.exit(1);
      }
      
      for (const address of addresses) {
        try {
          const isWhitelisted = await whitelistContract.isWhitelisted(address);
          const isFrozen = await whitelistContract.isFrozen(address);
          
          console.log(`Status for address ${address}:`);
          console.log(`- Whitelisted: ${isWhitelisted}`);
          console.log(`- Frozen: ${isFrozen}`);
          
          // Check KYC status if available
          try {
            const isKycApproved = await whitelistContract.isKycApproved(address);
            console.log(`- KYC Approved: ${isKycApproved}`);
          } catch (error) {
            console.log("- KYC check not available");
          }
          
          // Check token balance
          const balance = await token.balanceOf(address);
          console.log(`- Token Balance: ${hre.ethers.formatEther(balance)} ${tokenSymbol}`);
        } catch (error) {
          console.error(`Error checking status for ${address}:`, error.message);
        }
      }
      
      process.exit(0);
    }
    
    // For other operations, addresses are required
    if (addresses.length === 0) {
      console.error("At least one address is required");
      process.exit(1);
    }
    
    // Execute operation
    let tx;
    switch (operation) {
      case OPERATIONS.ADD:
        if (addresses.length === 1) {
          tx = await token.addToWhitelist(addresses[0]);
          console.log(`Adding address ${addresses[0]} to whitelist...`);
        } else {
          tx = await token.batchAddToWhitelist(addresses);
          console.log(`Adding ${addresses.length} addresses to whitelist...`);
        }
        break;
      
      case OPERATIONS.REMOVE:
        if (addresses.length === 1) {
          tx = await token.removeFromWhitelist(addresses[0]);
          console.log(`Removing address ${addresses[0]} from whitelist...`);
        } else {
          tx = await token.batchRemoveFromWhitelist(addresses);
          console.log(`Removing ${addresses.length} addresses from whitelist...`);
        }
        break;
      
      case OPERATIONS.FREEZE:
        if (addresses.length === 1) {
          tx = await token.freezeAddress(addresses[0]);
          console.log(`Freezing address ${addresses[0]}...`);
        } else {
          tx = await token.batchFreezeAddresses(addresses);
          console.log(`Freezing ${addresses.length} addresses...`);
        }
        break;
      
      case OPERATIONS.UNFREEZE:
        if (addresses.length === 1) {
          tx = await token.unfreezeAddress(addresses[0]);
          console.log(`Unfreezing address ${addresses[0]}...`);
        } else {
          tx = await token.batchUnfreezeAddresses(addresses);
          console.log(`Unfreezing ${addresses.length} addresses...`);
        }
        break;
      
      case OPERATIONS.APPROVE_KYC:
        if (addresses.length === 1) {
          tx = await whitelistContract.approveKyc(addresses[0]);
          console.log(`Approving KYC for address ${addresses[0]}...`);
        } else {
          tx = await whitelistContract.batchApproveKyc(addresses);
          console.log(`Approving KYC for ${addresses.length} addresses...`);
        }
        break;
      
      case OPERATIONS.REVOKE_KYC:
        if (addresses.length === 1) {
          tx = await whitelistContract.revokeKyc(addresses[0]);
          console.log(`Revoking KYC for address ${addresses[0]}...`);
        } else {
          tx = await whitelistContract.batchRevokeKyc(addresses);
          console.log(`Revoking KYC for ${addresses.length} addresses...`);
        }
        break;
    }
    
    console.log("Transaction sent:", tx.hash);
    console.log("Waiting for confirmation...");
    
    await tx.wait();
    console.log("Transaction confirmed");
    
    // Print summary
    console.log("Operation completed successfully");
    
  } catch (error) {
    console.error("Error during whitelist management:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });