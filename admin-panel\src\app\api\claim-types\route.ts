import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

// GET - Fetch all claim types
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const offset = parseInt(searchParams.get('offset') || '0');
    const limit = parseInt(searchParams.get('limit') || '50');

    const claimRegistryAddress = process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      return NextResponse.json({ error: 'Claim registry not configured' }, { status: 500 });
    }

    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || process.env.NEXT_PUBLIC_AMOY_RPC_URL);

    const claimRegistryABI = [
      "function getActiveClaimTypes(uint256 offset, uint256 limit) external view returns (tuple(uint256 id, string name, string description, address creator, uint256 createdAt, bool active)[])",
      "function getTotalClaimTypes() external view returns (uint256)"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, provider);

    const [claimTypes, totalCount] = await Promise.all([
      claimRegistry.getActiveClaimTypes(offset, limit),
      claimRegistry.getTotalClaimTypes()
    ]);

    const formattedClaimTypes = claimTypes.map((ct: any) => ({
      id: ct.id.toString(),
      name: ct.name,
      description: ct.description,
      creator: ct.creator,
      createdAt: new Date(Number(ct.createdAt) * 1000).toISOString(),
      active: ct.active
    }));

    return NextResponse.json({
      claimTypes: formattedClaimTypes,
      pagination: {
        offset,
        limit,
        total: Number(totalCount)
      }
    });

  } catch (error) {
    console.error('Error fetching claim types:', error);
    return NextResponse.json({ error: 'Failed to fetch claim types' }, { status: 500 });
  }
}

// POST - Create new claim type
export async function POST(request: NextRequest) {
  try {
    const { name, description } = await request.json();

    if (!name || !description) {
      return NextResponse.json({ error: 'Name and description are required' }, { status: 400 });
    }

    const claimRegistryAddress = process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      return NextResponse.json({ error: 'Claim registry not configured' }, { status: 500 });
    }

    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || process.env.NEXT_PUBLIC_AMOY_RPC_URL);
    const adminWallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY!, provider);

    const claimRegistryABI = [
      "function createClaimType(string calldata name, string calldata description) external returns (uint256)"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);

    const tx = await claimRegistry.createClaimType(name, description);
    const receipt = await tx.wait();

    // Extract the claim type ID from the transaction logs
    const claimTypeCreatedEvent = receipt.logs.find((log: any) => {
      try {
        const parsed = claimRegistry.interface.parseLog(log);
        return parsed?.name === 'ClaimTypeCreated';
      } catch {
        return false;
      }
    });

    let claimTypeId = null;
    if (claimTypeCreatedEvent) {
      const parsed = claimRegistry.interface.parseLog(claimTypeCreatedEvent);
      claimTypeId = parsed?.args?.claimTypeId?.toString();
    }

    return NextResponse.json({
      success: true,
      claimTypeId,
      transactionHash: receipt.transactionHash,
      name,
      description,
      message: 'Claim type created successfully'
    });

  } catch (error) {
    console.error('Error creating claim type:', error);
    return NextResponse.json({ error: 'Failed to create claim type' }, { status: 500 });
  }
}

// PUT - Update claim type
export async function PUT(request: NextRequest) {
  try {
    const { claimTypeId, name, description, active } = await request.json();

    if (!claimTypeId || !name || !description || active === undefined) {
      return NextResponse.json({ error: 'All fields are required' }, { status: 400 });
    }

    const claimRegistryAddress = process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      return NextResponse.json({ error: 'Claim registry not configured' }, { status: 500 });
    }

    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || process.env.NEXT_PUBLIC_AMOY_RPC_URL);
    const adminWallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY!, provider);

    const claimRegistryABI = [
      "function updateClaimType(uint256 claimTypeId, string calldata name, string calldata description, bool active) external"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);

    const tx = await claimRegistry.updateClaimType(claimTypeId, name, description, active);
    const receipt = await tx.wait();

    return NextResponse.json({
      success: true,
      transactionHash: receipt.transactionHash,
      claimTypeId,
      name,
      description,
      active,
      message: 'Claim type updated successfully'
    });

  } catch (error) {
    console.error('Error updating claim type:', error);
    return NextResponse.json({ error: 'Failed to update claim type' }, { status: 500 });
  }
}
