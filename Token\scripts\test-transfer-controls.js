const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("🧪 Testing Advanced Transfer Controls...");

  // Load deployment info
  const deploymentPath = path.join(__dirname, '..', 'deployments', 'advanced-controls-token.json');
  if (!fs.existsSync(deploymentPath)) {
    console.error("❌ Deployment info not found. Please run the deployment script first.");
    process.exit(1);
  }

  const deploymentInfo = JSON.parse(fs.readFileSync(deploymentPath, 'utf8'));
  console.log("📄 Loaded deployment info for token:", deploymentInfo.token);

  const signers = await ethers.getSigners();
  const deployer = signers[0];
  console.log("👤 Deployer:", deployer.address);

  // For testing, we'll use the deployer as both user1 and user2
  // In a real test environment, you would have multiple test accounts
  const user1 = deployer;
  const user2 = deployer;
  console.log("👤 User1 (deployer):", user1.address);
  console.log("👤 User2 (deployer):", user2.address);

  // Get contract instances
  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  const token = SecurityToken.attach(deploymentInfo.token);

  const Whitelist = await ethers.getContractFactory("Whitelist");
  const whitelist = Whitelist.attach(deploymentInfo.whitelist);

  console.log("\n🔍 Current State:");
  const deployerBalance = await token.balanceOf(deployer.address);
  console.log("Deployer balance:", ethers.formatUnits(deployerBalance, 0), "tokens");

  // Check current transfer controls status
  console.log("\n🔍 Checking transfer controls status...");
  const conditionalEnabled = await token.conditionalTransfersEnabled();
  const whitelistEnabled = await token.transferWhitelistEnabled();
  const feesEnabled = await token.transferFeesEnabled();
  const [feePercentage, feeCollector] = await token.getTransferFeeConfig();

  console.log("Conditional Transfers:", conditionalEnabled ? "✅ Enabled" : "❌ Disabled");
  console.log("Transfer Whitelisting:", whitelistEnabled ? "✅ Enabled" : "❌ Disabled");
  console.log("Transfer Fees:", feesEnabled ? `✅ Enabled (${Number(feePercentage) / 100}%)` : "❌ Disabled");
  console.log("Fee Collector:", feeCollector);

  // If features are not enabled, enable them
  if (!conditionalEnabled || !whitelistEnabled || !feesEnabled) {
    console.log("\n🔧 Enabling transfer controls...");

    if (!conditionalEnabled) {
      await token.setConditionalTransfers(true);
      console.log("✅ Conditional transfers enabled");
    }

    if (!whitelistEnabled) {
      await token.setTransferWhitelist(true);
      console.log("✅ Transfer whitelisting enabled");
    }

    if (!feesEnabled) {
      await token.setTransferFees(true, 100, deployer.address);
      console.log("✅ Transfer fees enabled (1%)");
    }
  }

  // Test 1: Try regular transfer (should fail due to conditional transfers)
  console.log("\n🧪 Test 1: Regular transfer (should fail)");
  try {
    await token.connect(user1).transfer(user2.address, ethers.parseUnits("100", 0));
    console.log("❌ Transfer succeeded (unexpected!)");
  } catch (error) {
    console.log("✅ Transfer failed as expected:", error.message.split('(')[0]);
  }

  // Test 2: Whitelist user1 for transfers
  console.log("\n🧪 Test 2: Whitelist user1 for transfers");
  await token.setTransferWhitelistAddress(user1.address, true);
  console.log("✅ User1 whitelisted for transfers");

  // Test 3: Try transfer again (should still fail due to conditional transfers)
  console.log("\n🧪 Test 3: Transfer after whitelisting (should still fail)");
  try {
    await token.connect(user1).transfer(user2.address, ethers.parseUnits("100", 0));
    console.log("❌ Transfer succeeded (unexpected!)");
  } catch (error) {
    console.log("✅ Transfer failed as expected:", error.message.split('(')[0]);
  }

  // Test 4: Approve transfer
  console.log("\n🧪 Test 4: Approve and execute transfer");
  const transferAmount = ethers.parseUnits("100", 0);
  const nonce = await token.getTransferNonce(user1.address);
  console.log("Current nonce for user1:", nonce.toString());

  // Approve the transfer
  await token.approveTransfer(user1.address, user2.address, transferAmount, nonce);
  console.log("✅ Transfer approved by agent");

  // Execute the approved transfer
  await token.connect(user1).executeApprovedTransfer(user2.address, transferAmount, nonce);
  console.log("✅ Approved transfer executed");

  // Check balances after transfer
  const user1BalanceAfter = await token.balanceOf(user1.address);
  const user2BalanceAfter = await token.balanceOf(user2.address);
  const deployerBalanceAfter = await token.balanceOf(deployer.address);

  console.log("\n📊 Balances after transfer:");
  console.log("User1:", ethers.formatUnits(user1BalanceAfter, 0), "tokens");
  console.log("User2:", ethers.formatUnits(user2BalanceAfter, 0), "tokens");
  console.log("Deployer (fee collector):", ethers.formatUnits(deployerBalanceAfter, 0), "tokens");

  // Calculate fee
  const expectedFee = transferAmount * BigInt(100) / BigInt(10000); // 1%
  const actualFeeCollected = deployerBalanceAfter - deployerBalance;

  console.log("\n💰 Fee Analysis:");
  console.log("Expected fee (1%):", ethers.formatUnits(expectedFee, 0), "tokens");
  console.log("Actual fee collected:", ethers.formatUnits(actualFeeCollected, 0), "tokens");
  console.log("Fee collection:", expectedFee === actualFeeCollected ? "✅ Correct" : "❌ Incorrect");

  // Test 5: Disable conditional transfers and try regular transfer
  console.log("\n🧪 Test 5: Disable conditional transfers");
  await token.setConditionalTransfers(false);
  console.log("✅ Conditional transfers disabled");

  // Now try a regular transfer (should work with fees)
  const transferAmount2 = ethers.parseUnits("50", 0);
  const user1BalanceBefore = await token.balanceOf(user1.address);
  const user2BalanceBefore = await token.balanceOf(user2.address);
  const deployerBalanceBefore = await token.balanceOf(deployer.address);

  await token.connect(user1).transfer(user2.address, transferAmount2);
  console.log("✅ Regular transfer executed (conditional transfers disabled)");

  const user1BalanceFinal = await token.balanceOf(user1.address);
  const user2BalanceFinal = await token.balanceOf(user2.address);
  const deployerBalanceFinal = await token.balanceOf(deployer.address);

  console.log("\n📊 Final balances:");
  console.log("User1:", ethers.formatUnits(user1BalanceFinal, 0), "tokens");
  console.log("User2:", ethers.formatUnits(user2BalanceFinal, 0), "tokens");
  console.log("Deployer:", ethers.formatUnits(deployerBalanceFinal, 0), "tokens");

  const expectedFee2 = transferAmount2 * BigInt(100) / BigInt(10000); // 1%
  const actualFeeCollected2 = deployerBalanceFinal - deployerBalanceBefore;

  console.log("\n💰 Second transfer fee analysis:");
  console.log("Expected fee (1%):", ethers.formatUnits(expectedFee2, 0), "tokens");
  console.log("Actual fee collected:", ethers.formatUnits(actualFeeCollected2, 0), "tokens");
  console.log("Fee collection:", expectedFee2 === actualFeeCollected2 ? "✅ Correct" : "❌ Incorrect");

  // Test 6: Check transfer controls status
  console.log("\n🔍 Final transfer controls status:");
  const finalConditionalEnabled = await token.conditionalTransfersEnabled();
  const finalWhitelistEnabled = await token.transferWhitelistEnabled();
  const finalFeesEnabled = await token.transferFeesEnabled();
  const [finalFeePercentage, finalFeeCollector] = await token.getTransferFeeConfig();

  console.log("Conditional Transfers:", finalConditionalEnabled ? "✅ Enabled" : "❌ Disabled");
  console.log("Transfer Whitelisting:", finalWhitelistEnabled ? "✅ Enabled" : "❌ Disabled");
  console.log("Transfer Fees:", finalFeesEnabled ? `✅ Enabled (${Number(finalFeePercentage) / 100}%)` : "❌ Disabled");
  console.log("Fee Collector:", finalFeeCollector);

  console.log("\n🎉 All tests completed successfully!");
  console.log("\n🌐 View in admin panel:");
  console.log(`http://localhost:7788/transfer-controls?token=${deploymentInfo.token}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
