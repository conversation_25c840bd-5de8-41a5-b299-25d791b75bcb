"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                // Try to get more recent data from localStorage first\n                const storageKey = \"qualification_progress_\".concat(tokenAddress);\n                let localProgress = null;\n                try {\n                    const stored = localStorage.getItem(storageKey);\n                    if (stored) {\n                        localProgress = JSON.parse(stored);\n                        console.log('📱 Found localStorage progress:', localProgress);\n                    }\n                } catch (error) {\n                    console.error('Error reading localStorage:', error);\n                }\n                // Use localStorage data if it's more recent, otherwise use API data\n                const progressToUse = localProgress || qualificationProgress;\n                const newStepData = {\n                    country: progressToUse.country || '',\n                    agreementAccepted: progressToUse.agreementAccepted || false,\n                    profileCompleted: progressToUse.profileCompleted || !!profile,\n                    walletConnected: progressToUse.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: progressToUse.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = progressToUse.currentStep || 0;\n                // Allow users to progress through all steps without blocking\n                // Only auto-advance to next incomplete step if current step is completed\n                if (calculatedStep === 0 && newStepData.country) {\n                    calculatedStep = 1; // Move to agreement if country is selected\n                } else if (calculatedStep === 1 && newStepData.agreementAccepted) {\n                    calculatedStep = 2; // Move to profile if agreement is accepted\n                } else if (calculatedStep === 2 && newStepData.profileCompleted) {\n                    calculatedStep = 3; // Move to wallet if profile is completed\n                } else if (calculatedStep === 3 && newStepData.walletConnected) {\n                    calculatedStep = 4; // Move to KYC if wallet is connected\n                } else if (calculatedStep === 4 && newStepData.kycCompleted) {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: progressToUse,\n                    source: localProgress ? 'localStorage' : 'API'\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus,\n        tokenAddress\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            const progressData = {\n                ...updatedStepData,\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: Object.values(updatedStepData).filter(Boolean).length\n            };\n            console.log('💾 Saving progress to database:', progressData);\n            // Save to backend database via admin panel API\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully to database:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    // Step navigation functions\n    const canNavigateToStep = (stepIndex)=>{\n        // Users can always navigate to completed steps or the next incomplete step\n        if (stepIndex === 0) return true; // Country selection always available\n        if (stepIndex === 1) return stepData.country !== ''; // Agreement if country selected\n        if (stepIndex === 2) return stepData.agreementAccepted; // Profile if agreement accepted\n        if (stepIndex === 3) return stepData.profileCompleted; // Wallet if profile completed\n        if (stepIndex === 4) return stepData.walletConnected; // KYC if wallet connected\n        if (stepIndex === 5) return stepData.kycCompleted; // Completion if KYC done\n        return false;\n    };\n    const handleStepClick = (stepIndex)=>{\n        if (canNavigateToStep(stepIndex)) {\n            setCurrentStep(stepIndex);\n            // Save the current step navigation\n            saveProgress(stepData, stepIndex);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border text-center \".concat(getStepColor(step.status)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Complete!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". You can now proceed with your investment.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3F1YWxpZmljYXRpb24vUXVhbGlmaWNhdGlvbkZsb3cudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNLO0FBQ2lEO0FBQzVDO0FBQ0o7QUFDTztBQUNGO0FBQ1I7QUFleEMsU0FBU1csa0JBQWtCLEtBQWdFO1FBQWhFLEVBQUVDLFlBQVksRUFBRUMsU0FBUyxFQUFFQyxXQUFXLEVBQTBCLEdBQWhFOztJQUNoQyxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2lCLFVBQVVDLFlBQVksR0FBR2xCLCtDQUFRQSxDQUFDO1FBQ3ZDbUIsU0FBUztRQUNUQyxtQkFBbUI7UUFDbkJDLGtCQUFrQjtRQUNsQkMsaUJBQWlCO1FBQ2pCQyxjQUFjO0lBQ2hCO0lBQ0EsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUd6QiwrQ0FBUUEsQ0FBUztJQUNuRCxNQUFNLENBQUMwQixVQUFVQyxZQUFZLEdBQUczQiwrQ0FBUUEsQ0FBZ0I7SUFFeEQsd0NBQXdDO0lBQ3hDLE1BQU0sRUFBRTRCLE1BQU1DLHFCQUFxQixFQUFFQyxTQUFTLEVBQUUsR0FBRzVCLCtEQUFRQSxDQUFDO1FBQzFENkIsVUFBVTtZQUFDO1lBQTBCbkI7U0FBYTtRQUNsRG9CLE9BQU87MENBQUU7Z0JBQ1AsTUFBTUMsU0FBUyxJQUFJQztnQkFDbkIsSUFBSXRCLGNBQWNxQixPQUFPRSxNQUFNLENBQUMsZ0JBQWdCdkI7Z0JBRWhELE1BQU13QixXQUFXLE1BQU1DLE1BQU0sc0NBQXdELE9BQWxCSixPQUFPSyxRQUFRO2dCQUNsRixJQUFJRixTQUFTRyxFQUFFLEVBQUU7b0JBQ2YsT0FBT0gsU0FBU0ksSUFBSTtnQkFDdEI7Z0JBQ0EsT0FBTztZQUNUOztJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU0sRUFBRVosTUFBTWEsT0FBTyxFQUFFLEdBQUd2QywrREFBUUEsQ0FBQztRQUNqQzZCLFVBQVU7WUFBQztTQUFpQjtRQUM1QkMsT0FBTzswQ0FBRTtnQkFDUCxNQUFNSSxXQUFXLE1BQU1DLE1BQU07Z0JBQzdCLElBQUlELFNBQVNHLEVBQUUsRUFBRTtvQkFDZixPQUFPSCxTQUFTSSxJQUFJO2dCQUN0QjtnQkFDQSxPQUFPO1lBQ1Q7O0lBQ0Y7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTSxFQUFFWixNQUFNYyxZQUFZLEVBQUUsR0FBR3hDLCtEQUFRQSxDQUFDO1FBQ3RDNkIsVUFBVTtZQUFDO1NBQWdCO1FBQzNCQyxPQUFPOzBDQUFFO2dCQUNQLE1BQU1JLFdBQVcsTUFBTUMsTUFBTTtnQkFDN0IsSUFBSUQsU0FBU0csRUFBRSxFQUFFO29CQUNmLE9BQU9ILFNBQVNJLElBQUk7Z0JBQ3RCO2dCQUNBLE9BQU87WUFDVDs7SUFDRjtJQUVBLDZDQUE2QztJQUM3Q3ZDLGdEQUFTQTt1Q0FBQztZQUNSLElBQUk0Qix1QkFBdUI7Z0JBQ3pCLHNEQUFzRDtnQkFDdEQsTUFBTWMsYUFBYSwwQkFBdUMsT0FBYi9CO2dCQUM3QyxJQUFJZ0MsZ0JBQWdCO2dCQUNwQixJQUFJO29CQUNGLE1BQU1DLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQ0o7b0JBQ3BDLElBQUlFLFFBQVE7d0JBQ1ZELGdCQUFnQkksS0FBS0MsS0FBSyxDQUFDSjt3QkFDM0JLLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNQO29CQUNqRDtnQkFDRixFQUFFLE9BQU9RLE9BQU87b0JBQ2RGLFFBQVFFLEtBQUssQ0FBQywrQkFBK0JBO2dCQUMvQztnQkFFQSxvRUFBb0U7Z0JBQ3BFLE1BQU1DLGdCQUFnQlQsaUJBQWlCZjtnQkFFdkMsTUFBTXlCLGNBQWM7b0JBQ2xCbkMsU0FBU2tDLGNBQWNsQyxPQUFPLElBQUk7b0JBQ2xDQyxtQkFBbUJpQyxjQUFjakMsaUJBQWlCLElBQUk7b0JBQ3REQyxrQkFBa0JnQyxjQUFjaEMsZ0JBQWdCLElBQUksQ0FBQyxDQUFDb0I7b0JBQ3REbkIsaUJBQWlCK0IsY0FBYy9CLGVBQWUsSUFBSSxDQUFDLEVBQUNvQix5QkFBQUEsbUNBQUFBLGFBQWNhLFFBQVE7b0JBQzFFaEMsY0FBYzhCLGNBQWM5QixZQUFZLElBQUtrQixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNqQixTQUFTLE1BQUs7Z0JBQ3RFO2dCQUVBTixZQUFZb0M7Z0JBRVosK0VBQStFO2dCQUMvRSxJQUFJRSxpQkFBaUJILGNBQWN0QyxXQUFXLElBQUk7Z0JBRWxELDZEQUE2RDtnQkFDN0QseUVBQXlFO2dCQUN6RSxJQUFJeUMsbUJBQW1CLEtBQUtGLFlBQVluQyxPQUFPLEVBQUU7b0JBQy9DcUMsaUJBQWlCLEdBQUcsMkNBQTJDO2dCQUNqRSxPQUFPLElBQUlBLG1CQUFtQixLQUFLRixZQUFZbEMsaUJBQWlCLEVBQUU7b0JBQ2hFb0MsaUJBQWlCLEdBQUcsMkNBQTJDO2dCQUNqRSxPQUFPLElBQUlBLG1CQUFtQixLQUFLRixZQUFZakMsZ0JBQWdCLEVBQUU7b0JBQy9EbUMsaUJBQWlCLEdBQUcseUNBQXlDO2dCQUMvRCxPQUFPLElBQUlBLG1CQUFtQixLQUFLRixZQUFZaEMsZUFBZSxFQUFFO29CQUM5RGtDLGlCQUFpQixHQUFHLHFDQUFxQztnQkFDM0QsT0FBTyxJQUFJQSxtQkFBbUIsS0FBS0YsWUFBWS9CLFlBQVksRUFBRTtvQkFDM0RpQyxpQkFBaUIsR0FBRyxnQkFBZ0I7Z0JBQ3RDO2dCQUVBeEMsZUFBZXdDO2dCQUVmTixRQUFRQyxHQUFHLENBQUMsb0NBQW9DO29CQUM5Q2xDLFVBQVVxQztvQkFDVnZDLGFBQWF5QztvQkFDYkMsZUFBZUo7b0JBQ2ZLLFFBQVFkLGdCQUFnQixpQkFBaUI7Z0JBQzNDO1lBQ0Y7UUFDRjtzQ0FBRztRQUFDZjtRQUF1Qlk7UUFBU0M7UUFBYzlCO0tBQWE7SUFFL0QsTUFBTStDLFFBQTZCO1FBQ2pDO1lBQ0VDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFFBQVE5QyxTQUFTRSxPQUFPLEdBQUcsY0FBY0osZ0JBQWdCLElBQUksWUFBWTtRQUMzRTtRQUNBO1lBQ0U2QyxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYSxjQUFtQyxPQUFyQmpELGFBQWEsU0FBUTtZQUNoRGtELFFBQVE5QyxTQUFTRyxpQkFBaUIsR0FBRyxjQUFjTCxnQkFBZ0IsSUFBSSxZQUFZO1FBQ3JGO1FBQ0E7WUFDRTZDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFFBQVE5QyxTQUFTSSxnQkFBZ0IsR0FBRyxjQUFjTixnQkFBZ0IsSUFBSSxZQUFZO1FBQ3BGO1FBQ0E7WUFDRTZDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFFBQVE5QyxTQUFTSyxlQUFlLEdBQUcsY0FBY1AsZ0JBQWdCLElBQUksWUFBWTtRQUNuRjtRQUNBO1lBQ0U2QyxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxRQUFROUMsU0FBU00sWUFBWSxHQUFHLGNBQ3hCQyxjQUFjLFdBQVcsVUFDekJULGdCQUFnQixJQUFJLFlBQVk7UUFDMUM7S0FDRDtJQUVELE1BQU1pRCxjQUFjLENBQUNEO1FBQ25CLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQzVELDJJQUFlQTtvQkFBQzhELFdBQVU7Ozs7OztZQUNwQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDN0QsMklBQVNBO29CQUFDNkQsV0FBVTs7Ozs7O1lBQzlCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUM1RCw0SUFBdUJBO29CQUFDNEQsV0FBVTs7Ozs7O1lBQzVDO2dCQUNFLHFCQUFPLDhEQUFDQztvQkFBSUQsV0FBVTs7Ozs7O1FBQzFCO0lBQ0Y7SUFFQSxNQUFNRSxlQUFlLENBQUNKO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFTLE9BQU87WUFDckI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsMENBQTBDO0lBQzFDLE1BQU1LLGVBQWUsT0FBT0MsaUJBQXNCQztRQUNoRCxJQUFJO1lBQ0YsTUFBTUMsZUFBZTtnQkFDbkIsR0FBR0YsZUFBZTtnQkFDbEJ6RDtnQkFDQUcsYUFBYXVEO2dCQUNiRSxnQkFBZ0JDLE9BQU9DLE1BQU0sQ0FBQ0wsaUJBQWlCTSxNQUFNLENBQUNDLFNBQVNDLE1BQU07WUFDdkU7WUFFQTNCLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNvQjtZQUUvQywrQ0FBK0M7WUFDL0MsTUFBTW5DLFdBQVcsTUFBTUMsTUFBTSxzQ0FBc0M7Z0JBQ2pFeUMsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTWhDLEtBQUtpQyxTQUFTLENBQUNWO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDbkMsU0FBU0csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUkyQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUMsU0FBUyxNQUFNL0MsU0FBU0ksSUFBSTtZQUNsQ1UsUUFBUUMsR0FBRyxDQUFDLDhDQUE4Q2dDO1FBQzVELEVBQUUsT0FBTy9CLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsNENBQTRDO1FBQzlDO0lBQ0Y7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTWdDLHdCQUF3QixPQUFPakU7UUFDbkMsTUFBTWtELGtCQUFrQjtZQUFFLEdBQUdwRCxRQUFRO1lBQUVFO1lBQVNrRSxrQkFBa0I7UUFBSztRQUN2RW5FLFlBQVltRDtRQUNackQsZUFBZTtRQUVmLGdCQUFnQjtRQUNoQixNQUFNb0QsYUFBYUMsaUJBQWlCO0lBQ3RDO0lBRUEsTUFBTWlCLDBCQUEwQjtRQUM5QixpQ0FBaUM7UUFDakMsSUFBSTtZQUNGLE1BQU1sRCxXQUFXLE1BQU1DLE1BQU0sK0JBQStCO2dCQUMxRHlDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1oQyxLQUFLaUMsU0FBUyxDQUFDO29CQUNuQnJFO29CQUNBRTtvQkFDQXlFLFVBQVU7Z0JBQ1o7WUFDRjtZQUVBLElBQUksQ0FBQ25ELFNBQVNHLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJMkMsTUFBTTtZQUNsQjtZQUVBaEMsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ25EO1FBRUEsZ0NBQWdDO1FBQ2hDLE1BQU1pQixrQkFBa0I7WUFBRSxHQUFHcEQsUUFBUTtZQUFFRyxtQkFBbUI7UUFBSztRQUMvREYsWUFBWW1EO1FBQ1pyRCxlQUFlO1FBRWYsZ0JBQWdCO1FBQ2hCLE1BQU1vRCxhQUFhQyxpQkFBaUI7SUFDdEM7SUFFQSxNQUFNbUIsd0JBQXdCO1FBQzVCLE1BQU1uQixrQkFBa0I7WUFBRSxHQUFHcEQsUUFBUTtZQUFFSSxrQkFBa0I7UUFBSztRQUM5REgsWUFBWW1EO1FBQ1pyRCxlQUFlO1FBRWYsZ0JBQWdCO1FBQ2hCLE1BQU1vRCxhQUFhQyxpQkFBaUI7SUFDdEM7SUFFQSxNQUFNb0IsdUJBQXVCO1FBQzNCLE1BQU1wQixrQkFBa0I7WUFBRSxHQUFHcEQsUUFBUTtZQUFFSyxpQkFBaUI7UUFBSztRQUM3REosWUFBWW1EO1FBQ1pyRCxlQUFlO1FBRWYsZ0JBQWdCO1FBQ2hCLE1BQU1vRCxhQUFhQyxpQkFBaUI7SUFDdEM7SUFFQSxNQUFNcUIsd0JBQXdCLE9BQU8zQixRQUFnQlg7UUFDbkQzQixhQUFhc0M7UUFDYixJQUFJWCxPQUFPO1lBQ1R6QixZQUFZeUI7UUFDZCxPQUFPO1lBQ0x6QixZQUFZO1FBQ2Q7UUFFQSxJQUFJb0MsV0FBVyxhQUFhO1lBQzFCLE1BQU1NLGtCQUFrQjtnQkFBRSxHQUFHcEQsUUFBUTtnQkFBRU0sY0FBYztZQUFLO1lBQzFETCxZQUFZbUQ7WUFDWnJELGVBQWU7WUFFZixnQkFBZ0I7WUFDaEIsTUFBTW9ELGFBQWFDLGlCQUFpQjtRQUN0QztJQUNGO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU1zQixvQkFBb0IsQ0FBQ0M7UUFDekIsMkVBQTJFO1FBQzNFLElBQUlBLGNBQWMsR0FBRyxPQUFPLE1BQU0scUNBQXFDO1FBQ3ZFLElBQUlBLGNBQWMsR0FBRyxPQUFPM0UsU0FBU0UsT0FBTyxLQUFLLElBQUksZ0NBQWdDO1FBQ3JGLElBQUl5RSxjQUFjLEdBQUcsT0FBTzNFLFNBQVNHLGlCQUFpQixFQUFFLGdDQUFnQztRQUN4RixJQUFJd0UsY0FBYyxHQUFHLE9BQU8zRSxTQUFTSSxnQkFBZ0IsRUFBRSw4QkFBOEI7UUFDckYsSUFBSXVFLGNBQWMsR0FBRyxPQUFPM0UsU0FBU0ssZUFBZSxFQUFFLDBCQUEwQjtRQUNoRixJQUFJc0UsY0FBYyxHQUFHLE9BQU8zRSxTQUFTTSxZQUFZLEVBQUUseUJBQXlCO1FBQzVFLE9BQU87SUFDVDtJQUVBLE1BQU1zRSxrQkFBa0IsQ0FBQ0Q7UUFDdkIsSUFBSUQsa0JBQWtCQyxZQUFZO1lBQ2hDNUUsZUFBZTRFO1lBQ2YsbUNBQW1DO1lBQ25DeEIsYUFBYW5ELFVBQVUyRTtRQUN6QjtJQUNGO0lBRUEsSUFBSTlELFdBQVc7UUFDYixxQkFDRSw4REFBQ29DO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLE1BQU1PLGlCQUFpQmIsTUFBTWdCLE1BQU0sQ0FBQ21CLENBQUFBLE9BQVFBLEtBQUsvQixNQUFNLEtBQUssYUFBYWMsTUFBTTtJQUMvRSxNQUFNa0IsYUFBYXBDLE1BQU1rQixNQUFNO0lBQy9CLE1BQU1tQixxQkFBcUIsaUJBQWtCRCxhQUFjO0lBRTNELHFCQUNFLDhEQUFDN0I7UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ2dDO3dCQUFHaEMsV0FBVTtrQ0FDWHBELFlBQVksR0FBYSxPQUFWQSxXQUFVLG9CQUFrQjs7Ozs7O2tDQUU5Qyw4REFBQ3FGO3dCQUFFakMsV0FBVTs7NEJBQTZCOzRCQUNLcEQsYUFBYTs0QkFBUTs7Ozs7OztrQ0FJcEUsOERBQUNxRDt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQ0M7NEJBQ0NELFdBQVU7NEJBQ1ZrQyxPQUFPO2dDQUFFQyxPQUFPLEdBQXNCLE9BQW5CSixvQkFBbUI7NEJBQUc7Ozs7Ozs7Ozs7O2tDQUk3Qyw4REFBQ0U7d0JBQUVqQyxXQUFVOzs0QkFDVk87NEJBQWU7NEJBQUt1Qjs0QkFBVzs7Ozs7Ozs7Ozs7OzswQkFLcEMsOERBQUM3QjtnQkFBSUQsV0FBVTswQkFDWk4sTUFBTTBDLEdBQUcsQ0FBQyxDQUFDUCxNQUFNUSxzQkFDaEIsOERBQUNwQzt3QkFFQ0QsV0FBVyxxQ0FBK0QsT0FBMUJFLGFBQWEyQixLQUFLL0IsTUFBTTs7MENBRXhFLDhEQUFDRztnQ0FBSUQsV0FBVTswQ0FDWkQsWUFBWThCLEtBQUsvQixNQUFNOzs7Ozs7MENBRTFCLDhEQUFDd0M7Z0NBQUd0QyxXQUFVOzBDQUE4QjZCLEtBQUtqQyxLQUFLOzs7Ozs7MENBQ3RELDhEQUFDcUM7Z0NBQUVqQyxXQUFVOzBDQUFXNkIsS0FBS2hDLFdBQVc7Ozs7Ozs7dUJBUG5DZ0MsS0FBS2xDLEVBQUU7Ozs7Ozs7Ozs7MEJBYWxCLDhEQUFDTTtnQkFBSUQsV0FBVTs7b0JBQ1psRCxnQkFBZ0IsbUJBQ2YsOERBQUNULCtEQUFnQkE7d0JBQ2ZrRyxZQUFZcEI7d0JBQ1pxQixpQkFBaUJ4RixTQUFTRSxPQUFPO3dCQUNqQ3VGLGFBQWF6RixTQUFTRSxPQUFPLEtBQUs7Ozs7OztvQkFJckNKLGdCQUFnQixtQkFDZiw4REFBQ1IsMkRBQWNBO3dCQUNiaUcsWUFBWWxCO3dCQUNaekUsV0FBV0E7d0JBQ1hDLGFBQWFBO3dCQUNiNEYsYUFBYXpGLFNBQVNHLGlCQUFpQjs7Ozs7O29CQUkxQ0wsZ0JBQWdCLG1CQUNmLDhEQUFDbUQ7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUMwQzt3Q0FBRzFDLFdBQVU7a0RBQXdDOzs7Ozs7a0RBQ3RELDhEQUFDaUM7d0NBQUVqQyxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUkvQiw4REFBQ3pELGlFQUFpQkE7Z0NBQ2hCZ0csWUFBWWhCO2dDQUNab0IsaUJBQWlCbkU7Ozs7Ozs7Ozs7OztvQkFLdEIxQixnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQzBDO3dDQUFHMUMsV0FBVTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUNpQzt3Q0FBRWpDLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBSS9CLDhEQUFDeEQsK0RBQWdCQTtnQ0FBQ29HLG1CQUFtQnBCOzs7Ozs7Ozs7Ozs7b0JBSXhDMUUsZ0JBQWdCLG1CQUNmLDhEQUFDbUQ7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUMwQzt3Q0FBRzFDLFdBQVU7a0RBQXdDOzs7Ozs7a0RBQ3RELDhEQUFDaUM7d0NBQUVqQyxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUkvQiw4REFBQ3ZELHVEQUFZQTtnQ0FDWG9HLGdCQUFnQnBCO2dDQUNoQmtCLGlCQUFpQm5FOzs7Ozs7Ozs7Ozs7b0JBS3RCMUIsZ0JBQWdCLG1CQUNmLDhEQUFDbUQ7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDOUQsMklBQWVBO2dDQUFDOEQsV0FBVTs7Ozs7OzBDQUMzQiw4REFBQzBDO2dDQUFHMUMsV0FBVTswQ0FBd0M7Ozs7OzswQ0FHdEQsOERBQUNpQztnQ0FBRWpDLFdBQVU7O29DQUFxQjtvQ0FDNkJwRCxhQUFhO29DQUFhOzs7Ozs7OzBDQUd6Riw4REFBQ2tHO2dDQUNDQyxTQUFTLElBQU1DLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO2dDQUN0Q2xELFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFiO0dBM2FnQnREOztRQWFxQ1QsMkRBQVFBO1FBZWpDQSwyREFBUUE7UUFZSEEsMkRBQVFBOzs7S0F4Q3pCUyIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xccXVhbGlmaWNhdGlvblxcUXVhbGlmaWNhdGlvbkZsb3cudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVF1ZXJ5IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IENoZWNrQ2lyY2xlSWNvbiwgQ2xvY2tJY29uLCBFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBDb3VudHJ5U2VsZWN0aW9uIH0gZnJvbSAnLi9Db3VudHJ5U2VsZWN0aW9uJztcbmltcG9ydCB7IFRva2VuQWdyZWVtZW50IH0gZnJvbSAnLi9Ub2tlbkFncmVlbWVudCc7XG5pbXBvcnQgeyBRdWFsaWZpY2F0aW9uRm9ybSB9IGZyb20gJy4uL1F1YWxpZmljYXRpb25Gb3JtJztcbmltcG9ydCB7IFdhbGxldENvbm5lY3Rpb24gfSBmcm9tICcuLi9XYWxsZXRDb25uZWN0aW9uJztcbmltcG9ydCB7IEF1dG9tYXRpY0tZQyB9IGZyb20gJy4uL0F1dG9tYXRpY0tZQyc7XG5cbmludGVyZmFjZSBRdWFsaWZpY2F0aW9uRmxvd1Byb3BzIHtcbiAgdG9rZW5BZGRyZXNzPzogc3RyaW5nO1xuICB0b2tlbk5hbWU/OiBzdHJpbmc7XG4gIHRva2VuU3ltYm9sPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgUXVhbGlmaWNhdGlvblN0ZXAge1xuICBpZDogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBzdGF0dXM6ICdjb21wbGV0ZWQnIHwgJ2N1cnJlbnQnIHwgJ3BlbmRpbmcnIHwgJ2Vycm9yJztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFF1YWxpZmljYXRpb25GbG93KHsgdG9rZW5BZGRyZXNzLCB0b2tlbk5hbWUsIHRva2VuU3ltYm9sIH06IFF1YWxpZmljYXRpb25GbG93UHJvcHMpIHtcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3N0ZXBEYXRhLCBzZXRTdGVwRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgY291bnRyeTogJycsXG4gICAgYWdyZWVtZW50QWNjZXB0ZWQ6IGZhbHNlLFxuICAgIHByb2ZpbGVDb21wbGV0ZWQ6IGZhbHNlLFxuICAgIHdhbGxldENvbm5lY3RlZDogZmFsc2UsXG4gICAga3ljQ29tcGxldGVkOiBmYWxzZSxcbiAgfSk7XG4gIGNvbnN0IFtreWNTdGF0dXMsIHNldEt5Y1N0YXR1c10gPSB1c2VTdGF0ZTxzdHJpbmc+KCdpZGxlJyk7XG4gIGNvbnN0IFtreWNFcnJvciwgc2V0S3ljRXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gRmV0Y2ggZXhpc3RpbmcgcXVhbGlmaWNhdGlvbiBwcm9ncmVzc1xuICBjb25zdCB7IGRhdGE6IHF1YWxpZmljYXRpb25Qcm9ncmVzcywgaXNMb2FkaW5nIH0gPSB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsncXVhbGlmaWNhdGlvbi1wcm9ncmVzcycsIHRva2VuQWRkcmVzc10sXG4gICAgcXVlcnlGbjogYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgICAgaWYgKHRva2VuQWRkcmVzcykgcGFyYW1zLmFwcGVuZCgndG9rZW5BZGRyZXNzJywgdG9rZW5BZGRyZXNzKTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jbGllbnQvcXVhbGlmaWNhdGlvbi1wcm9ncmVzcz8ke3BhcmFtcy50b1N0cmluZygpfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5qc29uKCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9LFxuICB9KTtcblxuICAvLyBGZXRjaCBjbGllbnQgcHJvZmlsZVxuICBjb25zdCB7IGRhdGE6IHByb2ZpbGUgfSA9IHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydjbGllbnQtcHJvZmlsZSddLFxuICAgIHF1ZXJ5Rm46IGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2xpZW50L3Byb2ZpbGUnKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfSxcbiAgfSk7XG5cbiAgLy8gRmV0Y2ggd2FsbGV0IHN0YXR1c1xuICBjb25zdCB7IGRhdGE6IHdhbGxldFN0YXR1cyB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ3dhbGxldC1zdGF0dXMnXSxcbiAgICBxdWVyeUZuOiBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudC93YWxsZXQnKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfSxcbiAgfSk7XG5cbiAgLy8gVXBkYXRlIHN0ZXAgZGF0YSBiYXNlZCBvbiBmZXRjaGVkIHByb2dyZXNzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHF1YWxpZmljYXRpb25Qcm9ncmVzcykge1xuICAgICAgLy8gVHJ5IHRvIGdldCBtb3JlIHJlY2VudCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlIGZpcnN0XG4gICAgICBjb25zdCBzdG9yYWdlS2V5ID0gYHF1YWxpZmljYXRpb25fcHJvZ3Jlc3NfJHt0b2tlbkFkZHJlc3N9YDtcbiAgICAgIGxldCBsb2NhbFByb2dyZXNzID0gbnVsbDtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpO1xuICAgICAgICBpZiAoc3RvcmVkKSB7XG4gICAgICAgICAgbG9jYWxQcm9ncmVzcyA9IEpTT04ucGFyc2Uoc3RvcmVkKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TsSBGb3VuZCBsb2NhbFN0b3JhZ2UgcHJvZ3Jlc3M6JywgbG9jYWxQcm9ncmVzcyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlYWRpbmcgbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcbiAgICAgIH1cblxuICAgICAgLy8gVXNlIGxvY2FsU3RvcmFnZSBkYXRhIGlmIGl0J3MgbW9yZSByZWNlbnQsIG90aGVyd2lzZSB1c2UgQVBJIGRhdGFcbiAgICAgIGNvbnN0IHByb2dyZXNzVG9Vc2UgPSBsb2NhbFByb2dyZXNzIHx8IHF1YWxpZmljYXRpb25Qcm9ncmVzcztcblxuICAgICAgY29uc3QgbmV3U3RlcERhdGEgPSB7XG4gICAgICAgIGNvdW50cnk6IHByb2dyZXNzVG9Vc2UuY291bnRyeSB8fCAnJyxcbiAgICAgICAgYWdyZWVtZW50QWNjZXB0ZWQ6IHByb2dyZXNzVG9Vc2UuYWdyZWVtZW50QWNjZXB0ZWQgfHwgZmFsc2UsXG4gICAgICAgIHByb2ZpbGVDb21wbGV0ZWQ6IHByb2dyZXNzVG9Vc2UucHJvZmlsZUNvbXBsZXRlZCB8fCAhIXByb2ZpbGUsXG4gICAgICAgIHdhbGxldENvbm5lY3RlZDogcHJvZ3Jlc3NUb1VzZS53YWxsZXRDb25uZWN0ZWQgfHwgISF3YWxsZXRTdGF0dXM/LnZlcmlmaWVkLFxuICAgICAgICBreWNDb21wbGV0ZWQ6IHByb2dyZXNzVG9Vc2Uua3ljQ29tcGxldGVkIHx8IChwcm9maWxlPy5reWNTdGF0dXMgPT09ICdBUFBST1ZFRCcpLFxuICAgICAgfTtcblxuICAgICAgc2V0U3RlcERhdGEobmV3U3RlcERhdGEpO1xuXG4gICAgICAvLyBTZXQgY3VycmVudCBzdGVwIGJhc2VkIG9uIHNhdmVkIHByb2dyZXNzIG9yIGNhbGN1bGF0ZSBmcm9tIGNvbXBsZXRpb24gc3RhdHVzXG4gICAgICBsZXQgY2FsY3VsYXRlZFN0ZXAgPSBwcm9ncmVzc1RvVXNlLmN1cnJlbnRTdGVwIHx8IDA7XG5cbiAgICAgIC8vIEFsbG93IHVzZXJzIHRvIHByb2dyZXNzIHRocm91Z2ggYWxsIHN0ZXBzIHdpdGhvdXQgYmxvY2tpbmdcbiAgICAgIC8vIE9ubHkgYXV0by1hZHZhbmNlIHRvIG5leHQgaW5jb21wbGV0ZSBzdGVwIGlmIGN1cnJlbnQgc3RlcCBpcyBjb21wbGV0ZWRcbiAgICAgIGlmIChjYWxjdWxhdGVkU3RlcCA9PT0gMCAmJiBuZXdTdGVwRGF0YS5jb3VudHJ5KSB7XG4gICAgICAgIGNhbGN1bGF0ZWRTdGVwID0gMTsgLy8gTW92ZSB0byBhZ3JlZW1lbnQgaWYgY291bnRyeSBpcyBzZWxlY3RlZFxuICAgICAgfSBlbHNlIGlmIChjYWxjdWxhdGVkU3RlcCA9PT0gMSAmJiBuZXdTdGVwRGF0YS5hZ3JlZW1lbnRBY2NlcHRlZCkge1xuICAgICAgICBjYWxjdWxhdGVkU3RlcCA9IDI7IC8vIE1vdmUgdG8gcHJvZmlsZSBpZiBhZ3JlZW1lbnQgaXMgYWNjZXB0ZWRcbiAgICAgIH0gZWxzZSBpZiAoY2FsY3VsYXRlZFN0ZXAgPT09IDIgJiYgbmV3U3RlcERhdGEucHJvZmlsZUNvbXBsZXRlZCkge1xuICAgICAgICBjYWxjdWxhdGVkU3RlcCA9IDM7IC8vIE1vdmUgdG8gd2FsbGV0IGlmIHByb2ZpbGUgaXMgY29tcGxldGVkXG4gICAgICB9IGVsc2UgaWYgKGNhbGN1bGF0ZWRTdGVwID09PSAzICYmIG5ld1N0ZXBEYXRhLndhbGxldENvbm5lY3RlZCkge1xuICAgICAgICBjYWxjdWxhdGVkU3RlcCA9IDQ7IC8vIE1vdmUgdG8gS1lDIGlmIHdhbGxldCBpcyBjb25uZWN0ZWRcbiAgICAgIH0gZWxzZSBpZiAoY2FsY3VsYXRlZFN0ZXAgPT09IDQgJiYgbmV3U3RlcERhdGEua3ljQ29tcGxldGVkKSB7XG4gICAgICAgIGNhbGN1bGF0ZWRTdGVwID0gNTsgLy8gQWxsIGNvbXBsZXRlZFxuICAgICAgfVxuXG4gICAgICBzZXRDdXJyZW50U3RlcChjYWxjdWxhdGVkU3RlcCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFJlc3RvcmVkIHF1YWxpZmljYXRpb24gc3RhdGU6Jywge1xuICAgICAgICBzdGVwRGF0YTogbmV3U3RlcERhdGEsXG4gICAgICAgIGN1cnJlbnRTdGVwOiBjYWxjdWxhdGVkU3RlcCxcbiAgICAgICAgc2F2ZWRQcm9ncmVzczogcHJvZ3Jlc3NUb1VzZSxcbiAgICAgICAgc291cmNlOiBsb2NhbFByb2dyZXNzID8gJ2xvY2FsU3RvcmFnZScgOiAnQVBJJ1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbcXVhbGlmaWNhdGlvblByb2dyZXNzLCBwcm9maWxlLCB3YWxsZXRTdGF0dXMsIHRva2VuQWRkcmVzc10pO1xuXG4gIGNvbnN0IHN0ZXBzOiBRdWFsaWZpY2F0aW9uU3RlcFtdID0gW1xuICAgIHtcbiAgICAgIGlkOiAnY291bnRyeScsXG4gICAgICB0aXRsZTogJ0NvdW50cnkgU2VsZWN0aW9uJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnU2VsZWN0IHlvdXIgY291bnRyeSBvZiByZXNpZGVuY2UgZm9yIGNvbXBsaWFuY2UnLFxuICAgICAgc3RhdHVzOiBzdGVwRGF0YS5jb3VudHJ5ID8gJ2NvbXBsZXRlZCcgOiBjdXJyZW50U3RlcCA9PT0gMCA/ICdjdXJyZW50JyA6ICdwZW5kaW5nJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnYWdyZWVtZW50JyxcbiAgICAgIHRpdGxlOiAnVG9rZW4gQWdyZWVtZW50JyxcbiAgICAgIGRlc2NyaXB0aW9uOiBgQWNjZXB0IHRoZSAke3Rva2VuTmFtZSB8fCAndG9rZW4nfSBzcGVjaWZpYyBpbnZlc3RtZW50IGFncmVlbWVudGAsXG4gICAgICBzdGF0dXM6IHN0ZXBEYXRhLmFncmVlbWVudEFjY2VwdGVkID8gJ2NvbXBsZXRlZCcgOiBjdXJyZW50U3RlcCA9PT0gMSA/ICdjdXJyZW50JyA6ICdwZW5kaW5nJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAncHJvZmlsZScsXG4gICAgICB0aXRsZTogJ01haW4gSW5mb3JtYXRpb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdDb21wbGV0ZSB5b3VyIHBlcnNvbmFsIGFuZCBmaW5hbmNpYWwgaW5mb3JtYXRpb24nLFxuICAgICAgc3RhdHVzOiBzdGVwRGF0YS5wcm9maWxlQ29tcGxldGVkID8gJ2NvbXBsZXRlZCcgOiBjdXJyZW50U3RlcCA9PT0gMiA/ICdjdXJyZW50JyA6ICdwZW5kaW5nJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnd2FsbGV0JyxcbiAgICAgIHRpdGxlOiAnV2FsbGV0IENvbm5lY3Rpb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdDb25uZWN0IGFuZCB2ZXJpZnkgeW91ciBjcnlwdG9jdXJyZW5jeSB3YWxsZXQnLFxuICAgICAgc3RhdHVzOiBzdGVwRGF0YS53YWxsZXRDb25uZWN0ZWQgPyAnY29tcGxldGVkJyA6IGN1cnJlbnRTdGVwID09PSAzID8gJ2N1cnJlbnQnIDogJ3BlbmRpbmcnLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdreWMnLFxuICAgICAgdGl0bGU6ICdLWUMgVmVyaWZpY2F0aW9uJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29tcGxldGUgaWRlbnRpdHkgdmVyaWZpY2F0aW9uIHVzaW5nIFN1bXN1YicsXG4gICAgICBzdGF0dXM6IHN0ZXBEYXRhLmt5Y0NvbXBsZXRlZCA/ICdjb21wbGV0ZWQnIDogXG4gICAgICAgICAgICAgIGt5Y1N0YXR1cyA9PT0gJ2ZhaWxlZCcgPyAnZXJyb3InIDogXG4gICAgICAgICAgICAgIGN1cnJlbnRTdGVwID09PSA0ID8gJ2N1cnJlbnQnIDogJ3BlbmRpbmcnLFxuICAgIH0sXG4gIF07XG5cbiAgY29uc3QgZ2V0U3RlcEljb24gPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnY29tcGxldGVkJzpcbiAgICAgICAgcmV0dXJuIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWdyZWVuLTUwMFwiIC8+O1xuICAgICAgY2FzZSAnY3VycmVudCc6XG4gICAgICAgIHJldHVybiA8Q2xvY2tJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTUwMFwiIC8+O1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gPEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1yZWQtNTAwXCIgLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJoLTYgdy02IHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItZ3JheS0zMDBcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RlcENvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6IHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTIwMCc7XG4gICAgICBjYXNlICdjdXJyZW50JzogcmV0dXJuICd0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJztcbiAgICAgIGNhc2UgJ2Vycm9yJzogcmV0dXJuICd0ZXh0LXJlZC02MDAgYmctcmVkLTUwIGJvcmRlci1yZWQtMjAwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAndGV4dC1ncmF5LTYwMCBiZy1ncmF5LTUwIGJvcmRlci1ncmF5LTIwMCc7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bmN0aW9uIHRvIHNhdmUgcXVhbGlmaWNhdGlvbiBwcm9ncmVzc1xuICBjb25zdCBzYXZlUHJvZ3Jlc3MgPSBhc3luYyAodXBkYXRlZFN0ZXBEYXRhOiBhbnksIG5ld0N1cnJlbnRTdGVwOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcHJvZ3Jlc3NEYXRhID0ge1xuICAgICAgICAuLi51cGRhdGVkU3RlcERhdGEsXG4gICAgICAgIHRva2VuQWRkcmVzcyxcbiAgICAgICAgY3VycmVudFN0ZXA6IG5ld0N1cnJlbnRTdGVwLFxuICAgICAgICBjb21wbGV0ZWRTdGVwczogT2JqZWN0LnZhbHVlcyh1cGRhdGVkU3RlcERhdGEpLmZpbHRlcihCb29sZWFuKS5sZW5ndGgsXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+SviBTYXZpbmcgcHJvZ3Jlc3MgdG8gZGF0YWJhc2U6JywgcHJvZ3Jlc3NEYXRhKTtcblxuICAgICAgLy8gU2F2ZSB0byBiYWNrZW5kIGRhdGFiYXNlIHZpYSBhZG1pbiBwYW5lbCBBUElcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2xpZW50L3F1YWxpZmljYXRpb24tcHJvZ3Jlc3MnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocHJvZ3Jlc3NEYXRhKSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIHNhdmUgcHJvZ3Jlc3MnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coJ+KchSBQcm9ncmVzcyBzYXZlZCBzdWNjZXNzZnVsbHkgdG8gZGF0YWJhc2U6JywgcmVzdWx0KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIHNhdmluZyBwcm9ncmVzczonLCBlcnJvcik7XG4gICAgICAvLyBEb24ndCBibG9jayB0aGUgdXNlciBmbG93IGlmIHNhdmluZyBmYWlsc1xuICAgIH1cbiAgfTtcblxuICAvLyBTdGVwIGNvbXBsZXRpb24gaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlQ291bnRyeUNvbXBsZXRlID0gYXN5bmMgKGNvdW50cnk6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHVwZGF0ZWRTdGVwRGF0YSA9IHsgLi4uc3RlcERhdGEsIGNvdW50cnksIGNvdW50cnlDb21wbGV0ZWQ6IHRydWUgfTtcbiAgICBzZXRTdGVwRGF0YSh1cGRhdGVkU3RlcERhdGEpO1xuICAgIHNldEN1cnJlbnRTdGVwKDEpO1xuXG4gICAgLy8gU2F2ZSBwcm9ncmVzc1xuICAgIGF3YWl0IHNhdmVQcm9ncmVzcyh1cGRhdGVkU3RlcERhdGEsIDEpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFncmVlbWVudENvbXBsZXRlID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIEZpcnN0IHNhdmUgdGhlIHRva2VuIGFncmVlbWVudFxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudC90b2tlbi1hZ3JlZW1lbnQnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHRva2VuQWRkcmVzcyxcbiAgICAgICAgICB0b2tlblN5bWJvbCxcbiAgICAgICAgICBhY2NlcHRlZDogdHJ1ZSxcbiAgICAgICAgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGFncmVlbWVudCcpO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFRva2VuIGFncmVlbWVudCBzYXZlZCBzdWNjZXNzZnVsbHknKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIHNhdmluZyB0b2tlbiBhZ3JlZW1lbnQ6JywgZXJyb3IpO1xuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBzdGVwIGRhdGEgYW5kIHByb2dyZXNzXG4gICAgY29uc3QgdXBkYXRlZFN0ZXBEYXRhID0geyAuLi5zdGVwRGF0YSwgYWdyZWVtZW50QWNjZXB0ZWQ6IHRydWUgfTtcbiAgICBzZXRTdGVwRGF0YSh1cGRhdGVkU3RlcERhdGEpO1xuICAgIHNldEN1cnJlbnRTdGVwKDIpO1xuXG4gICAgLy8gU2F2ZSBwcm9ncmVzc1xuICAgIGF3YWl0IHNhdmVQcm9ncmVzcyh1cGRhdGVkU3RlcERhdGEsIDIpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVByb2ZpbGVDb21wbGV0ZSA9IGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB1cGRhdGVkU3RlcERhdGEgPSB7IC4uLnN0ZXBEYXRhLCBwcm9maWxlQ29tcGxldGVkOiB0cnVlIH07XG4gICAgc2V0U3RlcERhdGEodXBkYXRlZFN0ZXBEYXRhKTtcbiAgICBzZXRDdXJyZW50U3RlcCgzKTtcblxuICAgIC8vIFNhdmUgcHJvZ3Jlc3NcbiAgICBhd2FpdCBzYXZlUHJvZ3Jlc3ModXBkYXRlZFN0ZXBEYXRhLCAzKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVXYWxsZXRDb21wbGV0ZSA9IGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB1cGRhdGVkU3RlcERhdGEgPSB7IC4uLnN0ZXBEYXRhLCB3YWxsZXRDb25uZWN0ZWQ6IHRydWUgfTtcbiAgICBzZXRTdGVwRGF0YSh1cGRhdGVkU3RlcERhdGEpO1xuICAgIHNldEN1cnJlbnRTdGVwKDQpO1xuXG4gICAgLy8gU2F2ZSBwcm9ncmVzc1xuICAgIGF3YWl0IHNhdmVQcm9ncmVzcyh1cGRhdGVkU3RlcERhdGEsIDQpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtZQ1N0YXR1c0NoYW5nZSA9IGFzeW5jIChzdGF0dXM6IHN0cmluZywgZXJyb3I/OiBzdHJpbmcpID0+IHtcbiAgICBzZXRLeWNTdGF0dXMoc3RhdHVzKTtcbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHNldEt5Y0Vycm9yKGVycm9yKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0S3ljRXJyb3IobnVsbCk7XG4gICAgfVxuXG4gICAgaWYgKHN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpIHtcbiAgICAgIGNvbnN0IHVwZGF0ZWRTdGVwRGF0YSA9IHsgLi4uc3RlcERhdGEsIGt5Y0NvbXBsZXRlZDogdHJ1ZSB9O1xuICAgICAgc2V0U3RlcERhdGEodXBkYXRlZFN0ZXBEYXRhKTtcbiAgICAgIHNldEN1cnJlbnRTdGVwKDUpO1xuXG4gICAgICAvLyBTYXZlIHByb2dyZXNzXG4gICAgICBhd2FpdCBzYXZlUHJvZ3Jlc3ModXBkYXRlZFN0ZXBEYXRhLCA1KTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU3RlcCBuYXZpZ2F0aW9uIGZ1bmN0aW9uc1xuICBjb25zdCBjYW5OYXZpZ2F0ZVRvU3RlcCA9IChzdGVwSW5kZXg6IG51bWJlcikgPT4ge1xuICAgIC8vIFVzZXJzIGNhbiBhbHdheXMgbmF2aWdhdGUgdG8gY29tcGxldGVkIHN0ZXBzIG9yIHRoZSBuZXh0IGluY29tcGxldGUgc3RlcFxuICAgIGlmIChzdGVwSW5kZXggPT09IDApIHJldHVybiB0cnVlOyAvLyBDb3VudHJ5IHNlbGVjdGlvbiBhbHdheXMgYXZhaWxhYmxlXG4gICAgaWYgKHN0ZXBJbmRleCA9PT0gMSkgcmV0dXJuIHN0ZXBEYXRhLmNvdW50cnkgIT09ICcnOyAvLyBBZ3JlZW1lbnQgaWYgY291bnRyeSBzZWxlY3RlZFxuICAgIGlmIChzdGVwSW5kZXggPT09IDIpIHJldHVybiBzdGVwRGF0YS5hZ3JlZW1lbnRBY2NlcHRlZDsgLy8gUHJvZmlsZSBpZiBhZ3JlZW1lbnQgYWNjZXB0ZWRcbiAgICBpZiAoc3RlcEluZGV4ID09PSAzKSByZXR1cm4gc3RlcERhdGEucHJvZmlsZUNvbXBsZXRlZDsgLy8gV2FsbGV0IGlmIHByb2ZpbGUgY29tcGxldGVkXG4gICAgaWYgKHN0ZXBJbmRleCA9PT0gNCkgcmV0dXJuIHN0ZXBEYXRhLndhbGxldENvbm5lY3RlZDsgLy8gS1lDIGlmIHdhbGxldCBjb25uZWN0ZWRcbiAgICBpZiAoc3RlcEluZGV4ID09PSA1KSByZXR1cm4gc3RlcERhdGEua3ljQ29tcGxldGVkOyAvLyBDb21wbGV0aW9uIGlmIEtZQyBkb25lXG4gICAgcmV0dXJuIGZhbHNlO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVN0ZXBDbGljayA9IChzdGVwSW5kZXg6IG51bWJlcikgPT4ge1xuICAgIGlmIChjYW5OYXZpZ2F0ZVRvU3RlcChzdGVwSW5kZXgpKSB7XG4gICAgICBzZXRDdXJyZW50U3RlcChzdGVwSW5kZXgpO1xuICAgICAgLy8gU2F2ZSB0aGUgY3VycmVudCBzdGVwIG5hdmlnYXRpb25cbiAgICAgIHNhdmVQcm9ncmVzcyhzdGVwRGF0YSwgc3RlcEluZGV4KTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IGNvbXBsZXRlZFN0ZXBzID0gc3RlcHMuZmlsdGVyKHN0ZXAgPT4gc3RlcC5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKS5sZW5ndGg7XG4gIGNvbnN0IHRvdGFsU3RlcHMgPSBzdGVwcy5sZW5ndGg7XG4gIGNvbnN0IHByb2dyZXNzUGVyY2VudGFnZSA9IChjb21wbGV0ZWRTdGVwcyAvIHRvdGFsU3RlcHMpICogMTAwO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBzcGFjZS15LThcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAge3Rva2VuTmFtZSA/IGAke3Rva2VuTmFtZX0gUXVhbGlmaWNhdGlvbmAgOiAnVG9rZW4gUXVhbGlmaWNhdGlvbid9XG4gICAgICAgIDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgQ29tcGxldGUgdGhlIGZvbGxvd2luZyBzdGVwcyB0byBxdWFsaWZ5IGZvciB7dG9rZW5OYW1lIHx8ICd0b2tlbid9IGludmVzdG1lbnRcbiAgICAgICAgPC9wPlxuXG4gICAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTMgbWItOFwiPlxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGgtMyByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtwcm9ncmVzc1BlcmNlbnRhZ2V9JWAgfX1cbiAgICAgICAgICA+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgIHtjb21wbGV0ZWRTdGVwc30gb2Yge3RvdGFsU3RlcHN9IHN0ZXBzIGNvbXBsZXRlZFxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN0ZXBzIE92ZXJ2aWV3ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy01IGdhcC00IG1iLThcIj5cbiAgICAgICAge3N0ZXBzLm1hcCgoc3RlcCwgaW5kZXgpID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e3N0ZXAuaWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BwLTQgcm91bmRlZC1sZyBib3JkZXIgdGV4dC1jZW50ZXIgJHtnZXRTdGVwQ29sb3Ioc3RlcC5zdGF0dXMpfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG1iLTJcIj5cbiAgICAgICAgICAgICAge2dldFN0ZXBJY29uKHN0ZXAuc3RhdHVzKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCBtYi0xXCI+e3N0ZXAudGl0bGV9PC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHNcIj57c3RlcC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDdXJyZW50IFN0ZXAgQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAge2N1cnJlbnRTdGVwID09PSAwICYmIChcbiAgICAgICAgICA8Q291bnRyeVNlbGVjdGlvblxuICAgICAgICAgICAgb25Db21wbGV0ZT17aGFuZGxlQ291bnRyeUNvbXBsZXRlfVxuICAgICAgICAgICAgc2VsZWN0ZWRDb3VudHJ5PXtzdGVwRGF0YS5jb3VudHJ5fVxuICAgICAgICAgICAgaXNDb21wbGV0ZWQ9e3N0ZXBEYXRhLmNvdW50cnkgIT09ICcnfVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2N1cnJlbnRTdGVwID09PSAxICYmIChcbiAgICAgICAgICA8VG9rZW5BZ3JlZW1lbnRcbiAgICAgICAgICAgIG9uQ29tcGxldGU9e2hhbmRsZUFncmVlbWVudENvbXBsZXRlfVxuICAgICAgICAgICAgdG9rZW5OYW1lPXt0b2tlbk5hbWV9XG4gICAgICAgICAgICB0b2tlblN5bWJvbD17dG9rZW5TeW1ib2x9XG4gICAgICAgICAgICBpc0NvbXBsZXRlZD17c3RlcERhdGEuYWdyZWVtZW50QWNjZXB0ZWR9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDIgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5NYWluIEluZm9ybWF0aW9uPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIFBsZWFzZSBwcm92aWRlIHlvdXIgY29tcGxldGUgcGVyc29uYWwgYW5kIGZpbmFuY2lhbCBpbmZvcm1hdGlvbi5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8UXVhbGlmaWNhdGlvbkZvcm1cbiAgICAgICAgICAgICAgb25Db21wbGV0ZT17aGFuZGxlUHJvZmlsZUNvbXBsZXRlfVxuICAgICAgICAgICAgICBleGlzdGluZ1Byb2ZpbGU9e3Byb2ZpbGV9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPldhbGxldCBDb25uZWN0aW9uPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIENvbm5lY3QgeW91ciBjcnlwdG9jdXJyZW5jeSB3YWxsZXQgdXNpbmcgUmVvd24gKFdhbGxldENvbm5lY3QpLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxXYWxsZXRDb25uZWN0aW9uIG9uV2FsbGV0Q29ubmVjdGVkPXtoYW5kbGVXYWxsZXRDb21wbGV0ZX0gLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5LWUMgVmVyaWZpY2F0aW9uPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIENvbXBsZXRlIHlvdXIgaWRlbnRpdHkgdmVyaWZpY2F0aW9uIHVzaW5nIFN1bXN1Yi5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8QXV0b21hdGljS1lDXG4gICAgICAgICAgICAgIG9uU3RhdHVzQ2hhbmdlPXtoYW5kbGVLWUNTdGF0dXNDaGFuZ2V9XG4gICAgICAgICAgICAgIGV4aXN0aW5nUHJvZmlsZT17cHJvZmlsZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2N1cnJlbnRTdGVwID09PSA1ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyZWVuLTUwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgUXVhbGlmaWNhdGlvbiBDb21wbGV0ZSFcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgWW91IGhhdmUgc3VjY2Vzc2Z1bGx5IGNvbXBsZXRlZCBhbGwgcXVhbGlmaWNhdGlvbiBzdGVwcyBmb3Ige3Rva2VuTmFtZSB8fCAndGhpcyB0b2tlbid9LlxuICAgICAgICAgICAgICBZb3UgY2FuIG5vdyBwcm9jZWVkIHdpdGggeW91ciBpbnZlc3RtZW50LlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvJ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFJldHVybiB0byBEYXNoYm9hcmRcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VRdWVyeSIsIkNoZWNrQ2lyY2xlSWNvbiIsIkNsb2NrSWNvbiIsIkV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIiwiQ291bnRyeVNlbGVjdGlvbiIsIlRva2VuQWdyZWVtZW50IiwiUXVhbGlmaWNhdGlvbkZvcm0iLCJXYWxsZXRDb25uZWN0aW9uIiwiQXV0b21hdGljS1lDIiwiUXVhbGlmaWNhdGlvbkZsb3ciLCJ0b2tlbkFkZHJlc3MiLCJ0b2tlbk5hbWUiLCJ0b2tlblN5bWJvbCIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJzdGVwRGF0YSIsInNldFN0ZXBEYXRhIiwiY291bnRyeSIsImFncmVlbWVudEFjY2VwdGVkIiwicHJvZmlsZUNvbXBsZXRlZCIsIndhbGxldENvbm5lY3RlZCIsImt5Y0NvbXBsZXRlZCIsImt5Y1N0YXR1cyIsInNldEt5Y1N0YXR1cyIsImt5Y0Vycm9yIiwic2V0S3ljRXJyb3IiLCJkYXRhIiwicXVhbGlmaWNhdGlvblByb2dyZXNzIiwiaXNMb2FkaW5nIiwicXVlcnlLZXkiLCJxdWVyeUZuIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiYXBwZW5kIiwicmVzcG9uc2UiLCJmZXRjaCIsInRvU3RyaW5nIiwib2siLCJqc29uIiwicHJvZmlsZSIsIndhbGxldFN0YXR1cyIsInN0b3JhZ2VLZXkiLCJsb2NhbFByb2dyZXNzIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsInByb2dyZXNzVG9Vc2UiLCJuZXdTdGVwRGF0YSIsInZlcmlmaWVkIiwiY2FsY3VsYXRlZFN0ZXAiLCJzYXZlZFByb2dyZXNzIiwic291cmNlIiwic3RlcHMiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJzdGF0dXMiLCJnZXRTdGVwSWNvbiIsImNsYXNzTmFtZSIsImRpdiIsImdldFN0ZXBDb2xvciIsInNhdmVQcm9ncmVzcyIsInVwZGF0ZWRTdGVwRGF0YSIsIm5ld0N1cnJlbnRTdGVwIiwicHJvZ3Jlc3NEYXRhIiwiY29tcGxldGVkU3RlcHMiLCJPYmplY3QiLCJ2YWx1ZXMiLCJmaWx0ZXIiLCJCb29sZWFuIiwibGVuZ3RoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJzdHJpbmdpZnkiLCJFcnJvciIsInJlc3VsdCIsImhhbmRsZUNvdW50cnlDb21wbGV0ZSIsImNvdW50cnlDb21wbGV0ZWQiLCJoYW5kbGVBZ3JlZW1lbnRDb21wbGV0ZSIsImFjY2VwdGVkIiwiaGFuZGxlUHJvZmlsZUNvbXBsZXRlIiwiaGFuZGxlV2FsbGV0Q29tcGxldGUiLCJoYW5kbGVLWUNTdGF0dXNDaGFuZ2UiLCJjYW5OYXZpZ2F0ZVRvU3RlcCIsInN0ZXBJbmRleCIsImhhbmRsZVN0ZXBDbGljayIsInN0ZXAiLCJ0b3RhbFN0ZXBzIiwicHJvZ3Jlc3NQZXJjZW50YWdlIiwiaDEiLCJwIiwic3R5bGUiLCJ3aWR0aCIsIm1hcCIsImluZGV4IiwiaDMiLCJvbkNvbXBsZXRlIiwic2VsZWN0ZWRDb3VudHJ5IiwiaXNDb21wbGV0ZWQiLCJoMiIsImV4aXN0aW5nUHJvZmlsZSIsIm9uV2FsbGV0Q29ubmVjdGVkIiwib25TdGF0dXNDaGFuZ2UiLCJidXR0b24iLCJvbkNsaWNrIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});