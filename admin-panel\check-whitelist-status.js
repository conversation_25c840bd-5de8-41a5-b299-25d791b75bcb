const { ethers } = require("ethers");

async function checkWhitelistStatus() {
  console.log("🔍 Checking Whitelist Status");
  console.log("============================");

  // Configuration
  const WHITELIST_ADDRESS = "******************************************";
  const CLIENT_ADDRESS = "******************************************"; // The address you're trying to whitelist
  const ADMIN_ADDRESS = "******************************************"; // Your admin address
  const RPC_URL = process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology/";

  try {
    // Connect to the network
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    console.log(`📡 Connected to: ${RPC_URL}`);

    // Load the Whitelist ABI
    const WhitelistABI = require("./src/contracts/Whitelist.json");
    
    // Connect to the whitelist contract
    const whitelistContract = new ethers.Contract(
      WHITELIST_ADDRESS,
      WhitelistABI.abi,
      provider
    );

    console.log(`📋 Whitelist Contract: ${WHITELIST_ADDRESS}`);
    console.log(`👤 Client Address: ${CLIENT_ADDRESS}`);
    console.log(`🔑 Admin Address: ${ADMIN_ADDRESS}`);
    console.log("");

    // Check whitelist status
    console.log("📊 Current Status:");
    console.log("==================");

    // 1. Check if client is whitelisted
    const isClientWhitelisted = await whitelistContract.isWhitelisted(CLIENT_ADDRESS);
    console.log(`Client whitelisted: ${isClientWhitelisted ? "✅ YES" : "❌ NO"}`);

    // 2. Check if admin is whitelisted
    const isAdminWhitelisted = await whitelistContract.isWhitelisted(ADMIN_ADDRESS);
    console.log(`Admin whitelisted: ${isAdminWhitelisted ? "✅ YES" : "❌ NO"}`);

    // 3. Check admin roles
    try {
      const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, ADMIN_ADDRESS);
      console.log(`Admin has AGENT_ROLE: ${hasAgentRole ? "✅ YES" : "❌ NO"}`);

      const DEFAULT_ADMIN_ROLE = await whitelistContract.DEFAULT_ADMIN_ROLE();
      const hasDefaultAdminRole = await whitelistContract.hasRole(DEFAULT_ADMIN_ROLE, ADMIN_ADDRESS);
      console.log(`Admin has DEFAULT_ADMIN_ROLE: ${hasDefaultAdminRole ? "✅ YES" : "❌ NO"}`);
    } catch (error) {
      console.log(`❌ Error checking roles: ${error.message}`);
    }

    // 4. Check if contract is paused
    try {
      const isPaused = await whitelistContract.paused();
      console.log(`Contract paused: ${isPaused ? "⚠️ YES" : "✅ NO"}`);
    } catch (error) {
      console.log("ℹ️  Contract doesn't have pause functionality");
    }

    console.log("");
    console.log("💡 Next Steps:");
    console.log("==============");

    if (isClientWhitelisted) {
      console.log("✅ Client is already whitelisted - no action needed!");
    } else {
      console.log("❌ Client is not whitelisted");
      
      // Check if we can whitelist
      try {
        const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
        const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, ADMIN_ADDRESS);
        
        if (hasAgentRole) {
          console.log("✅ Admin has permission to whitelist");
          console.log("🔧 You can proceed with whitelisting");
        } else {
          console.log("❌ Admin doesn't have AGENT_ROLE");
          console.log("🔧 Need to grant AGENT_ROLE to admin first");
        }
      } catch (error) {
        console.log("❌ Could not verify permissions");
      }
    }

  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

// Run the check
checkWhitelistStatus().catch(console.error);
