const { PrismaClient } = require('@prisma/client');

async function createTestOrder() {
  console.log('🧪 Creating Test Order for Minting');
  console.log('===================================');

  const prisma = new PrismaClient();

  try {
    // 1. Check if we have any tokens
    console.log('1️⃣ Checking for tokens...');
    
    const tokens = await prisma.token.findMany({
      take: 1
    });

    if (tokens.length === 0) {
      console.log('❌ No tokens found');
      console.log('💡 Create a token first at http://localhost:6677/create-token');
      return;
    }

    const token = tokens[0];
    console.log(`✅ Found token: ${token.name} (${token.symbol})`);
    console.log(`   Address: ${token.address}`);

    // 2. Check if we have any clients
    console.log('\n2️⃣ Checking for clients...');
    
    const clients = await prisma.client.findMany({
      take: 1
    });

    if (clients.length === 0) {
      console.log('❌ No clients found');
      console.log('💡 Create a client first at http://localhost:6677/clients');
      return;
    }

    const client = clients[0];
    console.log(`✅ Found client: ${client.firstName} ${client.lastName}`);
    console.log(`   Email: ${client.email}`);
    console.log(`   Wallet: ${client.walletAddress || 'NOT SET'}`);

    // 3. Update client with wallet address if missing
    if (!client.walletAddress) {
      console.log('\n3️⃣ Adding wallet address to client...');
      
      const updatedClient = await prisma.client.update({
        where: { id: client.id },
        data: {
          walletAddress: '******************************************' // Your test wallet
        }
      });
      
      console.log(`✅ Added wallet address: ${updatedClient.walletAddress}`);
    }

    // 4. Create a test order
    console.log('\n4️⃣ Creating test order...');
    
    const tokensOrdered = '100';
    const tokenPrice = token.tokenPrice || '10';
    const amountToPay = (parseFloat(tokensOrdered) * parseFloat(tokenPrice.replace(/[^\d.-]/g, ''))).toString();
    
    // Generate payment reference
    const paymentReference = 'TEST-' + Math.random().toString(36).substr(2, 9).toUpperCase();

    const order = await prisma.order.create({
      data: {
        tokenId: token.id,
        clientId: client.id,
        tokensOrdered,
        tokenPrice,
        amountToPay,
        paymentReference,
        status: 'CONFIRMED' // Create it as CONFIRMED so we can mint immediately
      },
      include: {
        token: {
          select: {
            name: true,
            symbol: true,
            address: true
          }
        },
        client: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        }
      }
    });

    console.log('✅ Created test order:');
    console.log(`   Order ID: ${order.id}`);
    console.log(`   Status: ${order.status}`);
    console.log(`   Tokens: ${order.tokensOrdered} ${order.token.symbol}`);
    console.log(`   Amount: $${order.amountToPay}`);
    console.log(`   Reference: ${order.paymentReference}`);
    console.log(`   Client: ${order.client.firstName} ${order.client.lastName}`);
    console.log(`   Wallet: ${order.client.walletAddress}`);

    console.log('\n🚀 Ready to test minting!');
    console.log('1. Go to http://localhost:6677/orders');
    console.log('2. Find the CONFIRMED order');
    console.log('3. Click the green MINT button');
    console.log('4. Check for transaction hash in popup');
    console.log('5. Verify Tx button appears after minting');

  } catch (error) {
    console.error('❌ Failed to create test order:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createTestOrder().catch(console.error);
