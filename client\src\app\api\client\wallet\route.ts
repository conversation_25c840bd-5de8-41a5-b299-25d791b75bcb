import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { verifyMessage } from 'viem';

// POST /api/client/wallet - Save wallet address and signature
export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { address, signature, message } = await request.json();

    if (!address || !signature || !message) {
      return NextResponse.json({ error: 'Address, signature, and message are required' }, { status: 400 });
    }

    // Verify the signature using the exact message that was signed
    console.log('Verifying signature:', { address, message: message.substring(0, 100) + '...', signature: signature.substring(0, 20) + '...' });

    try {
      const isValid = await verifyMessage({
        address: address as `0x${string}`,
        message,
        signature: signature as `0x${string}`,
      });

      console.log('Signature verification result:', isValid);

      if (!isValid) {
        console.error('Signature verification failed - invalid signature');
        return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
      }
    } catch (error) {
      console.error('Signature verification error:', error);
      return NextResponse.json({ error: 'Signature verification failed' }, { status: 400 });
    }

    const userEmail = session.user.email;
    console.log('Updating client profile for email:', userEmail);

    // Update client profile with wallet information
    const response = await fetch(`${process.env.ADMIN_API_BASE_URL}/clients`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: userEmail,
        walletAddress: address,
        walletSignature: signature,
        walletVerifiedAt: new Date().toISOString(),
      }),
    });

    console.log('Admin API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Admin API error:', errorData);
      throw new Error(errorData.error || `Admin API error: ${response.status}`);
    }

    const updatedClient = await response.json();

    return NextResponse.json({
      address,
      signature,
      verified: true,
      verifiedAt: updatedClient.walletVerifiedAt,
    });
  } catch (error) {
    console.error('Error saving wallet:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to save wallet' },
      { status: 500 }
    );
  }
}

// GET /api/client/wallet - Get current user's wallet status
export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userEmail = session.user.email;

    // Get client profile from admin panel
    let response;
    try {
      response = await fetch(
        `${process.env.ADMIN_API_BASE_URL}/clients?search=${encodeURIComponent(userEmail)}&limit=1`
      );

      if (!response.ok) {
        throw new Error(`Admin API error: ${response.status}`);
      }
    } catch (error) {
      // If admin panel is not running, return 404 (no wallet found)
      console.log('Admin panel not available, returning no wallet');
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    const data = await response.json();
    const client = data.clients?.[0];

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    return NextResponse.json({
      address: client.walletAddress,
      verified: !!client.walletVerifiedAt,
      verifiedAt: client.walletVerifiedAt,
    });
  } catch (error) {
    console.error('Error fetching wallet status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wallet status' },
      { status: 500 }
    );
  }
}
