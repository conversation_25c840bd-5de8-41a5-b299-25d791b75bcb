// Update existing client with your wallet address
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateClientWallet() {
  console.log('=== Updating Client with Your Wallet ===');
  
  const yourWallet = '******************************************';
  
  try {
    // First, let's see what clients exist
    const existingClients = await prisma.client.findMany({
      select: { id: true, email: true, walletAddress: true }
    });
    
    console.log('Existing clients:');
    existingClients.forEach(client => {
      console.log(`  - ID: ${client.id}, Email: ${client.email}, Wallet: ${client.walletAddress || 'None'}`);
    });
    
    // Update the first client (or create a new one with unique email)
    let client;
    if (existingClients.length > 0) {
      // Update existing client
      client = await prisma.client.update({
        where: { id: existingClients[0].id },
        data: {
          walletAddress: yourWallet,
          walletSignature: '0xabcdef1234567890...',
          walletVerifiedAt: new Date(),
          isWhitelisted: true,
          whitelistedAt: new Date(),
          kycStatus: 'APPROVED',
          kycCompletedAt: new Date()
        }
      });
      
      console.log(`✅ Updated existing client (ID: ${client.id}) with your wallet`);
    } else {
      // Create new client
      client = await prisma.client.create({
        data: {
          firstName: 'Your',
          lastName: 'Name',
          gender: 'MALE',
          nationality: 'US',
          birthday: new Date('1990-01-01'),
          birthPlace: 'New York',
          identificationType: 'PASSPORT',
          passportNumber: 'US987654321',
          documentExpiration: new Date('2030-01-01'),
          phoneNumber: '+**********',
          email: '<EMAIL>',
          occupation: 'Developer',
          sectorOfActivity: 'Technology',
          pepStatus: 'NOT_PEP',
          street: '456 Test St',
          buildingNumber: '456',
          city: 'Test City',
          state: 'CA',
          country: 'United States',
          zipCode: '90210',
          sourceOfWealth: 'Employment',
          bankAccountNumber: 'US987654321098',
          sourceOfFunds: 'Salary',
          taxIdentificationNumber: 'US987654321',
          kycStatus: 'APPROVED',
          kycCompletedAt: new Date(),
          walletAddress: yourWallet,
          walletSignature: '0xabcdef1234567890...',
          walletVerifiedAt: new Date(),
          isWhitelisted: true,
          whitelistedAt: new Date(),
          agreementAccepted: true,
          agreementAcceptedAt: new Date()
        }
      });
      
      console.log(`✅ Created new client with your wallet`);
    }

    console.log(`   Email: ${client.email}`);
    console.log(`   Wallet: ${client.walletAddress}`);
    console.log(`   KYC Status: ${client.kycStatus}`);
    console.log(`   Global Whitelisted: ${client.isWhitelisted}`);

    return client;
  } catch (error) {
    console.error('Error updating client:', error);
    throw error;
  }
}

async function addTokenApprovals(clientId) {
  console.log('\n=== Adding Token Approvals ===');
  
  try {
    // Delete existing approvals for this client
    await prisma.tokenClientApproval.deleteMany({
      where: { clientId: clientId }
    });
    
    // Get all tokens from the database
    const tokens = await prisma.token.findMany({
      select: { id: true, name: true, symbol: true, address: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens found in database');
      return;
    }

    console.log(`Found ${tokens.length} tokens, creating approvals...`);

    // Whitelist the main tokens you mentioned you're already whitelisted for
    const tokensToWhitelist = ['AUG019', 'AUG01Z', 'TZD', 'EURT', 'ETHF'];

    for (const token of tokens) {
      const shouldWhitelist = tokensToWhitelist.includes(token.symbol);

      const approval = await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: clientId,
          approvalStatus: shouldWhitelist ? 'APPROVED' : 'PENDING',
          kycApproved: true,
          whitelistApproved: shouldWhitelist,
          approvedBy: shouldWhitelist ? '<EMAIL>' : null,
          approvedAt: shouldWhitelist ? new Date() : null,
          notes: shouldWhitelist ? 'Whitelisted for your wallet' : 'Pending approval'
        }
      });

      const status = shouldWhitelist ? '✅ WHITELISTED' : '⏳ PENDING';
      console.log(`   ${token.symbol.padEnd(10)} | ${status}`);
    }

    const whitelistedCount = tokensToWhitelist.length;
    console.log(`\n✅ Token approvals created: ${whitelistedCount} whitelisted, ${tokens.length - whitelistedCount} pending`);
    
  } catch (error) {
    console.error('Error creating token approvals:', error);
    throw error;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Whitelist API Response:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${symbol.padEnd(10)} | ${status}`);
      });
      
      const whitelistedCount = data.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nSummary: ${whitelistedCount}/${data.tokens.length} tokens whitelisted for your wallet`);
      
    } else {
      console.log('❌ Whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing whitelist API:', error);
  }
}

async function main() {
  const yourWallet = '******************************************';
  
  try {
    // Update client with your wallet
    const client = await updateClientWallet();
    
    // Add token approvals
    await addTokenApprovals(client.id);
    
    // Test the whitelist API
    await testWhitelistAPI(yourWallet);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\n🎯 READY TO TEST WHITELIST TAGS:');
    console.log('1. Open client app: http://localhost:3003/offers');
    console.log(`2. Login with email: ${client.email}`);
    console.log('3. Connect wallet: ******************************************');
    console.log('4. You should see green WHITELISTED tags on approved tokens');
    
    console.log('\n📋 WHITELISTED TOKENS:');
    console.log('   - AUG019 (Augment_019)');
    console.log('   - AUG01Z (Augment_01z)');
    console.log('   - TZD (Test Zero Decimals Token)');
    console.log('   - EURT (European Real Estate Token)');
    console.log('   - ETHF (Ethereum DeFi Fund)');
    
  } catch (error) {
    console.error('Error in main:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
