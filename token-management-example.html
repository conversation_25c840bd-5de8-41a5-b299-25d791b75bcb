<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token Management Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dashboard-title {
            margin: 0;
            font-size: 1.8rem;
        }
        
        .token-info {
            display: flex;
            align-items: center;
        }
        
        .token-symbol {
            font-weight: bold;
            margin-right: 15px;
        }
        
        .token-address {
            font-size: 0.8rem;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .dashboard-section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .section-header {
            background-color: #f1f2f6;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .section-header h2 {
            margin: 0;
            font-size: 1.3rem;
            color: #2c3e50;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .token-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .stat-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
        }
        
        .stat-title {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .connect-wallet {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .connect-wallet:hover {
            background-color: #2980b9;
        }
        
        @media (max-width: 768px) {
            header .container {
                flex-direction: column;
                text-align: center;
            }
            
            .token-info {
                margin-top: 15px;
            }
            
            .token-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1 class="dashboard-title">Token Management Dashboard</h1>
            <div class="token-info">
                <span class="token-symbol">ERC3643</span>
                <span class="token-address" id="tokenAddress">******************************************</span>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="dashboard-section">
            <div class="section-header">
                <h2>Token Overview</h2>
            </div>
            <div class="section-content">
                <div class="token-stats">
                    <div class="stat-card">
                        <div class="stat-title">Total Supply</div>
                        <div class="stat-value" id="totalSupply">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">Max Supply</div>
                        <div class="stat-value" id="maxSupply">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">Whitelisted Addresses</div>
                        <div class="stat-value" id="whitelistedCount">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">Paused Status</div>
                        <div class="stat-value" id="pausedStatus">-</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- This is where the agent management block will be loaded -->
        <div id="agentManagementContainer"></div>
        
        <div class="dashboard-section">
            <div class="section-header">
                <h2>Whitelist Management</h2>
            </div>
            <div class="section-content">
                <p>Whitelist management interface would be here.</p>
            </div>
        </div>
        
        <div class="dashboard-section">
            <div class="section-header">
                <h2>Token Operations</h2>
            </div>
            <div class="section-content">
                <p>Token operations like minting, pausing, etc. would be here.</p>
            </div>
        </div>
    </div>
    
    <!-- Web3.js library -->
    <script src="https://cdn.jsdelivr.net/npm/web3@1.5.2/dist/web3.min.js"></script>
    
    <!-- Agent Management Integration Script -->
    <script src="agent-management-integration.js"></script>
    
    <script>
        // Example token management dashboard script
        document.addEventListener('DOMContentLoaded', async function() {
            // Simulate token address
            const tokenAddress = '******************************************';
            document.getElementById('tokenAddress').textContent = tokenAddress;
            
            // Store the current user's address
            window.userAddress = '******************************************'; // This would come from connected wallet
            
            // Simulate token stats
            document.getElementById('totalSupply').textContent = '1,000,000';
            document.getElementById('maxSupply').textContent = '10,000,000';
            document.getElementById('whitelistedCount').textContent = '53';
            document.getElementById('pausedStatus').textContent = 'Active';
            
            // Initialize Web3 (for demo purposes)
            if (window.ethereum) {
                window.web3 = new Web3(window.ethereum);
                try {
                    // Request account access if needed
                    await window.ethereum.request({ method: 'eth_requestAccounts' });
                    
                    // Get the user's address
                    const accounts = await web3.eth.getAccounts();
                    window.userAddress = accounts[0];
                    
                    console.log('Connected wallet address:', window.userAddress);
                } catch (error) {
                    console.error('User denied account access:', error);
                }
            } else if (window.web3) {
                window.web3 = new Web3(web3.currentProvider);
            } else {
                console.log('Non-Ethereum browser detected. Consider trying MetaMask!');
            }
            
            // Load the agent management component
            loadAgentManagement('#agentManagementContainer', tokenAddress);
        });
    </script>
</body>
</html> 