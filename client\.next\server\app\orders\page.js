/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/orders/page";
exports.ids = ["app/orders/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(rsc)/./src/app/orders/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'orders',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/orders/page\",\n        pathname: \"/orders\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZvcmRlcnMlMkZwYWdlJnBhZ2U9JTJGb3JkZXJzJTJGcGFnZSZhcHBQYXRocz0lMkZvcmRlcnMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGb3JkZXJzJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNnaXRodWIlNUN0b2tlbmRldi1uZXdyb28lNUNjbGllbnQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNnaXRodWIlNUN0b2tlbmRldi1uZXdyb28lNUNjbGllbnQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBOEY7QUFDcEgsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLDhKQUFvRztBQUd0SDtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBc1A7QUFDMVI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUFzUDtBQUMxUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxnaXRodWJcXFxcdG9rZW5kZXYtbmV3cm9vXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG9yZGVyc1xcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnb3JkZXJzJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcb3JkZXJzXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJEOlxcXFxnaXRodWJcXFxcdG9rZW5kZXYtbmV3cm9vXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxnaXRodWJcXFxcdG9rZW5kZXYtbmV3cm9vXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxcZ2l0aHViXFxcXHRva2VuZGV2LW5ld3Jvb1xcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxvcmRlcnNcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL29yZGVycy9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9vcmRlcnNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/index.js */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AppLayout.tsx */ \"(rsc)/./src/components/AppLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(rsc)/./src/components/providers/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/MockAuthProvider.tsx */ \"(rsc)/./src/components/providers/MockAuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ReactQueryProvider.tsx */ \"(rsc)/./src/components/providers/ReactQueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WalletProvider.tsx */ \"(rsc)/./src/components/WalletProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(rsc)/./src/app/orders/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcmRlcnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW9HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxnaXRodWJcXFxcdG9rZW5kZXYtbmV3cm9vXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG9yZGVyc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_providers_ReactQueryProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/ReactQueryProvider */ \"(rsc)/./src/components/providers/ReactQueryProvider.tsx\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(rsc)/./src/components/providers/MockAuthProvider.tsx\");\n/* harmony import */ var _components_WalletProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/WalletProvider */ \"(rsc)/./src/components/WalletProvider.tsx\");\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AppLayout */ \"(rsc)/./src/components/AppLayout.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"TokenDev Client Portal\",\n    description: \"Secure token management and KYC compliance platform\",\n    keywords: [\n        \"tokens\",\n        \"blockchain\",\n        \"KYC\",\n        \"compliance\",\n        \"investment\"\n    ]\n};\nfunction RootLayout({ children }) {\n    const useMockAuth = \"false\" === 'true';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className)} antialiased`,\n            children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.MockAuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ReactQueryProvider__WEBPACK_IMPORTED_MODULE_3__.ReactQueryProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletProvider__WEBPACK_IMPORTED_MODULE_6__.WalletProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_7__.AppLayout, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ReactQueryProvider__WEBPACK_IMPORTED_MODULE_3__.ReactQueryProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletProvider__WEBPACK_IMPORTED_MODULE_6__.WalletProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_7__.AppLayout, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/orders/page.tsx":
/*!*********************************!*\
  !*** ./src/app/orders/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\app\\orders\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/AppLayout.tsx":
/*!**************************************!*\
  !*** ./src/components/AppLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppLayout: () => (/* binding */ AppLayout)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AppLayout = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\AppLayout.tsx",
"AppLayout",
);

/***/ }),

/***/ "(rsc)/./src/components/WalletProvider.tsx":
/*!*******************************************!*\
  !*** ./src/components/WalletProvider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WalletProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WalletProvider() from the server but WalletProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\WalletProvider.tsx",
"WalletProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\AuthProvider.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/MockAuthProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/MockAuthProvider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MockAuthProvider: () => (/* binding */ MockAuthProvider),
/* harmony export */   useMockUser: () => (/* binding */ useMockUser)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const MockAuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call MockAuthProvider() from the server but MockAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\MockAuthProvider.tsx",
"MockAuthProvider",
);const useMockUser = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useMockUser() from the server but useMockUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\MockAuthProvider.tsx",
"useMockUser",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/ReactQueryProvider.tsx":
/*!*********************************************************!*\
  !*** ./src/components/providers/ReactQueryProvider.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ReactQueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\ReactQueryProvider.tsx",
"ReactQueryProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/index.js */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AppLayout.tsx */ \"(ssr)/./src/components/AppLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/MockAuthProvider.tsx */ \"(ssr)/./src/components/providers/MockAuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ReactQueryProvider.tsx */ \"(ssr)/./src/components/providers/ReactQueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WalletProvider.tsx */ \"(ssr)/./src/components/WalletProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(ssr)/./src/app/orders/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcmRlcnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW9HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxnaXRodWJcXFxcdG9rZW5kZXYtbmV3cm9vXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG9yZGVyc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/orders/page.tsx":
/*!*********************************!*\
  !*** ./src/app/orders/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_OrderCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/OrderCard */ \"(ssr)/./src/components/OrderCard.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(ssr)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction OrdersPage() {\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const mockAuth = useMockAuth ? (0,_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const authLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient)();\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"OrdersPage.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"OrdersPage.useQuery\"],\n        enabled: !!user\n    });\n    // Fetch orders\n    const { data: ordersData, isLoading: ordersLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-orders',\n            selectedStatus,\n            currentPage\n        ],\n        queryFn: {\n            \"OrdersPage.useQuery\": async ()=>{\n                const params = new URLSearchParams({\n                    page: currentPage.toString(),\n                    limit: '10'\n                });\n                if (selectedStatus !== 'ALL') {\n                    params.append('status', selectedStatus);\n                }\n                const response = await fetch(`/api/client-orders?${params.toString()}`);\n                if (!response.ok) {\n                    throw new Error('Failed to fetch orders');\n                }\n                return response.json();\n            }\n        }[\"OrdersPage.useQuery\"],\n        enabled: !!user && !!clientProfile\n    });\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // AppLayout handles authentication\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"My Orders\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Track your token purchase orders and their status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>refetch(),\n                                    disabled: ordersLoading,\n                                    className: \"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors disabled:opacity-50\",\n                                    children: ordersLoading ? 'Refreshing...' : 'Refresh'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\",\n                                    children: \"Browse Tokens\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                ordersData?.orders && ordersData.orders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Order Summary\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: (()=>{\n                                const statusCounts = ordersData.orders.reduce((acc, order)=>{\n                                    acc[order.status] = (acc[order.status] || 0) + 1;\n                                    return acc;\n                                }, {});\n                                return [\n                                    {\n                                        status: 'PENDING_APPROVAL',\n                                        label: 'Pending',\n                                        color: 'text-yellow-600',\n                                        bgColor: 'bg-yellow-50'\n                                    },\n                                    {\n                                        status: 'CONFIRMED',\n                                        label: 'Confirmed',\n                                        color: 'text-blue-600',\n                                        bgColor: 'bg-blue-50'\n                                    },\n                                    {\n                                        status: 'MINTED',\n                                        label: 'Minted',\n                                        color: 'text-green-600',\n                                        bgColor: 'bg-green-50'\n                                    },\n                                    {\n                                        status: 'CANCELLED',\n                                        label: 'Cancelled',\n                                        color: 'text-red-600',\n                                        bgColor: 'bg-red-50'\n                                    }\n                                ].map(({ status, label, color, bgColor })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${bgColor} rounded-lg p-4 text-center`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-2xl font-bold ${color}`,\n                                                children: statusCounts[status] || 0\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, status, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, this));\n                            })()\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"status-filter\",\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Filter by Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"status-filter\",\n                                        value: selectedStatus,\n                                        onChange: (e)=>{\n                                            setSelectedStatus(e.target.value);\n                                            setCurrentPage(1);\n                                        },\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ALL\",\n                                                children: \"All Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING_APPROVAL\",\n                                                children: \"Pending Approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CONFIRMED\",\n                                                children: \"Confirmed\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MINTED\",\n                                                children: \"Minted\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CANCELLED\",\n                                                children: \"Cancelled\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: ordersData?.pagination && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Showing \",\n                                        ordersData.orders.length,\n                                        \" of \",\n                                        ordersData.pagination.totalCount,\n                                        \" orders\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                ordersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Loading your orders...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: \"Error loading orders. Please try again.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>refetch(),\n                            className: \"mt-4 text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this) : !ordersData?.orders || ordersData.orders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No Orders Found\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: selectedStatus === 'ALL' ? \"You haven't placed any orders yet.\" : `No orders found with status: ${selectedStatus}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\",\n                            children: \"Browse Available Tokens\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: ordersData.orders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OrderCard__WEBPACK_IMPORTED_MODULE_4__.OrderCard, {\n                                    order: order,\n                                    onRefresh: ()=>refetch()\n                                }, order.id, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this),\n                        ordersData.pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Showing page \",\n                                            ordersData.pagination.page,\n                                            \" of \",\n                                            ordersData.pagination.totalPages,\n                                            \"(\",\n                                            ordersData.pagination.totalCount,\n                                            \" total orders)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentPage(currentPage - 1),\n                                                disabled: currentPage === 1,\n                                                className: \"px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentPage(currentPage + 1),\n                                                disabled: currentPage === ordersData.pagination.totalPages,\n                                                className: \"px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/orders/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AppLayout.tsx":
/*!**************************************!*\
  !*** ./src/components/AppLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/MockAuthProvider */ \"(ssr)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\n\n\nfunction AppLayout({ children }) {\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const mockAuth = useMockAuth ? (0,_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"AppLayout.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"AppLayout.useQuery\"],\n        enabled: !!user\n    });\n    // Loading state\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    // Not authenticated - show login screen\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"TokenDev Client Portal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Complete your KYC qualification to access investment opportunities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: mockAuth.login,\n                                    disabled: mockAuth.isLoading,\n                                    className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                    children: mockAuth.isLoading ? 'Signing In...' : 'Sign In (Demo)'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/api/auth/login\",\n                                    className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center\",\n                                    children: \"Sign In with Auth0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"New to TokenDev?\",\n                                        ' ',\n                                        useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: mockAuth.login,\n                                            className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                            children: \"Create an account (Demo)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/api/auth/login?screen_hint=signup\",\n                                            className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                            children: \"Create an account with Auth0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 pt-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"\\uD83D\\uDD12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: \"Secure\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: \"KYC Compliant\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: \"Real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    // Authenticated - show app layout\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                user: user,\n                clientProfile: clientProfile,\n                onLogout: useMockAuth ? mockAuth.logout : undefined,\n                useMockAuth: useMockAuth\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-[calc(100vh-4rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                        user: user,\n                        onLogout: useMockAuth ? mockAuth.logout : undefined,\n                        useMockAuth: useMockAuth\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transition/transition.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Header,Navbar auto */ \n\n\n\n\nfunction Header({ user, clientProfile, onLogout, useMockAuth }) {\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"TokenDev\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu, {\n                            as: \"div\",\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Button, {\n                                        className: \"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open user menu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    user.picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-8 w-8 rounded-full\",\n                                                        src: user.picture,\n                                                        alt: user.name || 'User avatar'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden md:block text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: user.name || 'User'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 54,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 57,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Transition, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"transition ease-out duration-100\",\n                                    enterFrom: \"transform opacity-0 scale-95\",\n                                    enterTo: \"transform opacity-100 scale-100\",\n                                    leave: \"transition ease-in duration-75\",\n                                    leaveFrom: \"transform opacity-100 scale-100\",\n                                    leaveTo: \"transform opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Items, {\n                                        className: \"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-3 border-b border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: user.name || 'User'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    clientProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-2 h-2 rounded-full mr-2 ${clientProfile.kycStatus === 'APPROVED' ? 'bg-green-500' : clientProfile.kycStatus === 'IN_REVIEW' ? 'bg-yellow-500' : clientProfile.kycStatus === 'REJECTED' ? 'bg-red-500' : 'bg-gray-400'}`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"KYC: \",\n                                                                    clientProfile.kycStatus.replace('_', ' ')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Item, {\n                                                children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/qualification\",\n                                                        className: `${active ? 'bg-gray-100' : ''} flex items-center px-4 py-2 text-sm text-gray-700`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            clientProfile ? 'View Profile & Qualification' : 'Complete Qualification'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-100\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Item, {\n                                                children: ({ active })=>useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onLogout,\n                                                        className: `${active ? 'bg-gray-100' : ''} flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Sign out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `/api/auth/logout?returnTo=${\"http://localhost:7788\" || 0}`,\n                                                        className: `${active ? 'bg-gray-100' : ''} flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Sign out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n// Keep the old Navbar component for backward compatibility during transition\nfunction Navbar({ user, clientProfile, onGetQualified, onLogout, useMockAuth }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n        user: user,\n        clientProfile: clientProfile,\n        onLogout: onLogout,\n        useMockAuth: useMockAuth\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 160,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OrderCard.tsx":
/*!**************************************!*\
  !*** ./src/components/OrderCard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderCard: () => (/* binding */ OrderCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ OrderCard auto */ \n\n\nfunction OrderCard({ order, onRefresh }) {\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getStatusBadgeColor = (status)=>{\n        switch(status){\n            case 'PENDING_APPROVAL':\n                return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n            case 'CONFIRMED':\n                return 'bg-blue-100 text-blue-800 border-blue-200';\n            case 'MINTED':\n                return 'bg-green-100 text-green-800 border-green-200';\n            case 'CANCELLED':\n                return 'bg-red-100 text-red-800 border-red-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'PENDING_APPROVAL':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this);\n            case 'CONFIRMED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this);\n            case 'MINTED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this);\n            case 'CANCELLED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const formatCurrency = (amount, currency = 'USD')=>{\n        const numAmount = parseFloat(amount);\n        // Handle crypto currencies that don't have standard currency codes\n        const cryptoCurrencies = [\n            'ETH',\n            'BTC',\n            'USDC',\n            'USDT',\n            'DAI'\n        ];\n        if (cryptoCurrencies.includes(currency.toUpperCase())) {\n            return `${numAmount} ${currency.toUpperCase()}`;\n        }\n        // Handle standard fiat currencies\n        try {\n            return new Intl.NumberFormat('en-US', {\n                style: 'currency',\n                currency: currency,\n                minimumFractionDigits: 0,\n                maximumFractionDigits: 2\n            }).format(numAmount);\n        } catch (error) {\n            // Fallback for unsupported currencies\n            return `${numAmount} ${currency}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: order.token.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 font-mono\",\n                                        children: order.token.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(order.status)}`,\n                                    children: [\n                                        getStatusIcon(order.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: order.status.replace('_', ' ')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsExpanded(!isExpanded),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: `w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`,\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 9l-7 7-7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"Tokens Ordered\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-semibold text-gray-900\",\n                                    children: order.tokensOrdered\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"Amount to Pay\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-semibold text-gray-900\",\n                                    children: formatCurrency(order.amountToPay, order.token.currency)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"Order Date\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-semibold text-gray-900\",\n                                    children: formatDate(order.createdAt)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"Reference\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-semibold text-gray-900 font-mono text-xs\",\n                                    children: order.paymentReference\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-100 p-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 mb-1\",\n                                            children: \"Token Price\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                formatCurrency(order.tokenPrice, order.token.currency),\n                                                \" per token\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 mb-1\",\n                                            children: \"Tokens Confirmed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: order.tokensConfirmed\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 mb-1\",\n                                            children: \"Confirmed Payment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: formatCurrency(order.confirmedPayment, order.token.currency)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 mb-1\",\n                                            children: \"Last Updated\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: formatDate(order.updatedAt)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"Token Contract\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-mono text-xs bg-white p-2 rounded border\",\n                                    children: order.token.address\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 pt-2 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Order ID: \",\n                                        order.id\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `/offers/${order.token.address}`,\n                                            className: \"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: \"View Token\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        onRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onRefresh,\n                                            className: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        order.status === 'PENDING_APPROVAL' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 border border-yellow-200 rounded-md p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-yellow-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-yellow-800\",\n                                                children: \"Awaiting Approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-yellow-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Your order is pending admin approval. You will be notified once it's processed.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 15\n                        }, this),\n                        order.status === 'CONFIRMED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-md p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-blue-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Order Confirmed\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-blue-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Your order has been confirmed. Payment processing is in progress.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 15\n                        }, this),\n                        order.status === 'MINTED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-md p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-green-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Tokens Minted\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-green-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Your tokens have been successfully minted and transferred to your wallet.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 15\n                        }, this),\n                        order.status === 'CANCELLED' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Order Cancelled\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"This order has been cancelled. Please contact support if you have questions.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\OrderCard.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OrderCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/QuestionMarkCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'Orders',\n        href: '/orders',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Help',\n        href: '/help',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    }\n];\nfunction Sidebar({ user, onLogout, useMockAuth }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    if (!user) {\n        return null;\n    }\n    const SidebarContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-4 py-6 space-y-2\",\n                    children: navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: `group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n                            onClick: ()=>setSidebarOpen(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: `mr-3 h-5 w-5 flex-shrink-0 ${isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-4 border-t border-gray-200\",\n                    children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onLogout,\n                        className: \"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign out\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: `/api/auth/logout?returnTo=${\"http://localhost:7788\" || 0}`,\n                        className: \"group flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign out\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 37,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed top-4 left-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    className: \"bg-white p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 shadow-md\",\n                    onClick: ()=>setSidebarOpen(true),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Open sidebar\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-40 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Close sidebar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-0 flex-1 bg-white border-r border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WalletProvider.tsx":
/*!*******************************************!*\
  !*** ./src/components/WalletProvider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _config_wallet__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/wallet */ \"(ssr)/./src/config/wallet.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletProvider auto */ \n\n\n\n// Set up queryClient\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction WalletProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_3__.WagmiProvider, {\n        config: _config_wallet__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\WalletProvider.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\WalletProvider.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9XYWxsZXRQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHcUM7QUFDbUM7QUFDaEM7QUFFeEMscUJBQXFCO0FBQ3JCLE1BQU1JLGNBQWMsSUFBSUgsOERBQVdBLENBQUM7SUFDbENJLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLFdBQVcsS0FBSztZQUNoQkMsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQU1PLFNBQVNDLGVBQWUsRUFBRUMsUUFBUSxFQUF1QjtJQUM5RCxxQkFDRSw4REFBQ1YsZ0RBQWFBO1FBQUNHLFFBQVFBLGtEQUFNQTtrQkFDM0IsNEVBQUNELHNFQUFtQkE7WUFBQ1MsUUFBUVA7c0JBQzFCTTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xcV2FsbGV0UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFdhZ21pUHJvdmlkZXIgfSBmcm9tICd3YWdtaSdcbmltcG9ydCB7IFF1ZXJ5Q2xpZW50LCBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5J1xuaW1wb3J0IHsgY29uZmlnIH0gZnJvbSAnQC9jb25maWcvd2FsbGV0J1xuXG4vLyBTZXQgdXAgcXVlcnlDbGllbnRcbmNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICBzdGFsZVRpbWU6IDYwICogMTAwMCwgLy8gMSBtaW51dGVcbiAgICAgIHJldHJ5OiAxLFxuICAgIH0sXG4gIH0sXG59KVxuXG5pbnRlcmZhY2UgV2FsbGV0UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFdhbGxldFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogV2FsbGV0UHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxXYWdtaVByb3ZpZGVyIGNvbmZpZz17Y29uZmlnfT5cbiAgICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICAgPC9XYWdtaVByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiV2FnbWlQcm92aWRlciIsIlF1ZXJ5Q2xpZW50IiwiUXVlcnlDbGllbnRQcm92aWRlciIsImNvbmZpZyIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwicmV0cnkiLCJXYWxsZXRQcm92aWRlciIsImNoaWxkcmVuIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WalletProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\n\n\nfunction AuthProvider({ children }) {\n    const { user, isLoading } = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (user) {\n                // Set the user in the API client for authenticated requests\n                _lib_api_client__WEBPACK_IMPORTED_MODULE_3__.apiClient.setUser({\n                    sub: user.sub,\n                    email: user.email || undefined,\n                    name: user.name || undefined\n                });\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        user\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\AuthProvider.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/MockAuthProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/MockAuthProvider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockAuthProvider: () => (/* binding */ MockAuthProvider),\n/* harmony export */   useMockUser: () => (/* binding */ useMockUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MockAuthProvider,useMockUser auto */ \n\nconst MockAuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MockAuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const login = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setUser({\n                sub: 'mock-user-123',\n                name: 'Demo User',\n                email: '<EMAIL>',\n                picture: 'https://via.placeholder.com/40'\n            });\n            setIsLoading(false);\n        }, 1000);\n    };\n    const logout = ()=>{\n        setUser(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MockAuthContext.Provider, {\n        value: {\n            user,\n            error: undefined,\n            isLoading,\n            login,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\MockAuthProvider.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\nfunction useMockUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MockAuthContext);\n    if (context === undefined) {\n        throw new Error('useMockUser must be used within a MockAuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/MockAuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ReactQueryProvider.tsx":
/*!*********************************************************!*\
  !*** ./src/components/providers/ReactQueryProvider.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ReactQueryProvider auto */ \n\n\nfunction ReactQueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ReactQueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1,\n                        refetchOnWindowFocus: false\n                    }\n                }\n            })\n    }[\"ReactQueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\ReactQueryProvider.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUmVhY3RRdWVyeVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUV5RTtBQUN4QztBQUUxQixTQUFTRyxtQkFBbUIsRUFBRUMsUUFBUSxFQUFpQztJQUM1RSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBO3VDQUM1QixJQUNFLElBQUlGLDhEQUFXQSxDQUFDO2dCQUNkTSxnQkFBZ0I7b0JBQ2RDLFNBQVM7d0JBQ1BDLFdBQVcsS0FBSzt3QkFDaEJDLE9BQU87d0JBQ1BDLHNCQUFzQjtvQkFDeEI7Z0JBQ0Y7WUFDRjs7SUFHSixxQkFDRSw4REFBQ1Qsc0VBQW1CQTtRQUFDVSxRQUFRTjtrQkFDMUJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxSZWFjdFF1ZXJ5UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUXVlcnlDbGllbnQsIFF1ZXJ5Q2xpZW50UHJvdmlkZXIgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWFjdFF1ZXJ5UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBRdWVyeUNsaWVudCh7XG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiB7XG4gICAgICAgICAgcXVlcmllczoge1xuICAgICAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgICAgICByZXRyeTogMSxcbiAgICAgICAgICAgIHJlZmV0Y2hPbldpbmRvd0ZvY3VzOiBmYWxzZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJ1c2VTdGF0ZSIsIlJlYWN0UXVlcnlQcm92aWRlciIsImNoaWxkcmVuIiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJyZXRyeSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ReactQueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/wallet.ts":
/*!******************************!*\
  !*** ./src/config/wallet.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   modal: () => (/* binding */ modal),\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n\n\n\n// 1. Get projectId from https://cloud.reown.com\nconst projectId = \"5b68ddabca3cc662e86f74a3044a95d4\" || 0;\nif (!projectId) {\n    throw new Error('NEXT_PUBLIC_REOWN_PROJECT_ID is not set');\n}\n// 2. Configure custom networks with RPC URLs\nconst amoyNetwork = {\n    ..._reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.polygonAmoy,\n    rpcUrls: {\n        default: {\n            http: [\n                \"https://rpc-amoy.polygon.technology\"\n            ]\n        },\n        public: {\n            http: [\n                \"https://rpc-amoy.polygon.technology\"\n            ]\n        }\n    }\n};\nconst polygonNetwork = {\n    ..._reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.polygon,\n    rpcUrls: {\n        default: {\n            http: [\n                \"https://polygon-rpc.com\"\n            ]\n        },\n        public: {\n            http: [\n                \"https://polygon-rpc.com\"\n            ]\n        }\n    }\n};\n// 3. Set up Wagmi adapter with AMOY as primary network\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    networks: [\n        amoyNetwork,\n        polygonNetwork\n    ],\n    projectId,\n    ssr: true\n});\n// 3. Configure the metadata\nconst metadata = {\n    name: 'Security Token Client Portal',\n    description: 'Client portal for ERC-3643 security token qualification',\n    url: process.env.AUTH0_BASE_URL,\n    icons: [\n        'https://avatars.githubusercontent.com/u/179229932'\n    ]\n};\n// 4. Create the modal with AMOY as default network\nconst modal = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter\n    ],\n    networks: [\n        amoyNetwork,\n        polygonNetwork\n    ],\n    defaultNetwork: amoyNetwork,\n    metadata,\n    projectId,\n    features: {\n        analytics: true,\n        email: false,\n        socials: [\n            'google',\n            'x',\n            'github',\n            'discord',\n            'apple'\n        ],\n        emailShowWallets: true // Optional - defaults to your Cloud configuration\n    }\n});\nconst config = wagmiAdapter.wagmiConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/wallet.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   useApiClient: () => (/* binding */ useApiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nclass ApiClient {\n    constructor(){\n        this.user = null;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: \"http://localhost:7788/api\",\n            timeout: 10000,\n            withCredentials: true\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            if (error.response?.status === 401) {\n                // Handle unauthorized access\n                window.location.href = '/api/auth/login';\n            }\n            return Promise.reject(error);\n        });\n    }\n    setUser(user) {\n        this.user = user;\n    }\n    // Client profile API calls\n    async getClientProfile() {\n        try {\n            const response = await this.client.get('/client/profile');\n            return response.data;\n        } catch (error) {\n            if (error?.response?.status === 404) {\n                return null; // Client profile doesn't exist yet\n            }\n            throw error;\n        }\n    }\n    async createClientProfile(profileData) {\n        const response = await this.client.post('/client/profile', profileData);\n        return response.data;\n    }\n    async updateClientProfile(profileData) {\n        const response = await this.client.put('/client/profile', profileData);\n        return response.data;\n    }\n    // KYC-related API calls\n    async submitKYCApplication(kycData) {\n        const response = await this.client.post('/client/kyc', kycData);\n        return response.data;\n    }\n    async getKYCStatus() {\n        const response = await this.client.get('/client/kyc');\n        return response.data;\n    }\n    // Whitelist-related API calls\n    async getWhitelistStatus() {\n        const response = await this.client.get('/client/whitelist');\n        return response.data;\n    }\n    // Generic API call method\n    async request(config) {\n        const response = await this.client.request(config);\n        return response.data;\n    }\n}\n// Create a singleton instance\nconst apiClient = new ApiClient();\n// Hook for React components\nfunction useApiClient() {\n    return apiClient;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api-client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/next","vendor-chunks/lit-html","vendor-chunks/@lit","vendor-chunks/lit","vendor-chunks/viem","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/@swc","vendor-chunks/@walletconnect","vendor-chunks/ox","vendor-chunks/@wagmi","vendor-chunks/abitype","vendor-chunks/tr46","vendor-chunks/@floating-ui","vendor-chunks/mime-db","vendor-chunks/zod","vendor-chunks/axios","vendor-chunks/@headlessui","vendor-chunks/@tanstack","vendor-chunks/@react-aria","vendor-chunks/pino","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/valtio","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/tabbable","vendor-chunks/big.js","vendor-chunks/follow-redirects","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/wagmi","vendor-chunks/@heroicons","vendor-chunks/zustand","vendor-chunks/thread-stream","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/sonic-boom","vendor-chunks/dayjs","vendor-chunks/eventemitter3","vendor-chunks/lit-element","vendor-chunks/asynckit","vendor-chunks/detect-browser","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/pino-std-serializers","vendor-chunks/webidl-conversions","vendor-chunks/combined-stream","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/quick-format-unescaped","vendor-chunks/proxy-compare","vendor-chunks/mipd","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/destr","vendor-chunks/derive-valtio","vendor-chunks/utf-8-validate","vendor-chunks/@react-stately","vendor-chunks/process-warning","vendor-chunks/es-set-tostringtag","vendor-chunks/atomic-sleep","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/bufferutil","vendor-chunks/on-exit-leak-free","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/bs58","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();