// Script to fix token decimals and verify database data
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3002/api';

async function updateTokenDecimals() {
  console.log('=== Fixing Token Decimals ===');
  
  try {
    // First, get the current token
    const getResponse = await fetch(`${ADMIN_API_URL}/tokens?source=database`);
    const tokens = await getResponse.json();
    
    if (tokens.length === 0) {
      console.log('No tokens found');
      return;
    }
    
    const token = tokens[0];
    console.log(`Current token decimals: ${token.decimals}`);
    console.log(`Token address: ${token.address}`);
    
    // Update the token with correct decimals
    const updateData = {
      ...token,
      decimals: 0, // Fix the decimals to 0 as intended
      totalSupply: '0' // Ensure totalSupply is set correctly
    };
    
    // Since there's no PUT endpoint, let's delete and recreate
    console.log('Updating token data...');
    
    // For now, let's create a new token with correct data
    const newTokenData = {
      address: '0xe5F81d7dCeB8a8F97274C749773659B7288EcF90', // Augment_01z
      name: 'Augment_01z',
      symbol: 'AUG01Z',
      decimals: 0,
      maxSupply: '1000000',
      totalSupply: '0',
      tokenType: 'commodity',
      tokenPrice: '10 USD',
      currency: 'USD',
      bonusTiers: 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%',
      whitelistAddress: '******************************************',
      adminAddress: '******************************************',
      hasKYC: true,
      network: 'amoy',
      deploymentNotes: 'Commodity token with 0 decimals'
    };
    
    const createResponse = await fetch(`${ADMIN_API_URL}/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newTokenData),
    });
    
    const result = await createResponse.json();
    
    if (createResponse.ok) {
      console.log('New token created successfully!');
      console.log(`Token ID: ${result.id}`);
      console.log(`Decimals: ${result.decimals}`);
    } else {
      console.log(`Failed to create token: ${result.error}`);
    }
    
  } catch (error) {
    console.error('Error updating token:', error.message);
  }
}

async function testTokensAfterFix() {
  console.log('\n=== Testing Tokens After Fix ===');
  
  try {
    // Test admin API
    const adminResponse = await fetch(`${ADMIN_API_URL}/tokens?source=database`);
    const adminTokens = await adminResponse.json();
    
    console.log(`Admin API - Number of tokens: ${adminTokens.length}`);
    
    adminTokens.forEach((token, index) => {
      console.log(`\nToken ${index + 1}:`);
      console.log(`- Name: ${token.name}`);
      console.log(`- Symbol: ${token.symbol}`);
      console.log(`- Decimals: ${token.decimals}`);
      console.log(`- Total Supply: ${token.totalSupply}`);
      console.log(`- Max Supply: ${token.maxSupply}`);
    });
    
    // Test client API
    const clientResponse = await fetch('http://localhost:3003/api/tokens');
    const clientTokens = await clientResponse.json();
    
    console.log(`\nClient API - Number of tokens: ${clientTokens.length}`);
    
    clientTokens.forEach((token, index) => {
      console.log(`\nClient Token ${index + 1}:`);
      console.log(`- Name: ${token.name}`);
      console.log(`- Symbol: ${token.symbol}`);
      console.log(`- Decimals: ${token.decimals}`);
      console.log(`- Total Supply: ${token.totalSupply}`);
      console.log(`- Max Supply: ${token.maxSupply}`);
    });
    
  } catch (error) {
    console.error('Error testing tokens:', error.message);
  }
}

async function main() {
  await updateTokenDecimals();
  await testTokensAfterFix();
}

main().catch(console.error);
