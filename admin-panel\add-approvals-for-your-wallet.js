// Add token approvals for your existing wallet
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addApprovalsForYourWallet() {
  console.log('=== Adding Token Approvals for Your Wallet ===');
  
  const yourWallet = '******************************************';
  
  try {
    // Find the client with your wallet address
    const client = await prisma.client.findFirst({
      where: { 
        walletAddress: {
          equals: yourWallet,
          mode: 'insensitive'
        }
      }
    });
    
    if (!client) {
      console.log('❌ No client found with your wallet address');
      return;
    }
    
    console.log('✅ Found client with your wallet:');
    console.log(`   ID: ${client.id}`);
    console.log(`   Email: ${client.email}`);
    console.log(`   Wallet: ${client.walletAddress}`);
    console.log(`   KYC Status: ${client.kycStatus}`);
    console.log(`   Global Whitelisted: ${client.isWhitelisted}`);
    
    // Delete existing approvals for this client
    const deletedApprovals = await prisma.tokenClientApproval.deleteMany({
      where: { clientId: client.id }
    });
    console.log(`\n🗑️  Deleted ${deletedApprovals.count} existing token approvals`);
    
    // Get all tokens from the database
    const tokens = await prisma.token.findMany({
      select: { id: true, name: true, symbol: true, address: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens found in database');
      return;
    }

    console.log(`\n📋 Found ${tokens.length} tokens, creating approvals...`);

    // Whitelist the main tokens you mentioned you're already whitelisted for
    const tokensToWhitelist = ['AUG019', 'AUG01Z', 'TZD', 'EURT', 'ETHF'];

    for (const token of tokens) {
      const shouldWhitelist = tokensToWhitelist.includes(token.symbol);

      const approval = await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: client.id,
          approvalStatus: shouldWhitelist ? 'APPROVED' : 'PENDING',
          kycApproved: true,
          whitelistApproved: shouldWhitelist,
          approvedBy: shouldWhitelist ? '<EMAIL>' : null,
          approvedAt: shouldWhitelist ? new Date() : null,
          notes: shouldWhitelist ? 'Whitelisted for your wallet' : 'Pending approval'
        }
      });

      const status = shouldWhitelist ? '✅ WHITELISTED' : '⏳ PENDING';
      console.log(`   ${token.symbol.padEnd(10)} | ${status}`);
    }

    const whitelistedCount = tokensToWhitelist.length;
    console.log(`\n✅ Token approvals created: ${whitelistedCount} whitelisted, ${tokens.length - whitelistedCount} pending`);
    
    return client;
    
  } catch (error) {
    console.error('Error adding token approvals:', error);
    throw error;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Whitelist API Response:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${symbol.padEnd(10)} | ${status}`);
      });
      
      const whitelistedCount = data.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nSummary: ${whitelistedCount}/${data.tokens.length} tokens whitelisted for your wallet`);
      
    } else {
      console.log('❌ Whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing whitelist API:', error);
  }
}

async function testClientTokensAPI(walletAddress) {
  console.log('\n=== Testing Client Tokens API ===');
  
  try {
    const fetch = require('node-fetch');
    
    const response = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(walletAddress)}`);
    
    if (response.ok) {
      const tokens = await response.json();
      console.log(`Client tokens API returned ${tokens.length} tokens`);
      
      console.log('\nToken whitelist status in client API:');
      tokens.forEach(token => {
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${token.symbol.padEnd(10)} | ${status} | ${token.price} ${token.currency}`);
      });
      
      const whitelistedCount = tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nClient API Summary: ${whitelistedCount}/${tokens.length} tokens whitelisted`);
      
    } else {
      console.log('❌ Client tokens API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing client tokens API:', error);
  }
}

async function main() {
  const yourWallet = '******************************************';
  
  try {
    // Add token approvals for your wallet
    const client = await addApprovalsForYourWallet();
    
    if (!client) {
      console.log('❌ Could not find or update client');
      return;
    }
    
    // Test the whitelist API
    await testWhitelistAPI(yourWallet);
    
    // Test the client tokens API
    await testClientTokensAPI(yourWallet);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\n🎯 READY TO TEST WHITELIST TAGS:');
    console.log('1. Open client app: http://localhost:3003/offers');
    console.log(`2. Login with email: ${client.email}`);
    console.log('3. Connect wallet: ******************************************');
    console.log('4. You should see green WHITELISTED tags on approved tokens');
    
    console.log('\n📋 WHITELISTED TOKENS FOR YOUR WALLET:');
    console.log('   - AUG019 (Augment_019)');
    console.log('   - AUG01Z (Augment_01z)');
    console.log('   - TZD (Test Zero Decimals Token)');
    console.log('   - EURT (European Real Estate Token)');
    console.log('   - ETHF (Ethereum DeFi Fund)');
    
    console.log('\n🌐 TESTING URLS:');
    console.log(`   - With your wallet: http://localhost:3003/offers?testWallet=${encodeURIComponent(yourWallet)}`);
    console.log('   - Normal login: http://localhost:3003/offers');
    
  } catch (error) {
    console.error('Error in main:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
