// <PERSON>ript to fix the checksum issue with the implementation address
const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("Fixing checksum issue in OpenZeppelin storage...");

  // The problematic address with incorrect checksum
  const problematicAddress = "******************************************";
  
  // Get the correct checksum address
  const correctedAddress = ethers.getAddress(problematicAddress.toLowerCase());
  console.log(`Original address: ${problematicAddress}`);
  console.log(`Corrected address: ${correctedAddress}`);

  // Find the .openzeppelin directory
  const openzeppelinDir = path.join(__dirname, "../.openzeppelin");
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(openzeppelinDir)) {
    console.log(".openzeppelin directory not found, creating it...");
    fs.mkdirSync(openzeppelinDir, { recursive: true });
  }

  // Check .openzeppelin directory contents
  console.log("Checking .openzeppelin directory contents...");
  const files = fs.readdirSync(openzeppelinDir);
  console.log(`Found ${files.length} files in .openzeppelin directory`);
  
  let foundAndFixed = false;
  
  // Process each file in the directory
  for (const file of files) {
    if (file.endsWith(".json")) {
      const filePath = path.join(openzeppelinDir, file);
      console.log(`Checking file: ${file}`);
      
      try {
        // Read the file
        const content = fs.readFileSync(filePath, "utf8");
        
        // Check if the file contains the problematic address
        if (content.includes(problematicAddress)) {
          console.log(`Found problematic address in ${file}`);
          
          // Fix the JSON directly by parsing and updating
          const jsonData = JSON.parse(content);
          
          // Process the implementation addresses
          for (const implKey in jsonData.impls) {
            if (jsonData.impls[implKey].address === problematicAddress) {
              console.log(`Found implementation with key ${implKey}`);
              jsonData.impls[implKey].address = correctedAddress;
              console.log(`Updated address to ${correctedAddress}`);
            }
          }
          
          // Write the corrected content back to the file
          fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2));
          console.log(`Fixed checksum in ${file}`);
          foundAndFixed = true;
        }
      } catch (error) {
        console.error(`Error processing file ${file}: ${error.message}`);
      }
    }
  }
  
  if (!foundAndFixed) {
    console.log("No files with the problematic address were found or fixed.");
  } else {
    console.log("Successfully fixed checksum issues. You can now try running the upgrade script again.");
  }
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 