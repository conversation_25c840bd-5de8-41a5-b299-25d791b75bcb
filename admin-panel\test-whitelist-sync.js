// Test whitelist sync functionality
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testWhitelistSync() {
  console.log('🧪 Testing Whitelist Sync Functionality');
  console.log('=====================================');

  const yourWallet = '******************************************';
  const testWallet = '******************************************'; // The wallet you mentioned

  try {
    // Check current state
    console.log('\n1. Current Database State:');

    // Check your wallet
    const yourUser = await prisma.client.findFirst({
      where: {
        walletAddress: {
          equals: yourWallet,
          mode: 'insensitive'
        }
      },
      include: {
        tokenApprovals: {
          include: {
            token: {
              select: { symbol: true, address: true }
            }
          }
        }
      }
    });

    if (yourUser) {
      console.log(`✅ Your wallet (${yourWallet}):`);
      console.log(`   Email: ${yourUser.email}`);
      console.log(`   KYC Status: ${yourUser.kycStatus}`);
      console.log(`   Global Whitelisted: ${yourUser.isWhitelisted}`);

      const whitelistedTokens = yourUser.tokenApprovals.filter(a => a.whitelistApproved);
      console.log(`   Whitelisted Tokens: ${whitelistedTokens.length}/${yourUser.tokenApprovals.length}`);

      whitelistedTokens.forEach(approval => {
        console.log(`     - ${approval.token.symbol} (${approval.token.address})`);
      });
    } else {
      console.log(`❌ Your wallet (${yourWallet}) not found in database`);
    }

    // Check test wallet
    const testUser = await prisma.client.findFirst({
      where: {
        walletAddress: {
          equals: testWallet,
          mode: 'insensitive'
        }
      },
      include: {
        tokenApprovals: {
          include: {
            token: {
              select: { symbol: true, address: true }
            }
          }
        }
      }
    });

    if (testUser) {
      console.log(`\n✅ Test wallet (${testWallet}):`);
      console.log(`   Email: ${testUser.email}`);
      console.log(`   KYC Status: ${testUser.kycStatus}`);
      console.log(`   Global Whitelisted: ${testUser.isWhitelisted}`);

      const whitelistedTokens = testUser.tokenApprovals.filter(a => a.whitelistApproved);
      console.log(`   Whitelisted Tokens: ${whitelistedTokens.length}/${testUser.tokenApprovals.length}`);

      whitelistedTokens.forEach(approval => {
        console.log(`     - ${approval.token.symbol} (${approval.token.address})`);
      });
    } else {
      console.log(`\n❌ Test wallet (${testWallet}) not found in database`);
      console.log('   This wallet needs to be added to the database first');
      console.log('   You can add it through the admin panel client management');
    }

    // Test API endpoints
    console.log('\n2. Testing API Endpoints:');

    const fetch = require('node-fetch');

    // Test admin whitelist API for your wallet
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });
    const tokenAddresses = tokens.map(t => t.address);

    console.log('\n   Testing admin whitelist API for your wallet...');
    const adminResponse = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        walletAddress: yourWallet,
        tokenAddresses: tokenAddresses
      })
    });

    if (adminResponse.ok) {
      const adminData = await adminResponse.json();
      const adminWhitelisted = adminData.tokens.filter(t => t.isWhitelisted).length;
      console.log(`   ✅ Admin API: ${adminWhitelisted}/${adminData.tokens.length} tokens whitelisted`);
    } else {
      console.log('   ❌ Admin API test failed');
    }

    // Test client tokens API for your wallet
    console.log('\n   Testing client tokens API for your wallet...');
    const clientResponse = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(yourWallet)}`);

    if (clientResponse.ok) {
      const clientTokens = await clientResponse.json();
      const clientWhitelisted = clientTokens.filter(t => t.isWhitelisted).length;
      console.log(`   ✅ Client API: ${clientWhitelisted}/${clientTokens.length} tokens whitelisted`);
    } else {
      console.log('   ❌ Client API test failed');
    }

    // Test for test wallet if it exists
    if (testUser) {
      console.log('\n   Testing client tokens API for test wallet...');
      const testClientResponse = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(testWallet)}`);

      if (testClientResponse.ok) {
        const testClientTokens = await testClientResponse.json();
        const testClientWhitelisted = testClientTokens.filter(t => t.isWhitelisted).length;
        console.log(`   ✅ Test wallet client API: ${testClientWhitelisted}/${testClientTokens.length} tokens whitelisted`);

        if (testClientWhitelisted === 0) {
          console.log('   ⚠️  Test wallet shows 0 whitelisted tokens in client API');
          console.log('   This means the blockchain whitelist is not synced to database');
        }
      } else {
        console.log('   ❌ Test wallet client API test failed');
      }
    }

  } catch (error) {
    console.error('Error testing whitelist sync:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function createTestUser() {
  console.log('\n🔧 Creating Test User for Wallet ******************************************');

  const testWallet = '******************************************';

  try {
    // Check if user already exists
    const existingUser = await prisma.client.findFirst({
      where: {
        walletAddress: {
          equals: testWallet,
          mode: 'insensitive'
        }
      }
    });

    if (existingUser) {
      console.log('✅ Test user already exists');
      return existingUser;
    }

    // Create test user
    const testUser = await prisma.client.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Blockchain',
        walletAddress: testWallet,
        walletSignature: '0xtest123...',
        walletVerifiedAt: new Date(),
        kycStatus: 'APPROVED',
        kycCompletedAt: new Date(),
        isWhitelisted: true,
        whitelistedAt: new Date(),
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'US',
        phoneNumber: '+1234567890',
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'US',
        gender: 'MALE',
        birthday: new Date('1990-01-01')
      }
    });

    console.log('✅ Created test user:', testUser.email);

    // Create token approvals for all tokens (but not whitelisted yet)
    const tokens = await prisma.token.findMany();

    for (const token of tokens) {
      await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: testUser.id,
          approvalStatus: 'PENDING',
          kycApproved: true,
          whitelistApproved: false, // Not whitelisted in database yet
          notes: 'Created for blockchain sync testing'
        }
      });
    }

    console.log(`✅ Created ${tokens.length} token approval records (all pending)`);

    return testUser;

  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--create-test-user')) {
    await createTestUser();
  }

  await testWhitelistSync();

  console.log('\n🎯 NEXT STEPS:');
  console.log('1. If test wallet (******************************************) shows 0 whitelisted tokens:');
  console.log('   - Go to admin panel: http://localhost:3000/tokens/******************************************');
  console.log('   - Use the whitelist functionality to add your wallet');
  console.log('   - The database should automatically sync');
  console.log('   - Test client API again to see whitelisted tokens');
  console.log('');
  console.log('2. To create test user: node test-whitelist-sync.js --create-test-user');
  console.log('');
  console.log('3. Test the complete flow:');
  console.log('   - Whitelist via admin panel');
  console.log('   - Check database sync');
  console.log('   - Verify client API shows whitelisted status');
}

main().catch(console.error);
