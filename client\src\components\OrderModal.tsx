'use client';

import { useState } from 'react';

interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  totalSupply: string;
  maxSupply: string;
  price: string;
  currency: string;
  category: string;
  description?: string;
  imageUrl?: string;
  network: string;
  decimals: number;
  version: string;
  bonusTiers?: string;
  whitelistAddress: string;
  createdAt: string;
  isWhitelisted: boolean;
}

interface ClientProfile {
  id: string;
  kycStatus: string;
}

interface OrderModalProps {
  token: Token;
  clientProfile: ClientProfile;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function OrderModal({ token, clientProfile, isOpen, onClose, onSuccess }: OrderModalProps) {
  const [orderAmount, setOrderAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatPrice = (price: string, currency: string) => {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(numPrice);
  };

  const handleSubmit = async () => {
    if (!orderAmount || !token || !clientProfile) {
      setError('Please fill in all required fields');
      return;
    }

    // Validate order amount
    const numAmount = Number(orderAmount);
    if (isNaN(numAmount) || numAmount <= 0) {
      setError('Please enter a valid number of tokens');
      return;
    }

    if (numAmount > Number(token.maxSupply)) {
      setError(`Cannot order more than ${token.maxSupply} tokens`);
      return;
    }

    // Check if user is whitelisted for this token
    if (!token.isWhitelisted) {
      setError('You must be whitelisted for this token before placing an order');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/client-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenId: token.id,
          clientId: clientProfile.id,
          tokensOrdered: orderAmount,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit order');
      }

      // Order submitted successfully
      const totalAmount = formatPrice((numAmount * Number(token.price)).toString(), token.currency);
      alert(`Order submitted successfully!\n\nToken: ${token.name}\nAmount: ${orderAmount} tokens\nTotal: ${totalAmount}\n\nYou will be notified once it is approved.`);
      
      setOrderAmount('');
      onSuccess();
      onClose();

    } catch (err: any) {
      console.error('Error submitting order:', err);
      setError(err.message || 'Failed to submit order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setOrderAmount('');
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
      <div className="relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Order {token.name} ({token.symbol})
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="space-y-4">
            {/* Token Details */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Token Details
              </label>
              <div className="bg-gray-50 p-3 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Token:</span> {token.name} ({token.symbol})
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Price:</span> {formatPrice(token.price, token.currency)} per token
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Available:</span> {token.maxSupply} tokens
                </p>
              </div>
            </div>
            
            {/* Order Amount Input */}
            <div>
              <label htmlFor="order-amount" className="block text-sm font-medium text-gray-700 mb-1">
                Number of Tokens to Order
              </label>
              <input
                id="order-amount"
                type="number"
                min="1"
                step="1"
                value={orderAmount}
                onChange={(e) => {
                  const value = e.target.value;
                  setOrderAmount(value);
                  setError(null);
                }}
                placeholder="Enter amount of tokens"
                className="block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            {/* Total Calculation */}
            {orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && (
              <div className="bg-blue-50 p-3 rounded-md">
                <p className="text-sm text-blue-800">
                  <span className="font-medium">Total Amount:</span> {formatPrice((Number(orderAmount) * Number(token.price)).toString(), token.currency)}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  This order will be submitted for admin approval
                </p>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="mt-6 flex flex-col space-y-3">
            <button
              onClick={handleSubmit}
              disabled={isSubmitting || !orderAmount}
              className="w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Order'}
            </button>
            <button
              onClick={handleClose}
              className="w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
