/**
 * Agent Management Integration Script
 * This script dynamically loads the agent management block into any page
 * 
 * Usage:
 * 1. Include this script in your page:
 *    <script src="agent-management-integration.js"></script>
 * 
 * 2. Call the function in your page where you want to add the agent management:
 *    <script>
 *      document.addEventListener('DOMContentLoaded', function() {
 *        loadAgentManagement('#container-selector', 'YOUR_TOKEN_ADDRESS');
 *      });
 *    </script>
 */

async function loadAgentManagement(containerSelector, tokenAddress) {
  // Get the container element
  const container = document.querySelector(containerSelector);
  
  if (!container) {
    console.error(`Agent Management: Container ${containerSelector} not found`);
    return;
  }
  
  // Store token address to be used by the agent management component
  window.tokenContractAddress = tokenAddress;
  
  try {
    // Fetch the agent management HTML
    const response = await fetch('agent-management.html');
    if (!response.ok) {
      throw new Error(`Failed to load agent management HTML: ${response.status}`);
    }
    
    const html = await response.text();
    
    // Create a temporary container to parse the HTML
    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = html;
    
    // Extract the agent management component
    const agentManagementComponent = tempContainer.querySelector('.agent-management');
    if (!agentManagementComponent) {
      throw new Error('Agent management component not found in the loaded HTML');
    }
    
    // Extract the script from the loaded HTML
    const scriptContent = Array.from(tempContainer.querySelectorAll('script'))
      .map(script => script.textContent)
      .join('\n');
    
    // Extract the style from the loaded HTML
    const styleContent = Array.from(tempContainer.querySelectorAll('style'))
      .map(style => style.textContent)
      .join('\n');
    
    // Add the agent management component to the container
    container.appendChild(agentManagementComponent);
    
    // Add the style to the document head
    if (styleContent) {
      const styleElement = document.createElement('style');
      styleElement.textContent = styleContent;
      document.head.appendChild(styleElement);
    }
    
    // Execute the script
    if (scriptContent) {
      const scriptElement = document.createElement('script');
      scriptElement.textContent = scriptContent;
      document.body.appendChild(scriptElement);
    }
    
    console.log('Agent management component loaded successfully');
  } catch (error) {
    console.error('Error loading agent management component:', error);
    container.innerHTML += `
      <div style="padding: 20px; border-radius: 8px; background-color: #f8d7da; color: #721c24; margin: 20px 0;">
        <h3>Failed to load Agent Management</h3>
        <p>${error.message}</p>
      </div>
    `;
  }
} 