import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { UserProvider } from '@auth0/nextjs-auth0/client';
import { ReactQueryProvider } from '@/components/providers/ReactQueryProvider';
import { AuthProvider } from '@/components/providers/AuthProvider';
import { MockAuthProvider } from '@/components/providers/MockAuthProvider';
import { WalletProvider } from '@/components/WalletProvider';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TokenDev Client Portal",
  description: "Secure token management and KYC compliance platform",
  keywords: ["tokens", "blockchain", "KYC", "compliance", "investment"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const useMockAuth = process.env.NEXT_PUBLIC_USE_MOCK_AUTH === 'true';

  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        {useMockAuth ? (
          <MockAuthProvider>
            <ReactQueryProvider>
              <WalletProvider>
                {children}
              </WalletProvider>
            </ReactQueryProvider>
          </MockAuthProvider>
        ) : (
          <UserProvider>
            <ReactQueryProvider>
              <AuthProvider>
                <WalletProvider>
                  {children}
                </WalletProvider>
              </AuthProvider>
            </ReactQueryProvider>
          </UserProvider>
        )}
      </body>
    </html>
  );
}
