import { createConfig, http } from 'wagmi'
import { polygon, polygonAmoy } from 'wagmi/chains'
import { injected, metaMask } from 'wagmi/connectors'

// Define the chains we support
export const chains = [polygonAmoy, polygon] as const

// Create wagmi config
export const config = createConfig({
  chains,
  connectors: [
    injected(),
    metaMask(),
  ],
  transports: {
    [polygonAmoy.id]: http('https://rpc-amoy.polygon.technology'),
    [polygon.id]: http('https://polygon-rpc.com'),
  },
})

declare module 'wagmi' {
  interface Register {
    config: typeof config
  }
}
