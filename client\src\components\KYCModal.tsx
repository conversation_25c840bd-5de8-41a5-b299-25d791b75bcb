'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useApiClient, type ClientProfile } from '@/lib/api-client';

interface KYCModalProps {
  onClose: () => void;
  existingProfile?: ClientProfile | null;
}

export function KYCModal({ onClose, existingProfile }: KYCModalProps) {
  const [currentStep, setCurrentStep] = useState(1);

  // Helper function to format date for input
  const formatDateForInput = (dateString: string | null | undefined) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toISOString().split('T')[0];
    } catch {
      return '';
    }
  };

  // Check if KYC can be edited (only if REJECTED or EXPIRED)
  const canEdit = !existingProfile?.kycStatus ||
                  existingProfile.kycStatus === 'REJECTED' ||
                  existingProfile.kycStatus === 'EXPIRED';

  const [formData, setFormData] = useState({
    firstName: existingProfile?.firstName || '',
    lastName: existingProfile?.lastName || '',
    gender: existingProfile?.gender || '',
    nationality: existingProfile?.nationality || '',
    birthday: formatDateForInput(existingProfile?.birthday),
    birthPlace: existingProfile?.birthPlace || '',
    identificationType: existingProfile?.identificationType || '',
    passportNumber: existingProfile?.passportNumber || '',
    idCardNumber: existingProfile?.idCardNumber || '',
    documentExpiration: formatDateForInput(existingProfile?.documentExpiration),
    phoneNumber: existingProfile?.phoneNumber || '',
    email: existingProfile?.email || '',
    occupation: existingProfile?.occupation || '',
    sectorOfActivity: existingProfile?.sectorOfActivity || '',
    pepStatus: existingProfile?.pepStatus || 'NOT_PEP',
    pepDetails: existingProfile?.pepDetails || '',
    street: existingProfile?.street || '',
    buildingNumber: existingProfile?.buildingNumber || '',
    city: existingProfile?.city || '',
    state: existingProfile?.state || '',
    country: existingProfile?.country || '',
    zipCode: existingProfile?.zipCode || '',
    sourceOfWealth: existingProfile?.sourceOfWealth || '',
    bankAccountNumber: existingProfile?.bankAccountNumber || '',
    sourceOfFunds: existingProfile?.sourceOfFunds || '',
    taxIdentificationNumber: existingProfile?.taxIdentificationNumber || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  const submitKYCMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      if (existingProfile) {
        return apiClient.updateClientProfile(data);
      } else {
        return apiClient.createClientProfile(data);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['client-profile'] });
      onClose();
    },
    onError: (error: any) => {
      setErrors({ general: error.message || 'Failed to submit KYC application' });
    },
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required field validations
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.nationality.trim()) newErrors.nationality = 'Nationality is required';
    if (!formData.birthday) newErrors.birthday = 'Birthday is required';
    if (!formData.birthPlace.trim()) newErrors.birthPlace = 'Birth place is required';
    if (!formData.identificationType) newErrors.identificationType = 'Identification type is required';
    if (!formData.documentExpiration) newErrors.documentExpiration = 'Document expiration is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.occupation.trim()) newErrors.occupation = 'Occupation is required';
    if (!formData.sectorOfActivity.trim()) newErrors.sectorOfActivity = 'Sector of activity is required';
    if (!formData.street.trim()) newErrors.street = 'Street is required';
    if (!formData.buildingNumber.trim()) newErrors.buildingNumber = 'Building number is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.country.trim()) newErrors.country = 'Country is required';
    if (!formData.zipCode.trim()) newErrors.zipCode = 'Zip code is required';
    if (!formData.sourceOfWealth.trim()) newErrors.sourceOfWealth = 'Source of wealth is required';
    if (!formData.bankAccountNumber.trim()) newErrors.bankAccountNumber = 'Bank account number is required';
    if (!formData.sourceOfFunds.trim()) newErrors.sourceOfFunds = 'Source of funds is required';
    if (!formData.taxIdentificationNumber.trim()) newErrors.taxIdentificationNumber = 'Tax identification number is required';

    // Validate birthday (must be 18-120 years old)
    if (formData.birthday) {
      const birthDate = new Date(formData.birthday);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 18 || age > 120) {
        newErrors.birthday = 'Must be between 18 and 120 years old';
      }
    }

    // Validate document expiration (must be in the future)
    if (formData.documentExpiration) {
      const expirationDate = new Date(formData.documentExpiration);
      const today = new Date();
      if (expirationDate <= today) {
        newErrors.documentExpiration = 'Document expiration must be in the future';
      }
    }

    // Validate identification document number based on type
    if (formData.identificationType === 'PASSPORT' && !formData.passportNumber.trim()) {
      newErrors.passportNumber = 'Passport number is required';
    }
    if ((formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE') && !formData.idCardNumber.trim()) {
      newErrors.idCardNumber = 'ID card number is required';
    }

    return newErrors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors({});
    submitKYCMutation.mutate(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Get status display info
  const getStatusInfo = () => {
    if (!existingProfile?.kycStatus) return null;

    const statusConfig = {
      PENDING: { color: 'yellow', text: 'Pending Review', description: 'Your KYC application is waiting to be reviewed.' },
      IN_REVIEW: { color: 'blue', text: 'Under Review', description: 'Your KYC application is currently being reviewed by our team.' },
      APPROVED: { color: 'green', text: 'Approved', description: 'Your KYC application has been approved.' },
      REJECTED: { color: 'red', text: 'Rejected', description: 'Your KYC application was rejected. Please review the feedback and resubmit.' },
      EXPIRED: { color: 'gray', text: 'Expired', description: 'Your KYC approval has expired. Please resubmit your application.' },
    };

    return statusConfig[existingProfile.kycStatus as keyof typeof statusConfig];
  };

  const statusInfo = getStatusInfo();

  // Helper function to get input styling
  const getInputClassName = (hasError = false) => {
    const baseClasses = "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500";
    if (!canEdit) {
      return `${baseClasses} bg-gray-50 border-gray-200 cursor-not-allowed`;
    }
    return `${baseClasses} ${hasError ? 'border-red-500' : 'border-gray-300'}`;
  };

  const validateCurrentStep = () => {
    const stepErrors: Record<string, string> = {};

    if (currentStep === 1) {
      // Step 1: Personal Information
      if (!formData.firstName.trim()) stepErrors.firstName = 'First name is required';
      if (!formData.lastName.trim()) stepErrors.lastName = 'Last name is required';
      if (!formData.gender) stepErrors.gender = 'Gender is required';
      if (!formData.nationality.trim()) stepErrors.nationality = 'Nationality is required';
      if (!formData.birthday) stepErrors.birthday = 'Birthday is required';
      if (!formData.birthPlace.trim()) stepErrors.birthPlace = 'Birth place is required';

      // Validate age if birthday is provided
      if (formData.birthday) {
        const birthDate = new Date(formData.birthday);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        if (age < 18 || age > 120) {
          stepErrors.birthday = 'Must be between 18 and 120 years old';
        }
      }
    } else if (currentStep === 2) {
      // Step 2: Identification & Contact
      if (!formData.identificationType) stepErrors.identificationType = 'Identification type is required';
      if (!formData.documentExpiration) stepErrors.documentExpiration = 'Document expiration is required';
      if (!formData.phoneNumber.trim()) stepErrors.phoneNumber = 'Phone number is required';

      // Validate document expiration
      if (formData.documentExpiration) {
        const expirationDate = new Date(formData.documentExpiration);
        const today = new Date();
        if (expirationDate <= today) {
          stepErrors.documentExpiration = 'Document expiration must be in the future';
        }
      }

      // Validate identification document number
      if (formData.identificationType === 'PASSPORT' && !formData.passportNumber.trim()) {
        stepErrors.passportNumber = 'Passport number is required';
      }
      if ((formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE') && !formData.idCardNumber.trim()) {
        stepErrors.idCardNumber = 'ID card number is required';
      }
    } else if (currentStep === 3) {
      // Step 3: Professional & Address
      if (!formData.occupation.trim()) stepErrors.occupation = 'Occupation is required';
      if (!formData.sectorOfActivity.trim()) stepErrors.sectorOfActivity = 'Sector of activity is required';
      if (!formData.street.trim()) stepErrors.street = 'Street is required';
      if (!formData.buildingNumber.trim()) stepErrors.buildingNumber = 'Building number is required';
      if (!formData.city.trim()) stepErrors.city = 'City is required';
      if (!formData.country.trim()) stepErrors.country = 'Country is required';
      if (!formData.zipCode.trim()) stepErrors.zipCode = 'Zip code is required';
    }

    return stepErrors;
  };

  const nextStep = () => {
    const stepErrors = validateCurrentStep();
    if (Object.keys(stepErrors).length > 0) {
      setErrors(stepErrors);
      return;
    }

    setErrors({});
    if (currentStep < 4) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const steps = [
    'Personal Information',
    'Identification & Contact',
    'Professional & Address',
    'Financial Information'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {existingProfile ? 'KYC Information' : 'Get Qualified - KYC Application'}
            </h2>
            <p className="text-gray-600 mt-1">
              {canEdit ? `Step ${currentStep} of ${steps.length}: ${steps[currentStep - 1]}` : 'View your KYC application details'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* KYC Status Display */}
        {statusInfo && (
          <div className={`px-6 py-4 border-b border-gray-200 ${
            statusInfo.color === 'green' ? 'bg-green-50' :
            statusInfo.color === 'red' ? 'bg-red-50' :
            statusInfo.color === 'blue' ? 'bg-blue-50' :
            statusInfo.color === 'yellow' ? 'bg-yellow-50' :
            'bg-gray-50'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  statusInfo.color === 'green' ? 'text-green-600 bg-green-100' :
                  statusInfo.color === 'red' ? 'text-red-600 bg-red-100' :
                  statusInfo.color === 'blue' ? 'text-blue-600 bg-blue-100' :
                  statusInfo.color === 'yellow' ? 'text-yellow-600 bg-yellow-100' :
                  'text-gray-600 bg-gray-100'
                }`}>
                  {statusInfo.text}
                </span>
                <span className="text-sm text-gray-600">{statusInfo.description}</span>
              </div>
              {existingProfile?.kycNotes && (
                <div className="text-sm text-gray-500">
                  <strong>Admin Notes:</strong> {existingProfile.kycNotes}
                </div>
              )}
            </div>
            {!canEdit && (
              <div className="mt-2 text-sm text-gray-600">
                <strong>Note:</strong> Your KYC application cannot be edited in its current status.
                {existingProfile?.kycStatus === 'APPROVED' && ' Your application has been approved.'}
                {existingProfile?.kycStatus === 'PENDING' && ' Your application is pending review.'}
                {existingProfile?.kycStatus === 'IN_REVIEW' && ' Your application is currently under review.'}
              </div>
            )}
          </div>
        )}

        {/* Progress Bar - Only show when editing */}
        {canEdit && (
          <div className="px-6 py-4 bg-gray-50">
            <div className="flex items-center">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center flex-1">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index + 1 <= currentStep
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>
                  <div className={`flex-1 h-1 mx-2 ${
                    index + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`}></div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Form Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {errors.general && (
            <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {errors.general}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Step 1: Personal Information */}
            {(canEdit ? currentStep === 1 : true) && (
              <div className="space-y-4">
                {!canEdit && <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        !canEdit ? 'bg-gray-50 border-gray-200' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        !canEdit ? 'bg-gray-50 border-gray-200' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Gender *
                    </label>
                    <select
                      name="gender"
                      value={formData.gender}
                      onChange={handleChange}
                      required={canEdit}
                      disabled={!canEdit}
                      className={getInputClassName()}
                    >
                      <option value="">Select Gender</option>
                      <option value="MALE">Male</option>
                      <option value="FEMALE">Female</option>
                      <option value="OTHER">Other</option>
                      <option value="PREFER_NOT_TO_SAY">Prefer not to say</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nationality *
                    </label>
                    <input
                      type="text"
                      name="nationality"
                      value={formData.nationality}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Birthday *
                    </label>
                    <input
                      type="date"
                      name="birthday"
                      value={formData.birthday}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName(!!errors.birthday)}
                    />
                    {errors.birthday && (
                      <p className="mt-1 text-sm text-red-600">{errors.birthday}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Birth Place *
                    </label>
                    <input
                      type="text"
                      name="birthPlace"
                      value={formData.birthPlace}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Identification & Contact */}
            {(canEdit ? currentStep === 2 : true) && (
              <div className="space-y-4">
                {!canEdit && <h3 className="text-lg font-semibold text-gray-900 mb-4">Identification & Contact Information</h3>}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Identification Type *
                    </label>
                    <select
                      name="identificationType"
                      value={formData.identificationType}
                      onChange={handleChange}
                      required={canEdit}
                      disabled={!canEdit}
                      className={getInputClassName()}
                    >
                      <option value="">Select Type</option>
                      <option value="PASSPORT">Passport</option>
                      <option value="ID_CARD">ID Card</option>
                      <option value="DRIVERS_LICENSE">Driver's License</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>

                  {formData.identificationType === 'PASSPORT' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Passport Number *
                      </label>
                      <input
                        type="text"
                        name="passportNumber"
                        value={formData.passportNumber}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName(!!errors.passportNumber)}
                      />
                      {errors.passportNumber && (
                        <p className="mt-1 text-sm text-red-600">{errors.passportNumber}</p>
                      )}
                    </div>
                  )}

                  {(formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ID Card Number *
                      </label>
                      <input
                        type="text"
                        name="idCardNumber"
                        value={formData.idCardNumber}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName(!!errors.idCardNumber)}
                      />
                      {errors.idCardNumber && (
                        <p className="mt-1 text-sm text-red-600">{errors.idCardNumber}</p>
                      )}
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Document Expiration *
                    </label>
                    <input
                      type="date"
                      name="documentExpiration"
                      value={formData.documentExpiration}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName(!!errors.documentExpiration)}
                    />
                    {errors.documentExpiration && (
                      <p className="mt-1 text-sm text-red-600">{errors.documentExpiration}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Professional & Address */}
            {(canEdit ? currentStep === 3 : true) && (
              <div className="space-y-6">
                {/* Professional Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Professional Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Occupation *
                      </label>
                      <input
                        type="text"
                        name="occupation"
                        value={formData.occupation}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sector of Activity *
                      </label>
                      <input
                        type="text"
                        name="sectorOfActivity"
                        value={formData.sectorOfActivity}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PEP Status *
                      </label>
                      <select
                        name="pepStatus"
                        value={formData.pepStatus}
                        onChange={handleChange}
                        required={canEdit}
                        disabled={!canEdit}
                        className={getInputClassName()}
                      >
                        <option value="NOT_PEP">Not PEP</option>
                        <option value="DOMESTIC_PEP">Domestic PEP</option>
                        <option value="FOREIGN_PEP">Foreign PEP</option>
                        <option value="INTERNATIONAL_ORG_PEP">International Organization PEP</option>
                        <option value="FAMILY_MEMBER">Family Member</option>
                        <option value="CLOSE_ASSOCIATE">Close Associate</option>
                      </select>
                    </div>

                    {formData.pepStatus !== 'NOT_PEP' && (
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          PEP Details
                        </label>
                        <textarea
                          name="pepDetails"
                          value={formData.pepDetails}
                          onChange={handleChange}
                          rows={3}
                          readOnly={!canEdit}
                          className={getInputClassName()}
                          placeholder="Please provide more details on your PEP status"
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Address Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Address Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Street *
                      </label>
                      <input
                        type="text"
                        name="street"
                        value={formData.street}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Building Number *
                      </label>
                      <input
                        type="text"
                        name="buildingNumber"
                        value={formData.buildingNumber}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City *
                      </label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        State
                      </label>
                      <input
                        type="text"
                        name="state"
                        value={formData.state}
                        onChange={handleChange}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Country *
                      </label>
                      <input
                        type="text"
                        name="country"
                        value={formData.country}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Zip Code *
                      </label>
                      <input
                        type="text"
                        name="zipCode"
                        value={formData.zipCode}
                        onChange={handleChange}
                        required={canEdit}
                        readOnly={!canEdit}
                        className={getInputClassName()}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Financial Information */}
            {(canEdit ? currentStep === 4 : true) && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Source of Wealth *
                    </label>
                    <input
                      type="text"
                      name="sourceOfWealth"
                      value={formData.sourceOfWealth}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bank Account Number *
                    </label>
                    <input
                      type="text"
                      name="bankAccountNumber"
                      value={formData.bankAccountNumber}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Source of Funds *
                    </label>
                    <input
                      type="text"
                      name="sourceOfFunds"
                      value={formData.sourceOfFunds}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tax Identification Number *
                    </label>
                    <input
                      type="text"
                      name="taxIdentificationNumber"
                      value={formData.taxIdentificationNumber}
                      onChange={handleChange}
                      required={canEdit}
                      readOnly={!canEdit}
                      className={getInputClassName()}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
              {canEdit ? (
                <>
                  <button
                    type="button"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>

                    {currentStep < steps.length ? (
                      <button
                        type="button"
                        onClick={nextStep}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        Next
                      </button>
                    ) : (
                      <button
                        type="submit"
                        disabled={submitKYCMutation.isPending}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                      >
                        {submitKYCMutation.isPending ? 'Submitting...' : 'Submit Application'}
                      </button>
                    )}
                  </div>
                </>
              ) : (
                <div className="flex justify-end w-full space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Close
                  </button>
                  {(existingProfile?.kycStatus === 'REJECTED' || existingProfile?.kycStatus === 'EXPIRED') && (
                    <button
                      type="button"
                      onClick={() => window.location.reload()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Edit Application
                    </button>
                  )}
                </div>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
