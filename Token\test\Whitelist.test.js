const { expect } = require("chai");
const { ethers } = require("hardhat");
const TestUtils = require("./utils/TestUtils");

describe("Whitelist", function () {
  let whitelist, owner, agent, user1, user2, user3;

  beforeEach(async function () {
    // Deploy contracts and get signers
    const deployment = await TestUtils.deployContracts();
    whitelist = deployment.whitelist;
    owner = deployment.owner;
    agent = deployment.agent;
    user1 = deployment.user1;
    user2 = deployment.user2;
    user3 = deployment.user3;
  });

  describe("Role Management", function () {
    it("should set the right admin", async function () {
      const adminRole = await whitelist.DEFAULT_ADMIN_ROLE();
      expect(await whitelist.hasRole(adminRole, owner.address)).to.equal(true);
    });

    it("should correctly assign and revoke agent role", async function () {
      const agentRole = await whitelist.AGENT_ROLE();
      
      // Agent should have the AGENT_ROLE
      expect(await whitelist.hasRole(agentRole, agent.address)).to.equal(true);
      
      // Revoke AGENT_ROLE from agent
      await whitelist.connect(owner).removeAgent(agent.address);
      expect(await whitelist.hasRole(agentRole, agent.address)).to.equal(false);
      
      // Add AGENT_ROLE to user1
      await whitelist.connect(owner).addAgent(user1.address);
      expect(await whitelist.hasRole(agentRole, user1.address)).to.equal(true);
    });

    it("should revert when non-admin tries to add an agent", async function () {
      await expect(whitelist.connect(user1).addAgent(user2.address))
        .to.be.revertedWithCustomError(whitelist, "AccessControlUnauthorizedAccount")
        .withArgs(user1.address, await whitelist.DEFAULT_ADMIN_ROLE());
    });
  });

  describe("Whitelist Management", function () {
    it("should add an address to the whitelist", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
    });

    it("should remove an address from the whitelist", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
      
      await whitelist.connect(agent).removeFromWhitelist(user1.address);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(false);
    });

    it("should revert when trying to add an already whitelisted address", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      await expect(whitelist.connect(agent).addToWhitelist(user1.address))
        .to.be.revertedWith("Whitelist: address already whitelisted");
    });

    it("should revert when trying to remove an address not on the whitelist", async function () {
      await expect(whitelist.connect(agent).removeFromWhitelist(user1.address))
        .to.be.revertedWith("Whitelist: address not whitelisted");
    });

    it("should revert when non-agent tries to manage whitelist", async function () {
      await expect(whitelist.connect(user1).addToWhitelist(user2.address))
        .to.be.revertedWithCustomError(whitelist, "AccessControlUnauthorizedAccount")
        .withArgs(user1.address, await whitelist.AGENT_ROLE());
    });

    it("should perform batch operations on the whitelist", async function () {
      // Batch add to whitelist
      await whitelist.connect(agent).batchAddToWhitelist([user1.address, user2.address, user3.address]);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
      expect(await whitelist.isWhitelisted(user2.address)).to.equal(true);
      expect(await whitelist.isWhitelisted(user3.address)).to.equal(true);
      
      // Batch remove from whitelist
      await whitelist.connect(agent).batchRemoveFromWhitelist([user1.address, user3.address]);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(false);
      expect(await whitelist.isWhitelisted(user2.address)).to.equal(true);
      expect(await whitelist.isWhitelisted(user3.address)).to.equal(false);
    });
  });

  describe("Freeze Management", function () {
    beforeEach(async function () {
      // Add users to whitelist
      await whitelist.connect(agent).batchAddToWhitelist([user1.address, user2.address, user3.address]);
    });

    it("should freeze an address", async function () {
      await whitelist.connect(agent).freezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
    });

    it("should unfreeze an address", async function () {
      await whitelist.connect(agent).freezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
      
      await whitelist.connect(agent).unfreezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(false);
    });

    it("should revert when trying to freeze an already frozen address", async function () {
      await whitelist.connect(agent).freezeAddress(user1.address);
      await expect(whitelist.connect(agent).freezeAddress(user1.address))
        .to.be.revertedWith("Whitelist: address already frozen");
    });

    it("should revert when trying to unfreeze an address not frozen", async function () {
      await expect(whitelist.connect(agent).unfreezeAddress(user1.address))
        .to.be.revertedWith("Whitelist: address not frozen");
    });

    it("should revert when non-agent tries to freeze or unfreeze", async function () {
      await expect(whitelist.connect(user1).freezeAddress(user2.address))
        .to.be.revertedWithCustomError(whitelist, "AccessControlUnauthorizedAccount")
        .withArgs(user1.address, await whitelist.AGENT_ROLE());
    });

    it("should perform batch operations for freezing and unfreezing", async function () {
      // Batch freeze
      await whitelist.connect(agent).batchFreezeAddresses([user1.address, user2.address, user3.address]);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
      expect(await whitelist.isFrozen(user2.address)).to.equal(true);
      expect(await whitelist.isFrozen(user3.address)).to.equal(true);
      
      // Batch unfreeze
      await whitelist.connect(agent).batchUnfreezeAddresses([user1.address, user3.address]);
      expect(await whitelist.isFrozen(user1.address)).to.equal(false);
      expect(await whitelist.isFrozen(user2.address)).to.equal(true);
      expect(await whitelist.isFrozen(user3.address)).to.equal(false);
    });
  });

  describe("Upgradeability", function () {
    it("should allow admin to authorize an upgrade", async function () {
      // Skip this test as upgradeProxy is problematic with viaIR
      this.skip();
    });

    it("should revert when non-admin tries to authorize an upgrade", async function () {
      // Skip this test as upgradeProxy is problematic with viaIR
      this.skip();
    });
  });
});