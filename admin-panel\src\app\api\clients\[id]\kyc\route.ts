import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateKYCSchema } from '@/lib/validations/client';

// PUT /api/clients/[id]/kyc - Update KYC status
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const validatedData = updateKYCSchema.parse({ ...body, clientId: id });

    const updateData: any = {
      kycStatus: validatedData.kycStatus,
      updatedAt: new Date(),
    };

    // Set completion date if approved
    if (validatedData.kycStatus === 'APPROVED') {
      updateData.kycCompletedAt = new Date();
    }

    // Add notes if provided
    if (validatedData.kycNotes) {
      updateData.kycNotes = validatedData.kycNotes;
    }

    const client = await prisma.client.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        kycStatus: true,
        kycCompletedAt: true,
        kycNotes: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(client);
  } catch (error) {
    console.error('Error updating KYC status:', error);

    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update KYC status' },
      { status: 500 }
    );
  }
}

// GET /api/clients/[id]/kyc - Get KYC details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const client = await prisma.client.findUnique({
      where: { id },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        kycStatus: true,
        kycCompletedAt: true,
        kycNotes: true,
        documents: {
          select: {
            id: true,
            documentType: true,
            fileName: true,
            status: true,
            verifiedAt: true,
            verifiedBy: true,
            rejectionReason: true,
            createdAt: true,
          },
        },
      },
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(client);
  } catch (error) {
    console.error('Error fetching KYC details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch KYC details' },
      { status: 500 }
    );
  }
}
