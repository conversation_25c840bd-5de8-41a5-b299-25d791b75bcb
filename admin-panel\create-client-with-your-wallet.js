// Create client with your actual wallet address
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createClientWithYourWallet() {
  console.log('=== Creating Client with Your Wallet ===');
  
  const yourWallet = '******************************************';
  
  try {
    // Create a client with your wallet address
    const client = await prisma.client.create({
      data: {
        firstName: 'Test',
        lastName: 'User',
        gender: 'MALE',
        nationality: 'US',
        birthday: new Date('1990-01-01'),
        birthPlace: 'New York',
        identificationType: 'PASSPORT',
        passportNumber: 'US987654321',
        documentExpiration: new Date('2030-01-01'),
        phoneNumber: '+**********',
        email: '<EMAIL>', // You can change this to your actual email
        occupation: 'Developer',
        sectorOfActivity: 'Technology',
        pepStatus: 'NOT_PEP',
        street: '456 Test St',
        buildingNumber: '456',
        city: 'Test City',
        state: 'CA',
        country: 'United States',
        zipCode: '90210',
        sourceOfWealth: 'Employment',
        bankAccountNumber: 'US987654321098',
        sourceOfFunds: 'Salary',
        taxIdentificationNumber: 'US987654321',
        kycStatus: 'APPROVED',
        kycCompletedAt: new Date(),
        walletAddress: yourWallet,
        walletSignature: '0xabcdef1234567890...',
        walletVerifiedAt: new Date(),
        isWhitelisted: true,
        whitelistedAt: new Date(),
        agreementAccepted: true,
        agreementAcceptedAt: new Date()
      }
    });

    console.log('✅ Client created successfully:');
    console.log(`   ID: ${client.id}`);
    console.log(`   Email: ${client.email}`);
    console.log(`   Wallet: ${client.walletAddress}`);
    console.log(`   KYC Status: ${client.kycStatus}`);
    console.log(`   Global Whitelisted: ${client.isWhitelisted}`);

    return client;
  } catch (error) {
    console.error('Error creating client:', error);
    throw error;
  }
}

async function addTokenApprovals(clientId) {
  console.log('\n=== Adding Token Approvals ===');
  
  try {
    // Get all tokens from the database
    const tokens = await prisma.token.findMany({
      select: { id: true, name: true, symbol: true, address: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens found in database');
      return;
    }

    console.log(`Found ${tokens.length} tokens, creating approvals...`);

    // Whitelist specific tokens that you mentioned you're already whitelisted for
    // Let's whitelist the main tokens: AUG019, AUG01Z, and a few others
    const tokensToWhitelist = ['AUG019', 'AUG01Z', 'TZD', 'EURT', 'ETHF']; // You can modify this list

    for (const token of tokens) {
      const shouldWhitelist = tokensToWhitelist.includes(token.symbol);

      const approval = await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: clientId,
          approvalStatus: shouldWhitelist ? 'APPROVED' : 'PENDING',
          kycApproved: true,
          whitelistApproved: shouldWhitelist,
          approvedBy: shouldWhitelist ? '<EMAIL>' : null,
          approvedAt: shouldWhitelist ? new Date() : null,
          notes: shouldWhitelist ? 'Whitelisted for testing' : 'Pending approval'
        }
      });

      const status = shouldWhitelist ? '✅ WHITELISTED' : '⏳ PENDING';
      console.log(`   ${token.symbol.padEnd(10)} | ${status}`);
    }

    const whitelistedCount = tokensToWhitelist.length;
    console.log(`\n✅ Token approvals created: ${whitelistedCount} whitelisted, ${tokens.length - whitelistedCount} pending`);
    
  } catch (error) {
    console.error('Error creating token approvals:', error);
    throw error;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Whitelist API Response:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${symbol.padEnd(10)} | ${status}`);
      });
      
      const whitelistedCount = data.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nSummary: ${whitelistedCount}/${data.tokens.length} tokens whitelisted for your wallet`);
      
    } else {
      console.log('❌ Whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing whitelist API:', error);
  }
}

async function main() {
  const yourWallet = '******************************************';
  
  try {
    // Create client with your wallet
    const client = await createClientWithYourWallet();
    
    // Add token approvals
    await addTokenApprovals(client.id);
    
    // Test the whitelist API
    await testWhitelistAPI(yourWallet);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\n🎯 READY TO TEST WHITELIST TAGS:');
    console.log('1. Open client app: http://localhost:3003/offers');
    console.log('2. Login with email: <EMAIL> (or update the email above)');
    console.log('3. Connect wallet: ******************************************');
    console.log('4. You should see green WHITELISTED tags on approved tokens');
    
    console.log('\n📋 WHITELISTED TOKENS:');
    console.log('   - AUG019 (Augment_019)');
    console.log('   - AUG01Z (Augment_01z)');
    console.log('   - TZD (Test Zero Decimals Token)');
    console.log('   - EURT (European Real Estate Token)');
    console.log('   - ETHF (Ethereum DeFi Fund)');
    
  } catch (error) {
    console.error('Error in main:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
