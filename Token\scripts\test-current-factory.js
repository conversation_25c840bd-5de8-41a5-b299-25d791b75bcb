const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Testing current factory for enumeration capabilities...");
  
  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  // Current factory address from config
  const FACTORY_ADDRESS = "******************************************";
  
  try {
    // Connect to the current factory
    const factory = await ethers.getContractAt("SecurityTokenFactory", FACTORY_ADDRESS);
    console.log("✅ Connected to factory at:", FACTORY_ADDRESS);

    // Test basic factory functions
    console.log("\n📊 Testing basic factory functions...");
    
    try {
      const tokenImpl = await factory.securityTokenImplementation();
      const whitelistImpl = await factory.whitelistImplementation();
      const whitelistKYCImpl = await factory.whitelistWithKYCImplementation();
      
      console.log("✅ Implementation addresses:");
      console.log(`   SecurityToken: ${tokenImpl}`);
      console.log(`   Whitelist: ${whitelistImpl}`);
      console.log(`   WhitelistWithKYC: ${whitelistKYCImpl}`);
    } catch (error) {
      console.log("❌ Error reading implementations:", error.message);
    }

    // Test enumeration functions
    console.log("\n🔍 Testing enumeration functions...");
    
    try {
      const tokenCount = await factory.getTokenCount();
      console.log(`✅ getTokenCount() works! Count: ${tokenCount}`);
      
      if (tokenCount > 0) {
        console.log("\n📋 Getting deployed tokens...");
        
        // Test getAllDeployedTokens
        try {
          const allTokens = await factory.getAllDeployedTokens();
          console.log(`✅ getAllDeployedTokens() works! Found ${allTokens.length} tokens:`);
          
          for (let i = 0; i < Math.min(allTokens.length, 5); i++) {
            console.log(`   ${i + 1}. ${allTokens[i]}`);
            
            // Try to get token details
            try {
              const token = await ethers.getContractAt("SecurityToken", allTokens[i]);
              const name = await token.name();
              const symbol = await token.symbol();
              const decimals = await token.decimals();
              console.log(`      ${name} (${symbol}) - ${decimals} decimals`);
            } catch (error) {
              console.log(`      ⚠️ Could not read token details: ${error.message}`);
            }
          }
          
          if (allTokens.length > 5) {
            console.log(`   ... and ${allTokens.length - 5} more tokens`);
          }
        } catch (error) {
          console.log("❌ getAllDeployedTokens() failed:", error.message);
        }
        
        // Test individual token access
        try {
          const firstToken = await factory.getDeployedToken(0);
          console.log(`✅ getDeployedToken(0) works: ${firstToken}`);
        } catch (error) {
          console.log("❌ getDeployedToken() failed:", error.message);
        }
        
      } else {
        console.log("📝 No tokens deployed yet");
        
        // Test that getAllDeployedTokens returns empty array
        try {
          const allTokens = await factory.getAllDeployedTokens();
          if (allTokens.length === 0) {
            console.log("✅ getAllDeployedTokens() correctly returns empty array");
          } else {
            console.log("❌ getAllDeployedTokens() should return empty array");
          }
        } catch (error) {
          console.log("❌ getAllDeployedTokens() failed:", error.message);
        }
      }
      
    } catch (error) {
      console.log("❌ Enumeration functions not available:", error.message);
      console.log("🔄 This factory does not support enumeration. Need to deploy updated factory.");
    }

    // Test role checking
    console.log("\n👤 Testing role permissions...");
    try {
      const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
      const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
      
      const isAdmin = await factory.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
      const isDeployer = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
      
      console.log("✅ Role status:");
      console.log(`   Is Admin: ${isAdmin}`);
      console.log(`   Is Deployer: ${isDeployer}`);
      
      if (!isDeployer) {
        console.log("⚠️ Warning: Current account is not a deployer. Cannot create tokens.");
      }
    } catch (error) {
      console.log("❌ Error checking roles:", error.message);
    }

    // Test token creation capability (if deployer)
    console.log("\n🏭 Testing token creation capability...");
    try {
      const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
      const isDeployer = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
      
      if (isDeployer) {
        console.log("✅ Account has deployer role - can create tokens");
        console.log("💡 You can create a test token to verify enumeration works");
      } else {
        console.log("⚠️ Account does not have deployer role");
        console.log("💡 To test token creation, grant deployer role first");
      }
    } catch (error) {
      console.log("❌ Error checking deployer capability:", error.message);
    }

    console.log("\n🎯 Summary:");
    console.log("=".repeat(50));
    
    // Check if enumeration is supported
    try {
      await factory.getTokenCount();
      console.log("✅ Factory supports enumeration - dashboard will work!");
      console.log("🚀 You can now use the admin panel to automatically load tokens");
    } catch (error) {
      console.log("❌ Factory does not support enumeration");
      console.log("🔄 Need to deploy updated factory for automatic token loading");
      console.log("📝 Current dashboard will require manual token addition");
    }

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
