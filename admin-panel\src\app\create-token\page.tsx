'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { tokenTypes, getContractAddresses, getNetworkConfig } from '../../config';
import SecurityTokenFactoryABI from '../../contracts/SecurityTokenFactory.json';
import SecurityTokenABI from '../../contracts/SecurityToken.json';
import {
  TokenForm,
  TokenDeployment,
  NetworkBanner,
  StatusNotification,
  FactoryInfo
} from './components';
import { useTokenDeployment } from './hooks';
import { DeploymentStep, DeployedToken, TokenFormData } from './types';

/**
 * CreateTokenPage Component
 *
 * Main component for the token creation page. Manages the overall flow of token
 * creation including form handling, deployment process, and result display.
 */
export default function CreateTokenPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultNetwork = searchParams.get('network') || 'amoy';

  // State Management
  const [network, setNetwork] = useState(defaultNetwork);
  const [factoryAddress, setFactoryAddress] = useState<string>('');
  const [hasDeployerRole, setHasDeployerRole] = useState<boolean>(false);
  const [kycSupported, setKYCSupported] = useState(true);
  const [formData, setFormData] = useState<TokenFormData>({
    name: '',
    symbol: '',
    decimals: 18, // Current factory uses 18 decimals by default
    maxSupply: '1000000',
    ownerAddress: '',
    tokenPrice: '10',
    currency: 'USD',
    tokenType: 'equity',
    bonusTiers: 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%',
    enableKYC: false,
    tokenImageUrl: '',
    selectedClaims: ['10101010000001', '10101010000004'], // Default to KYC_VERIFICATION and GENERAL_QUALIFICATION (Tokeny-style Topic IDs)
    issuerCountry: 'US' // Default country
  });

  // Use the custom hook for token deployment logic
  const {
    isSubmitting,
    error,
    success,
    deployedToken,
    transactionHash,
    deploymentStep,
    deployToken
  } = useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported);

  useEffect(() => {
    // Get factory address for current network
    const addresses = getContractAddresses(network);
    setFactoryAddress(addresses.factory || '');

    // Auto-fill owner address from connected wallet and check permissions
    if (window.ethereum) {
      initWallet();
    }
  }, [network, factoryAddress]);

  /**
   * Initialize wallet and check permissions
   */
  const initWallet = async () => {
    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const accounts = await provider.listAccounts();
      if (accounts.length > 0) {
        const userAddress = accounts[0].address;
        setFormData((prev: TokenFormData) => ({
          ...prev,
          ownerAddress: userAddress
        }));

        // Check if user has DEPLOYER_ROLE and if KYC is supported
        if (factoryAddress) {
          checkDeployerPermissions(provider, userAddress);
        }
      }
    } catch (err) {
      console.error("Error initializing wallet:", err);
    }
  };

  /**
   * Check if the user has DEPLOYER_ROLE and if KYC is supported
   */
  const checkDeployerPermissions = async (provider: ethers.Provider, userAddress: string) => {
    try {
      const factory = new ethers.Contract(
        factoryAddress,
        SecurityTokenFactoryABI.abi,
        provider
      );

      // Check if user has DEPLOYER_ROLE
      const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
      const hasRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);
      setHasDeployerRole(hasRole);

      if (!hasRole) {
        console.warn("Connected wallet does not have DEPLOYER_ROLE");
      }

      // Check if KYC is supported
      try {
        await factory.whitelistWithKYCImplementation();

        const hasKYCFunction = factory.interface.fragments.some(
          fragment => fragment.type === "function" &&
                    'name' in fragment &&
                    fragment.name === "deploySecurityTokenWithOptions"
        );

        setKYCSupported(hasKYCFunction);
      } catch (err) {
        console.warn("KYC functionality not supported in this factory contract");
        setKYCSupported(false);
      }
    } catch (err) {
      console.error("Error checking deployer role:", err);
    }
  };

  /**
   * Handle input changes in the form
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    let processedValue: any = value;

    // Convert decimals to number
    if (name === 'decimals') {
      processedValue = parseInt(value, 10);
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }

    setFormData({
      ...formData,
      [name]: processedValue
    });
  };

  /**
   * Handle network change
   */
  const handleNetworkChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setNetwork(e.target.value);
    console.log(`Switched to network: ${e.target.value}`);
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await deployToken(formData);
  };

  /**
   * Add token manually to the dashboard
   */
  const addTokenManually = () => {
    router.push(`/?add=${formData.symbol}`);
  };

  /**
   * Get the network label for display
   */
  const getNetworkLabel = (networkKey: string) => {
    switch(networkKey) {
      case 'amoy': return 'Amoy Testnet';
      case 'polygon': return 'Polygon Mainnet';
      default: return networkKey;
    }
  };

  /**
   * Get block explorer URL for token or address
   */
  const getBlockExplorerUrl = (network: string, address: string) => {
    if (network === 'amoy') {
      return `https://www.oklink.com/amoy/address/${address}`;
    } else if (network === 'polygon') {
      return `https://polygonscan.com/address/${address}`;
    }
    return '#';
  };

  /**
   * Get transaction explorer URL
   */
  const getTransactionExplorerUrl = (network: string, txHash: string) => {
    if (network === 'amoy') {
      return `https://www.oklink.com/amoy/tx/${txHash}`;
    } else if (network === 'polygon') {
      return `https://polygonscan.com/tx/${txHash}`;
    }
    return '#';
  };

  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link href="/" className="text-blue-600 hover:text-blue-800 mr-4">
          &larr; Back to Dashboard
        </Link>
        <h1 className="text-3xl font-bold">Create New Security Token</h1>
        <span className="ml-4 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
          {getNetworkLabel(network)}
        </span>
      </div>

      {/* Advanced Features Notice */}
      <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-green-600 text-xl">🎉</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">
              Advanced Transfer Controls Now Available!
            </h3>
            <div className="mt-1 text-sm text-green-700">
              <p>All new tokens automatically include advanced transfer control features:</p>
              <ul className="mt-1 list-disc list-inside space-y-1">
                <li><strong>Conditional Transfers:</strong> Require approval for all transfers</li>
                <li><strong>Transfer Whitelisting:</strong> Additional access control layer</li>
                <li><strong>Transfer Fees:</strong> Configurable percentage-based fees</li>
              </ul>
              <p className="mt-2 text-xs">
                💡 After creating your token, visit the <strong>Transfer Controls</strong> page to configure these features.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Factory Information Banner */}
      <FactoryInfo
        factoryAddress={factoryAddress}
        network={network}
        hasDeployerRole={hasDeployerRole}
      />

      {/* Network-specific Banner */}
      <NetworkBanner network={network} />

      {/* Error Notification */}
      {error && (
        <StatusNotification
          type="error"
          message={error}
          deploymentStep={deploymentStep}
          tokenSymbol={formData.symbol}
          onAddManually={addTokenManually}
        />
      )}

      {/* Success Notification - Token Deployment Result */}
      {deployedToken && (
        <TokenDeployment
          deployedToken={deployedToken}
          transactionHash={transactionHash}
          network={network}
          getBlockExplorerUrl={getBlockExplorerUrl}
          getTransactionExplorerUrl={getTransactionExplorerUrl}
        />
      )}

      {/* Token Creation Form */}
      {!deployedToken && (
        <TokenForm
          formData={formData}
          handleInputChange={handleInputChange}
          handleNetworkChange={handleNetworkChange}
          handleSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          network={network}
          getNetworkLabel={getNetworkLabel}
          deploymentStep={deploymentStep}
          kycSupported={kycSupported}
        />
      )}
    </div>
  );
}