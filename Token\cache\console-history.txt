.exit
console.log("Is admin whitelisted:", await whitelist.isWhitelisted("******************************************"));
console.log("Testing whitelist functions...");
console.log("Whitelist owner:", await whitelist.owner());
const whitelist = await ethers.getContractAt("Whitelist", whitelistAddress);
console.log("Whitelist address:", whitelistAddress);
const whitelistAddress = await token.identityRegistry();
const token = await ethers.getContractAt("SecurityToken", "******************************************");