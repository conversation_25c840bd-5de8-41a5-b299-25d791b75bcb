const { ethers, upgrades } = require("hardhat");

/**
 * Test utility functions
 */
const TestUtils = {
  /**
   * Deploy the test contracts
   * @returns {Promise<Object>} The deployed contracts
   */
  async deployContracts() {
    const [owner, agent, user1, user2, user3] = await ethers.getSigners();
    
    // Deploy Whitelist implementation
    const Whitelist = await ethers.getContractFactory("Whitelist");
    const whitelist = await upgrades.deployProxy(Whitelist, [owner.address], {
      kind: "uups",
    });
    await whitelist.waitForDeployment();

    // Add agent role to agent
    await whitelist.addAgent(agent.address);
    
    // Deploy SecurityToken implementation
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const token = await upgrades.deployProxy(
      SecurityToken,
      [
        "Security Token", 
        "SEC", 
        ethers.parseEther("1000000"), // 1M tokens
        await whitelist.getAddress(),
        owner.address,
        "10 USD",
        "Tier 1: 5%, Tier 2: 10%"
      ],
      {
        kind: "uups",
      }
    );
    await token.waitForDeployment();
    
    // Deploy SecurityTokenFactory
    const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    const factory = await SecurityTokenFactory.deploy(owner.address);
    await factory.waitForDeployment();
    
    return {
      whitelist,
      token,
      factory,
      owner,
      agent,
      user1,
      user2,
      user3
    };
  },
  
  /**
   * Deploy a new token and whitelist using the factory
   * @param {Contract} factory The factory contract
   * @param {Object} params The token parameters
   * @param {Signer} deployer The deployer signer
   * @returns {Promise<Object>} The deployed token and whitelist
   */
  async deployFromFactory(factory, params, deployer) {
    const deployTx = await factory.connect(deployer).deploySecurityToken(
      params.name,
      params.symbol,
      params.maxSupply,
      params.admin,
      params.tokenPrice,
      params.bonusTiers
    );
    
    const receipt = await deployTx.wait();
    const event = receipt.logs.find(
      log => log.fragment && log.fragment.name === 'TokenDeployed'
    );
    
    const tokenAddress = event.args.tokenAddress;
    const whitelistAddress = event.args.whitelistAddress;
    
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const token = await SecurityToken.attach(tokenAddress);
    
    const Whitelist = await ethers.getContractFactory("Whitelist");
    const whitelist = await Whitelist.attach(whitelistAddress);
    
    return { token, whitelist };
  }
};

module.exports = TestUtils;