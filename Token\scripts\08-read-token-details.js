const hre = require("hardhat");

async function main() {
  try {
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Reading token details with the account:", deployer.address);
    console.log("Network:", networkName);

    // Get token address from environment
    const tokenAddress = process.env.TOKEN_ADDRESS;
    if (!tokenAddress) {
      console.error("Please set TOKEN_ADDRESS environment variable");
      process.exit(1);
    }

    console.log("Token address:", tokenAddress);

    // Connect to the token contract
    const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);

    console.log("\n=== BASIC TOKEN INFORMATION ===");

    try {
      const name = await token.name();
      console.log("Name:", name);
    } catch (err) {
      console.log("Name: Could not read");
    }

    try {
      const symbol = await token.symbol();
      console.log("Symbol:", symbol);
    } catch (err) {
      console.log("Symbol: Could not read");
    }

    try {
      const decimals = await token.decimals();
      console.log("Decimals:", decimals.toString());
    } catch (err) {
      console.log("Decimals: Could not read");
    }

    try {
      const totalSupply = await token.totalSupply();
      console.log("Total Supply:", totalSupply.toString());
    } catch (err) {
      console.log("Total Supply: Could not read");
    }

    try {
      const maxSupply = await token.maxSupply();
      console.log("Max Supply:", maxSupply.toString());
    } catch (err) {
      console.log("Max Supply: Could not read");
    }

    console.log("\n=== ADDITIONAL TOKEN INFORMATION ===");

    try {
      const tokenDetails = await token.tokenDetails();
      console.log("Token Details:", tokenDetails);
    } catch (err) {
      console.log("Token Details: Could not read -", err.message);
    }

    try {
      const tokenPrice = await token.tokenPrice();
      console.log("Token Price:", tokenPrice);
    } catch (err) {
      console.log("Token Price: Could not read -", err.message);
    }

    try {
      const bonusTiers = await token.bonusTiers();
      console.log("Bonus Tiers:", bonusTiers);
    } catch (err) {
      console.log("Bonus Tiers: Could not read -", err.message);
    }

    try {
      const version = await token.version();
      console.log("Version:", version);
    } catch (err) {
      console.log("Version: Could not read -", err.message);
    }

    try {
      const paused = await token.paused();
      console.log("Paused:", paused);
    } catch (err) {
      console.log("Paused: Could not read -", err.message);
    }

    console.log("\n=== WHITELIST INFORMATION ===");

    try {
      const whitelistAddress = await token.identityRegistry();
      console.log("Whitelist Address:", whitelistAddress);

      if (whitelistAddress !== hre.ethers.ZeroAddress) {
        console.log("KYC/Whitelist: Enabled");
      } else {
        console.log("KYC/Whitelist: Disabled");
      }
    } catch (err) {
      console.log("Whitelist Address: Could not read -", err.message);
    }

    console.log("\n=== ADMIN/ROLE INFORMATION ===");

    try {
      // Try the new agent functions first
      const agentCount = await token.getAgentCount();
      console.log("Agent Count:", agentCount.toString());

      if (agentCount > 0) {
        console.log("Agents:");
        for (let i = 0; i < agentCount; i++) {
          const agent = await token.getAgentAt(i);
          console.log(`  ${i + 1}. ${agent}`);
        }

        console.log("First Agent (usually admin):", await token.getAgentAt(0));
      }
    } catch (err) {
      console.log("Agent Info (new functions): Could not read -", err.message);

      // Fallback to getAllAgents
      try {
        const allAgents = await token.getAllAgents();
        console.log("All Agents:", allAgents);
        if (allAgents.length > 0) {
          console.log("First Agent (usually admin):", allAgents[0]);
        }
      } catch (err2) {
        console.log("Agent Info (getAllAgents): Could not read -", err2.message);
      }
    }

    try {
      const AGENT_ROLE = await token.AGENT_ROLE();
      console.log("AGENT_ROLE hash:", AGENT_ROLE);
    } catch (err) {
      console.log("AGENT_ROLE: Could not read -", err.message);
    }

    console.log("\n=== TOKEN TYPE ANALYSIS ===");

    try {
      const tokenDetails = await token.tokenDetails();
      console.log("Raw Token Details:", tokenDetails);

      const details = tokenDetails.toLowerCase();

      // Extract custom token category (matching admin panel logic)
      let tokenCategory = "Not specified";
      if (details.includes('equity')) tokenCategory = "Equity";
      else if (details.includes('bond')) tokenCategory = "Bond";
      else if (details.includes('debenture')) tokenCategory = "Debenture";
      else if (details.includes('warrant')) tokenCategory = "Warrant";
      else if (details.includes('real estate') || details.includes('realestate')) tokenCategory = "Real Estate";
      else if (details.includes('carbon')) tokenCategory = "Carbon Credit";
      else if (details.includes('commodity')) tokenCategory = "Commodity";

      console.log("Token Type (General):", "Security Token");
      console.log("Token Category (Custom):", tokenCategory);

      console.log("\n=== ADMIN PANEL DISPLAY ===");
      console.log("Token Type field will show: Security Token");
      console.log("Token Category field will show:", tokenCategory);

    } catch (err) {
      console.log("Token Type: Could not determine");
    }

  } catch (error) {
    console.error("Error reading token details:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
