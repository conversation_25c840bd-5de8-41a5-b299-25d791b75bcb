const { ethers, upgrades } = require("hardhat");

async function main() {
  const proxyAddress = "******************************************";
  
  try {
    // Get the implementation address
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    console.log("Implementation contract address: ", implementationAddress);
    console.log(`View on OKLink: https://www.oklink.com/amoy/address/${implementationAddress}`);
    
    // Get the admin address
    const adminAddress = await upgrades.erc1967.getAdminAddress(proxyAddress);
    console.log("Proxy admin address: ", adminAddress);
    console.log(`View on OKLink: https://www.oklink.com/amoy/address/${adminAddress}`);
  } catch (error) {
    console.error("Error:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 