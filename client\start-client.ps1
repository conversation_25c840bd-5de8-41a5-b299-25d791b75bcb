Write-Host "Starting TokenDev Client Portal..." -ForegroundColor Green
Write-Host "Current Directory: $(Get-Location)" -ForegroundColor Yellow

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "Error: package.json not found. Please run this script from the client directory." -ForegroundColor Red
    exit 1
}

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Try different methods to start the server
Write-Host "Attempting to start Next.js development server..." -ForegroundColor Yellow

try {
    # Method 1: Direct npx
    Write-Host "Method 1: Using npx..." -ForegroundColor Cyan
    & npx next dev --port 3001
} catch {
    Write-Host "Method 1 failed. Trying Method 2..." -ForegroundColor Yellow
    
    try {
        # Method 2: Using local Next.js binary
        Write-Host "Method 2: Using local Next.js binary..." -ForegroundColor Cyan
        & node "node_modules\next\dist\bin\next" dev --port 3001
    } catch {
        Write-Host "Method 2 failed. Trying Method 3..." -ForegroundColor Yellow
        
        try {
            # Method 3: Using npm run
            Write-Host "Method 3: Using npm run..." -ForegroundColor Cyan
            & npm run dev
        } catch {
            Write-Host "All methods failed. Please check your Node.js installation." -ForegroundColor Red
            Write-Host "Current Node.js version:" -ForegroundColor Yellow
            & node --version
            Write-Host "Current npm version:" -ForegroundColor Yellow
            & npm --version
            Read-Host "Press Enter to exit"
        }
    }
}
