// Test admin panel API directly
const fetch = require('node-fetch');

async function testAdminAPI() {
  console.log('=== Testing Admin Panel API ===');
  
  const yourEmail = '<EMAIL>';
  const yourWallet = '******************************************';
  
  try {
    // Test client search API
    console.log('1. Testing client search API...');
    const searchResponse = await fetch(`http://localhost:3000/api/clients?search=${encodeURIComponent(yourEmail)}&limit=1`);
    
    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      const client = searchData.clients?.[0];
      
      if (client) {
        console.log('✅ Admin API client search result:');
        console.log(`   Email: ${client.email}`);
        console.log(`   Wallet: ${client.walletAddress}`);
        console.log(`   KYC Status: ${client.kycStatus}`);
        console.log(`   Global Whitelisted: ${client.isWhitelisted}`);
        
        if (client.walletAddress === yourWallet) {
          console.log('✅ Wallet address matches expected value');
        } else {
          console.log(`❌ Wallet address mismatch! Expected: ${yourWallet}, Got: ${client.walletAddress}`);
        }
      } else {
        console.log('❌ No client found in search results');
      }
    } else {
      console.log(`❌ Client search API failed: ${searchResponse.status}`);
    }
    
    // Test whitelist API
    console.log('\n2. Testing whitelist API...');
    
    // Get all tokens first
    const tokensResponse = await fetch('http://localhost:3000/api/tokens');
    if (!tokensResponse.ok) {
      console.log('❌ Failed to fetch tokens');
      return;
    }
    
    const tokens = await tokensResponse.json();
    const tokenAddresses = tokens.map(t => t.address);
    
    const whitelistResponse = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: yourWallet,
        tokenAddresses: tokenAddresses
      })
    });
    
    if (whitelistResponse.ok) {
      const whitelistData = await whitelistResponse.json();
      console.log('✅ Admin whitelist API result:');
      console.log(`   Wallet: ${whitelistData.walletAddress}`);
      console.log(`   Global Whitelisted: ${whitelistData.globalWhitelisted}`);
      console.log(`   KYC Status: ${whitelistData.kycStatus}`);
      
      const whitelistedTokens = whitelistData.tokens.filter(t => t.isWhitelisted);
      console.log(`   Whitelisted Tokens: ${whitelistedTokens.length}/${whitelistData.tokens.length}`);
      
      whitelistedTokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        console.log(`     - ${symbol}`);
      });
    } else {
      console.log(`❌ Whitelist API failed: ${whitelistResponse.status}`);
    }
    
    // Test client tokens API
    console.log('\n3. Testing client tokens API...');
    const clientTokensResponse = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(yourWallet)}`);
    
    if (clientTokensResponse.ok) {
      const clientTokens = await clientTokensResponse.json();
      const clientWhitelisted = clientTokens.filter(t => t.isWhitelisted);
      console.log('✅ Client tokens API result:');
      console.log(`   Total Tokens: ${clientTokens.length}`);
      console.log(`   Whitelisted Tokens: ${clientWhitelisted.length}`);
      
      if (clientWhitelisted.length > 0) {
        console.log('   Whitelisted tokens:');
        clientWhitelisted.forEach(token => {
          console.log(`     - ${token.symbol}`);
        });
      } else {
        console.log('   ❌ No tokens showing as whitelisted in client API');
      }
    } else {
      console.log(`❌ Client tokens API failed: ${clientTokensResponse.status}`);
    }
    
  } catch (error) {
    console.error('Error testing APIs:', error);
  }
}

async function main() {
  await testAdminAPI();
  
  console.log('\n🔍 DIAGNOSIS:');
  console.log('If admin APIs show correct data but client API shows wrong data:');
  console.log('1. Client API might be caching old user data');
  console.log('2. Client API might not be calling admin whitelist API');
  console.log('3. There might be a session/authentication issue');
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Check if client API is actually calling admin whitelist API');
  console.log('2. Clear any caches or restart client server');
  console.log('3. Test with direct login to see if session data updates');
}

main().catch(console.error);
