import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import crypto from 'crypto';

/**
 * Sumsub KYC Session Management
 * Creates and retrieves verification sessions with Sumsub
 */

const SUMSUB_BASE_URL = process.env.SUMSUB_BASE_URL!;
const SUMSUB_TOKEN = process.env.SUMSUB_TOKEN!;
const SUMSUB_SECRET = process.env.SUMSUB_SECRET!;

/**
 * Generate Sumsub API signature
 * Format: timestamp + method + url + body (body only if present)
 */
function generateSignature(method: string, url: string, body: string, timestamp: number): string {
  // For token requests (POST with no body), don't include body in signature
  const signatureSource = timestamp.toString() + method.toUpperCase() + url + body;
  const signature = crypto.createHmac('sha256', SUMSUB_SECRET).update(signatureSource).digest('hex');

  console.log('Signature generation:', {
    timestamp,
    method: method.toUpperCase(),
    url,
    bodyLength: body.length,
    signatureSource: signatureSource.substring(0, 100) + '...',
    signature
  });

  return signature;
}

/**
 * Map common country names to ISO 3166-1 alpha-3 codes
 */
function mapCountryToAlpha3(country: string): string {
  if (!country) return 'USA'; // Default fallback

  const countryMap: Record<string, string> = {
    'united states': 'USA',
    'usa': 'USA',
    'us': 'USA',
    'america': 'USA',
    'united kingdom': 'GBR',
    'uk': 'GBR',
    'britain': 'GBR',
    'england': 'GBR',
    'germany': 'DEU',
    'deutschland': 'DEU',
    'france': 'FRA',
    'canada': 'CAN',
    'australia': 'AUS',
    'japan': 'JPN',
    'china': 'CHN',
    'india': 'IND',
    'brazil': 'BRA',
    'russia': 'RUS',
    'italy': 'ITA',
    'spain': 'ESP',
    'netherlands': 'NLD',
    'sweden': 'SWE',
    'norway': 'NOR',
    'denmark': 'DNK',
    'finland': 'FIN',
    'poland': 'POL',
    'switzerland': 'CHE',
    'austria': 'AUT',
    'belgium': 'BEL',
    'portugal': 'PRT',
    'ireland': 'IRL',
    'greece': 'GRC',
    'turkey': 'TUR',
    'south korea': 'KOR',
    'korea': 'KOR',
    'mexico': 'MEX',
    'argentina': 'ARG',
    'chile': 'CHL',
    'colombia': 'COL',
    'peru': 'PER',
    'venezuela': 'VEN',
    'south africa': 'ZAF',
    'egypt': 'EGY',
    'israel': 'ISR',
    'saudi arabia': 'SAU',
    'uae': 'ARE',
    'united arab emirates': 'ARE',
    'singapore': 'SGP',
    'malaysia': 'MYS',
    'thailand': 'THA',
    'indonesia': 'IDN',
    'philippines': 'PHL',
    'vietnam': 'VNM',
    'new zealand': 'NZL',
  };

  const normalized = country.toLowerCase().trim();

  // Check if it's already a 3-letter code
  if (normalized.length === 3 && /^[A-Z]{3}$/i.test(normalized)) {
    return normalized.toUpperCase();
  }

  // Look up in mapping
  const mapped = countryMap[normalized];
  if (mapped) {
    return mapped;
  }

  // If no mapping found, return USA as fallback
  console.warn(`Unknown country: ${country}, using USA as fallback`);
  return 'USA';
}

/**
 * Get detailed applicant data from Sumsub
 */
async function getSumsubApplicantData(applicantId: string) {
  try {
    const url = `/resources/applicants/${applicantId}/one`;
    const headers = createSumsubHeaders('GET', url);

    const response = await fetch(`${SUMSUB_BASE_URL}${url}`, {
      method: 'GET',
      headers: headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to get Sumsub applicant data:', errorText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting Sumsub applicant data:', error);
    return null;
  }
}

/**
 * Get applicant documents from Sumsub
 */
async function getSumsubApplicantDocuments(applicantId: string) {
  try {
    const url = `/resources/applicants/${applicantId}/info/idDoc`;
    const headers = createSumsubHeaders('GET', url);

    const response = await fetch(`${SUMSUB_BASE_URL}${url}`, {
      method: 'GET',
      headers: headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to get Sumsub applicant documents:', errorText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting Sumsub applicant documents:', error);
    return null;
  }
}

/**
 * Create Sumsub headers with authentication
 */
function createSumsubHeaders(method: string, url: string, body: string = '') {
  const timestamp = Math.floor(Date.now() / 1000);
  const signature = generateSignature(method, url, body, timestamp);

  const headers: Record<string, string> = {
    'Accept': 'application/json',
    'X-App-Token': SUMSUB_TOKEN,
    'X-App-Access-Sig': signature,
    'X-App-Access-Ts': timestamp.toString(),
  };

  // Only add Content-Type for requests with body
  if (body && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
    headers['Content-Type'] = 'application/json';
  }

  return headers;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request, NextResponse.json({}));
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { callback } = await request.json();

    // Get user profile for comparison data
    const profileResponse = await fetch(`${process.env.AUTH0_BASE_URL}/api/client/profile`, {
      headers: {
        'Cookie': request.headers.get('cookie') || '',
      },
    });

    let userProfile = null;
    if (profileResponse.ok) {
      userProfile = await profileResponse.json();
    }

    // Step 1: Create applicant with user data
    const applicantData = {
      externalUserId: session.user.sub,
      info: userProfile ? {
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        dob: userProfile.birthday,
        country: mapCountryToAlpha3(userProfile.nationality), // Map to valid alpha-3 code
        phone: userProfile.phoneNumber,
        ...(userProfile.email && { email: userProfile.email }),
      } : {
        firstName: session.user.given_name || '',
        lastName: session.user.family_name || '',
        country: 'USA', // Default country
        ...(session.user.email && { email: session.user.email }),
      },
      metadata: [
        {
          key: 'qualification_data',
          value: JSON.stringify({
            user_id: session.user.sub,
            email: session.user.email,
            expected_data: userProfile ? {
              first_name: userProfile.firstName,
              last_name: userProfile.lastName,
              date_of_birth: userProfile.birthday,
              nationality: userProfile.nationality,
              document_type: userProfile.identificationType,
              document_number: userProfile.passportNumber || userProfile.idCardNumber,
              document_expiration: userProfile.documentExpiration,
              phone_number: userProfile.phoneNumber,
            } : null
          })
        }
      ]
    };

    // Use the same level name for consistency
    const levelName = process.env.SUMSUB_LEVEL_NAME || 'basic-kyc-level';
    const applicantUrl = `/resources/applicants?levelName=${encodeURIComponent(levelName)}`;
    const applicantBody = JSON.stringify(applicantData);
    const applicantHeaders = createSumsubHeaders('POST', applicantUrl, applicantBody);

    const applicantResponse = await fetch(`${SUMSUB_BASE_URL}${applicantUrl}`, {
      method: 'POST',
      headers: applicantHeaders,
      body: applicantBody,
    });

    let applicantResult;
    let applicantId;

    if (!applicantResponse.ok) {
      const errorText = await applicantResponse.text();
      let errorData;

      try {
        errorData = JSON.parse(errorText);
      } catch {
        console.error('Sumsub applicant creation failed:', errorText);
        return NextResponse.json(
          { error: 'Failed to create applicant', details: errorText },
          { status: applicantResponse.status }
        );
      }

      // Handle case where applicant already exists (409 conflict)
      if (errorData.code === 409 && errorData.description?.includes('already exists')) {
        console.log('Applicant already exists, retrieving existing applicant...');

        // Extract applicant ID from error message
        const existingIdMatch = errorData.description.match(/already exists: ([a-f0-9]+)/);
        if (existingIdMatch) {
          applicantId = existingIdMatch[1];

          // Get existing applicant data
          const getApplicantUrl = `/resources/applicants/${applicantId}/one`;
          const getApplicantHeaders = createSumsubHeaders('GET', getApplicantUrl);

          const getApplicantResponse = await fetch(`${SUMSUB_BASE_URL}${getApplicantUrl}`, {
            method: 'GET',
            headers: getApplicantHeaders,
          });

          if (getApplicantResponse.ok) {
            applicantResult = await getApplicantResponse.json();
            console.log('Retrieved existing applicant:', applicantId);
          } else {
            const getErrorText = await getApplicantResponse.text();
            console.error('Failed to retrieve existing applicant:', getErrorText);
            return NextResponse.json(
              { error: 'Failed to retrieve existing applicant', details: getErrorText },
              { status: getApplicantResponse.status }
            );
          }
        } else {
          console.error('Could not extract applicant ID from error:', errorData.description);
          return NextResponse.json(
            { error: 'Applicant exists but could not retrieve ID', details: errorText },
            { status: 409 }
          );
        }
      } else {
        // Handle other errors
        console.error('Sumsub applicant creation failed:', errorData);
        return NextResponse.json(
          {
            error: 'Failed to create applicant',
            details: errorData.description || errorText,
            correlationId: errorData.correlationId
          },
          { status: applicantResponse.status }
        );
      }
    } else {
      // Successful creation
      applicantResult = await applicantResponse.json();
      applicantId = applicantResult.id;
      console.log('Created new applicant:', applicantId);
    }

    // Step 2: Generate access token for the applicant
    console.log('Using Sumsub level name:', levelName);

    const tokenUrl = `/resources/accessTokens?userId=${encodeURIComponent(session.user.sub)}&levelName=${encodeURIComponent(levelName)}&ttlInSecs=600`;
    const tokenHeaders = createSumsubHeaders('POST', tokenUrl, ''); // Empty body for token request

    const tokenResponse = await fetch(`${SUMSUB_BASE_URL}${tokenUrl}`, {
      method: 'POST',
      headers: tokenHeaders,
      body: null, // Explicitly set to null like in the working example
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      let errorData;

      try {
        errorData = JSON.parse(errorText);
        console.error('Sumsub token generation failed:', errorData);

        // Provide helpful error message for level not found
        let errorMessage = errorData.description || errorText;
        if (errorData.description?.includes('not found')) {
          errorMessage = `Level '${levelName}' not found. Please create this verification level in your Sumsub Dashboard at https://cockpit.sumsub.com or set SUMSUB_LEVEL_NAME environment variable to an existing level name.`;
        }

        return NextResponse.json(
          {
            error: 'Failed to generate access token',
            details: errorMessage,
            correlationId: errorData.correlationId,
            levelName: levelName
          },
          { status: tokenResponse.status }
        );
      } catch {
        console.error('Sumsub token generation failed:', errorText);
        return NextResponse.json(
          { error: 'Failed to generate access token', details: errorText },
          { status: tokenResponse.status }
        );
      }
    }

    const tokenResult = await tokenResponse.json();

    // Return session data with applicant information
    const sessionData = {
      session_id: applicantId,
      session_token: tokenResult.token, // This is the access token for the SDK
      status: 'created',
      applicant_id: applicantId,
      user_id: session.user.sub,
      level_name: levelName,
      applicant_data: {
        id: applicantResult.id,
        externalUserId: applicantResult.externalUserId,
        info: applicantResult.info,
        review: applicantResult.review,
        createdAt: applicantResult.createdAt,
        type: applicantResult.type,
      },
    };

    return NextResponse.json({
      success: true,
      data: sessionData,
    });

  } catch (error) {
    console.error('Sumsub session creation error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request, NextResponse.json({}));
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Get applicant status from Sumsub
    const statusUrl = `/resources/applicants/${sessionId}/one`;
    const statusHeaders = createSumsubHeaders('GET', statusUrl);

    const statusResponse = await fetch(`${SUMSUB_BASE_URL}${statusUrl}`, {
      method: 'GET',
      headers: statusHeaders,
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      let errorData;

      try {
        errorData = JSON.parse(errorText);
        console.error('Sumsub status check failed:', errorData);
        return NextResponse.json(
          {
            error: 'Failed to check status',
            details: errorData.description || errorText,
            correlationId: errorData.correlationId
          },
          { status: statusResponse.status }
        );
      } catch {
        console.error('Sumsub status check failed:', errorText);
        return NextResponse.json(
          { error: 'Failed to check status', details: errorText },
          { status: statusResponse.status }
        );
      }
    }

    const statusResult = await statusResponse.json();

    // Map Sumsub status to our internal status
    const mapSumsubStatus = (review: any) => {
      if (!review) return 'pending';

      switch (review.reviewStatus) {
        case 'completed':
          return review.reviewResult?.reviewAnswer === 'GREEN' ? 'approved' : 'rejected';
        case 'pending':
          return 'pending';
        case 'init':
          return 'pending';
        default:
          return 'pending';
      }
    };

    const sessionData = {
      session_id: sessionId,
      status: mapSumsubStatus(statusResult.review),
      applicant_data: statusResult,
      review_status: statusResult.review?.reviewStatus,
      review_result: statusResult.review?.reviewResult,
    };

    return NextResponse.json({
      success: true,
      data: sessionData,
    });

  } catch (error) {
    console.error('Sumsub session status error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
