const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Managing whitelist roles with the account:", deployer.address);
    console.log("Network:", networkName);

    // Get token address from environment or prompt
    const tokenAddress = process.env.TOKEN_ADDRESS;
    if (!tokenAddress) {
      console.error("Please set TOKEN_ADDRESS environment variable");
      process.exit(1);
    }

    console.log("Token address:", tokenAddress);

    // Connect to the token contract
    const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);

    // Get the whitelist contract address
    const whitelistAddress = await token.identityRegistry();
    console.log("Whitelist address:", whitelistAddress);

    if (whitelistAddress === hre.ethers.ZeroAddress) {
      console.error("No whitelist contract found for this token");
      process.exit(1);
    }

    // Connect to the whitelist contract
    const whitelist = await hre.ethers.getContractAt("Whitelist", whitelistAddress);

    // Define roles
    const AGENT_ROLE = await whitelist.AGENT_ROLE();
    const DEFAULT_ADMIN_ROLE = await whitelist.DEFAULT_ADMIN_ROLE();

    console.log("\n=== ROLE INFORMATION ===");
    console.log("AGENT_ROLE:", AGENT_ROLE);
    console.log("DEFAULT_ADMIN_ROLE:", DEFAULT_ADMIN_ROLE);

    // Check current user's roles
    console.log("\n=== CURRENT USER ROLES ===");
    const hasAgentRole = await whitelist.hasRole(AGENT_ROLE, deployer.address);
    const hasAdminRole = await whitelist.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);

    console.log(`${deployer.address} has AGENT_ROLE:`, hasAgentRole);
    console.log(`${deployer.address} has DEFAULT_ADMIN_ROLE:`, hasAdminRole);

    // Get action from environment
    const action = process.env.ACTION || "check";
    const targetAddress = process.env.TARGET_ADDRESS || deployer.address;

    console.log("\n=== ACTION ===");
    console.log("Action:", action);
    console.log("Target address:", targetAddress);

    if (action === "check") {
      console.log("\n=== ROLE CHECK COMPLETE ===");
      console.log("Use ACTION=grant_agent to grant AGENT_ROLE");
      console.log("Use ACTION=grant_admin to grant DEFAULT_ADMIN_ROLE");
      console.log("Use TARGET_ADDRESS=0x... to specify target address");
      return;
    }

    if (action === "grant_agent") {
      if (!hasAdminRole) {
        console.error("You need DEFAULT_ADMIN_ROLE to grant AGENT_ROLE");
        process.exit(1);
      }

      console.log(`Granting AGENT_ROLE to ${targetAddress}...`);
      const tx = await whitelist.grantRole(AGENT_ROLE, targetAddress);
      console.log("Transaction hash:", tx.hash);
      
      await tx.wait();
      console.log("✅ AGENT_ROLE granted successfully!");
      
      // Verify
      const hasRole = await whitelist.hasRole(AGENT_ROLE, targetAddress);
      console.log(`${targetAddress} now has AGENT_ROLE:`, hasRole);
    }

    if (action === "grant_admin") {
      if (!hasAdminRole) {
        console.error("You need DEFAULT_ADMIN_ROLE to grant DEFAULT_ADMIN_ROLE");
        process.exit(1);
      }

      console.log(`Granting DEFAULT_ADMIN_ROLE to ${targetAddress}...`);
      const tx = await whitelist.grantRole(DEFAULT_ADMIN_ROLE, targetAddress);
      console.log("Transaction hash:", tx.hash);
      
      await tx.wait();
      console.log("✅ DEFAULT_ADMIN_ROLE granted successfully!");
      
      // Verify
      const hasRole = await whitelist.hasRole(DEFAULT_ADMIN_ROLE, targetAddress);
      console.log(`${targetAddress} now has DEFAULT_ADMIN_ROLE:`, hasRole);
    }

    if (action === "revoke_agent") {
      if (!hasAdminRole) {
        console.error("You need DEFAULT_ADMIN_ROLE to revoke AGENT_ROLE");
        process.exit(1);
      }

      console.log(`Revoking AGENT_ROLE from ${targetAddress}...`);
      const tx = await whitelist.revokeRole(AGENT_ROLE, targetAddress);
      console.log("Transaction hash:", tx.hash);
      
      await tx.wait();
      console.log("✅ AGENT_ROLE revoked successfully!");
      
      // Verify
      const hasRole = await whitelist.hasRole(AGENT_ROLE, targetAddress);
      console.log(`${targetAddress} now has AGENT_ROLE:`, hasRole);
    }

    if (action === "list_agents") {
      console.log("\n=== LISTING AGENTS ===");
      // This would require event parsing to get all role grants
      console.log("Note: To see all agents, check the blockchain explorer for RoleGranted events");
      console.log("AGENT_ROLE hash:", AGENT_ROLE);
    }

  } catch (error) {
    console.error("Error managing whitelist roles:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
