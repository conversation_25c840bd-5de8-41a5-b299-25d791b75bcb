const { PrismaClient } = require('@prisma/client');
const { ethers } = require('ethers');

const prisma = new PrismaClient();

async function checkTokenWhitelist() {
  console.log('🔍 Checking Token Whitelist Details');
  console.log('===================================');

  const YOUR_WALLET = '******************************************';
  const TOKEN_ADDRESS = '******************************************';

  try {
    // 1. Get token details from database
    console.log('1️⃣ Getting token details from database...');
    
    const token = await prisma.token.findFirst({
      where: {
        address: {
          equals: TOKEN_ADDRESS,
          mode: 'insensitive'
        }
      },
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        whitelistAddress: true,
        adminAddress: true,
        tokenPrice: true,
        currency: true
      }
    });

    if (!token) {
      console.log('❌ Token not found in database');
      return;
    }

    console.log(`   ✅ Token found: ${token.name} (${token.symbol})`);
    console.log(`   Address: ${token.address}`);
    console.log(`   Whitelist Contract: ${token.whitelistAddress}`);
    console.log(`   Admin Address: ${token.adminAddress}`);
    console.log(`   Price: ${token.tokenPrice}`);
    console.log(`   Currency: ${token.currency}`);

    // 2. Check if whitelist contract exists and is valid
    console.log('\n2️⃣ Checking whitelist contract...');
    
    if (!token.whitelistAddress || token.whitelistAddress === '******************************************') {
      console.log('❌ No whitelist contract address found');
      return;
    }

    // 3. Check blockchain whitelist status
    console.log('\n3️⃣ Checking blockchain whitelist status...');
    
    try {
      const provider = new ethers.JsonRpcProvider(process.env.AMOY_RPC_URL);
      console.log(`   Using RPC: ${process.env.AMOY_RPC_URL}`);

      // Load whitelist ABI
      const WhitelistABI = require('./src/contracts/Whitelist.json');
      const whitelistContract = new ethers.Contract(
        token.whitelistAddress,
        WhitelistABI.abi,
        provider
      );

      // Check if your wallet is whitelisted
      const isWhitelisted = await whitelistContract.isWhitelisted(YOUR_WALLET);
      console.log(`   Your wallet whitelisted: ${isWhitelisted ? '✅ YES' : '❌ NO'}`);

      // Check admin roles
      try {
        const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
        const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, YOUR_WALLET);
        console.log(`   You have AGENT_ROLE: ${hasAgentRole ? '✅ YES' : '❌ NO'}`);

        const DEFAULT_ADMIN_ROLE = await whitelistContract.DEFAULT_ADMIN_ROLE();
        const hasDefaultAdminRole = await whitelistContract.hasRole(DEFAULT_ADMIN_ROLE, YOUR_WALLET);
        console.log(`   You have DEFAULT_ADMIN_ROLE: ${hasDefaultAdminRole ? '✅ YES' : '❌ NO'}`);
      } catch (error) {
        console.log('   ⚠️  Could not check roles:', error.message);
      }

      // 4. If not whitelisted, show how to whitelist
      if (!isWhitelisted) {
        console.log('\n4️⃣ How to whitelist yourself for this token:');
        console.log('============================================');
        console.log('Option 1: Use Admin Panel');
        console.log(`   1. Go to: http://localhost:6677/tokens/${token.address}`);
        console.log('   2. Find your client in the client management section');
        console.log('   3. Click "Approve" to whitelist yourself');
        console.log('');
        console.log('Option 2: Direct Blockchain Call');
        console.log('   Run this command in admin-panel directory:');
        console.log(`   node -e "`);
        console.log(`     const { ethers } = require('ethers');`);
        console.log(`     const provider = new ethers.JsonRpcProvider(process.env.AMOY_RPC_URL);`);
        console.log(`     const wallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);`);
        console.log(`     const abi = require('./src/contracts/Whitelist.json').abi;`);
        console.log(`     const contract = new ethers.Contract('${token.whitelistAddress}', abi, wallet);`);
        console.log(`     contract.addToWhitelist('${YOUR_WALLET}').then(tx => {`);
        console.log(`       console.log('Transaction:', tx.hash);`);
        console.log(`       return tx.wait();`);
        console.log(`     }).then(() => console.log('Whitelisted successfully!'));`);
        console.log(`   "`);
      } else {
        console.log('\n✅ You are already whitelisted for this token!');
        console.log('   The client app should now allow you to place orders.');
      }

    } catch (error) {
      console.log(`   ❌ Blockchain check failed: ${error.message}`);
    }

    // 5. Check database token approval record
    console.log('\n5️⃣ Checking database token approval...');
    
    const client = await prisma.client.findFirst({
      where: {
        walletAddress: {
          equals: YOUR_WALLET,
          mode: 'insensitive'
        }
      },
      include: {
        tokenApprovals: {
          where: { tokenId: token.id },
          select: {
            id: true,
            approvalStatus: true,
            whitelistApproved: true,
            kycApproved: true,
            approvedAt: true,
            approvedBy: true
          }
        }
      }
    });

    if (!client) {
      console.log('   ❌ Client not found in database');
    } else {
      console.log(`   ✅ Client found: ${client.firstName} ${client.lastName}`);
      console.log(`   Global KYC: ${client.kycStatus}`);
      console.log(`   Global Whitelist: ${client.isWhitelisted}`);
      
      const tokenApproval = client.tokenApprovals[0];
      if (!tokenApproval) {
        console.log('   ❌ No token approval record found');
        console.log('   💡 This is normal - approvals are created when you request access');
      } else {
        console.log(`   Token approval status: ${tokenApproval.approvalStatus}`);
        console.log(`   Token whitelist approved: ${tokenApproval.whitelistApproved}`);
        console.log(`   Token KYC approved: ${tokenApproval.kycApproved}`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTokenWhitelist().catch(console.error);
