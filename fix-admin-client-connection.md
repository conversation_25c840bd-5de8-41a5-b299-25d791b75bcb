# Fix Admin Panel <-> Client Portal Connection

## 🔧 Quick Fix Steps

### 1. Set up Admin Panel Database

```bash
cd admin-panel
node setup-database.js
```

If the script fails, run manually:
```bash
cd admin-panel
npm install
npx prisma generate
npx prisma db push
npm run dev
```

### 2. Test Admin Panel

```bash
# In project root
node test-admin-panel.js
```

### 3. Start Both Applications

**Terminal 1 - Admin Panel:**
```bash
cd admin-panel
npm run dev
```
Should start on http://localhost:3000

**Terminal 2 - Client Portal:**
```bash
cd client
npm run dev
```
Should start on http://localhost:7788

### 4. Test Connection

```bash
# In project root
node test-admin-client-connection.js
```

## 🔍 Troubleshooting

### Issue: Admin API returns 500 error

**Cause:** Database not set up or Prisma client not generated

**Fix:**
```bash
cd admin-panel
npx prisma generate
npx prisma db push
```

### Issue: CORS errors

**Cause:** Admin panel not allowing client portal requests

**Fix:** Already implemented in `admin-panel/src/middleware.ts` and `admin-panel/next.config.ts`

### Issue: Client can't connect to admin

**Check:**
1. Admin panel running on port 3000: http://localhost:3000/api/status
2. Client portal running on port 3001: http://localhost:7788/api/status
3. Environment variables in `client/.env.local`:
   ```
   ADMIN_API_BASE_URL="http://localhost:3000/api"
   ```

## 🧪 Test URLs

### Admin Panel
- Status: http://localhost:3000/api/status
- Clients API: http://localhost:3000/api/clients
- Client Management: http://localhost:3000/clients

### Client Portal
- Status: http://localhost:7788/api/status
- Main App: http://localhost:7788
- Profile API: http://localhost:7788/api/client/profile

## 📋 Expected Behavior

1. **Admin Panel API** should return client data
2. **Client Portal** should fetch profile from admin panel
3. **Agreement status** should sync between both applications
4. **CORS** should allow cross-origin requests

## 🎯 Success Indicators

✅ Admin panel status returns database info
✅ Client portal can fetch profile data
✅ Agreement acceptance works in client portal
✅ Admin panel shows agreement status
✅ No CORS errors in browser console

## 🚨 Common Issues

### Database Issues
- Run `npx prisma db push` in admin-panel
- Check `.env` file has `DATABASE_URL`

### Port Conflicts
- Admin panel must be on port 3000
- Client portal must be on port 3001
- Kill other processes using these ports

### Environment Variables
- Check `client/.env.local` has correct `ADMIN_API_BASE_URL`
- Check `admin-panel/.env` has database configuration

### Network Issues
- Disable firewall/antivirus temporarily
- Try accessing APIs directly in browser
- Check if localhost resolves correctly
