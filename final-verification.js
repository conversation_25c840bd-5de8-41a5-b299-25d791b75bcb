// Final verification script to confirm all fixes are working
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3000/api';
const CLIENT_API_URL = 'http://localhost:3003/api';

async function verifyTotalSupplyFix() {
  console.log('=== Verifying Total Supply Fix ===');

  try {
    const response = await fetch(`${CLIENT_API_URL}/tokens`);
    const tokens = await response.json();

    console.log(`✅ Client API returned ${tokens.length} tokens`);

    let hasValidTotalSupply = true;

    tokens.forEach((token, index) => {
      const totalSupply = token.totalSupply;
      if (totalSupply === undefined || totalSupply === null) {
        console.log(`❌ Token ${index + 1} (${token.symbol}) has invalid totalSupply: ${totalSupply}`);
        hasValidTotalSupply = false;
      } else {
        console.log(`✅ Token ${index + 1} (${token.symbol}) has totalSupply: ${totalSupply}`);
      }
    });

    if (hasValidTotalSupply) {
      console.log('✅ All tokens have valid totalSupply values');
    } else {
      console.log('❌ Some tokens have invalid totalSupply values');
    }

    return hasValidTotalSupply;

  } catch (error) {
    console.error('❌ Error verifying total supply fix:', error.message);
    return false;
  }
}

async function verifyDecimalsFix() {
  console.log('\n=== Verifying Decimals Fix ===');

  try {
    const response = await fetch(`${CLIENT_API_URL}/tokens`);
    const tokens = await response.json();

    let hasValidDecimals = true;
    let hasZeroDecimals = false;
    let hasNonZeroDecimals = false;

    tokens.forEach((token, index) => {
      const decimals = token.decimals;
      if (typeof decimals !== 'number' || decimals < 0 || decimals > 18) {
        console.log(`❌ Token ${index + 1} (${token.symbol}) has invalid decimals: ${decimals}`);
        hasValidDecimals = false;
      } else {
        if (decimals === 0) {
          hasZeroDecimals = true;
          console.log(`✅ Token ${index + 1} (${token.symbol}) has 0 decimals (correctly handled)`);
        } else {
          hasNonZeroDecimals = true;
          console.log(`✅ Token ${index + 1} (${token.symbol}) has ${decimals} decimals`);
        }
      }
    });

    if (hasValidDecimals) {
      console.log('✅ All tokens have valid decimals values');
    } else {
      console.log('❌ Some tokens have invalid decimals values');
    }

    if (hasZeroDecimals) {
      console.log('✅ Zero decimals tokens are handled correctly');
    } else {
      console.log('ℹ️  No zero decimals tokens found for testing');
    }

    if (hasNonZeroDecimals) {
      console.log('✅ Non-zero decimals tokens are handled correctly');
    }

    return hasValidDecimals;

  } catch (error) {
    console.error('❌ Error verifying decimals fix:', error.message);
    return false;
  }
}

async function verifyDataConsistency() {
  console.log('\n=== Verifying Data Consistency ===');

  try {
    // Get data from admin API
    const adminResponse = await fetch(`${ADMIN_API_URL}/tokens?source=database`);
    const adminTokens = await adminResponse.json();

    // Get data from client API
    const clientResponse = await fetch(`${CLIENT_API_URL}/tokens`);
    const clientTokens = await clientResponse.json();

    console.log(`Admin API returned ${adminTokens.length} tokens`);
    console.log(`Client API returned ${clientTokens.length} tokens`);

    if (adminTokens.length !== clientTokens.length) {
      console.log('❌ Token count mismatch between admin and client APIs');
      return false;
    }

    let isConsistent = true;

    // Check if data is consistent between APIs
    adminTokens.forEach((adminToken, index) => {
      const clientToken = clientTokens.find(ct => ct.address === adminToken.address);

      if (!clientToken) {
        console.log(`❌ Token ${adminToken.symbol} found in admin API but not in client API`);
        isConsistent = false;
        return;
      }

      // Check key fields
      const fieldsToCheck = [
        { admin: 'name', client: 'name' },
        { admin: 'symbol', client: 'symbol' },
        { admin: 'decimals', client: 'decimals' },
        { admin: 'maxSupply', client: 'maxSupply' },
        { admin: 'totalSupply', client: 'totalSupply' },
        { admin: 'tokenPrice', client: 'price' },
        { admin: 'currency', client: 'currency' },
        { admin: 'tokenType', client: 'category' }
      ];

      fieldsToCheck.forEach(field => {
        if (adminToken[field.admin] !== clientToken[field.client]) {
          console.log(`❌ ${adminToken.symbol}: ${field.admin}/${field.client} mismatch - Admin: "${adminToken[field.admin]}", Client: "${clientToken[field.client]}"`);
          isConsistent = false;
        }
      });
    });

    if (isConsistent) {
      console.log('✅ Data is consistent between admin and client APIs');
    }

    return isConsistent;

  } catch (error) {
    console.error('❌ Error verifying data consistency:', error.message);
    return false;
  }
}

async function generateSummaryReport() {
  console.log('\n=== SUMMARY REPORT ===');

  const totalSupplyOk = await verifyTotalSupplyFix();
  const decimalsOk = await verifyDecimalsFix();
  const consistencyOk = await verifyDataConsistency();

  console.log('\n--- FINAL RESULTS ---');
  console.log(`Total Supply Fix: ${totalSupplyOk ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`Decimals Fix: ${decimalsOk ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`Data Consistency: ${consistencyOk ? '✅ WORKING' : '❌ FAILED'}`);

  const allGood = totalSupplyOk && decimalsOk && consistencyOk;

  console.log(`\n🎯 OVERALL STATUS: ${allGood ? '✅ ALL FIXES WORKING CORRECTLY' : '❌ SOME ISSUES REMAIN'}`);

  if (allGood) {
    console.log('\n🎉 SUCCESS! The client offers page should now display:');
    console.log('   - Correct Total Supply values (no longer showing 0 incorrectly)');
    console.log('   - Correct Decimals values (including 0 decimals tokens)');
    console.log('   - Consistent data between admin panel and client application');
  }

  return allGood;
}

async function main() {
  await generateSummaryReport();
}

main().catch(console.error);
