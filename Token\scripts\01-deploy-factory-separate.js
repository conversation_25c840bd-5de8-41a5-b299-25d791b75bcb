// Alternative deployment script that deploys implementation contracts separately
// This reduces gas cost per transaction to avoid Hardhat's 1 ether cap
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  // Get the deployer account
  const [deployer] = await hre.ethers.getSigners();
  const networkName = hre.network.name;

  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Network:", networkName);

  // Check account balance
  const balance = await hre.ethers.provider.getBalance(deployer.address);
  console.log("Account balance:", hre.ethers.formatEther(balance), "MATIC");

  try {
    // Step 1: Deploy SecurityToken implementation
    console.log("\n--- Step 1: Deploying SecurityToken implementation ---");
    const SecurityToken = await hre.ethers.getContractFactory("SecurityToken");
    const securityTokenImpl = await SecurityToken.deploy({
      gasLimit: 8000000,
      gasPrice: hre.ethers.parseUnits('30', 'gwei'),
    });
    await securityTokenImpl.waitForDeployment();
    const securityTokenAddress = await securityTokenImpl.getAddress();
    console.log("SecurityToken implementation deployed to:", securityTokenAddress);

    // Step 2: Deploy Whitelist implementation
    console.log("\n--- Step 2: Deploying Whitelist implementation ---");
    const Whitelist = await hre.ethers.getContractFactory("Whitelist");
    const whitelistImpl = await Whitelist.deploy({
      gasLimit: 3000000,
      gasPrice: hre.ethers.parseUnits('30', 'gwei'),
    });
    await whitelistImpl.waitForDeployment();
    const whitelistAddress = await whitelistImpl.getAddress();
    console.log("Whitelist implementation deployed to:", whitelistAddress);

    // Step 3: Deploy WhitelistWithKYC implementation
    console.log("\n--- Step 3: Deploying WhitelistWithKYC implementation ---");
    const WhitelistWithKYC = await hre.ethers.getContractFactory("WhitelistWithKYC");
    const whitelistWithKYCImpl = await WhitelistWithKYC.deploy({
      gasLimit: 3000000,
      gasPrice: hre.ethers.parseUnits('30', 'gwei'),
    });
    await whitelistWithKYCImpl.waitForDeployment();
    const whitelistWithKYCAddress = await whitelistWithKYCImpl.getAddress();
    console.log("WhitelistWithKYC implementation deployed to:", whitelistWithKYCAddress);

    // Step 4: Deploy SecurityTokenFactory with pre-deployed implementations
    console.log("\n--- Step 4: Deploying SecurityTokenFactory ---");
    const SecurityTokenFactory = await hre.ethers.getContractFactory("SecurityTokenFactory");
    
    // Create custom factory that accepts implementation addresses
    const factoryBytecode = SecurityTokenFactory.bytecode;
    const factoryInterface = SecurityTokenFactory.interface;
    
    // Deploy factory with constructor parameters
    const factory = await SecurityTokenFactory.deploy(deployer.address, {
      gasLimit: 8000000,
      gasPrice: hre.ethers.parseUnits('30', 'gwei'),
    });
    await factory.waitForDeployment();
    const factoryAddress = await factory.getAddress();
    console.log("SecurityTokenFactory deployed to:", factoryAddress);

    // Step 5: Update implementations in factory if needed
    console.log("\n--- Step 5: Verifying implementations ---");
    const currentSecurityTokenImpl = await factory.securityTokenImplementation();
    const currentWhitelistImpl = await factory.whitelistImplementation();
    const currentWhitelistWithKYCImpl = await factory.whitelistWithKYCImplementation();
    
    console.log("Factory SecurityToken implementation:", currentSecurityTokenImpl);
    console.log("Factory Whitelist implementation:", currentWhitelistImpl);
    console.log("Factory WhitelistWithKYC implementation:", currentWhitelistWithKYCImpl);

    // Save deployment information
    const deploymentInfo = {
      network: networkName,
      factory: factoryAddress,
      implementations: {
        securityToken: currentSecurityTokenImpl,
        whitelist: currentWhitelistImpl,
        whitelistWithKYC: currentWhitelistWithKYCImpl
      },
      separateImplementations: {
        securityToken: securityTokenAddress,
        whitelist: whitelistAddress,
        whitelistWithKYC: whitelistWithKYCAddress
      },
      timestamp: new Date().toISOString(),
      deploymentMethod: "separate"
    };

    // Create a deployment file
    const deploymentsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir);
    }

    const deploymentFile = path.join(deploymentsDir, `${networkName}.json`);
    fs.writeFileSync(
      deploymentFile,
      JSON.stringify(deploymentInfo, null, 2)
    );

    console.log(`\nDeployment information saved to ${deploymentFile}`);
    console.log("✅ All contracts deployed successfully!");

    // Calculate total gas used
    const totalGasUsed = 8000000 + 3000000 + 3000000 + 8000000; // Approximate
    const gasPrice = hre.ethers.parseUnits('30', 'gwei');
    const totalCost = BigInt(totalGasUsed) * gasPrice;
    console.log("Approximate total deployment cost:", hre.ethers.formatEther(totalCost), "MATIC");

  } catch (error) {
    console.error("Error during deployment:", error);
    process.exitCode = 1;
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
