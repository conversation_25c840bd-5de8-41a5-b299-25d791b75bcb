const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Tokeny-Style ClaimRegistry with Topic IDs...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "MATIC");

  try {
    // Deploy the SimpleClaimRegistry with Tokeny-style Topic IDs
    console.log("\n1️⃣ Deploying Tokeny-Style ClaimRegistry...");
    
    const SimpleClaimRegistry = await ethers.getContractFactory("SimpleClaimRegistry");
    
    // Deploy with constructor parameter
    const claimRegistry = await SimpleClaimRegistry.deploy(deployer.address, {
      gasLimit: 5000000,
      maxFeePerGas: ethers.parseUnits('100', 'gwei'),
      maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
    });

    await claimRegistry.waitForDeployment();
    const claimRegistryAddress = await claimRegistry.getAddress();
    
    console.log("✅ Tokeny-Style ClaimRegistry deployed to:", claimRegistryAddress);

    // Wait for block confirmations
    console.log("\n⏳ Waiting for block confirmations...");
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Test the Tokeny-style Topic IDs
    console.log("\n2️⃣ Testing Tokeny-Style Topic IDs...");
    
    try {
      // Test getTotalClaimTypes
      const totalClaimTypes = await claimRegistry.getTotalClaimTypes();
      console.log("📊 Total claim types:", totalClaimTypes.toString());

      // Test getActiveClaimTypes to see the Topic IDs
      const activeClaimTypes = await claimRegistry.getActiveClaimTypes(0, 10);
      console.log("📋 Active claim types with Tokeny-style Topic IDs:");
      
      for (let i = 0; i < activeClaimTypes.length; i++) {
        const claimType = activeClaimTypes[i];
        console.log(`   Topic ID: ${claimType.id} | Name: ${claimType.name}`);
        console.log(`   Description: ${claimType.description}`);
        console.log(`   Creator: ${claimType.creator}`);
        console.log("   " + "-".repeat(50));
      }

      // Test creating a custom claim with specific Topic ID
      console.log("\n3️⃣ Creating Custom Claim with Specific Topic ID...");
      const customTopicId = "10101010000648"; // Like Tokeny example
      const createTx = await claimRegistry.createClaimTypeWithTopicId(
        customTopicId,
        "SPECIFIC_KYC_STATUS",
        "Specific KYC Status for qualified investors",
        {
          gasLimit: 500000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await createTx.wait();
      console.log(`✅ Custom claim created with Topic ID: ${customTopicId}`);

      // Test issuing a claim to an investor
      console.log("\n4️⃣ Issuing Claim to Investor (On-Chain Storage)...");
      const investorWallet = "******************************************";
      const kycTopicId = "10101010000001"; // KYC_VERIFICATION
      
      const claimData = ethers.toUtf8Bytes(JSON.stringify({
        kycProvider: "Sumsub",
        verificationLevel: "basic-kyc-level",
        verifiedAt: new Date().toISOString(),
        status: "APPROVED"
      }));

      const issueTx = await claimRegistry.issueClaim(
        investorWallet,
        kycTopicId,
        "0x", // Signature (can be empty for test)
        claimData,
        "https://api.sumsub.com/resources/applicants/verification", // URI
        0, // No expiration
        {
          gasLimit: 400000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await issueTx.wait();
      console.log(`✅ KYC claim issued to investor: ${investorWallet}`);
      console.log(`   Topic ID: ${kycTopicId} (KYC_VERIFICATION)`);

      // Verify the claim was stored on-chain
      const hasValidClaim = await claimRegistry.hasValidClaim(investorWallet, kycTopicId);
      console.log(`🔍 Investor has valid KYC claim: ${hasValidClaim}`);

      // Get claim IDs for the investor
      const claimIds = await claimRegistry.getClaimIds(investorWallet, kycTopicId);
      console.log(`📋 Number of claims for this investor: ${claimIds.length}`);

    } catch (testError) {
      console.log("⚠️ Error testing functions:", testError.message);
    }

    // Display deployment summary
    console.log("\n" + "=" .repeat(60));
    console.log("🎉 TOKENY-STYLE DEPLOYMENT SUCCESSFUL!");
    console.log("=" .repeat(60));
    console.log("📋 Contract Address:");
    console.log("   ClaimRegistry:", claimRegistryAddress);
    console.log("\n🏷️ Default Topic IDs (Tokeny-Style):");
    console.log("   10101010000001 - KYC_VERIFICATION");
    console.log("   10101010000002 - ACCREDITED_INVESTOR");
    console.log("   10101010000003 - JURISDICTION_COMPLIANCE");
    console.log("   10101010000004 - GENERAL_QUALIFICATION");
    console.log("   10101010000648 - SPECIFIC_KYC_STATUS (Custom)");
    console.log("\n📝 Environment Variables to Update:");
    console.log("   CLAIM_REGISTRY_ADDRESS=" + claimRegistryAddress);
    console.log("   NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS=" + claimRegistryAddress);
    console.log("\n🔧 Key Features:");
    console.log("   ✅ Tokeny-style Topic IDs (10101010000xxx)");
    console.log("   ✅ Claims stored on-chain for each investor");
    console.log("   ✅ Custom Topic ID creation");
    console.log("   ✅ Claim verification and validation");

    // Save deployment info
    const fs = require('fs');
    const deploymentInfo = {
      network: "amoy",
      timestamp: new Date().toISOString(),
      deployer: deployer.address,
      contracts: {
        ClaimRegistry: claimRegistryAddress
      },
      defaultTopicIds: {
        "KYC_VERIFICATION": "10101010000001",
        "ACCREDITED_INVESTOR": "10101010000002", 
        "JURISDICTION_COMPLIANCE": "10101010000003",
        "GENERAL_QUALIFICATION": "10101010000004",
        "SPECIFIC_KYC_STATUS": "10101010000648"
      },
      environmentVariables: {
        CLAIM_REGISTRY_ADDRESS: claimRegistryAddress,
        NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS: claimRegistryAddress
      }
    };

    fs.writeFileSync(
      'deployment-tokeny-claims.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("\n💾 Deployment info saved to: deployment-tokeny-claims.json");

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
