import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET endpoint to fix qualification progress flags for a specific user
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userEmail = searchParams.get('userEmail');
    const tokenAddress = searchParams.get('tokenAddress');

    if (!userEmail || !tokenAddress) {
      return NextResponse.json({ error: 'Missing userEmail or tokenAddress parameters' }, { status: 400 });
    }

    console.log('🔧 Fixing qualification progress flags for:', { userEmail, tokenAddress });

    // Find the client
    const client = await prisma.client.findUnique({
      where: { email: userEmail }
    });

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // First find the token by address
    const token = await prisma.token.findUnique({
      where: { address: tokenAddress }
    });

    if (!token) {
      return NextResponse.json({ error: 'Token not found' }, { status: 404 });
    }

    // Find existing qualification progress
    const existingProgress = await prisma.qualificationProgress.findFirst({
      where: {
        clientId: client.id,
        tokenId: token.id
      }
    });

    if (!existingProgress) {
      return NextResponse.json({ error: 'Qualification progress not found' }, { status: 404 });
    }

    // Calculate correct completion flags based on current state
    const countrySelected = existingProgress.completedSteps >= 1 || !!existingProgress.countryValue;
    const agreementAccepted = existingProgress.completedSteps >= 2;
    const profileCompleted = existingProgress.completedSteps >= 3 || !!client.firstName;
    const walletConnected = existingProgress.completedSteps >= 4 || !!client.walletAddress;
    const kycCompleted = existingProgress.completedSteps >= 5 || client.kycStatus === 'APPROVED';

    // Prepare update data
    const updateData: any = {
      countrySelected,
      agreementAccepted,
      profileCompleted,
      walletConnected,
      kycCompleted,
    };

    // Only update countryValue if it's not already set and country is selected
    if (!existingProgress.countryValue && countrySelected) {
      updateData.countryValue = 'Fixed by admin';
    }

    // Update the qualification progress with correct flags
    const updatedProgress = await prisma.qualificationProgress.update({
      where: { id: existingProgress.id },
      data: updateData
    });

    console.log('✅ Fixed qualification progress flags:', {
      clientEmail: client.email,
      tokenAddress,
      before: {
        countrySelected: existingProgress.countrySelected,
        agreementAccepted: existingProgress.agreementAccepted,
        profileCompleted: existingProgress.profileCompleted,
        walletConnected: existingProgress.walletConnected,
        kycCompleted: existingProgress.kycCompleted
      },
      after: {
        countrySelected,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted
      }
    });

    return NextResponse.json({
      message: 'Qualification progress flags fixed successfully',
      progress: updatedProgress,
      changes: {
        before: {
          countrySelected: existingProgress.countrySelected,
          agreementAccepted: existingProgress.agreementAccepted,
          profileCompleted: existingProgress.profileCompleted,
          walletConnected: existingProgress.walletConnected,
          kycCompleted: existingProgress.kycCompleted
        },
        after: {
          countrySelected,
          agreementAccepted,
          profileCompleted,
          walletConnected,
          kycCompleted
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fixing qualification progress:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
