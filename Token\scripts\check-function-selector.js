const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Checking Function Selectors...");

  // Get the SecurityToken contract factory
  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  
  console.log("SecurityToken contract interface:");
  
  // Check if initializeWithAgreement function exists
  const initializeWithAgreementFragment = SecurityToken.interface.getFunction("initializeWithAgreement");
  if (initializeWithAgreementFragment) {
    console.log("✅ initializeWithAgreement function found");
    console.log("Selector:", initializeWithAgreementFragment.selector);
    console.log("Signature:", initializeWithAgreementFragment.format());
  } else {
    console.log("❌ initializeWithAgreement function not found");
  }

  // Check if initialize function exists
  const initializeFragment = SecurityToken.interface.getFunction("initialize");
  if (initializeFragment) {
    console.log("✅ initialize function found");
    console.log("Selector:", initializeFragment.selector);
    console.log("Signature:", initializeFragment.format());
  } else {
    console.log("❌ initialize function not found");
  }

  // List all functions
  console.log("\nAll functions in SecurityToken:");
  const functions = SecurityToken.interface.fragments.filter(f => f.type === 'function');
  functions.forEach(func => {
    if (func.name.includes('initialize')) {
      console.log(`- ${func.name}: ${func.format()}`);
    }
  });

  // Test encoding
  console.log("\n🔍 Testing function encoding...");
  try {
    const encodedData = SecurityToken.interface.encodeFunctionData("initializeWithAgreement", [
      "Test Token",
      "TEST",
      0,
      ethers.parseUnits("1000", 0),
      "******************************************",
      "******************************************",
      "10 USD",
      "Tier 1: 5%",
      "Test token",
      "",
      "https://example.com/agreement.pdf"
    ]);
    console.log("✅ Function encoding successful");
    console.log("Encoded data length:", encodedData.length);
  } catch (error) {
    console.log("❌ Function encoding failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Check failed:", error);
    process.exit(1);
  });
