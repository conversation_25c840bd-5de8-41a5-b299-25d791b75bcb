import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { ethers } from 'ethers';
import path from 'path';
import fs from 'fs';

const prisma = new PrismaClient();

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: orderId } = await params;
    const body = await request.json();
    const { tokenAddress, recipientAddress, amount } = body;

    console.log('🪙 Starting minting process...');
    console.log('Order ID:', orderId);
    console.log('Token Address:', tokenAddress);
    console.log('Recipient Address:', recipientAddress);
    console.log('Amount:', amount);

    // 1. Validate input parameters
    if (!tokenAddress || !recipientAddress || !amount) {
      return NextResponse.json(
        { error: 'Missing required parameters: tokenAddress, recipientAddress, amount' },
        { status: 400 }
      );
    }

    // 2. Validate recipient address format
    let finalRecipientAddress: string;
    try {
      // Check if it's a valid Ethereum address
      finalRecipientAddress = ethers.getAddress(recipientAddress);
    } catch (error) {
      return NextResponse.json(
        { error: `Invalid recipient address format: ${recipientAddress}` },
        { status: 400 }
      );
    }

    // 3. Validate amount
    if (isNaN(Number(amount)) || Number(amount) <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // 4. Get order details from database
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        client: true,
        token: true
      }
    });

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    if (order.status !== 'CONFIRMED') {
      return NextResponse.json(
        { error: `Order must be in CONFIRMED status to mint. Current status: ${order.status}` },
        { status: 400 }
      );
    }

    // 5. Load environment variables and setup blockchain connection
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      console.error('Missing environment variables:', { rpcUrl: !!rpcUrl, privateKey: !!privateKey });
      return NextResponse.json(
        { error: 'Server configuration error: Missing blockchain credentials' },
        { status: 500 }
      );
    }

    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const wallet = new ethers.Wallet(privateKey, provider);

    console.log('Admin wallet address:', wallet.address);

    // 6. Load token contract ABI
    const contractPath = path.join(process.cwd(), 'src', 'contracts', 'SecurityToken.json');
    const tokenABIContent = fs.readFileSync(contractPath, 'utf8');
    const tokenABI = JSON.parse(tokenABIContent);
    const tokenContract = new ethers.Contract(
      tokenAddress,
      tokenABI.abi,
      wallet
    );

    // 7. Check token decimals for proper amount calculation
    const decimals = await tokenContract.decimals();
    const amountWithDecimals = ethers.parseUnits(amount.toString(), decimals);

    console.log('Token decimals:', decimals.toString());
    console.log('Amount to mint:', amountWithDecimals.toString());

    // 8. Check if admin has minting permissions (AGENT_ROLE or DEFAULT_ADMIN_ROLE)
    try {
      const hasAgentRole = await tokenContract.hasRole(
        await tokenContract.AGENT_ROLE(),
        wallet.address
      );

      const hasAdminRole = await tokenContract.hasRole(
        await tokenContract.DEFAULT_ADMIN_ROLE(),
        wallet.address
      );

      if (!hasAgentRole && !hasAdminRole) {
        return NextResponse.json(
          { error: `Admin wallet ${wallet.address} does not have AGENT_ROLE or DEFAULT_ADMIN_ROLE on this token contract` },
          { status: 403 }
        );
      }

      console.log('Admin permissions:', { hasAgentRole, hasAdminRole });
    } catch (error) {
      console.warn('Could not check admin roles, proceeding anyway:', error);
    }

    // 9. Mint tokens
    console.log('🪙 Minting tokens...');
    const mintTx = await tokenContract.mint(finalRecipientAddress, amountWithDecimals, {
      gasLimit: 500000, // Set a reasonable gas limit
    });

    const transactionHash = mintTx.hash;
    console.log('Transaction submitted:', transactionHash);

    // 10. Wait for transaction confirmation
    console.log('⏳ Waiting for transaction confirmation...');
    const receipt = await mintTx.wait();

    if (receipt.status !== 1) {
      throw new Error('Transaction failed');
    }

    console.log('✅ Transaction confirmed:', receipt.transactionHash);
    console.log('📋 Full receipt object:', JSON.stringify(receipt, null, 2));

    // 11. Update order status to MINTED and save transaction details
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'MINTED',
        transactionHash: transactionHash // Use the hash from the submitted transaction
      }
    });

    console.log('✅ Order status updated to MINTED');

    // 12. Return success response
    console.log('🔗 Transaction hash for response:', transactionHash);

    return NextResponse.json({
      success: true,
      transactionHash: transactionHash,
      blockNumber: receipt.blockNumber.toString(),
      recipientAddress: finalRecipientAddress,
      amount: amount.toString(),
      tokenAddress,
      orderId,
      message: 'Tokens minted successfully'
    });

  } catch (error: any) {
    console.error('❌ Minting error:', error);

    // Return detailed error information
    return NextResponse.json(
      {
        error: error.message || 'Unknown error occurred during minting',
        details: error.reason || error.code || 'No additional details available'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
