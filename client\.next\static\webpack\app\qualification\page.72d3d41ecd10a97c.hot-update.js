"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx":
/*!*********************************************************!*\
  !*** ./src/components/qualification/TokenAgreement.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenAgreement: () => (/* binding */ TokenAgreement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* __next_internal_client_entry_do_not_use__ TokenAgreement auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TokenAgreement(param) {\n    let { onComplete, tokenName = 'Security Token', tokenSymbol = 'TOKEN', isCompleted, agreementText } = param;\n    _s();\n    const [checkboxChecked, setCheckboxChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAcceptAgreement = async ()=>{\n        if (!checkboxChecked) {\n            alert('Please check the agreement checkbox before confirming.');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log('📄 Accepting token agreement for:', tokenName, tokenSymbol);\n            // Call the completion handler (which will save the agreement)\n            await onComplete();\n            console.log('✅ Token agreement accepted successfully');\n        } catch (error) {\n            console.error('❌ Error accepting agreement:', error);\n            alert('Failed to accept agreement. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // If already completed, show completion status\n    if (isCompleted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-green-50 border border-green-200 rounded-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-6 w-6 text-green-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800\",\n                                children: \"Agreement Accepted\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-700\",\n                                children: [\n                                    \"You have successfully accepted the \",\n                                    tokenName,\n                                    \" (\",\n                                    tokenSymbol,\n                                    \") investment agreement.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-12 w-12 text-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: [\n                            tokenName,\n                            \" Investment Agreement\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Please review and accept the specific terms for investing in \",\n                            tokenName,\n                            \" (\",\n                            tokenSymbol,\n                            \").\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-8 w-8 text-blue-600 mt-1 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: [\n                                        tokenName,\n                                        \" (\",\n                                        tokenSymbol,\n                                        \") Investment Terms\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                                    children: agreementText ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-700 whitespace-pre-wrap\",\n                                        children: agreementText\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-700 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: [\n                                                        \"INVESTMENT AGREEMENT FOR \",\n                                                        tokenName.toUpperCase(),\n                                                        \" (\",\n                                                        tokenSymbol,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 22\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"This agreement governs your investment in \",\n                                                    tokenName,\n                                                    \" security tokens. By proceeding, you acknowledge and agree to the following terms:\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"1. Token Details:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 24\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside ml-4 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"Token Name: \",\n                                                                    tokenName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"Token Symbol: \",\n                                                                    tokenSymbol\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Token Type: Security Token\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"2. Investment Terms:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 24\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside ml-4 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"This is a security token offering subject to applicable securities laws\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Tokens are restricted securities and may not be freely transferable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Investment involves significant risk and may result in total loss\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"No guarantee of returns or liquidity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"3. Compliance Requirements:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 24\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside ml-4 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"You must be an accredited investor or qualified purchaser\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"You must complete KYC/AML verification\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"You must comply with transfer restrictions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-4\",\n                                                children: \"This is a simplified version. The complete agreement will be provided upon investment confirmation.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Agreement Acceptance\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" This agreement is specific to \",\n                                        tokenName,\n                                        \" (\",\n                                        tokenSymbol,\n                                        \"). By accepting, you agree to the terms and conditions for this particular token investment.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"token-agreement-checkbox\",\n                                        checked: checkboxChecked,\n                                        onChange: (e)=>setCheckboxChecked(e.target.checked),\n                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"token-agreement-checkbox\",\n                                        className: \"text-sm text-gray-700 leading-relaxed\",\n                                        children: [\n                                            \"I have read, understood, and agree to the \",\n                                            tokenName,\n                                            \" (\",\n                                            tokenSymbol,\n                                            \") investment agreement terms and conditions. I acknowledge that this agreement is legally binding and governs my investment in this specific security token.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAcceptAgreement,\n                                        disabled: !checkboxChecked || isSubmitting,\n                                        className: \"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors \".concat(checkboxChecked && !isSubmitting ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500' : 'bg-gray-300 cursor-not-allowed'),\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Processing Agreement...\"\n                                            ]\n                                        }, void 0, true) : \"Accept \".concat(tokenName, \" Agreement\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    !checkboxChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-xs text-gray-500 text-center\",\n                                        children: \"Please check the agreement checkbox above to enable confirmation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenAgreement, \"ywFypMe+D57CnltBiKE/BAqbYTY=\");\n_c = TokenAgreement;\nvar _c;\n$RefreshReg$(_c, \"TokenAgreement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\n"));

/***/ })

});