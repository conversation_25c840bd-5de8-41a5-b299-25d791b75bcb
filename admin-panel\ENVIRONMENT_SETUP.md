# Environment Setup for Admin Panel

This document explains how to set up the necessary environment variables for the admin panel and API to function properly.

## Required Environment Variables

### For API Server

The API server requires a private key to sign transactions on behalf of the admin. This is necessary for operations that modify the blockchain state, such as upgrading contracts or managing whitelists.

Create a `.env.local` file in the admin-panel directory with the following variables:

```
# Admin wallet private key for signing transactions (required for API routes)
CONTRACT_ADMIN_PRIVATE_KEY=0x...your_private_key_here...

# RPC URLs for different networks (customize as needed)
AMOY_RPC_URL=https://rpc-amoy.polygon.technology/
POLYGON_RPC_URL=https://polygon-rpc.com
```

### Windows Environment Variables

If you prefer to set environment variables directly in your command line:

#### Command Prompt
```cmd
set CONTRACT_ADMIN_PRIVATE_KEY=0x...your_private_key_here...
set AMOY_RPC_URL=https://rpc-amoy.polygon.technology/
set POLYGON_RPC_URL=https://polygon-rpc.com
```

#### PowerShell
```powershell
$env:CONTRACT_ADMIN_PRIVATE_KEY="0x...your_private_key_here..."
$env:AMOY_RPC_URL="https://rpc-amoy.polygon.technology/"
$env:POLYGON_RPC_URL="https://polygon-rpc.com"
```

> **IMPORTANT SECURITY NOTE:** Never commit this file to version control. Add `.env.local` to your `.gitignore` file. The private key should be treated as sensitive information and protected accordingly.

## Security Considerations

### Production Environments

For production environments, consider using a more secure approach for managing private keys:

1. Use a Hardware Security Module (HSM) or secure key management service
2. Set up appropriate access controls and audit logging
3. Consider using a dedicated service account with limited permissions

### Alternative Approach

If you prefer not to configure the API with a private key, you can still use the command-line scripts for contract management:

#### Unix/Linux/Mac:
```bash
# For upgrading contracts
export CONTRACT_ADDRESS=<your-contract-address>
export CONTRACT_TYPE=token
npx hardhat run scripts/04-upgrade-contracts.js --network <your-network>

# For whitelist management
export TOKEN_ADDRESS=<your-token-address>
export ACTION=addToWhitelist
export ADDRESS=<address-to-whitelist>
npx hardhat run scripts/05-manage-token.js --network <your-network>
```

#### Windows Command Prompt:
```cmd
# For upgrading contracts
set CONTRACT_ADDRESS=<your-contract-address>
set CONTRACT_TYPE=token
npx hardhat run scripts/04-upgrade-contracts.js --network <your-network>

# For whitelist management
set TOKEN_ADDRESS=<your-token-address>
set ACTION=addToWhitelist
set ADDRESS=<address-to-whitelist>
npx hardhat run scripts/05-manage-token.js --network <your-network>
```

#### Windows PowerShell:
```powershell
# For upgrading contracts
$env:CONTRACT_ADDRESS="<your-contract-address>"
$env:CONTRACT_TYPE="token"
npx hardhat run scripts/04-upgrade-contracts.js --network <your-network>

# For whitelist management
$env:TOKEN_ADDRESS="<your-token-address>"
$env:ACTION="addToWhitelist"
$env:ADDRESS="<address-to-whitelist>"
npx hardhat run scripts/05-manage-token.js --network <your-network>
```

## Troubleshooting

If you see errors like "CONTRACT_ADMIN_PRIVATE_KEY environment variable is not set", it means your environment is not properly configured for the API routes. Either:

1. Set up the environment variables as described above, or
2. Use the command-line scripts provided in the error messages

## Creating a Safe Admin Account

To create a dedicated admin account for API operations:

1. Create a new wallet specifically for admin operations
2. Grant it the necessary roles (DEFAULT_ADMIN_ROLE, AGENT_ROLE) on your contracts
3. Fund it with enough native tokens for gas fees
4. Store its private key securely, following your organization's security policies