const { ethers } = require("hardhat");

async function main() {
    console.log("🔄 Migrating Whitelist Data to IdentityRegistry...");
    console.log("==================================================");

    const [deployer] = await ethers.getSigners();
    console.log("Migration account:", deployer.address);

    // Get contract addresses from environment
    const identityRegistryAddress = process.env.IDENTITY_REGISTRY_ADDRESS;
    const oldWhitelistAddress = process.env.WHITELIST_ADDRESS; // Old whitelist contract

    if (!identityRegistryAddress) {
        throw new Error("IDENTITY_REGISTRY_ADDRESS not found. Deploy IdentityRegistry first.");
    }

    if (!oldWhitelistAddress) {
        console.log("⚠️ WHITELIST_ADDRESS not found. Skipping migration from old whitelist.");
        console.log("You can manually register identities using the IdentityRegistry contract.");
        return;
    }

    console.log("IdentityRegistry:", identityRegistryAddress);
    console.log("Old Whitelist:", oldWhitelistAddress);

    // Get contract instances
    const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
    const identityRegistry = IdentityRegistry.attach(identityRegistryAddress);

    const Whitelist = await ethers.getContractFactory("Whitelist");
    const oldWhitelist = Whitelist.attach(oldWhitelistAddress);

    // 1. Get whitelisted addresses from old contract
    console.log("\n1️⃣ Fetching whitelisted addresses from old contract...");
    
    let whitelistedAddresses = [];
    try {
        // Try to get whitelisted addresses (method depends on old contract implementation)
        // This is a placeholder - adjust based on your old whitelist contract's interface
        const whitelistCount = await oldWhitelist.getWhitelistCount();
        console.log("Total whitelisted addresses:", whitelistCount.toString());

        for (let i = 0; i < whitelistCount; i++) {
            try {
                const address = await oldWhitelist.getWhitelistedAddress(i);
                whitelistedAddresses.push(address);
            } catch (error) {
                console.log(`⚠️ Failed to get address at index ${i}:`, error.message);
            }
        }
    } catch (error) {
        console.log("❌ Failed to fetch from old whitelist:", error.message);
        console.log("💡 You may need to manually provide addresses to migrate");
        
        // Fallback: Use addresses from admin panel database or manual list
        whitelistedAddresses = [
            // Add known addresses here if automatic migration fails
            // "0x1234...",
            // "0x5678...",
        ];
    }

    console.log(`Found ${whitelistedAddresses.length} addresses to migrate`);

    if (whitelistedAddresses.length === 0) {
        console.log("⚠️ No addresses to migrate. Exiting.");
        return;
    }

    // 2. Register identities in new IdentityRegistry
    console.log("\n2️⃣ Registering identities in IdentityRegistry...");
    
    const defaultCountry = 840; // USA (ISO-3166 numeric code)
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < whitelistedAddresses.length; i++) {
        const address = whitelistedAddresses[i];
        console.log(`Processing ${i + 1}/${whitelistedAddresses.length}: ${address}`);

        try {
            // Check if already registered
            const isVerified = await identityRegistry.isVerified(address);
            if (isVerified) {
                console.log(`  ✅ Already registered, adding to whitelist...`);
                
                // Just add to whitelist if not already
                const isWhitelisted = await identityRegistry.isWhitelisted(address);
                if (!isWhitelisted) {
                    const tx = await identityRegistry.addToWhitelist(address);
                    await tx.wait();
                    console.log(`  ✅ Added to whitelist`);
                } else {
                    console.log(`  ✅ Already whitelisted`);
                }
            } else {
                // Register new identity
                const tx1 = await identityRegistry.registerIdentity(address, defaultCountry);
                await tx1.wait();
                console.log(`  ✅ Identity registered`);

                // Add to whitelist
                const tx2 = await identityRegistry.addToWhitelist(address);
                await tx2.wait();
                console.log(`  ✅ Added to whitelist`);
            }

            successCount++;
        } catch (error) {
            console.log(`  ❌ Failed: ${error.message}`);
            failCount++;
        }

        // Add delay to avoid rate limiting
        if (i < whitelistedAddresses.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    // 3. Migrate KYC data if available
    console.log("\n3️⃣ Migrating KYC data...");
    
    let kycSuccessCount = 0;
    for (let i = 0; i < whitelistedAddresses.length; i++) {
        const address = whitelistedAddresses[i];
        
        try {
            // Check if address was KYC approved in old system
            const isKycApproved = await oldWhitelist.isKycApproved(address);
            if (isKycApproved) {
                const tx = await identityRegistry.approveKyc(address);
                await tx.wait();
                console.log(`  ✅ KYC approved for ${address}`);
                kycSuccessCount++;
            }
        } catch (error) {
            // KYC function might not exist in old contract
            console.log(`  ⚠️ KYC check failed for ${address}: ${error.message}`);
        }
    }

    // 4. Summary
    console.log("\n🎉 Migration Summary");
    console.log("===================");
    console.log(`Total addresses processed: ${whitelistedAddresses.length}`);
    console.log(`Successfully migrated: ${successCount}`);
    console.log(`Failed migrations: ${failCount}`);
    console.log(`KYC approvals migrated: ${kycSuccessCount}`);

    // 5. Verification
    console.log("\n5️⃣ Verifying migration...");
    
    const totalVerified = await identityRegistry.getVerifiedAddressCount();
    console.log(`Total verified addresses in IdentityRegistry: ${totalVerified.toString()}`);

    // Sample verification
    if (whitelistedAddresses.length > 0) {
        const sampleAddress = whitelistedAddresses[0];
        const isVerified = await identityRegistry.isVerified(sampleAddress);
        const isWhitelisted = await identityRegistry.isWhitelisted(sampleAddress);
        const country = isVerified ? await identityRegistry.investorCountry(sampleAddress) : 0;
        
        console.log(`Sample verification (${sampleAddress}):`);
        console.log(`  Verified: ${isVerified}`);
        console.log(`  Whitelisted: ${isWhitelisted}`);
        console.log(`  Country: ${country}`);
    }

    // 6. Next steps
    console.log("\n🔄 Next Steps:");
    console.log("==============");
    console.log("1. Update admin panel to use IdentityRegistry instead of old whitelist");
    console.log("2. Update SecurityToken contracts to use new IdentityRegistry");
    console.log("3. Test the new system thoroughly");
    console.log("4. Consider decommissioning the old whitelist contract");

    return {
        totalProcessed: whitelistedAddresses.length,
        successCount,
        failCount,
        kycSuccessCount
    };
}

// Handle script execution
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ Migration failed:", error);
            process.exit(1);
        });
}

module.exports = main;
