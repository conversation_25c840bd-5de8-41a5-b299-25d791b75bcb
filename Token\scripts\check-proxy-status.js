const { ethers } = require("hardhat");

async function main() {
  // Get contract address from environment variable or use the provided address
  const contractAddress = process.env.CONTRACT_ADDRESS || "******************************************";
  
  console.log(`Checking contract at ${contractAddress}...`);
  
  // Get the network
  const network = await ethers.provider.getNetwork();
  console.log(`Connected to network: ${network.name} (chainId: ${network.chainId})`);
  
  // Get current account
  const [deployer] = await ethers.getSigners();
  console.log(`Using account: ${deployer.address}`);
  
  // Check contract code
  const code = await ethers.provider.getCode(contractAddress);
  console.log(`Contract code size: ${(code.length - 2) / 2} bytes`); // -2 for '0x' prefix, divide by 2 for hex encoding
  
  if (code === "0x") {
    console.log("No contract deployed at this address!");
    return;
  }
  
  // Try to check if it's a token
  try {
    const token = await ethers.getContractAt("SecurityToken", contractAddress);
    console.log("Contract recognized as SecurityToken");
    
    // Check basic token info
    try {
      const name = await token.name();
      const symbol = await token.symbol();
      console.log(`Token name: ${name}`);
      console.log(`Token symbol: ${symbol}`);
    } catch (error) {
      console.log("Failed to get token name/symbol:", error.message);
    }
    
    // Try to get whitelist address
    try {
      const whitelistAddress = await token.whitelistAddress();
      console.log(`Whitelist address: ${whitelistAddress}`);
      
      // Check if the whitelist is functional
      const whitelist = await ethers.getContractAt("Whitelist", whitelistAddress);
      try {
        const isInitialized = await whitelist.hasRole(ethers.ZeroHash, ethers.ZeroAddress);
        console.log(`Whitelist contract initialized: ${isInitialized !== undefined}`);
      } catch (error) {
        console.log("Failed to check whitelist initialization:", error.message);
      }
    } catch (error) {
      console.log("Failed to get whitelist address:", error.message);
    }
  } catch (error) {
    console.log("Not a valid SecurityToken contract:", error.message);
  }
  
  // Check for ERC-1967 proxy implementation slot
  // The implementation slot for ERC-1967 is: 0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc
  const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
  let implementationAddress;
  
  try {
    const implementationData = await ethers.provider.getStorage(contractAddress, implementationSlot);
    implementationAddress = ethers.dataSliceHexString(implementationData, 12); // Convert to address (skip first 12 bytes)
    console.log(`Implementation address: ${implementationAddress}`);
    
    if (implementationAddress === "******************************************") {
      console.log("Not an ERC-1967 proxy (implementation slot is empty)");
    } else {
      console.log("This is an ERC-1967 proxy contract!");
      
      // Check implementation code
      const implCode = await ethers.provider.getCode(implementationAddress);
      console.log(`Implementation code size: ${(implCode.length - 2) / 2} bytes`);
    }
  } catch (error) {
    console.log("Failed to check for ERC-1967 proxy:", error.message);
  }
  
  // Check for admin slot (if it's a transparent proxy)
  // The admin slot for ERC-1967 is: 0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103
  const adminSlot = "0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103";
  
  try {
    const adminData = await ethers.provider.getStorage(contractAddress, adminSlot);
    const adminAddress = ethers.dataSliceHexString(adminData, 12);
    
    if (adminAddress !== "******************************************") {
      console.log(`Admin address: ${adminAddress}`);
      console.log("This is a TransparentUpgradeableProxy");
    }
  } catch (error) {
    console.log("Failed to check for transparent proxy:", error.message);
  }
  
  // Check for standard ERC20 functions to confirm it's at least a token
  try {
    const erc20 = await ethers.getContractAt("ERC20", contractAddress);
    const totalSupply = await erc20.totalSupply();
    console.log(`Behaves like an ERC20 token with total supply: ${ethers.formatEther(totalSupply)}`);
  } catch (error) {
    console.log("Does not behave like an ERC20 token:", error.message);
  }
  
  console.log("\nAnalysis complete.");
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 