const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying FIXED Tokeny-Style ClaimRegistry...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "MATIC");

  try {
    // Deploy the fixed SimpleClaimRegistry
    console.log("\n1️⃣ Deploying Fixed Tokeny-Style ClaimRegistry...");
    
    const SimpleClaimRegistry = await ethers.getContractFactory("SimpleClaimRegistry");
    
    // Deploy with constructor parameter
    const claimRegistry = await SimpleClaimRegistry.deploy(deployer.address, {
      gasLimit: 6000000, // Increased gas limit
      maxFeePerGas: ethers.parseUnits('100', 'gwei'),
      maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
    });

    await claimRegistry.waitForDeployment();
    const claimRegistryAddress = await claimRegistry.getAddress();
    
    console.log("✅ Fixed Tokeny-Style ClaimRegistry deployed to:", claimRegistryAddress);

    // Wait for block confirmations
    console.log("\n⏳ Waiting for block confirmations...");
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Test the fixed functions
    console.log("\n2️⃣ Testing Fixed Tokeny-Style Functions...");
    
    try {
      // Test getTotalClaimTypes (should return 4 now)
      const totalClaimTypes = await claimRegistry.getTotalClaimTypes();
      console.log("📊 Total claim types:", totalClaimTypes.toString());

      // Test getActiveClaimTypes - this should work now!
      const activeClaimTypes = await claimRegistry.getActiveClaimTypes(0, 10);
      console.log("📋 Active claim types with Tokeny-style Topic IDs:");
      
      for (let i = 0; i < activeClaimTypes.length; i++) {
        const claimType = activeClaimTypes[i];
        console.log(`   Topic ID: ${claimType.id} | Name: ${claimType.name}`);
        console.log(`   Description: ${claimType.description}`);
        console.log("   " + "-".repeat(50));
      }

      // Test creating a custom claim with specific Topic ID (like Tokeny example)
      console.log("\n3️⃣ Creating Custom Claim with Tokeny-Style Topic ID...");
      const customTopicId = "10101010000648"; // Exactly like Tokeny example
      const createTx = await claimRegistry.createClaimTypeWithTopicId(
        customTopicId,
        "SPECIFIC_KYC_STATUS",
        "Specific KYC Status for qualified investors - exactly like Tokeny",
        {
          gasLimit: 500000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await createTx.wait();
      console.log(`✅ Custom claim created with Topic ID: ${customTopicId}`);
      console.log("   This matches the Tokeny example exactly!");

      // Test issuing a claim to an investor (on-chain storage)
      console.log("\n4️⃣ Issuing Claim to Investor's ONCHAIN ID...");
      const investorWallet = "******************************************";
      const kycTopicId = "10101010000001"; // KYC_VERIFICATION
      
      const claimData = ethers.toUtf8Bytes(JSON.stringify({
        kycProvider: "Sumsub",
        verificationLevel: "basic-kyc-level",
        verifiedAt: new Date().toISOString(),
        status: "APPROVED",
        investorId: "INV_001"
      }));

      const issueTx = await claimRegistry.issueClaim(
        investorWallet, // This is the investor's ONCHAIN ID
        kycTopicId,
        "0x", // Signature
        claimData,
        "https://api.sumsub.com/resources/applicants/verification",
        0, // No expiration
        {
          gasLimit: 400000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await issueTx.wait();
      console.log(`✅ KYC claim issued to investor's ONCHAIN ID: ${investorWallet}`);
      console.log(`   Topic ID: ${kycTopicId} (KYC_VERIFICATION)`);
      console.log("   Claim is now stored ON-CHAIN for this investor!");

      // Verify the claim was stored on-chain
      const hasValidClaim = await claimRegistry.hasValidClaim(investorWallet, kycTopicId);
      console.log(`🔍 Investor has valid KYC claim on-chain: ${hasValidClaim}`);

      // Issue the custom SPECIFIC_KYC_STATUS claim too
      console.log("\n5️⃣ Issuing SPECIFIC_KYC_STATUS Claim (Tokeny-Style)...");
      const specificKycTx = await claimRegistry.issueClaim(
        investorWallet,
        customTopicId, // 10101010000648
        "0x",
        ethers.toUtf8Bytes("APPROVED_SPECIFIC_KYC"),
        "https://tokeny.com/kyc/specific",
        0,
        {
          gasLimit: 400000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await specificKycTx.wait();
      console.log(`✅ SPECIFIC_KYC_STATUS claim issued with Topic ID: ${customTopicId}`);

      const hasSpecificClaim = await claimRegistry.hasValidClaim(investorWallet, customTopicId);
      console.log(`🔍 Investor has SPECIFIC_KYC_STATUS claim: ${hasSpecificClaim}`);

    } catch (testError) {
      console.log("⚠️ Error testing functions:", testError.message);
    }

    // Display deployment summary
    console.log("\n" + "=" .repeat(60));
    console.log("🎉 FIXED TOKENY-STYLE DEPLOYMENT SUCCESSFUL!");
    console.log("=" .repeat(60));
    console.log("📋 Contract Address:");
    console.log("   ClaimRegistry:", claimRegistryAddress);
    console.log("\n🏷️ Tokeny-Style Topic IDs:");
    console.log("   10101010000001 - KYC_VERIFICATION");
    console.log("   10101010000002 - ACCREDITED_INVESTOR");
    console.log("   10101010000003 - JURISDICTION_COMPLIANCE");
    console.log("   10101010000004 - GENERAL_QUALIFICATION");
    console.log("   10101010000648 - SPECIFIC_KYC_STATUS (Custom - matches Tokeny!)");
    console.log("\n📝 Environment Variables to Update:");
    console.log("   CLAIM_REGISTRY_ADDRESS=" + claimRegistryAddress);
    console.log("   NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS=" + claimRegistryAddress);
    console.log("\n🔧 Key Features:");
    console.log("   ✅ Tokeny-style Topic IDs (10101010000xxx)");
    console.log("   ✅ Claims stored on-chain for each investor's ONCHAIN ID");
    console.log("   ✅ Custom Topic ID creation (like 10101010000648)");
    console.log("   ✅ Efficient claim type iteration (FIXED!)");
    console.log("   ✅ Matches Tokeny's claim structure exactly");

    // Save deployment info
    const fs = require('fs');
    const deploymentInfo = {
      network: "amoy",
      timestamp: new Date().toISOString(),
      deployer: deployer.address,
      contracts: {
        ClaimRegistry: claimRegistryAddress
      },
      tokenyStyleTopicIds: {
        "KYC_VERIFICATION": "10101010000001",
        "ACCREDITED_INVESTOR": "10101010000002", 
        "JURISDICTION_COMPLIANCE": "10101010000003",
        "GENERAL_QUALIFICATION": "10101010000004",
        "SPECIFIC_KYC_STATUS": "10101010000648"
      },
      environmentVariables: {
        CLAIM_REGISTRY_ADDRESS: claimRegistryAddress,
        NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS: claimRegistryAddress
      }
    };

    fs.writeFileSync(
      'deployment-fixed-tokeny-claims.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("\n💾 Deployment info saved to: deployment-fixed-tokeny-claims.json");

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
