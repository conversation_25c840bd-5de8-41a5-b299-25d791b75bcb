const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Granting deployer role with the account:", deployer.address);
    console.log("Network:", networkName);
    console.log("Account balance:", (await hre.ethers.provider.getBalance(deployer.address)).toString());

    // Load deployment information
    const deploymentFile = path.join(__dirname, "../deployments", `${networkName}.json`);
    if (!fs.existsSync(deploymentFile)) {
      console.error(`Deployment file not found: ${deploymentFile}`);
      process.exit(1);
    }

    const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));
    const factoryAddress = deploymentInfo.factory;

    if (!factoryAddress) {
      console.error("Factory address not found in deployment file");
      process.exit(1);
    }

    console.log("Using factory at:", factoryAddress);

    // Connect to the factory contract
    const factory = await hre.ethers.getContractAt("SecurityTokenFactory", factoryAddress);

    // Get the deployer role
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    console.log("DEPLOYER_ROLE:", DEPLOYER_ROLE);

    // Get the address to grant the role to
    const targetAddress = process.env.TARGET_ADDRESS || deployer.address;
    console.log("Target address:", targetAddress);

    // Check if the target already has the role
    const hasRole = await factory.hasRole(DEPLOYER_ROLE, targetAddress);
    console.log("Target already has DEPLOYER_ROLE:", hasRole);

    if (hasRole) {
      console.log("Target address already has DEPLOYER_ROLE, no action needed");
      return;
    }

    // Grant the deployer role
    console.log("Granting DEPLOYER_ROLE to:", targetAddress);
    const tx = await factory.addDeployer(targetAddress);
    console.log("Transaction sent:", tx.hash);
    
    const receipt = await tx.wait();
    console.log("Transaction confirmed in block:", receipt.blockNumber);

    // Verify the role was granted
    const hasRoleAfter = await factory.hasRole(DEPLOYER_ROLE, targetAddress);
    console.log("Target now has DEPLOYER_ROLE:", hasRoleAfter);

    if (hasRoleAfter) {
      console.log("✅ DEPLOYER_ROLE successfully granted!");
    } else {
      console.log("❌ Failed to grant DEPLOYER_ROLE");
    }

  } catch (error) {
    console.error("Error granting deployer role:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
