# Token Deployment API Integration Guide

## Overview

This guide explains how to integrate the token deployment functionality with your admin panel. We've created several scripts to handle token deployment on the Amoy testnet, with increasing levels of resilience against RPC issues.

## Recommended Approach

For API integration, we recommend using the `extreme-deploy-token.js` script, which provides:

1. Multiple RPC endpoint fallbacks
2. Retry logic for all operations
3. Transaction state recovery
4. Comprehensive logging
5. Progress tracking
6. Optimized gas values for successful deployments

## Integration Options

### Option 1: Direct Script Execution (Recommended)

Your API can directly execute the script using Node.js's child process:

```javascript
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

async function deployToken(params) {
  // Set environment variables for the script
  process.env.FACTORY_ADDRESS = params.factoryAddress || '0x1d3b71176345004b14ab24229576d99115914541';
  process.env.TOKEN_NAME = params.tokenName;
  process.env.TOKEN_SYMBOL = params.tokenSymbol;
  process.env.MAX_SUPPLY = params.maxSupply;
  process.env.TOKEN_PRICE = params.tokenPrice || '3';
  process.env.BONUS_TIERS = params.bonusTiers || 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%';
  process.env.GAS_LIMIT = params.gasLimit || '5000000';
  process.env.GAS_PRICE = params.gasPrice || '50';
  if (params.adminAddress) {
    process.env.ADMIN_ADDRESS = params.adminAddress;
  }
  
  return new Promise((resolve, reject) => {
    // Execute the script
    exec('npx hardhat run scripts/extreme-deploy-token.js --network amoy', 
      { maxBuffer: 1024 * 1024 }, // 1MB buffer
      (error, stdout, stderr) => {
        console.log(stdout);
        if (stderr) console.error(stderr);
        
        if (error) {
          // Check if the deployment actually succeeded despite errors
          try {
            const successPath = path.join(__dirname, 'successful-deployments.json');
            if (fs.existsSync(successPath)) {
              const deployments = JSON.parse(fs.readFileSync(successPath));
              const latest = deployments[deployments.length - 1];
              
              // If we have a deployment in the last minute, consider it successful
              const oneMinuteAgo = new Date(Date.now() - 60000);
              if (new Date(latest.endTime) > oneMinuteAgo) {
                resolve(latest);
                return;
              }
            }
          } catch (e) {
            // Ignore errors reading the file
          }
          
          reject(new Error(`Token deployment failed: ${error.message}`));
          return;
        }
        
        // Read the deployment result
        try {
          const successPath = path.join(__dirname, 'successful-deployments.json');
          const deployments = JSON.parse(fs.readFileSync(successPath));
          const latest = deployments[deployments.length - 1];
          resolve(latest);
        } catch (error) {
          reject(new Error(`Deployment may have succeeded but couldn't read result: ${error.message}`));
        }
      }
    );
  });
}
```

### Option 2: Import as Module

The script can also be imported as a module:

```javascript
const deployToken = require('./scripts/extreme-deploy-token');

async function handleDeployRequest(req, res) {
  try {
    // Set environment variables
    process.env.FACTORY_ADDRESS = req.body.factoryAddress || '0x1d3b71176345004b14ab24229576d99115914541';
    process.env.TOKEN_NAME = req.body.tokenName;
    process.env.TOKEN_SYMBOL = req.body.tokenSymbol;
    process.env.MAX_SUPPLY = req.body.maxSupply;
    // Add other parameters as needed
    
    // Execute deployment
    const result = await deployToken();
    res.json({ success: true, token: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
}
```

## Error Handling

The script handles errors extensively and creates log files:

- `successful-deployments.json` - Successful deployments
- `pending-deployments.json` - Deployments in progress 
- `failed-deployments.json` - Failed deployments with error details

Your API should check these files if the script execution fails to determine if the deployment actually succeeded or not.

## Suggested HTTP API Endpoint

Create an endpoint in your admin panel:

```
POST /api/tokens/deploy

Request Body:
{
  "tokenName": "Nova",
  "tokenSymbol": "NNN",
  "maxSupply": "1500000",
  "tokenPrice": "3",
  "bonusTiers": "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%",
  "adminAddress": "0x..." (optional)
}

Response:
{
  "success": true,
  "token": {
    "tokenAddress": "0x...",
    "whitelistAddress": "0x...",
    "tokenName": "Nova",
    "tokenSymbol": "NNN",
    "maxSupply": "1500000",
    "transactionHash": "0x...",
    "status": "success"
  }
}
```

## Checking Deployment Status

Create an endpoint to check the status of pending deployments:

```
GET /api/tokens/deployment-status

Response:
{
  "pendingDeployments": [...],
  "successfulDeployments": [...],
  "failedDeployments": [...]
}
```

## Testing

To test deployment manually from the command line:

```bash
# Windows
.\extreme-deploy.bat

# PowerShell with custom parameters
$env:FACTORY_ADDRESS="0x1d3b71176345004b14ab24229576d99115914541"; $env:TOKEN_NAME="Test Token"; $env:TOKEN_SYMBOL="TEST"; $env:MAX_SUPPLY="1000000"; npx hardhat run scripts/extreme-deploy-token.js --network amoy
``` 