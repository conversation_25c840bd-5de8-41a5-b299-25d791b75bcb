// Script to check token information
const { ethers } = require("hardhat");

async function main() {
  // Get the token address from environment variables
  const tokenAddress = process.env.TOKEN_ADDRESS;
  if (!tokenAddress) {
    throw new Error("TOKEN_ADDRESS environment variable not set");
  }

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log(`Executor: ${deployer.address}`);

  // Connect to the token contract
  const token = await ethers.getContractAt("SecurityToken", tokenAddress);
  console.log(`Connected to SecurityToken at: ${tokenAddress}`);
  
  // Display token information
  console.log("\nToken Information:");
  
  try {
    console.log("\n--- Basic Token Info ---");
    try { console.log(`Name: ${await token.name()}`); } 
    catch (err) { console.log(`Name: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`Symbol: ${await token.symbol()}`); } 
    catch (err) { console.log(`Symbol: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`Total Supply: ${ethers.formatEther(await token.totalSupply())} tokens`); } 
    catch (err) { console.log(`Total Supply: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`Max Supply: ${ethers.formatEther(await token.maxSupply())} tokens`); } 
    catch (err) { console.log(`Max Supply: Error: ${err.shortMessage || err.message}`); }
    
    console.log("\n--- Status Info ---");
    try { console.log(`Paused: ${await token.paused()}`); } 
    catch (err) { console.log(`Paused: Error: ${err.shortMessage || err.message}`); }
    
    console.log("\n--- Whitelist Info ---");
    let whitelistAddress;
    try { 
      whitelistAddress = await token.whitelistAddress();
      console.log(`Whitelist Address: ${whitelistAddress}`);
    } 
    catch (err) { 
      console.log(`Whitelist Address: Error: ${err.shortMessage || err.message}`);
      whitelistAddress = null;
    }
    
    console.log("\n--- Metadata ---");
    try { console.log(`Token Price: ${await token.tokenPrice()}`); } 
    catch (err) { console.log(`Token Price: Error: ${err.shortMessage || err.message}`); }
    
    try { console.log(`Bonus Tiers: ${await token.bonusTiers()}`); } 
    catch (err) { console.log(`Bonus Tiers: Error: ${err.shortMessage || err.message}`); }
    
    if (whitelistAddress) {
      console.log("\n--- Whitelist Management Instructions ---");
      console.log("\nTo manage the whitelist directly, use:");
      console.log(`
# For Unix/Linux/Mac:
export WHITELIST_ADDRESS=${whitelistAddress}
export ACTION=add
export ADDRESS=0xYourAddressHere
npx hardhat run scripts/03-manage-whitelist.js --network amoy

# For Windows Command Prompt:
set WHITELIST_ADDRESS=${whitelistAddress}
set ACTION=add
set ADDRESS=0xYourAddressHere
npx hardhat run scripts/03-manage-whitelist.js --network amoy

# For Windows PowerShell:
$env:WHITELIST_ADDRESS="${whitelistAddress}"
$env:ACTION="add"
$env:ADDRESS="0xYourAddressHere"
npx hardhat run scripts/03-manage-whitelist.js --network amoy
      `);
    }
    
    console.log("\n--- Upgrade Instructions ---");
    console.log("If the token contract needs to be upgraded to add whitelist functions, use:");
    console.log(`
# For Unix/Linux/Mac:
export CONTRACT_ADDRESS=${tokenAddress}
export CONTRACT_TYPE=token
npx hardhat run scripts/04-upgrade-contracts.js --network amoy

# For Windows Command Prompt:
set CONTRACT_ADDRESS=${tokenAddress}
set CONTRACT_TYPE=token
npx hardhat run scripts/04-upgrade-contracts.js --network amoy

# For Windows PowerShell:
$env:CONTRACT_ADDRESS="${tokenAddress}"
$env:CONTRACT_TYPE="token"
npx hardhat run scripts/04-upgrade-contracts.js --network amoy
    `);
    
  } catch (err) {
    console.log(`Error displaying token information: ${err.message}`);
  }
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });