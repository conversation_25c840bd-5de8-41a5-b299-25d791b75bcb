"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wagmi";
exports.ids = ["vendor-chunks/@wagmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: () => (/* binding */ metaMask)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/engine.io-client\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/socket.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-parser\"), __webpack_require__.e(\"vendor-chunks/whatwg-url\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/engine.io-parser\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/utf-8-validate\"), __webpack_require__.e(\"vendor-chunks/tr46\"), __webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/@socket.io\"), __webpack_require__.e(\"vendor-chunks/xmlhttprequest-ssl\"), __webpack_require__.e(\"vendor-chunks/webidl-conversions\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/has-flag\"), __webpack_require__.e(\"vendor-chunks/eventemitter2\"), __webpack_require__.e(\"vendor-chunks/cross-fetch\"), __webpack_require__.e(\"vendor-chunks/@metamask\")]).then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(ssr)/./node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/reconnect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reconnect: () => (/* binding */ reconnect)\n/* harmony export */ });\nlet isReconnecting = false;\n/** https://wagmi.sh/core/api/actions/reconnect */\nasync function reconnect(config, parameters = {}) {\n    // If already reconnecting, do nothing\n    if (isReconnecting)\n        return [];\n    isReconnecting = true;\n    config.setState((x) => ({\n        ...x,\n        status: x.current ? 'reconnecting' : 'connecting',\n    }));\n    const connectors = [];\n    if (parameters.connectors?.length) {\n        for (const connector_ of parameters.connectors) {\n            let connector;\n            // \"Register\" connector if not already created\n            if (typeof connector_ === 'function')\n                connector = config._internal.connectors.setup(connector_);\n            else\n                connector = connector_;\n            connectors.push(connector);\n        }\n    }\n    else\n        connectors.push(...config.connectors);\n    // Try recently-used connectors first\n    let recentConnectorId;\n    try {\n        recentConnectorId = await config.storage?.getItem('recentConnectorId');\n    }\n    catch { }\n    const scores = {};\n    for (const [, connection] of config.state.connections) {\n        scores[connection.connector.id] = 1;\n    }\n    if (recentConnectorId)\n        scores[recentConnectorId] = 0;\n    const sorted = Object.keys(scores).length > 0\n        ? // .toSorted()\n            [...connectors].sort((a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10))\n        : connectors;\n    // Iterate through each connector and try to connect\n    let connected = false;\n    const connections = [];\n    const providers = [];\n    for (const connector of sorted) {\n        const provider = await connector.getProvider().catch(() => undefined);\n        if (!provider)\n            continue;\n        // If we already have an instance of this connector's provider,\n        // then we have already checked it (ie. injected connectors can\n        // share the same `window.ethereum` instance, so we don't want to\n        // connect to it again).\n        if (providers.some((x) => x === provider))\n            continue;\n        const isAuthorized = await connector.isAuthorized();\n        if (!isAuthorized)\n            continue;\n        const data = await connector\n            .connect({ isReconnecting: true })\n            .catch(() => null);\n        if (!data)\n            continue;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        config.setState((x) => {\n            const connections = new Map(connected ? x.connections : new Map()).set(connector.uid, { accounts: data.accounts, chainId: data.chainId, connector });\n            return {\n                ...x,\n                current: connected ? x.current : connector.uid,\n                connections,\n            };\n        });\n        connections.push({\n            accounts: data.accounts,\n            chainId: data.chainId,\n            connector,\n        });\n        providers.push(provider);\n        connected = true;\n    }\n    // Prevent overwriting connected status from race condition\n    if (config.state.status === 'reconnecting' ||\n        config.state.status === 'connecting') {\n        // If connecting didn't succeed, set to disconnected\n        if (!connected)\n            config.setState((x) => ({\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            }));\n        else\n            config.setState((x) => ({ ...x, status: 'connected' }));\n    }\n    isReconnecting = false;\n    return connections;\n}\n//# sourceMappingURL=reconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConnector: () => (/* binding */ createConnector)\n/* harmony export */ });\nfunction createConnector(createConnectorFn) {\n    return createConnectorFn;\n}\n//# sourceMappingURL=createConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vY29ubmVjdG9ycy9jcmVhdGVDb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxjb25uZWN0b3JzXFxjcmVhdGVDb25uZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNvbm5lY3RvcihjcmVhdGVDb25uZWN0b3JGbikge1xuICAgIHJldHVybiBjcmVhdGVDb25uZWN0b3JGbjtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyZWF0ZUNvbm5lY3Rvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/injected.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injected: () => (/* binding */ injected)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\ninjected.type = 'injected';\nfunction injected(parameters = {}) {\n    const { shimDisconnect = true, unstable_shimAsyncInject } = parameters;\n    function getTarget() {\n        const target = parameters.target;\n        if (typeof target === 'function') {\n            const result = target();\n            if (result)\n                return result;\n        }\n        if (typeof target === 'object')\n            return target;\n        if (typeof target === 'string')\n            return {\n                ...(targetMap[target] ?? {\n                    id: target,\n                    name: `${target[0].toUpperCase()}${target.slice(1)}`,\n                    provider: `is${target[0].toUpperCase()}${target.slice(1)}`,\n                }),\n            };\n        return {\n            id: 'injected',\n            name: 'Injected',\n            provider(window) {\n                return window?.ethereum;\n            },\n        };\n    }\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let disconnect;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        get icon() {\n            return getTarget().icon;\n        },\n        get id() {\n            return getTarget().id;\n        },\n        get name() {\n            return getTarget().name;\n        },\n        /** @deprecated */\n        get supportsSimulation() {\n            return true;\n        },\n        type: injected.type,\n        async setup() {\n            const provider = await this.getProvider();\n            // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n            if (provider?.on && parameters.target) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            else if (shimDisconnect) {\n                // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n                try {\n                    const permissions = await provider.request({\n                        method: 'wallet_requestPermissions',\n                        params: [{ eth_accounts: {} }],\n                    });\n                    accounts = permissions[0]?.caveats?.[0]?.value?.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                    // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n                    // switch to `'eth_accounts'` ordering if more than one account is connected\n                    // https://github.com/wevm/wagmi/issues/4140\n                    if (accounts.length > 0) {\n                        const sortedAccounts = await this.getAccounts();\n                        accounts = sortedAccounts;\n                    }\n                }\n                catch (err) {\n                    const error = err;\n                    // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n                    // Only bubble up error if user rejects request\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    // Or prompt is already open\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                        throw error;\n                }\n            }\n            try {\n                if (!accounts?.length && !isReconnecting) {\n                    const requestedAccounts = await provider.request({\n                        method: 'eth_requestAccounts',\n                    });\n                    accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                }\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n                // Add connected shim if no target exists\n                if (!parameters.target)\n                    await config.storage?.setItem('injected.connected', true);\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            // Experimental support for MetaMask disconnect\n            // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n            try {\n                // Adding timeout as not all wallets support this method and can hang\n                // https://github.com/wevm/wagmi/issues/4064\n                await (0,viem__WEBPACK_IMPORTED_MODULE_4__.withTimeout)(() => \n                // TODO: Remove explicit type for viem@3\n                provider.request({\n                    // `'wallet_revokePermissions'` added in `viem@2.10.3`\n                    method: 'wallet_revokePermissions',\n                    params: [{ eth_accounts: {} }],\n                }), { timeout: 100 });\n            }\n            catch { }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect) {\n                await config.storage?.setItem(`${this.id}.disconnected`, true);\n            }\n            if (!parameters.target)\n                await config.storage?.removeItem('injected.connected');\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return Number(hexChainId);\n        },\n        async getProvider() {\n            if (typeof window === 'undefined')\n                return undefined;\n            let provider;\n            const target = getTarget();\n            if (typeof target.provider === 'function')\n                provider = target.provider(window);\n            else if (typeof target.provider === 'string')\n                provider = findProvider(window, target.provider);\n            else\n                provider = target.provider;\n            // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n            // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n            if (provider && !provider.removeListener) {\n                // Try using `off` handler if it exists, otherwise noop\n                if ('off' in provider && typeof provider.off === 'function')\n                    provider.removeListener =\n                        provider.off;\n                else\n                    provider.removeListener = () => { };\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem(`${this.id}.disconnected`));\n                if (isDisconnected)\n                    return false;\n                // Don't allow injected connector to connect if no target is set and it hasn't already connected\n                // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n                // automatically whenever there is a targeted connector configured.\n                if (!parameters.target) {\n                    const connected = await config.storage?.getItem('injected.connected');\n                    if (!connected)\n                        return false;\n                }\n                const provider = await this.getProvider();\n                if (!provider) {\n                    if (unstable_shimAsyncInject !== undefined &&\n                        unstable_shimAsyncInject !== false) {\n                        // If no provider is found, check for async injection\n                        // https://github.com/wevm/references/issues/167\n                        // https://github.com/MetaMask/detect-provider\n                        const handleEthereum = async () => {\n                            if (typeof window !== 'undefined')\n                                window.removeEventListener('ethereum#initialized', handleEthereum);\n                            const provider = await this.getProvider();\n                            return !!provider;\n                        };\n                        const timeout = typeof unstable_shimAsyncInject === 'number'\n                            ? unstable_shimAsyncInject\n                            : 1_000;\n                        const res = await Promise.race([\n                            ...(typeof window !== 'undefined'\n                                ? [\n                                    new Promise((resolve) => window.addEventListener('ethereum#initialized', () => resolve(handleEthereum()), { once: true })),\n                                ]\n                                : []),\n                            new Promise((resolve) => setTimeout(() => resolve(handleEthereum()), timeout)),\n                        ]);\n                        if (res)\n                            return true;\n                    }\n                    throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                }\n                // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n                // immediately resolve JSON-RPC requests on page load.\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_5__.withRetry)(() => this.getAccounts());\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError());\n            const promise = new Promise((resolve) => {\n                const listener = ((data) => {\n                    if ('chainId' in data && data.chainId === chainId) {\n                        config.emitter.off('change', listener);\n                        resolve();\n                    }\n                });\n                config.emitter.on('change', listener);\n            });\n            try {\n                await Promise.all([\n                    provider\n                        .request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId) }],\n                    })\n                        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                        // this callback or an externally emitted `'chainChanged'` event.\n                        // https://github.com/MetaMask/metamask-extension/issues/24247\n                        .then(async () => {\n                        const currentChainId = await this.getChainId();\n                        if (currentChainId === chainId)\n                            config.emitter.emit('change', { chainId });\n                    }),\n                    promise,\n                ]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else if (blockExplorer)\n                            blockExplorerUrls = [\n                                blockExplorer.url,\n                                ...Object.values(blockExplorers).map((x) => x.url),\n                            ];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await Promise.all([\n                            provider\n                                .request({\n                                method: 'wallet_addEthereumChain',\n                                params: [addEthereumChain],\n                            })\n                                .then(async () => {\n                                const currentChainId = await this.getChainId();\n                                if (currentChainId === chainId)\n                                    config.emitter.emit('change', { chainId });\n                                else\n                                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(new Error('User rejected switch after adding network.'));\n                            }),\n                            promise,\n                        ]);\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    }\n                }\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(error);\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0)\n                this.onDisconnect();\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            // Manage EIP-1193 event listeners\n            const provider = await this.getProvider();\n            if (provider) {\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n            // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n            // actually disconnected and we don't need to simulate it.\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (provider) {\n                if (chainChanged) {\n                    provider.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n            }\n        },\n    }));\n}\nconst targetMap = {\n    coinbaseWallet: {\n        id: 'coinbaseWallet',\n        name: 'Coinbase Wallet',\n        provider(window) {\n            if (window?.coinbaseWalletExtension)\n                return window.coinbaseWalletExtension;\n            return findProvider(window, 'isCoinbaseWallet');\n        },\n    },\n    metaMask: {\n        id: 'metaMask',\n        name: 'MetaMask',\n        provider(window) {\n            return findProvider(window, (provider) => {\n                if (!provider.isMetaMask)\n                    return false;\n                // Brave tries to make itself look like MetaMask\n                // Could also try RPC `web3_clientVersion` if following is unreliable\n                if (provider.isBraveWallet && !provider._events && !provider._state)\n                    return false;\n                // Other wallets that try to look like MetaMask\n                const flags = [\n                    'isApexWallet',\n                    'isAvalanche',\n                    'isBitKeep',\n                    'isBlockWallet',\n                    'isKuCoinWallet',\n                    'isMathWallet',\n                    'isOkxWallet',\n                    'isOKExWallet',\n                    'isOneInchIOSWallet',\n                    'isOneInchAndroidWallet',\n                    'isOpera',\n                    'isPhantom',\n                    'isPortal',\n                    'isRabby',\n                    'isTokenPocket',\n                    'isTokenary',\n                    'isUniswapWallet',\n                    'isZerion',\n                ];\n                for (const flag of flags)\n                    if (provider[flag])\n                        return false;\n                return true;\n            });\n        },\n    },\n    phantom: {\n        id: 'phantom',\n        name: 'Phantom',\n        provider(window) {\n            if (window?.phantom?.ethereum)\n                return window.phantom?.ethereum;\n            return findProvider(window, 'isPhantom');\n        },\n    },\n};\nfunction findProvider(window, select) {\n    function isProvider(provider) {\n        if (typeof select === 'function')\n            return select(provider);\n        if (typeof select === 'string')\n            return provider[select];\n        return true;\n    }\n    const ethereum = window.ethereum;\n    if (ethereum?.providers)\n        return ethereum.providers.find((provider) => isProvider(provider));\n    if (ethereum && isProvider(ethereum))\n        return ethereum;\n    return undefined;\n}\n//# sourceMappingURL=injected.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfig: () => (/* binding */ createConfig)\n/* harmony export */ });\n/* harmony import */ var mipd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mipd */ \"(ssr)/./node_modules/mipd/dist/esm/store.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var _connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors/injected.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _createEmitter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createEmitter.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\");\n/* harmony import */ var _createStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createStorage.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/uid.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\n\n\n\n\n\n\n\n\n\nfunction createConfig(parameters) {\n    const { multiInjectedProviderDiscovery = true, storage = (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.createStorage)({\n        storage: (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultStorage)(),\n    }), syncConnectedChain = true, ssr = false, ...rest } = parameters;\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Set up connectors, clients, etc.\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    const mipd = typeof window !== 'undefined' && multiInjectedProviderDiscovery\n        ? (0,mipd__WEBPACK_IMPORTED_MODULE_1__.createStore)()\n        : undefined;\n    const chains = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => rest.chains);\n    const connectors = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => {\n        const collection = [];\n        const rdnsSet = new Set();\n        for (const connectorFns of rest.connectors ?? []) {\n            const connector = setup(connectorFns);\n            collection.push(connector);\n            if (!ssr && connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    rdnsSet.add(rdns);\n                }\n            }\n        }\n        if (!ssr && mipd) {\n            const providers = mipd.getProviders();\n            for (const provider of providers) {\n                if (rdnsSet.has(provider.info.rdns))\n                    continue;\n                collection.push(setup(providerDetailToConnector(provider)));\n            }\n        }\n        return collection;\n    });\n    function setup(connectorFn) {\n        // Set up emitter with uid and add to connector so they are \"linked\" together.\n        const emitter = (0,_createEmitter_js__WEBPACK_IMPORTED_MODULE_3__.createEmitter)((0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_4__.uid)());\n        const connector = {\n            ...connectorFn({\n                emitter,\n                chains: chains.getState(),\n                storage,\n                transports: rest.transports,\n            }),\n            emitter,\n            uid: emitter.uid,\n        };\n        // Start listening for `connect` events on connector setup\n        // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n        emitter.on('connect', connect);\n        connector.setup?.();\n        return connector;\n    }\n    function providerDetailToConnector(providerDetail) {\n        const { info } = providerDetail;\n        const provider = providerDetail.provider;\n        return (0,_connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__.injected)({ target: { ...info, id: info.rdns, provider } });\n    }\n    const clients = new Map();\n    function getClient(config = {}) {\n        const chainId = config.chainId ?? store.getState().chainId;\n        const chain = chains.getState().find((x) => x.id === chainId);\n        // chainId specified and not configured\n        if (config.chainId && !chain)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        {\n            const client = clients.get(store.getState().chainId);\n            if (client && !chain)\n                return client;\n            if (!chain)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        }\n        // If a memoized client exists for a chain id, use that.\n        {\n            const client = clients.get(chainId);\n            if (client)\n                return client;\n        }\n        let client;\n        if (rest.client)\n            client = rest.client({ chain });\n        else {\n            const chainId = chain.id;\n            const chainIds = chains.getState().map((x) => x.id);\n            // Grab all properties off `rest` and resolve for use in `createClient`\n            const properties = {};\n            const entries = Object.entries(rest);\n            for (const [key, value] of entries) {\n                if (key === 'chains' ||\n                    key === 'client' ||\n                    key === 'connectors' ||\n                    key === 'transports')\n                    continue;\n                if (typeof value === 'object') {\n                    // check if value is chainId-specific since some values can be objects\n                    // e.g. { batch: { multicall: { batchSize: 1024 } } }\n                    if (chainId in value)\n                        properties[key] = value[chainId];\n                    else {\n                        // check if value is chainId-specific, but does not have value for current chainId\n                        const hasChainSpecificValue = chainIds.some((x) => x in value);\n                        if (hasChainSpecificValue)\n                            continue;\n                        properties[key] = value;\n                    }\n                }\n                else\n                    properties[key] = value;\n            }\n            client = (0,viem__WEBPACK_IMPORTED_MODULE_7__.createClient)({\n                ...properties,\n                chain,\n                batch: properties.batch ?? { multicall: true },\n                transport: (parameters) => rest.transports[chainId]({ ...parameters, connectors }),\n            });\n        }\n        clients.set(chainId, client);\n        return client;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Create store\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function getInitialState() {\n        return {\n            chainId: chains.getState()[0].id,\n            connections: new Map(),\n            current: null,\n            status: 'disconnected',\n        };\n    }\n    let currentVersion;\n    const prefix = '0.0.0-canary-';\n    if (_version_js__WEBPACK_IMPORTED_MODULE_8__.version.startsWith(prefix))\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.replace(prefix, ''));\n    // use package major version to version store\n    else\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.split('.')[0] ?? '0');\n    const store = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.subscribeWithSelector)(\n    // only use persist middleware if storage exists\n    storage\n        ? (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.persist)(getInitialState, {\n            migrate(persistedState, version) {\n                if (version === currentVersion)\n                    return persistedState;\n                const initialState = getInitialState();\n                const chainId = validatePersistedChainId(persistedState, initialState.chainId);\n                return { ...initialState, chainId };\n            },\n            name: 'store',\n            partialize(state) {\n                // Only persist \"critical\" store properties to preserve storage size.\n                return {\n                    connections: {\n                        __type: 'Map',\n                        value: Array.from(state.connections.entries()).map(([key, connection]) => {\n                            const { id, name, type, uid } = connection.connector;\n                            const connector = { id, name, type, uid };\n                            return [key, { ...connection, connector }];\n                        }),\n                    },\n                    chainId: state.chainId,\n                    current: state.current,\n                };\n            },\n            merge(persistedState, currentState) {\n                // `status` should not be persisted as it messes with reconnection\n                if (typeof persistedState === 'object' &&\n                    persistedState &&\n                    'status' in persistedState)\n                    delete persistedState.status;\n                // Make sure persisted `chainId` is valid\n                const chainId = validatePersistedChainId(persistedState, currentState.chainId);\n                return {\n                    ...currentState,\n                    ...persistedState,\n                    chainId,\n                };\n            },\n            skipHydration: ssr,\n            storage: storage,\n            version: currentVersion,\n        })\n        : getInitialState));\n    store.setState(getInitialState());\n    function validatePersistedChainId(persistedState, defaultChainId) {\n        return persistedState &&\n            typeof persistedState === 'object' &&\n            'chainId' in persistedState &&\n            typeof persistedState.chainId === 'number' &&\n            chains.getState().some((x) => x.id === persistedState.chainId)\n            ? persistedState.chainId\n            : defaultChainId;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Subscribe to changes\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Update default chain when connector chain changes\n    if (syncConnectedChain)\n        store.subscribe(({ connections, current }) => current ? connections.get(current)?.chainId : undefined, (chainId) => {\n            // If chain is not configured, then don't switch over to it.\n            const isChainConfigured = chains\n                .getState()\n                .some((x) => x.id === chainId);\n            if (!isChainConfigured)\n                return;\n            return store.setState((x) => ({\n                ...x,\n                chainId: chainId ?? x.chainId,\n            }));\n        });\n    // EIP-6963 subscribe for new wallet providers\n    mipd?.subscribe((providerDetails) => {\n        const connectorIdSet = new Set();\n        const connectorRdnsSet = new Set();\n        for (const connector of connectors.getState()) {\n            connectorIdSet.add(connector.id);\n            if (connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    connectorRdnsSet.add(rdns);\n                }\n            }\n        }\n        const newConnectors = [];\n        for (const providerDetail of providerDetails) {\n            if (connectorRdnsSet.has(providerDetail.info.rdns))\n                continue;\n            const connector = setup(providerDetailToConnector(providerDetail));\n            if (connectorIdSet.has(connector.id))\n                continue;\n            newConnectors.push(connector);\n        }\n        if (storage && !store.persist.hasHydrated())\n            return;\n        connectors.setState((x) => [...x, ...newConnectors], true);\n    });\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Emitter listeners\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function change(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (!connection)\n                return x;\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts ??\n                        connection.accounts,\n                    chainId: data.chainId ?? connection.chainId,\n                    connector: connection.connector,\n                }),\n            };\n        });\n    }\n    function connect(data) {\n        // Disable handling if reconnecting/connecting\n        if (store.getState().status === 'connecting' ||\n            store.getState().status === 'reconnecting')\n            return;\n        store.setState((x) => {\n            const connector = connectors.getState().find((x) => x.uid === data.uid);\n            if (!connector)\n                return x;\n            if (connector.emitter.listenerCount('connect'))\n                connector.emitter.off('connect', change);\n            if (!connector.emitter.listenerCount('change'))\n                connector.emitter.on('change', change);\n            if (!connector.emitter.listenerCount('disconnect'))\n                connector.emitter.on('disconnect', disconnect);\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts,\n                    chainId: data.chainId,\n                    connector: connector,\n                }),\n                current: data.uid,\n                status: 'connected',\n            };\n        });\n    }\n    function disconnect(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (connection) {\n                const connector = connection.connector;\n                if (connector.emitter.listenerCount('change'))\n                    connection.connector.emitter.off('change', change);\n                if (connector.emitter.listenerCount('disconnect'))\n                    connection.connector.emitter.off('disconnect', disconnect);\n                if (!connector.emitter.listenerCount('connect'))\n                    connection.connector.emitter.on('connect', connect);\n            }\n            x.connections.delete(data.uid);\n            if (x.connections.size === 0)\n                return {\n                    ...x,\n                    connections: new Map(),\n                    current: null,\n                    status: 'disconnected',\n                };\n            const nextConnection = x.connections.values().next().value;\n            return {\n                ...x,\n                connections: new Map(x.connections),\n                current: nextConnection.connector.uid,\n            };\n        });\n    }\n    return {\n        get chains() {\n            return chains.getState();\n        },\n        get connectors() {\n            return connectors.getState();\n        },\n        storage,\n        getClient,\n        get state() {\n            return store.getState();\n        },\n        setState(value) {\n            let newState;\n            if (typeof value === 'function')\n                newState = value(store.getState());\n            else\n                newState = value;\n            // Reset state if it got set to something not matching the base state\n            const initialState = getInitialState();\n            if (typeof newState !== 'object')\n                newState = initialState;\n            const isCorrupt = Object.keys(initialState).some((x) => !(x in newState));\n            if (isCorrupt)\n                newState = initialState;\n            store.setState(newState, true);\n        },\n        subscribe(selector, listener, options) {\n            return store.subscribe(selector, listener, options\n                ? {\n                    ...options,\n                    fireImmediately: options.emitImmediately,\n                    // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n                }\n                : undefined);\n        },\n        _internal: {\n            mipd,\n            store,\n            ssr: Boolean(ssr),\n            syncConnectedChain,\n            transports: rest.transports,\n            chains: {\n                setState(value) {\n                    const nextChains = (typeof value === 'function' ? value(chains.getState()) : value);\n                    if (nextChains.length === 0)\n                        return;\n                    return chains.setState(nextChains, true);\n                },\n                subscribe(listener) {\n                    return chains.subscribe(listener);\n                },\n            },\n            connectors: {\n                providerDetailToConnector,\n                setup: setup,\n                setState(value) {\n                    return connectors.setState(typeof value === 'function' ? value(connectors.getState()) : value, true);\n                },\n                subscribe(listener) {\n                    return connectors.subscribe(listener);\n                },\n            },\n            events: { change, connect, disconnect },\n        },\n    };\n}\n//# sourceMappingURL=createConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createEmitter.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter),\n/* harmony export */   createEmitter: () => (/* binding */ createEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/eventemitter3/index.mjs\");\n\nclass Emitter {\n    constructor(uid) {\n        Object.defineProperty(this, \"uid\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: uid\n        });\n        Object.defineProperty(this, \"_emitter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter()\n        });\n    }\n    on(eventName, fn) {\n        this._emitter.on(eventName, fn);\n    }\n    once(eventName, fn) {\n        this._emitter.once(eventName, fn);\n    }\n    off(eventName, fn) {\n        this._emitter.off(eventName, fn);\n    }\n    emit(eventName, ...params) {\n        const data = params[0];\n        this._emitter.emit(eventName, { uid: this.uid, ...data });\n    }\n    listenerCount(eventName) {\n        return this._emitter.listenerCount(eventName);\n    }\n}\nfunction createEmitter(uid) {\n    return new Emitter(uid);\n}\n//# sourceMappingURL=createEmitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createStorage.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   getDefaultStorage: () => (/* binding */ getDefaultStorage),\n/* harmony export */   noopStorage: () => (/* binding */ noopStorage)\n/* harmony export */ });\n/* harmony import */ var _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/deserialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\");\n/* harmony import */ var _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/serialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\");\n\n\nfunction createStorage(parameters) {\n    const { deserialize = _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize, key: prefix = 'wagmi', serialize = _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize, storage = noopStorage, } = parameters;\n    function unwrap(value) {\n        if (value instanceof Promise)\n            return value.then((x) => x).catch(() => null);\n        return value;\n    }\n    return {\n        ...storage,\n        key: prefix,\n        async getItem(key, defaultValue) {\n            const value = storage.getItem(`${prefix}.${key}`);\n            const unwrapped = await unwrap(value);\n            if (unwrapped)\n                return deserialize(unwrapped) ?? null;\n            return (defaultValue ?? null);\n        },\n        async setItem(key, value) {\n            const storageKey = `${prefix}.${key}`;\n            if (value === null)\n                await unwrap(storage.removeItem(storageKey));\n            else\n                await unwrap(storage.setItem(storageKey, serialize(value)));\n        },\n        async removeItem(key) {\n            await unwrap(storage.removeItem(`${prefix}.${key}`));\n        },\n    };\n}\nconst noopStorage = {\n    getItem: () => null,\n    setItem: () => { },\n    removeItem: () => { },\n};\nfunction getDefaultStorage() {\n    const storage = (() => {\n        if (typeof window !== 'undefined' && window.localStorage)\n            return window.localStorage;\n        return noopStorage;\n    })();\n    return {\n        getItem(key) {\n            return storage.getItem(key);\n        },\n        removeItem(key) {\n            storage.removeItem(key);\n        },\n        setItem(key, value) {\n            try {\n                storage.setItem(key, value);\n                // silence errors by default (QuotaExceededError, SecurityError, etc.)\n            }\n            catch { }\n        },\n    };\n}\n//# sourceMappingURL=createStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/base.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\");\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _BaseError_instances, _BaseError_walk;\n\nclass BaseError extends Error {\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/core';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)();\n    }\n    constructor(shortMessage, options = {}) {\n        super();\n        _BaseError_instances.add(this);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiCoreError'\n        });\n        const details = options.cause instanceof BaseError\n            ? options.cause.details\n            : options.cause?.message\n                ? options.cause.message\n                : options.details;\n        const docsPath = options.cause instanceof BaseError\n            ? options.cause.docsPath || options.docsPath\n            : options.docsPath;\n        this.message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n            ...(docsPath\n                ? [\n                    `Docs: ${this.docsBaseUrl}${docsPath}.html${options.docsSlug ? `#${options.docsSlug}` : ''}`,\n                ]\n                : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: ${this.version}`,\n        ].join('\\n');\n        if (options.cause)\n            this.cause = options.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = options.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, this, fn);\n    }\n}\n_BaseError_instances = new WeakSet(), _BaseError_walk = function _BaseError_walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err.cause)\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, err.cause, fn);\n    return err;\n};\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/config.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChainNotConfiguredError: () => (/* binding */ ChainNotConfiguredError),\n/* harmony export */   ConnectorAccountNotFoundError: () => (/* binding */ ConnectorAccountNotFoundError),\n/* harmony export */   ConnectorAlreadyConnectedError: () => (/* binding */ ConnectorAlreadyConnectedError),\n/* harmony export */   ConnectorChainMismatchError: () => (/* binding */ ConnectorChainMismatchError),\n/* harmony export */   ConnectorNotConnectedError: () => (/* binding */ ConnectorNotConnectedError),\n/* harmony export */   ConnectorNotFoundError: () => (/* binding */ ConnectorNotFoundError),\n/* harmony export */   ConnectorUnavailableReconnectingError: () => (/* binding */ ConnectorUnavailableReconnectingError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ChainNotConfiguredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Chain not configured.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ChainNotConfiguredError'\n        });\n    }\n}\nclass ConnectorAlreadyConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector already connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAlreadyConnectedError'\n        });\n    }\n}\nclass ConnectorNotConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotConnectedError'\n        });\n    }\n}\nclass ConnectorNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotFoundError'\n        });\n    }\n}\nclass ConnectorAccountNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address, connector, }) {\n        super(`Account \"${address}\" not found for connector \"${connector.name}\".`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAccountNotFoundError'\n        });\n    }\n}\nclass ConnectorChainMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connectionChainId, connectorChainId, }) {\n        super(`The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`, {\n            metaMessages: [\n                `Current Chain ID:  ${connectorChainId}`,\n                `Expected Chain ID: ${connectionChainId}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorChainMismatchError'\n        });\n    }\n}\nclass ConnectorUnavailableReconnectingError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n            details: [\n                'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n                'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n                'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n            ].join(' '),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorUnavailableReconnectingError'\n        });\n    }\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/connector.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderNotFoundError: () => (/* binding */ ProviderNotFoundError),\n/* harmony export */   SwitchChainNotSupportedError: () => (/* binding */ SwitchChainNotSupportedError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Provider not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ProviderNotFoundError'\n        });\n    }\n}\nclass SwitchChainNotSupportedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`\"${connector.name}\" does not support programmatic chain switching.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SwitchChainNotSupportedError'\n        });\n    }\n}\n//# sourceMappingURL=connector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Nvbm5lY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0Isb0NBQW9DLCtDQUFTO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTywyQ0FBMkMsK0NBQVM7QUFDM0Qsa0JBQWtCLFdBQVc7QUFDN0Isa0JBQWtCLGVBQWU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGVycm9yc1xcY29ubmVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VFcnJvciB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY2xhc3MgUHJvdmlkZXJOb3RGb3VuZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoJ1Byb3ZpZGVyIG5vdCBmb3VuZC4nKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ1Byb3ZpZGVyTm90Rm91bmRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIFN3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHsgY29ubmVjdG9yIH0pIHtcbiAgICAgICAgc3VwZXIoYFwiJHtjb25uZWN0b3IubmFtZX1cIiBkb2VzIG5vdCBzdXBwb3J0IHByb2dyYW1tYXRpYyBjaGFpbiBzd2l0Y2hpbmcuYCk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25uZWN0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/reconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\");\n\nfunction hydrate(config, parameters) {\n    const { initialState, reconnectOnMount } = parameters;\n    if (initialState && !config._internal.store.persist.hasHydrated())\n        config.setState({\n            ...initialState,\n            chainId: config.chains.some((x) => x.id === initialState.chainId)\n                ? initialState.chainId\n                : config.chains[0].id,\n            connections: reconnectOnMount ? initialState.connections : new Map(),\n            status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n        });\n    return {\n        async onMount() {\n            if (config._internal.ssr) {\n                await config._internal.store.persist.rehydrate();\n                if (config._internal.mipd) {\n                    config._internal.connectors.setState((connectors) => {\n                        const rdnsSet = new Set();\n                        for (const connector of connectors ?? []) {\n                            if (connector.rdns) {\n                                const rdnsValues = Array.isArray(connector.rdns)\n                                    ? connector.rdns\n                                    : [connector.rdns];\n                                for (const rdns of rdnsValues) {\n                                    rdnsSet.add(rdns);\n                                }\n                            }\n                        }\n                        const mipdConnectors = [];\n                        const providers = config._internal.mipd?.getProviders() ?? [];\n                        for (const provider of providers) {\n                            if (rdnsSet.has(provider.info.rdns))\n                                continue;\n                            const connectorFn = config._internal.connectors.providerDetailToConnector(provider);\n                            const connector = config._internal.connectors.setup(connectorFn);\n                            mipdConnectors.push(connector);\n                        }\n                        return [...connectors, ...mipdConnectors];\n                    });\n                }\n            }\n            if (reconnectOnMount)\n                (0,_actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__.reconnect)(config);\n            else if (config.storage)\n                // Reset connections that may have been hydrated from storage.\n                config.setState((x) => ({\n                    ...x,\n                    connections: new Map(),\n                }));\n        },\n    };\n}\n//# sourceMappingURL=hydrate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deserialize.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\nfunction deserialize(value, reviver) {\n    return JSON.parse(value, (key, value_) => {\n        let value = value_;\n        if (value?.__type === 'bigint')\n            value = BigInt(value.value);\n        if (value?.__type === 'Map')\n            value = new Map(value.value);\n        return reviver?.(key, value) ?? value;\n    });\n}\n//# sourceMappingURL=deserialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGRlc2VyaWFsaXplLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBkZXNlcmlhbGl6ZSh2YWx1ZSwgcmV2aXZlcikge1xuICAgIHJldHVybiBKU09OLnBhcnNlKHZhbHVlLCAoa2V5LCB2YWx1ZV8pID0+IHtcbiAgICAgICAgbGV0IHZhbHVlID0gdmFsdWVfO1xuICAgICAgICBpZiAodmFsdWU/Ll9fdHlwZSA9PT0gJ2JpZ2ludCcpXG4gICAgICAgICAgICB2YWx1ZSA9IEJpZ0ludCh2YWx1ZS52YWx1ZSk7XG4gICAgICAgIGlmICh2YWx1ZT8uX190eXBlID09PSAnTWFwJylcbiAgICAgICAgICAgIHZhbHVlID0gbmV3IE1hcCh2YWx1ZS52YWx1ZSk7XG4gICAgICAgIHJldHVybiByZXZpdmVyPy4oa2V5LCB2YWx1ZSkgPz8gdmFsdWU7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZXNlcmlhbGl6ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: () => (/* binding */ extractRpcUrls)\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZXh0cmFjdFJwY1VybHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AsWUFBWSxRQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxPQUFPO0FBQ25FO0FBQ0EsNkJBQTZCLE9BQU87QUFDcEM7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGV4dHJhY3RScGNVcmxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBleHRyYWN0UnBjVXJscyhwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBjaGFpbiB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBmYWxsYmFja1VybCA9IGNoYWluLnJwY1VybHMuZGVmYXVsdC5odHRwWzBdO1xuICAgIGlmICghcGFyYW1ldGVycy50cmFuc3BvcnRzKVxuICAgICAgICByZXR1cm4gW2ZhbGxiYWNrVXJsXTtcbiAgICBjb25zdCB0cmFuc3BvcnQgPSBwYXJhbWV0ZXJzLnRyYW5zcG9ydHM/LltjaGFpbi5pZF0/Lih7IGNoYWluIH0pO1xuICAgIGNvbnN0IHRyYW5zcG9ydHMgPSB0cmFuc3BvcnQ/LnZhbHVlPy50cmFuc3BvcnRzIHx8IFt0cmFuc3BvcnRdO1xuICAgIHJldHVybiB0cmFuc3BvcnRzLm1hcCgoeyB2YWx1ZSB9KSA9PiB2YWx1ZT8udXJsIHx8IGZhbGxiYWNrVXJsKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV4dHJhY3RScGNVcmxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getVersion.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\nconst getVersion = () => `@wagmi/core@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQyx3Q0FBd0MsZ0RBQU8sQ0FBQztBQUN2RCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXGdldFZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4uL3ZlcnNpb24uanMnO1xuZXhwb3J0IGNvbnN0IGdldFZlcnNpb24gPSAoKSA9PiBgQHdhZ21pL2NvcmVAJHt2ZXJzaW9ufWA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRWZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/serialize.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys, cutoff) {\n    return keys.slice(0, cutoff).join('.') || '.';\n}\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array, value) {\n    const { length } = array;\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n    return 0;\n}\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(replacer, circularReplacer) {\n    const hasReplacer = typeof replacer === 'function';\n    const hasCircularReplacer = typeof circularReplacer === 'function';\n    const cache = [];\n    const keys = [];\n    return function replace(key, value) {\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                }\n                else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n                keys[keys.length] = key;\n                const valueCutoff = getCutoff(cache, value);\n                if (valueCutoff !== 0) {\n                    return hasCircularReplacer\n                        ? circularReplacer.call(this, key, value, getReferenceKey(keys, valueCutoff))\n                        : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n                }\n            }\n            else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n        }\n        return hasReplacer ? replacer.call(this, key, value) : value;\n    };\n}\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nfunction serialize(value, replacer, indent, circularReplacer) {\n    return JSON.stringify(value, createReplacer((key, value_) => {\n        let value = value_;\n        if (typeof value === 'bigint')\n            value = { __type: 'bigint', value: value_.toString() };\n        if (value instanceof Map)\n            value = { __type: 'Map', value: Array.from(value_.entries()) };\n        return replacer?.(key, value) ?? value;\n    }, circularReplacer), indent ?? undefined);\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js":
/*!********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/uid.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdXRpbHNcXHVpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzaXplID0gMjU2O1xubGV0IGluZGV4ID0gc2l6ZTtcbmxldCBidWZmZXI7XG5leHBvcnQgZnVuY3Rpb24gdWlkKGxlbmd0aCA9IDExKSB7XG4gICAgaWYgKCFidWZmZXIgfHwgaW5kZXggKyBsZW5ndGggPiBzaXplICogMikge1xuICAgICAgICBidWZmZXIgPSAnJztcbiAgICAgICAgaW5kZXggPSAwO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNpemU7IGkrKykge1xuICAgICAgICAgICAgYnVmZmVyICs9ICgoMjU2ICsgTWF0aC5yYW5kb20oKSAqIDI1NikgfCAwKS50b1N0cmluZygxNikuc3Vic3RyaW5nKDEpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBidWZmZXIuc3Vic3RyaW5nKGluZGV4LCBpbmRleCsrICsgbGVuZ3RoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVpZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/version.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/version.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.17.2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjE3LjInO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\n");

/***/ })

};
;