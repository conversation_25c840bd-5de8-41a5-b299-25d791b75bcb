const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up Admin Panel Database...');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('❌ .env file not found. Creating a basic one...');
  
  const envContent = `# Database
DATABASE_URL="file:./dev.db"

# Admin Panel Configuration
ADMIN_API_BASE_URL="http://localhost:3000/api"

# Next.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
`;

  fs.writeFileSync(envPath, envContent);
  console.log('✅ Created .env file with basic configuration');
}

try {
  console.log('\n📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit', cwd: __dirname });

  console.log('\n🗄️ Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit', cwd: __dirname });

  console.log('\n🔄 Running database migrations...');
  execSync('npx prisma db push', { stdio: 'inherit', cwd: __dirname });

  console.log('\n🌱 Seeding database with test data...');
  
  // Create a simple seed script
  const seedScript = `
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');
  
  // Create a test client
  const testClient = await prisma.client.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: '+1234567890',
      nationality: 'US',
      gender: 'MALE',
      birthday: new Date('1990-01-01'),
      birthPlace: 'New York',
      identificationType: 'PASSPORT',
      passportNumber: 'P123456789',
      documentExpiration: new Date('2030-01-01'),
      occupation: 'Software Engineer',
      sectorOfActivity: 'Technology',
      pepStatus: 'NOT_PEP',
      street: '123 Main St',
      buildingNumber: '1A',
      city: 'New York',
      state: 'NY',
      country: 'US',
      zipCode: '10001',
      sourceOfWealth: 'Employment',
      sourceOfFunds: 'Salary',
      taxIdentificationNumber: 'TAX123456',
      kycStatus: 'PENDING',
      agreementAccepted: false,
    },
  });
  
  console.log('✅ Created test client:', testClient.email);
  
  // Create another test client with agreement accepted
  const testClient2 = await prisma.client.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phoneNumber: '+1234567891',
      nationality: 'US',
      gender: 'FEMALE',
      birthday: new Date('1985-05-15'),
      birthPlace: 'Los Angeles',
      identificationType: 'ID_CARD',
      idCardNumber: 'ID987654321',
      documentExpiration: new Date('2028-05-15'),
      occupation: 'Marketing Manager',
      sectorOfActivity: 'Marketing',
      pepStatus: 'NOT_PEP',
      street: '456 Oak Ave',
      buildingNumber: '2B',
      city: 'Los Angeles',
      state: 'CA',
      country: 'US',
      zipCode: '90210',
      sourceOfWealth: 'Employment',
      sourceOfFunds: 'Salary',
      taxIdentificationNumber: 'TAX654321',
      kycStatus: 'APPROVED',
      agreementAccepted: true,
      agreementAcceptedAt: new Date(),
    },
  });
  
  console.log('✅ Created test client with agreement:', testClient2.email);
  
  console.log('🎉 Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
`;

  fs.writeFileSync(path.join(__dirname, 'seed.js'), seedScript);
  execSync('node seed.js', { stdio: 'inherit', cwd: __dirname });

  console.log('\n🎉 Database setup completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Start the admin panel: npm run dev');
  console.log('2. Visit: http://localhost:3000/clients');
  console.log('3. Test the API: http://localhost:3000/api/status');
  console.log('4. Test client connection from client portal');

} catch (error) {
  console.error('❌ Database setup failed:', error.message);
  console.log('\n🔧 Manual setup steps:');
  console.log('1. cd admin-panel');
  console.log('2. npm install');
  console.log('3. npx prisma generate');
  console.log('4. npx prisma db push');
  console.log('5. npm run dev');
  process.exit(1);
}
