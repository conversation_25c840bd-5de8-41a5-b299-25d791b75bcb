'use client'

import { useState, useEffect } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { CheckCircleIcon, DocumentTextIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

interface AgreementAcceptanceProps {
  onComplete: () => void
  existingProfile?: any
}

export function AgreementAcceptance({ onComplete, existingProfile }: AgreementAcceptanceProps) {
  const [agreementAccepted, setAgreementAccepted] = useState(false)
  const [checkboxChecked, setCheckboxChecked] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Check if user has already accepted agreement
  const { data: agreementStatus } = useQuery({
    queryKey: ['agreement-status'],
    queryFn: async () => {
      const response = await fetch('/api/client/agreement')
      if (response.ok) {
        return response.json()
      }
      return null
    },
  })



  // Accept agreement mutation
  const acceptAgreementMutation = useMutation({
    mutationFn: async () => {
      console.log('📝 Submitting agreement acceptance...')
      const response = await fetch('/api/client/agreement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accepted: true
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || `HTTP ${response.status}`
        console.error('❌ Agreement acceptance failed:', response.status, errorMessage)
        throw new Error(`Failed to accept agreement: ${errorMessage}`)
      }

      const result = await response.json()
      console.log('✅ Agreement acceptance successful:', result)
      return result
    },
    onSuccess: (data) => {
      console.log('✅ Agreement accepted, updating UI and syncing to database...')
      setAgreementAccepted(true)
      onComplete()
    },
    onError: (error: Error) => {
      console.error('❌ Agreement acceptance error:', error)
      alert(`Error accepting agreement: ${error.message}`)
    },
  })

  useEffect(() => {
    // Check if user has already accepted agreement
    if (agreementStatus?.accepted) {
      setAgreementAccepted(true)
      onComplete()
    }

    setIsLoading(false)
  }, [agreementStatus, onComplete])

  const handleAcceptAgreement = () => {
    if (!checkboxChecked) {
      alert('Please check the agreement checkbox before confirming.')
      return
    }
    acceptAgreementMutation.mutate()
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading agreement...</span>
      </div>
    )
  }

  // If already accepted, show completion status
  if (agreementAccepted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-green-800">Agreement Accepted</h3>
            <p className="text-green-700">
              You have successfully accepted the investment agreement.
            </p>
          </div>
        </div>
      </div>
    )
  }



  return (
    <div className="space-y-6">
      {/* Agreement Document Section */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-start space-x-4">
          <DocumentTextIcon className="h-8 w-8 text-blue-600 mt-1" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Investment Agreement
            </h3>
            <p className="text-gray-600 mb-4">
              Please review the investment agreement document before proceeding. This document contains
              important terms and conditions for your investment. The agreement document will be provided
              by your investment advisor or can be accessed through your client portal.
            </p>
          </div>
        </div>
      </div>

      {/* Agreement Acceptance Section */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Agreement Acceptance
        </h3>

        <div className="space-y-4">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <p className="text-sm text-gray-700">
              <strong>Important:</strong> By accepting this agreement, you acknowledge that you have read,
              understood, and agree to be bound by all terms and conditions outlined in the investment
              agreement document.
            </p>
          </div>

          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="agreement-checkbox"
              checked={checkboxChecked}
              onChange={(e) => setCheckboxChecked(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
              disabled={acceptAgreementMutation.isPending}
            />
            <label htmlFor="agreement-checkbox" className="text-sm text-gray-700 leading-relaxed">
              I have read, understood, and agree to the investment agreement terms and conditions.
              I acknowledge that this agreement is legally binding and governs my investment participation.
            </label>
          </div>

          <div className="pt-2">
            <button
              onClick={handleAcceptAgreement}
              disabled={!checkboxChecked || acceptAgreementMutation.isPending}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
                checkboxChecked && !acceptAgreementMutation.isPending
                  ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              {acceptAgreementMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing Agreement...
                </>
              ) : (
                'Confirm Agreement Acceptance'
              )}
            </button>

            {!checkboxChecked && (
              <p className="mt-2 text-xs text-gray-500 text-center">
                Please check the agreement checkbox above to enable confirmation
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
