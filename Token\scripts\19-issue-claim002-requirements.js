const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 Issuing Missing Claims for Claim_002 Token...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Using account:", deployer.address);

  try {
    // Connect to the ClaimRegistry
    const claimRegistryAddress = "******************************************";
    const SimpleClaimRegistry = await ethers.getContractFactory("SimpleClaimRegistry");
    const claimRegistry = SimpleClaimRegistry.attach(claimRegistryAddress);

    const targetWallet = "******************************************";
    
    // Claim_002 requires these claims:
    const requiredClaims = [
      { id: "**************", name: "KYC_VERIFICATION" },
      { id: "**************", name: "GENERAL_QUALIFICATION" },
      { id: "**************", name: "JURISDICTION_COMPLIANCE" },
      { id: "**************", name: "SPECIFIC_KYC_STATUS" },
      { id: "**************", name: "ACCREDITED_INVESTOR" }
    ];

    console.log("\n1️⃣ Checking Current Claims Status...");
    
    // Check which claims the wallet already has
    const claimStatus = {};
    for (const claim of requiredClaims) {
      const hasValidClaim = await claimRegistry.hasValidClaim(targetWallet, claim.id);
      claimStatus[claim.name] = hasValidClaim;
      console.log(`   ${claim.name} (${claim.id}): ${hasValidClaim ? '✅ VALID' : '❌ MISSING'}`);
    }

    console.log("\n2️⃣ Issuing Missing Claims...");

    // Issue missing claims
    const missingClaims = requiredClaims.filter(claim => !claimStatus[claim.name]);
    
    if (missingClaims.length === 0) {
      console.log("✅ All required claims already exist!");
      return;
    }

    console.log(`Found ${missingClaims.length} missing claims to issue:`);
    
    for (const claim of missingClaims) {
      console.log(`\n🔄 Issuing ${claim.name} (${claim.id})...`);
      
      let claimData;
      let uri;
      
      // Customize claim data based on type
      switch (claim.name) {
        case "JURISDICTION_COMPLIANCE":
          claimData = ethers.toUtf8Bytes(JSON.stringify({
            jurisdiction: "USA",
            compliance: "APPROVED",
            verifiedAt: new Date().toISOString(),
            regulations: ["SEC", "FINRA"],
            status: "COMPLIANT"
          }));
          uri = "Jurisdiction compliance verification for USA";
          break;
          
        case "SPECIFIC_KYC_STATUS":
          claimData = ethers.toUtf8Bytes(JSON.stringify({
            kycProvider: "Enhanced KYC",
            level: "ENHANCED",
            verifiedAt: new Date().toISOString(),
            status: "APPROVED",
            riskLevel: "LOW"
          }));
          uri = "Enhanced KYC status verification (Tokeny-style)";
          break;
          
        case "ACCREDITED_INVESTOR":
          claimData = ethers.toUtf8Bytes(JSON.stringify({
            accreditation: "QUALIFIED_INSTITUTIONAL_BUYER",
            verifiedAt: new Date().toISOString(),
            status: "APPROVED",
            netWorth: "VERIFIED",
            income: "VERIFIED"
          }));
          uri = "Accredited investor qualification verification";
          break;
          
        default:
          claimData = ethers.toUtf8Bytes(JSON.stringify({
            type: claim.name,
            verifiedAt: new Date().toISOString(),
            status: "APPROVED"
          }));
          uri = `${claim.name} verification`;
      }

      // Issue the claim
      const tx = await claimRegistry.issueClaim(
        targetWallet,
        claim.id,
        "0x", // Empty signature for admin-issued claims
        claimData,
        uri,
        0, // Never expires
        {
          gasLimit: 400000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );

      console.log(`   Transaction sent: ${tx.hash}`);
      const receipt = await tx.wait();
      console.log(`   ✅ ${claim.name} claim issued successfully!`);
    }

    console.log("\n3️⃣ Final Verification...");
    
    // Verify all claims after issuance
    let allClaimsValid = true;
    for (const claim of requiredClaims) {
      const hasValidClaim = await claimRegistry.hasValidClaim(targetWallet, claim.id);
      console.log(`   ${claim.name} (${claim.id}): ${hasValidClaim ? '✅ VALID' : '❌ MISSING'}`);
      if (!hasValidClaim) allClaimsValid = false;
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 CLAIM ISSUANCE COMPLETE!");
    console.log("=" .repeat(60));
    console.log("📋 Summary:");
    console.log("   Wallet:", targetWallet);
    console.log("   Token: Claim_002");
    console.log("   All required claims valid:", allClaimsValid ? '✅ YES' : '❌ NO');
    console.log("\n🔧 Next Steps:");
    console.log("   1. Refresh the client application");
    console.log("   2. Check if Claim_002 token is now visible");
    console.log("   3. Client should see both Claim_001 and Claim_002 tokens");
    console.log("\n🎯 Token Access Status:");
    console.log("   Claim_001: ✅ Accessible (2/2 claims)");
    console.log("   Claim_002:", allClaimsValid ? '✅ Accessible (5/5 claims)' : '❌ Missing claims');

  } catch (error) {
    console.error("❌ Failed to issue claims:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
