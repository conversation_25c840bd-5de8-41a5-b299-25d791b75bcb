require("dotenv").config();
const { ethers } = require("hardhat");
const SecurityTokenFactoryABI = require("../admin-panel/src/contracts/SecurityTokenFactory.json").abi;

async function main() {
  try {
    console.log("Starting simple token deployment...");
    
    // Use hardcoded values for simplified deployment
    const tokenName = "Test KYC Token";
    const tokenSymbol = "TKYC";
    const maxSupply = "1000000";
    const adminAddress = "******************************************";
    const tokenPrice = "10 USD";
    const bonusTiers = "No bonus tiers";
    const enableKYC = true;
    
    // Factory address - the newly deployed factory with KYC support
    const factoryAddress = "******************************************";
    
    console.log("Deploying token with parameters:");
    console.log(`  Token Name: ${tokenName}`);
    console.log(`  Symbol: ${tokenSymbol}`);
    console.log(`  Max Supply: ${maxSupply}`);
    console.log(`  Admin Address: ${adminAddress}`);
    console.log(`  Token Price: ${tokenPrice}`);
    console.log(`  Bonus Tiers: ${bonusTiers}`);
    console.log(`  KYC Support: ${enableKYC ? 'Enabled' : 'Disabled'}`);
    console.log(`  Factory Address: ${factoryAddress}`);
    
    // Convert maxSupply to wei
    const maxSupplyWei = ethers.parseUnits(maxSupply, 18);
    
    // Get deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`Deployer address: ${deployer.address}`);
    
    // Connect to the factory contract
    const factory = new ethers.Contract(
      factoryAddress,
      SecurityTokenFactoryABI,
      deployer
    );
    
    console.log("Connected to factory, checking permissions...");
    
    // Check if the deployer has the DEPLOYER_ROLE
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    const hasRole = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
    
    if (!hasRole) {
      console.error(`Deployer address ${deployer.address} does not have the DEPLOYER_ROLE`);
      process.exit(1);
    }
    
    console.log("Deployer has the required permissions.");
    
    // Check if KYC is supported - reusing the web app detection code
    console.log("Checking KYC support...");
    let supportsKYC = true;
    
    try {
      // Check if whitelistWithKYCImplementation exists
      const kycImplementation = await factory.whitelistWithKYCImplementation();
      console.log("KYC implementation address:", kycImplementation);
      
      // Check function exists
      const hasKYCFunction = factory.interface.fragments.some(
        fragment => fragment.type === "function" && 
                    fragment.name === "deploySecurityTokenWithOptions"
      );
      
      if (!hasKYCFunction) {
        console.error("Error: Factory contract does not support deploySecurityTokenWithOptions function");
        supportsKYC = false;
      }
      
      if (kycImplementation === ethers.ZeroAddress) {
        console.error("Error: KYC implementation address is not set in the factory contract");
        supportsKYC = false;
      }
    } catch (err) {
      console.error("Error checking KYC support:", err.message);
      supportsKYC = false;
    }
    
    // If KYC is not supported but was requested, warn the user
    if (!supportsKYC && enableKYC) {
      console.warn("KYC was requested but is not supported by this factory contract.");
      console.warn("Proceeding with standard token deployment (no KYC).");
    }
    
    console.log(`Deploying token with${supportsKYC && enableKYC ? '' : 'out'} KYC support...`);
    
    // Deploy token with optimized gas settings
    const gasLimit = ethers.parseUnits("5000000", 0); // 5 million gas
    const gasPrice = ethers.parseUnits("50", "gwei");
    
    console.log("Using optimized gas settings:");
    console.log(`Gas limit: ${gasLimit.toString()}`);
    console.log(`Gas price: ${ethers.formatUnits(gasPrice, "gwei")} gwei`);
    
    let tx;
    if (supportsKYC && enableKYC) {
      console.log("Calling deploySecurityTokenWithOptions with KYC enabled");
      tx = await factory.deploySecurityTokenWithOptions(
        tokenName,
        tokenSymbol,
        maxSupplyWei,
        adminAddress,
        tokenPrice,
        bonusTiers,
        true, // Enable KYC
        { gasLimit, gasPrice }
      );
    } else {
      console.log("Calling deploySecurityToken (no KYC support)");
      tx = await factory.deploySecurityToken(
        tokenName,
        tokenSymbol,
        maxSupplyWei,
        adminAddress,
        tokenPrice,
        bonusTiers,
        { gasLimit, gasPrice }
      );
    }
    
    console.log(`Transaction sent: ${tx.hash}`);
    console.log("Waiting for confirmation...");
    
    // Wait for the transaction to be mined
    const receipt = await tx.wait();
    
    console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
    
    // Get the token address using getTokenAddressBySymbol
    const tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
    
    console.log("Deployment successful!");
    console.log(`Token Address: ${tokenAddress}`);
    
    // Look for the TokenDeployed event to get the whitelist address
    const deployEvent = receipt.logs
      .filter(log => log.topics[0] === factory.interface.getEventTopic('TokenDeployed'))
      .map(log => {
        try {
          return factory.interface.parseLog({
            topics: log.topics,
            data: log.data
          });
        } catch (e) {
          return null;
        }
      })
      .find(event => event !== null);
    
    if (deployEvent) {
      console.log(`Whitelist Address: ${deployEvent.args.whitelistAddress}`);
      console.log(`Has KYC: ${deployEvent.args.hasKYC}`);
    }
    
    console.log("---");
    console.log("All done! The token has been deployed.");
    
  } catch (error) {
    console.error("Error deploying token:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  }); 