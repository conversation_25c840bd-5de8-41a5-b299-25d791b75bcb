// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "./base/BaseIdentityRegistry.sol";
import "./base/BaseKYCRegistry.sol";
import "./interfaces/ICompleteWhitelist.sol";

/**
 * @title WhitelistWithKYC
 * @dev Implementation of the whitelist/identity registry functionality for ERC-3643 compliant tokens with KYC support
 * This contract serves as both a whitelist and an identity registry for a security token
 */
contract WhitelistWithKYC is 
    BaseIdentityRegistry,
    BaseKYCRegistry,
    ICompleteWhitelist
{
    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     */
    function initialize(address admin) public initializer {
        __BaseIdentityRegistry_init(admin);
        __BaseKYCRegistry_init(admin);
    }
    
    /**
     * @dev Special initialization function for factory deployment
     * This function can only be called during initialization
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE, AGENT_ROLE, and KYC_VERIFIER_ROLE
     */
    function initializeWithAgent(address admin) external initializer {
        __BaseIdentityRegistry_init(admin);
        __BaseKYCRegistry_init(admin);
        _grantRole(AGENT_ROLE, admin);
    }
    
    /**
     * @dev Override to approve KYC with agent role check and automatically whitelist
     * @param account The address to approve KYC for
     */
    function approveKyc(address account) public override(BaseKYCRegistry, IKYCRegistry) {
        super.approveKyc(account);
        
        // Also whitelist the address if not already whitelisted
        if (!isWhitelisted(account)) {
            addToWhitelist(account);
        }
    }
    
    /**
     * @dev Override to revoke KYC with agent role check and automatically freeze
     * @param account The address to revoke KYC approval from
     */
    function revokeKyc(address account) public override(BaseKYCRegistry, IKYCRegistry) {
        super.revokeKyc(account);
        
        // Also freeze the address as a safety measure when KYC is revoked
        if (!isFrozen(account)) {
            freezeAddress(account);
        }
    }
    
    /**
     * @dev Override batch approve KYC to also whitelist addresses
     * @param accounts The addresses to approve KYC for
     */
    function batchApproveKyc(address[] calldata accounts) external override(BaseKYCRegistry, IKYCRegistry) {
        uint256 length = accounts.length;
        require(length > 0, "WhitelistWithKYC: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && !isKycApproved(addr)) {
                _approveKyc(addr);
                
                // Also whitelist the address if not already whitelisted
                if (!isWhitelisted(addr)) {
                    addToWhitelist(addr);
                }
            }
        }
    }
    
    /**
     * @dev Override batch revoke KYC to also freeze addresses
     * @param accounts The addresses to revoke KYC approval from
     */
    function batchRevokeKyc(address[] calldata accounts) external override(BaseKYCRegistry, IKYCRegistry) {
        uint256 length = accounts.length;
        require(length > 0, "WhitelistWithKYC: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && isKycApproved(addr)) {
                _revokeKyc(addr);
                
                // Also freeze the address as a safety measure when KYC is revoked
                if (!isFrozen(addr)) {
                    freezeAddress(addr);
                }
            }
        }
    }
}