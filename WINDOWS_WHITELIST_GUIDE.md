# Windows Guide for Whitelist Management

This guide addresses specific issues encountered when running whitelist management commands on Windows systems.

## Common Issues and Solutions

### 1. "ADDRESS environment variable not set or invalid"

If you see this error, it means the script is missing the address that should be whitelisted.

**Solution:**

In PowerShell, you need to set these variables using `$env:` prefix:

```powershell
$env:TOKEN_ADDRESS="0x56f3726C92B8B92a6ab71983886F91718540d888"
$env:ACTION="addToWhitelist"
$env:ADDRESS="0xAddressToWhitelist"
npx hardhat run scripts/05-manage-token.js --network amoy
```

In Command Prompt, use `set` instead:

```cmd
set TOKEN_ADDRESS=0x56f3726C92B8B92a6ab71983886F91718540d888
set ACTION=addToWhitelist
set ADDRESS=0xAddressToWhitelist
npx hardhat run scripts/05-manage-token.js --network amoy
```

### 2. Errors Displaying Token Information

If the script completes the whitelist operation but shows errors when displaying token information, there may be issues with the contract at the provided address:

1. The address might not be a SecurityToken contract
2. The contract might have a different interface than expected
3. The contract might not be initialized properly

**Check your token contract:**

Make sure the TOKEN_ADDRESS points to a valid SecurityToken contract, not a Whitelist or other type of contract.

### 3. Complete Whitelist Command Examples

**For single address whitelisting:**

```powershell
# PowerShell
$env:TOKEN_ADDRESS="0x56f3726C92B8B92a6ab71983886F91718540d888"
$env:ACTION="addToWhitelist"
$env:ADDRESS="0xAddressToWhitelist"
npx hardhat run scripts/05-manage-token.js --network amoy
```

**For batch whitelisting:**

```powershell
# PowerShell
$env:TOKEN_ADDRESS="0x56f3726C92B8B92a6ab71983886F91718540d888"
$env:ACTION="batchAddToWhitelist"
$env:ADDRESSES="0xAddress1,0xAddress2,0xAddress3" 
npx hardhat run scripts/05-manage-token.js --network amoy
```

### 4. Verifying a Successful Operation

Even if token information display shows errors, you can verify the whitelist operation was successful by:

1. The transaction hash is displayed and successful
2. The message "Action completed successfully!" is shown
3. You can check the blockchain explorer for the transaction

## Important Notes

- In PowerShell, environment variables are set with `$env:NAME="value"`
- In Command Prompt, environment variables are set with `set NAME=value`
- Remember that environment variables are cleared when you close the terminal
- Double check that your addresses are valid Ethereum addresses (0x...)

## Troubleshooting "Execution Reverted" Errors

If you see an error message like this:

```
ProviderError: execution reverted
    at HttpProvider.request ...
```

This means the contract rejected the transaction. This commonly occurs because:

1. **The contract needs upgrading** - The token contract might not have whitelist functions yet
2. **Permission issue** - You might not have the AGENT_ROLE required to call this function
3. **Already whitelisted** - The address might already be in the whitelist
4. **Wrong contract type** - The contract might not be a SecurityToken

### How to Upgrade the Contract

If you need to upgrade the contract to add whitelist functionality:

```powershell
# In PowerShell:
$env:CONTRACT_ADDRESS="******************************************"
$env:CONTRACT_TYPE="token"
npx hardhat run scripts/04-upgrade-contracts.js --network amoy
```

After upgrading the contract, try the whitelist operation again.

### Alternative Approach

If the upgrade doesn't work, you can try to manage the whitelist directly through the Whitelist contract:

1. Use the token info helper script to get detailed information and the whitelist address:
   ```powershell
   # Connect to the token and get detailed information including the whitelist address
   $env:TOKEN_ADDRESS="******************************************"
   npx hardhat run scripts/check-token-info.js --network amoy
   ```

   This helper script provides clear information about the token and includes instructions for managing the whitelist.

2. Then use the whitelist management script:
   ```powershell
   $env:WHITELIST_ADDRESS="0xWhitelistAddressFromPreviousStep"
   $env:ACTION="add"
   $env:ADDRESS="0xAddressToWhitelist"
   npx hardhat run scripts/03-manage-whitelist.js --network amoy
   ```