{"name": "client-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 7788", "build": "next build", "start": "next start --port 7788", "lint": "next lint"}, "dependencies": {"@auth0/nextjs-auth0": "^3.7.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@reown/appkit": "^1.7.6", "@reown/appkit-adapter-wagmi": "^1.7.6", "@sumsub/websdk": "^2.3.16", "@tanstack/react-query": "^5.77.0", "@tanstack/react-query-devtools": "^5.77.0", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.9.0", "ethers": "^6.14.1", "framer-motion": "^12.12.2", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "viem": "^2.30.1", "wagmi": "^2.15.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}