# Database Setup Guide

This guide will help you set up the PostgreSQL database for the client management system.

## Prerequisites

- PostgreSQL 12 or higher installed (or MySQL 8.0+ / MariaDB 10.5+)
- Node.js and npm installed
- Admin panel dependencies installed

## Database Setup

### Database Choice

This system is configured for **PostgreSQL** by default, but can be easily adapted for **MySQL/MariaDB**:

**PostgreSQL Advantages:**
- Superior JSON/JSONB support for metadata
- Better enum handling
- Advanced full-text search
- Row-level security for compliance
- Better Prisma integration

**MySQL/MariaDB Alternative:**
If you prefer MySQL/MariaDB, update the `datasource` in `prisma/schema.prisma`:
```prisma
datasource db {
  provider = "mysql"  // Change from "postgresql"
  url      = env("DATABASE_URL")
}
```

### 1. Install Database Server

#### PostgreSQL Installation

#### Windows
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user

#### macOS
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Create Database and User

Connect to PostgreSQL as the postgres user:

```bash
# Windows/Linux
psql -U postgres

# macOS
psql postgres
```

Create the database and user:

```sql
-- Create database
CREATE DATABASE tokendev_clients;

-- Create user (optional, you can use postgres user)
CREATE USER tokendev_user WITH PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE tokendev_clients TO tokendev_user;

-- Exit psql
\q
```

### 3. Configure Environment Variables

Copy the example environment file:

```bash
cd admin-panel
cp .env.example .env.local
```

Update the `.env.local` file with your database configuration:

```env
# Database Configuration
DATABASE_URL="postgresql://tokendev_user:your_secure_password@localhost:5432/tokendev_clients?schema=public"

# Or if using the postgres user:
# DATABASE_URL="postgresql://postgres:your_postgres_password@localhost:5432/tokendev_clients?schema=public"

# Other required variables...
NEXTAUTH_SECRET="your-secret-key-here"
CONTRACT_ADMIN_PRIVATE_KEY=your_private_key_here
```

### 4. Install Dependencies

```bash
cd admin-panel
npm install
```

### 5. Initialize Database Schema

Generate Prisma client:

```bash
npm run db:generate
```

Push the schema to the database:

```bash
npm run db:push
```

### 6. Seed the Database (Optional)

Add sample data for testing:

```bash
npm run db:seed
```

### 7. Verify Setup

Start Prisma Studio to view your database:

```bash
npm run db:studio
```

This will open a web interface at http://localhost:5555 where you can view and manage your data.

## Database Schema Overview

The database includes the following main tables:

### Clients Table
- **Personal Information**: Name, gender, nationality, birthday, birth place
- **Identification**: Document type, numbers, expiration dates
- **Contact**: Phone, email
- **Professional**: Occupation, sector, PEP status
- **Address**: Complete address information
- **Financial**: Source of wealth, bank account, tax ID
- **KYC Status**: Approval status, completion dates, notes
- **Blockchain**: Wallet address, whitelist status

### Client Documents Table
- Document storage metadata
- Verification status
- File information

### Client Transactions Table
- Blockchain transaction history
- Token operations
- Status tracking

## Development Commands

```bash
# Generate Prisma client after schema changes
npm run db:generate

# Push schema changes to database
npm run db:push

# Create and run migrations (production)
npm run db:migrate

# Open database browser
npm run db:studio

# Seed database with sample data
npm run db:seed
```

## Production Considerations

### Security
1. Use strong passwords for database users
2. Enable SSL connections in production
3. Restrict database access to application servers only
4. Regular security updates

### Backup Strategy
1. Set up automated daily backups
2. Test backup restoration procedures
3. Store backups in secure, separate locations

### Performance
1. Add appropriate indexes for frequently queried fields
2. Monitor query performance
3. Consider connection pooling for high traffic

### Environment Variables
```env
# Production database URL
DATABASE_URL="********************************************/tokendev_clients?schema=public&sslmode=require"

# Connection pooling (optional)
DATABASE_URL="********************************************/tokendev_clients?schema=public&sslmode=require&connection_limit=20&pool_timeout=20"
```

## Troubleshooting

### Common Issues

1. **Connection refused**
   - Ensure PostgreSQL is running
   - Check if the port (5432) is correct
   - Verify firewall settings

2. **Authentication failed**
   - Double-check username and password
   - Ensure the user has proper permissions

3. **Database does not exist**
   - Create the database using the SQL commands above
   - Verify the database name in the connection string

4. **Prisma client not found**
   - Run `npm run db:generate` to generate the client
   - Ensure @prisma/client is installed

### Useful Commands

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Connect to database directly
psql -U tokendev_user -d tokendev_clients -h localhost

# View database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

## Next Steps

After setting up the database:

1. Start the development server: `npm run dev`
2. Navigate to the client management section
3. Test creating, viewing, and updating clients
4. Verify KYC and whitelist functionality
5. Check that all data is properly stored and retrieved

For any issues, check the application logs and database logs for error messages.
