import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { reason, adminId = 'system' } = body;

    if (!reason || reason.trim() === '') {
      return NextResponse.json({ error: 'Rejection reason is required' }, { status: 400 });
    }

    // Find the qualification
    const qualification = await prisma.qualificationProgress.findUnique({
      where: { id },
      include: {
        client: true,
        token: true,
      }
    });

    if (!qualification) {
      return NextResponse.json({ error: 'Qualification not found' }, { status: 404 });
    }

    // Update qualification status
    const updatedQualification = await prisma.qualificationProgress.update({
      where: { id },
      data: {
        qualificationStatus: 'REJECTED',
        approvedBy: adminId,
        approvedAt: new Date(),
        rejectedReason: reason.trim(),
      },
      include: {
        client: true,
        token: true,
      }
    });

    // If this was previously approved, remove from whitelist
    if (qualification.qualificationStatus === 'APPROVED' || qualification.qualificationStatus === 'FORCE_APPROVED') {
      try {
        await prisma.client.update({
          where: { id: qualification.clientId },
          data: {
            isWhitelisted: false,
            whitelistedAt: null,
          }
        });

        console.log('Removed client from whitelist due to rejection:', {
          clientEmail: qualification.client.email,
          tokenName: qualification.token?.name || 'Global',
        });
      } catch (error) {
        console.error('Error updating client whitelist status:', error);
        // Don't fail the rejection if whitelist update fails
      }
    }

    console.log('Rejected qualification:', {
      id: updatedQualification.id,
      clientEmail: updatedQualification.client.email,
      tokenName: updatedQualification.token?.name || 'Global',
      reason: reason.trim(),
      rejectedBy: adminId,
    });

    return NextResponse.json({
      success: true,
      message: 'Qualification rejected successfully',
      qualification: updatedQualification,
    });

  } catch (error) {
    console.error('Error rejecting qualification:', error);
    return NextResponse.json(
      { error: 'Failed to reject qualification' },
      { status: 500 }
    );
  }
}
