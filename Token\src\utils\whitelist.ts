import { ethers } from 'ethers';
import WhitelistABI from '../contracts/Whitelist.json';

/**
 * Whitelist utility functions for interacting with the Whitelist contract
 */

// Map of network names to their RPC URLs
const RPC_URLS: Record<string, string> = {
  amoy: 'https://rpc-amoy.polygon.technology',
  polygon: 'https://polygon-rpc.com',
  unknown: 'https://rpc-amoy.polygon.technology' // Default unknown networks to Amoy
};

/**
 * Get the actual network to use, defaults to Amoy for 'unknown' or invalid networks
 */
function getActualNetwork(network: string): string {
  return network === 'unknown' || !RPC_URLS[network] ? 'amoy' : network;
}

// ... rest of the file remains the same 