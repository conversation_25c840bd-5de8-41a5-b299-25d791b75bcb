{"_format": "hh-sol-artifact-1", "contractName": "Compliance", "sourceName": "contracts/Compliance.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "ComplianceRuleAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "ruleId", "type": "bytes32"}], "name": "ComplianceRuleRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "ComplianceRuleUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "holder", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "Holder<PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "holder", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldRegistry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newRegistry", "type": "address"}], "name": "IdentityRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "allowed", "type": "bool"}], "name": "TransferCompliance", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKEN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "maxHolders", "type": "uint256"}, {"internalType": "uint256", "name": "maxTokensPerHolder", "type": "uint256"}, {"internalType": "uint256", "name": "maxTotalSupply", "type": "uint256"}], "name": "addComplianceRule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "canTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "created", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "destroyed", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getActiveRuleIds", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}], "name": "getComplianceRule", "outputs": [{"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "maxHolders", "type": "uint256"}, {"internalType": "uint256", "name": "maxTokensPerHolder", "type": "uint256"}, {"internalType": "uint256", "name": "maxTotalSupply", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "getCountryHolderCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "getCountryLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalHolders", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTransferStats", "outputs": [{"internalType": "uint256", "name": "totalTransfers", "type": "uint256"}, {"internalType": "uint256", "name": "lastTransferTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "contract IdentityRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "_identityRegistry", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "isCountryRestricted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}], "name": "removeComplianceRule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "uint16", "name": "country", "type": "uint16"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "setCountryLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "uint16", "name": "country", "type": "uint16"}, {"internalType": "bool", "name": "restricted", "type": "bool"}], "name": "setCountryRestriction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenAddress", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferred", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "maxHolders", "type": "uint256"}, {"internalType": "uint256", "name": "maxTokensPerHolder", "type": "uint256"}, {"internalType": "uint256", "name": "maxTotalSupply", "type": "uint256"}], "name": "updateComplianceRule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newIdentityRegistry", "type": "address"}], "name": "updateIdentityRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}