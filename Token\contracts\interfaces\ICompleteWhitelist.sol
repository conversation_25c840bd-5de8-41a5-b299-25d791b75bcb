// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "./IIdentityRegistry.sol";
import "./IKYCRegistry.sol";

/**
 * @title ICompleteWhitelist
 * @dev Combined interface for whitelist functionality including both identity management and KYC
 * This ensures backward compatibility with the existing IWhitelist interface
 */
interface ICompleteWhitelist is IIdentityRegistry, IKYCRegistry {
    // This interface combines IIdentityRegistry and IKYCRegistry
    // No additional methods needed, serves as a convenient way to reference
    // the complete whitelist functionality
}