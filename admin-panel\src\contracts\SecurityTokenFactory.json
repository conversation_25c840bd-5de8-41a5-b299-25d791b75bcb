{"abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldTokenImplementation", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newTokenImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "oldWhitelistImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newWhitelistImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "oldWhitelistWithKYCImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newWhitelistWithKYCImplementation", "type": "address"}], "name": "ImplementationsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "symbol", "type": "string"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "admin", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "hasKYC", "type": "bool"}, {"indexed": false, "internalType": "string", "name": "tokenImageUrl", "type": "string"}], "name": "TokenDeployed", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEPLOYER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "deployer", "type": "address"}], "name": "addDeployer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice", "type": "string"}, {"internalType": "string", "name": "bonusTiers", "type": "string"}, {"internalType": "string", "name": "tokenDetails", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl", "type": "string"}], "name": "deploySecurityToken", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "address", "name": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "string", "name": "tokenPrice", "type": "string"}, {"internalType": "string", "name": "bonusTiers", "type": "string"}, {"internalType": "string", "name": "tokenDetails", "type": "string"}, {"internalType": "string", "name": "tokenImageUrl", "type": "string"}, {"internalType": "bool", "name": "withKYC", "type": "bool"}], "name": "deploySecurityTokenWithOptions", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "address", "name": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "deployedTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllDeployedTokens", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getDeployedToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "getTokenAddressBySymbol", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTokenCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "deployer", "type": "address"}], "name": "removeDeployer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "securityTokenImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newTokenImplementation", "type": "address"}, {"internalType": "address", "name": "newWhitelistImplementation", "type": "address"}, {"internalType": "address", "name": "newWhitelistWithKYCImplementation", "type": "address"}], "name": "updateImplementations", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "whitelistImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "whitelistWithKYCImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]}