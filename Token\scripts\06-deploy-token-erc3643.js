const { ethers, upgrades } = require("hardhat");

async function main() {
    console.log("🚀 Deploying ERC-3643 Compliant SecurityToken...");
    console.log("=================================================");

    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

    // Get required contract addresses
    const identityRegistryAddress = process.env.IDENTITY_REGISTRY_ADDRESS;
    const complianceAddress = process.env.COMPLIANCE_ADDRESS;

    if (!identityRegistryAddress) {
        throw new Error("IDENTITY_REGISTRY_ADDRESS not found. Deploy IdentityRegistry first using script 04.");
    }

    if (!complianceAddress) {
        throw new Error("COMPLIANCE_ADDRESS not found. Deploy Compliance first using script 04.");
    }

    console.log("Using IdentityRegistry:", identityRegistryAddress);
    console.log("Using Compliance:", complianceAddress);

    // Token configuration
    const tokenConfig = {
        name: process.env.TOKEN_NAME || "Augment Security Token",
        symbol: process.env.TOKEN_SYMBOL || "AST",
        decimals: parseInt(process.env.TOKEN_DECIMALS) || 0,
        maxSupply: ethers.parseUnits(process.env.TOKEN_MAX_SUPPLY || "1000000", parseInt(process.env.TOKEN_DECIMALS) || 0),
        tokenPrice: process.env.TOKEN_PRICE || "10 USD",
        bonusTiers: process.env.TOKEN_BONUS_TIERS || "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%",
        tokenDetails: process.env.TOKEN_DETAILS || "ERC-3643 compliant security token with advanced compliance features",
        tokenImageUrl: process.env.TOKEN_IMAGE_URL || ""
    };

    console.log("\n📋 Token Configuration:");
    console.log("=======================");
    console.log("Name:", tokenConfig.name);
    console.log("Symbol:", tokenConfig.symbol);
    console.log("Decimals:", tokenConfig.decimals);
    console.log("Max Supply:", ethers.formatUnits(tokenConfig.maxSupply, tokenConfig.decimals));
    console.log("Price:", tokenConfig.tokenPrice);
    console.log("Bonus Tiers:", tokenConfig.bonusTiers);

    // Deploy SecurityToken
    console.log("\n1️⃣ Deploying SecurityToken...");
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    
    const securityToken = await upgrades.deployProxy(
        SecurityToken,
        [
            tokenConfig.name,
            tokenConfig.symbol,
            tokenConfig.decimals,
            tokenConfig.maxSupply,
            identityRegistryAddress,
            complianceAddress,
            deployer.address, // admin
            tokenConfig.tokenPrice,
            tokenConfig.bonusTiers,
            tokenConfig.tokenDetails,
            tokenConfig.tokenImageUrl
        ],
        {
            initializer: "initialize",
            kind: "uups"
        }
    );
    
    await securityToken.waitForDeployment();
    const tokenAddress = await securityToken.getAddress();
    console.log("✅ SecurityToken deployed to:", tokenAddress);

    // Verify deployment
    console.log("\n2️⃣ Verifying deployment...");
    
    try {
        const name = await securityToken.name();
        const symbol = await securityToken.symbol();
        const decimals = await securityToken.decimals();
        const maxSupply = await securityToken.maxSupply();
        const version = await securityToken.version();
        const identityReg = await securityToken.identityRegistry();
        const compliance = await securityToken.compliance();
        
        console.log("✅ Token verification:");
        console.log("  Name:", name);
        console.log("  Symbol:", symbol);
        console.log("  Decimals:", decimals);
        console.log("  Max Supply:", ethers.formatUnits(maxSupply, decimals));
        console.log("  Version:", version);
        console.log("  Identity Registry:", identityReg);
        console.log("  Compliance:", compliance);
    } catch (error) {
        console.log("❌ Verification failed:", error.message);
    }

    // Test compliance integration
    console.log("\n3️⃣ Testing compliance integration...");
    
    try {
        // Test canTransfer function
        const canTransfer = await securityToken.canTransfer(deployer.address, deployer.address, ethers.parseUnits("1", tokenConfig.decimals));
        console.log("✅ canTransfer test:", canTransfer);
        
        // Check if deployer is verified/whitelisted
        const isVerified = await securityToken.isVerified(deployer.address);
        const isWhitelisted = await securityToken.isWhitelisted(deployer.address);
        console.log("✅ Deployer verification status:");
        console.log("  Verified:", isVerified);
        console.log("  Whitelisted:", isWhitelisted);
        
        if (!isVerified) {
            console.log("💡 Note: Deployer is not verified. Register identity first to enable transfers.");
        }
    } catch (error) {
        console.log("❌ Compliance test failed:", error.message);
    }

    // Optional: Register deployer identity and whitelist
    if (process.env.AUTO_REGISTER_DEPLOYER === "true") {
        console.log("\n4️⃣ Auto-registering deployer...");
        
        try {
            const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
            const identityRegistry = IdentityRegistry.attach(identityRegistryAddress);
            
            // Register identity
            const isVerified = await identityRegistry.isVerified(deployer.address);
            if (!isVerified) {
                const tx1 = await identityRegistry.registerIdentity(deployer.address, 840); // USA
                await tx1.wait();
                console.log("✅ Deployer identity registered");
            }
            
            // Add to whitelist
            const isWhitelisted = await identityRegistry.isWhitelisted(deployer.address);
            if (!isWhitelisted) {
                const tx2 = await identityRegistry.addToWhitelist(deployer.address);
                await tx2.wait();
                console.log("✅ Deployer added to whitelist");
            }
            
            // Approve KYC
            const isKycApproved = await identityRegistry.isKycApproved(deployer.address);
            if (!isKycApproved) {
                const tx3 = await identityRegistry.approveKyc(deployer.address);
                await tx3.wait();
                console.log("✅ Deployer KYC approved");
            }
            
        } catch (error) {
            console.log("❌ Auto-registration failed:", error.message);
        }
    }

    // Display deployment summary
    console.log("\n🎉 Deployment Summary");
    console.log("=====================");
    console.log("SecurityToken:", tokenAddress);
    console.log("IdentityRegistry:", identityRegistryAddress);
    console.log("Compliance:", complianceAddress);
    console.log("Admin:", deployer.address);

    // Environment variables
    console.log("\n📝 Add this to your .env.local file:");
    console.log("====================================");
    console.log(`TOKEN_ADDRESS=${tokenAddress}`);

    // Next steps
    console.log("\n🔄 Next Steps:");
    console.log("==============");
    console.log("1. Add the TOKEN_ADDRESS to your .env.local file");
    console.log("2. Register investor identities using IdentityRegistry");
    console.log("3. Configure compliance rules if needed");
    console.log("4. Test token transfers with registered identities");
    console.log("5. Update admin panel to use new token address");

    // Admin panel integration info
    console.log("\n🔧 Admin Panel Integration:");
    console.log("===========================");
    console.log("Use these addresses in your admin panel:");
    console.log(`- Token: ${tokenAddress}`);
    console.log(`- Identity Registry: ${identityRegistryAddress}`);
    console.log(`- Compliance: ${complianceAddress}`);

    return {
        token: tokenAddress,
        identityRegistry: identityRegistryAddress,
        compliance: complianceAddress,
        config: tokenConfig
    };
}

// Handle script execution
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ Deployment failed:", error);
            process.exit(1);
        });
}

module.exports = main;
