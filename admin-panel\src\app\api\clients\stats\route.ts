import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/clients/stats - Get client statistics
export async function GET(request: NextRequest) {
  try {
    // Get total clients
    const totalClients = await prisma.client.count();

    // Get KYC status breakdown
    const kycStats = await prisma.client.groupBy({
      by: ['kycStatus'],
      _count: {
        kycStatus: true,
      },
    });

    // Get whitelist status
    const whitelistStats = await prisma.client.groupBy({
      by: ['isWhitelisted'],
      _count: {
        isWhitelisted: true,
      },
    });

    // Get clients by nationality (top 10)
    const nationalityStats = await prisma.client.groupBy({
      by: ['nationality'],
      _count: {
        nationality: true,
      },
      orderBy: {
        _count: {
          nationality: 'desc',
        },
      },
      take: 10,
    });

    // Get recent registrations (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentRegistrations = await prisma.client.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get PEP status breakdown
    const pepStats = await prisma.client.groupBy({
      by: ['pepStatus'],
      _count: {
        pepStatus: true,
      },
    });

    // Format the response
    const stats = {
      totalClients,
      recentRegistrations,
      kycStatus: kycStats.reduce((acc, item) => {
        acc[item.kycStatus] = item._count.kycStatus;
        return acc;
      }, {} as Record<string, number>),
      whitelistStatus: {
        whitelisted: whitelistStats.find(item => item.isWhitelisted)?._count.isWhitelisted || 0,
        notWhitelisted: whitelistStats.find(item => !item.isWhitelisted)?._count.isWhitelisted || 0,
      },
      topNationalities: nationalityStats.map(item => ({
        nationality: item.nationality,
        count: item._count.nationality,
      })),
      pepStatus: pepStats.reduce((acc, item) => {
        acc[item.pepStatus] = item._count.pepStatus;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching client statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
}
