'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface MockUser {
  sub: string;
  name: string;
  email: string;
  picture: string;
}

interface MockAuthContextType {
  user: MockUser | undefined;
  error: Error | undefined;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
}

// Export MockAuthContextType
export type { MockAuthContextType };

const MockAuthContext = createContext<MockAuthContextType | undefined>(undefined);

export function MockAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<MockUser | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  const login = () => {
    setIsLoading(true);
    setTimeout(() => {
      setUser({
        sub: 'mock-user-123',
        name: 'Demo User',
        email: '<EMAIL>',
        picture: 'https://via.placeholder.com/40'
      });
      setIsLoading(false);
    }, 1000);
  };

  const logout = () => {
    setUser(undefined);
  };

  return (
    <MockAuthContext.Provider value={{
      user,
      error: undefined,
      isLoading,
      login,
      logout
    }}>
      {children}
    </MockAuthContext.Provider>
  );
}

export function useMockUser() {
  const context = useContext(MockAuthContext);
  if (context === undefined) {
    throw new Error('useMockUser must be used within a MockAuthProvider');
  }
  return context;
}
