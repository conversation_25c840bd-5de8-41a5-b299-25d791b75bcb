#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('Starting TokenDev Client Portal...');
console.log('Current directory:', process.cwd());

// Path to the Next.js binary
const nextBin = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');

console.log('Next.js binary path:', nextBin);

// Start the Next.js development server
const child = spawn('node', [nextBin, 'dev', '--port', '3001'], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('Failed to start server:', error);
});

child.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\nShutting down server...');
  child.kill('SIGTERM');
});
