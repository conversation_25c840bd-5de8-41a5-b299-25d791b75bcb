{"name": "admin-panel", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 6677", "dev:turbo": "next dev --turbopack --port 6677", "build": "next build", "start": "next start --port 6677", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.8.2", "@reown/appkit": "^1.7.6", "@reown/appkit-adapter-wagmi": "^1.7.6", "@tanstack/react-query": "^5.77.1", "bcryptjs": "^2.4.3", "dotenv": "^16.5.0", "ethers": "^6.14.1", "jsonwebtoken": "^9.0.2", "next": "15.3.2", "next-auth": "^4.24.10", "react": "^19.0.0", "react-dom": "^19.0.0", "viem": "^2.30.1", "wagmi": "^2.15.4", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^6.8.2", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5"}}