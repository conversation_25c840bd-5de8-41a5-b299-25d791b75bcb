require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  try {
    console.log("🚀 Deploying Updated SecurityTokenFactory with Image URL Support...");
    console.log("=" .repeat(70));

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);

    // Check balance
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");

    // Get network information
    const network = await deployer.provider.getNetwork();
    const networkName = network.name === "unknown" ? "amoy" : network.name;
    console.log("Network:", networkName, "Chain ID:", network.chainId.toString());

    console.log("\n📋 Deployment Configuration:");
    console.log("- Admin Address:", deployer.address);
    console.log("- Network:", networkName);
    console.log("- Gas Price: Auto");

    console.log("\n🏗️  Deploying SecurityTokenFactory...");

    // Deploy the factory with the deployer as admin
    const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    
    const factory = await SecurityTokenFactory.deploy(
      deployer.address, // admin
      {
        gasLimit: 6000000, // Increase gas limit for complex deployment
        gasPrice: ethers.parseUnits("50", "gwei") // Set gas price for Amoy
      }
    );

    console.log("Transaction sent:", factory.deploymentTransaction().hash);
    console.log("Waiting for deployment confirmation...");

    await factory.waitForDeployment();
    const factoryAddress = await factory.getAddress();

    console.log("\n✅ DEPLOYMENT SUCCESSFUL!");
    console.log("=" .repeat(70));
    console.log("📍 Factory Address:", factoryAddress);

    // Verify the deployment by checking the implementations
    console.log("\n🔍 Verifying Deployment...");
    
    try {
      const tokenImpl = await factory.securityTokenImplementation();
      const whitelistImpl = await factory.whitelistImplementation();
      const whitelistKYCImpl = await factory.whitelistWithKYCImplementation();
      
      console.log("✅ Token Implementation:", tokenImpl);
      console.log("✅ Whitelist Implementation:", whitelistImpl);
      console.log("✅ Whitelist with KYC Implementation:", whitelistKYCImpl);

      // Test enumeration functions
      const tokenCount = await factory.getTokenCount();
      console.log("✅ Token Count:", tokenCount.toString());

      // Test DEPLOYER_ROLE
      const deployerRole = await factory.DEPLOYER_ROLE();
      const hasRole = await factory.hasRole(deployerRole, deployer.address);
      console.log("✅ DEPLOYER_ROLE:", deployerRole);
      console.log("✅ Admin has DEPLOYER_ROLE:", hasRole);

      // Test if the factory supports image URLs by checking function signatures
      const factoryInterface = factory.interface;
      const hasImageUrlSupport = factoryInterface.fragments.some(
        fragment => fragment.type === "function" && 
                   'name' in fragment && 
                   fragment.name === "deploySecurityToken" &&
                   fragment.inputs.some(input => input.name === "tokenImageUrl")
      );
      
      console.log("✅ Image URL Support:", hasImageUrlSupport ? "YES" : "NO");

    } catch (error) {
      console.error("❌ Verification failed:", error.message);
    }

    console.log("\n📋 DEPLOYMENT SUMMARY:");
    console.log("=" .repeat(70));
    console.log("🏭 Factory Address:", factoryAddress);
    console.log("👤 Admin Address:", deployer.address);
    console.log("🌐 Network:", networkName);
    console.log("⛽ Gas Used: Check transaction receipt");
    console.log("🔗 Transaction:", factory.deploymentTransaction().hash);

    console.log("\n🔧 NEXT STEPS:");
    console.log("1. Update admin panel config with new factory address:");
    console.log(`   factory: "${factoryAddress}"`);
    console.log("2. Test token deployment with image URL support");
    console.log("3. Update any existing references to the old factory");

    console.log("\n🎯 ADMIN PANEL CONFIGURATION:");
    console.log("Add this to your admin-panel/src/config.ts:");
    console.log(`amoy: {`);
    console.log(`  factory: "${factoryAddress}",`);
    console.log(`  // ... other addresses`);
    console.log(`}`);

    // Save deployment info to a file
    const deploymentInfo = {
      network: networkName,
      chainId: network.chainId.toString(),
      factoryAddress: factoryAddress,
      adminAddress: deployer.address,
      deploymentHash: factory.deploymentTransaction().hash,
      timestamp: new Date().toISOString(),
      imageUrlSupport: true,
      enumerationSupport: true
    };

    const fs = require('fs');
    const path = require('path');
    const deploymentsDir = path.join(__dirname, '../deployments');
    
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    const deploymentFile = path.join(deploymentsDir, `factory-${networkName}-${Date.now()}.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("\n💾 Deployment info saved to:", deploymentFile);

  } catch (error) {
    console.error("💥 Deployment failed:", error);
    process.exitCode = 1;
  }
}

// Run the deployment
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
