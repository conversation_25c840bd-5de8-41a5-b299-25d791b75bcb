// Test whitelist API directly
const fetch = require('node-fetch');

async function testWhitelistAPI() {
  console.log('=== Testing Whitelist API Directly ===');
  
  const testWallet = '******************************************';
  const testTokens = ['******************************************'];
  
  try {
    console.log(`Testing wallet: ${testWallet}`);
    console.log(`Testing tokens: ${testTokens.join(', ')}`);
    
    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: testWallet,
        tokenAddresses: testTokens
      })
    });
    
    console.log(`Response status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

async function checkClientInDatabase() {
  console.log('\n=== Checking Client in Database ===');
  
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  
  try {
    const testWallet = '******************************************';
    
    // Check if client exists
    const client = await prisma.client.findUnique({
      where: { walletAddress: testWallet.toLowerCase() },
      include: {
        tokenApprovals: {
          include: {
            token: {
              select: { address: true, symbol: true, name: true }
            }
          }
        }
      }
    });
    
    if (client) {
      console.log('✅ Client found in database:');
      console.log(`   ID: ${client.id}`);
      console.log(`   Email: ${client.email}`);
      console.log(`   Wallet: ${client.walletAddress}`);
      console.log(`   Global Whitelisted: ${client.isWhitelisted}`);
      console.log(`   KYC Status: ${client.kycStatus}`);
      console.log(`   Token Approvals: ${client.tokenApprovals.length}`);
      
      client.tokenApprovals.forEach(approval => {
        console.log(`     - ${approval.token.symbol}: ${approval.whitelistApproved ? 'WHITELISTED' : 'NOT WHITELISTED'}`);
      });
    } else {
      console.log('❌ Client not found in database');
      
      // Try different case variations
      const variations = [
        testWallet,
        testWallet.toLowerCase(),
        testWallet.toUpperCase()
      ];
      
      for (const variation of variations) {
        const found = await prisma.client.findUnique({
          where: { walletAddress: variation }
        });
        if (found) {
          console.log(`✅ Found client with wallet address: ${variation}`);
          break;
        }
      }
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  await checkClientInDatabase();
  await testWhitelistAPI();
}

main().catch(console.error);
