import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { createAuthHeaders } from './jwt';

export interface User {
  sub: string;
  email?: string;
  name?: string;
}



export interface ClientProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  kycStatus: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  kycNotes?: string;
  isWhitelisted: boolean;
  walletAddress?: string;
  createdAt: string;

  // Personal Information
  gender?: string;
  nationality?: string;
  birthday?: string;
  birthPlace?: string;

  // Identification & Contact
  identificationType?: string;
  passportNumber?: string;
  idCardNumber?: string;
  documentExpiration?: string;
  phoneNumber?: string;

  // Professional Information
  occupation?: string;
  sectorOfActivity?: string;
  pepStatus?: string;
  pepDetails?: string;

  // Address Information
  street?: string;
  buildingNumber?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;

  // Financial Information
  sourceOfWealth?: string;
  bankAccountNumber?: string;
  sourceOfFunds?: string;
  taxIdentificationNumber?: string;
}

class ApiClient {
  private client: AxiosInstance;
  private user: User | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL!,
      timeout: 10000,
      withCredentials: true, // Include cookies for Auth0 session
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          window.location.href = '/api/auth/login';
        }
        return Promise.reject(error);
      }
    );
  }

  setUser(user: User) {
    this.user = user;
  }



  // Client profile API calls
  async getClientProfile(): Promise<ClientProfile | null> {
    try {
      const response = await this.client.get('/client/profile');
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 404) {
        return null; // Client profile doesn't exist yet
      }
      throw error;
    }
  }

  async createClientProfile(profileData: any): Promise<ClientProfile> {
    const response = await this.client.post('/client/profile', profileData);
    return response.data;
  }

  async updateClientProfile(profileData: Partial<ClientProfile>): Promise<ClientProfile> {
    const response = await this.client.put('/client/profile', profileData);
    return response.data;
  }

  // KYC-related API calls
  async submitKYCApplication(kycData: any): Promise<{ success: boolean; message: string }> {
    const response = await this.client.post('/client/kyc', kycData);
    return response.data;
  }

  async getKYCStatus(): Promise<{ status: string; notes?: string; submittedAt?: string }> {
    const response = await this.client.get('/client/kyc');
    return response.data;
  }

  // Whitelist-related API calls
  async getWhitelistStatus(): Promise<{ isWhitelisted: boolean; walletAddress?: string }> {
    const response = await this.client.get('/client/whitelist');
    return response.data;
  }

  // Generic API call method
  async request<T>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.client.request<T>(config);
    return response.data;
  }
}

// Create a singleton instance
export const apiClient = new ApiClient();

// Hook for React components
export function useApiClient() {
  return apiClient;
}
