"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                const newStepData = {\n                    country: qualificationProgress.country || '',\n                    agreementAccepted: qualificationProgress.agreementAccepted || false,\n                    profileCompleted: qualificationProgress.profileCompleted || !!profile,\n                    walletConnected: qualificationProgress.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: qualificationProgress.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = qualificationProgress.currentStep || 0;\n                // Validate and adjust step based on actual completion status\n                if (!newStepData.country) {\n                    calculatedStep = 0; // Country selection\n                } else if (!newStepData.agreementAccepted) {\n                    calculatedStep = 1; // Agreement acceptance\n                } else if (!newStepData.profileCompleted) {\n                    calculatedStep = 2; // Profile completion\n                } else if (!newStepData.walletConnected) {\n                    calculatedStep = 3; // Wallet connection\n                } else if (!newStepData.kycCompleted) {\n                    calculatedStep = 4; // KYC verification\n                } else {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: qualificationProgress\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            const progressData = {\n                ...updatedStepData,\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: Object.values(updatedStepData).filter(Boolean).length\n            };\n            console.log('💾 Saving progress:', progressData);\n            // Save to localStorage for immediate persistence\n            const storageKey = \"qualification_progress_\".concat(tokenAddress);\n            localStorage.setItem(storageKey, JSON.stringify(progressData));\n            console.log('💾 Saved to localStorage:', storageKey);\n            // Save to backend\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border text-center \".concat(getStepColor(step.status)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Complete!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". You can now proceed with your investment.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});