# Client Management System

A comprehensive client management system for tokenization platforms, featuring KY<PERSON> (Know Your Customer) compliance, document management, and blockchain integration.

## Features

### 🔐 Complete KYC Compliance
- **Personal Information**: Full name, gender, nationality, birthday, birth place
- **Identification Documents**: Passport, ID card, driver's license support
- **Contact Information**: Phone number, email address
- **Professional Details**: Occupation, sector of activity, PEP status
- **Address Information**: Complete address with international support
- **Financial Information**: Source of wealth, bank details, tax identification

### 📊 Client Management
- **Client Registration**: Comprehensive onboarding form
- **Client Search & Filtering**: Advanced search by multiple criteria
- **KYC Status Management**: Pending, In Review, Approved, Rejected, Expired
- **Document Management**: Upload, verify, and manage client documents
- **Audit Trail**: Complete history of changes and updates

### 🔗 Blockchain Integration
- **Wallet Management**: Link client profiles to blockchain wallets
- **Whitelist Management**: Add/remove clients from token whitelists
- **Transaction Tracking**: Monitor client blockchain transactions
- **Token Operations**: Mint, transfer, freeze/unfreeze tokens

### 📈 Analytics & Reporting
- **Client Statistics**: Total clients, KYC status breakdown
- **Geographic Distribution**: Clients by nationality
- **PEP Status Monitoring**: Track politically exposed persons
- **Recent Activity**: Monitor new registrations and updates

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Validation**: Zod schema validation
- **State Management**: TanStack Query (React Query)
- **Blockchain**: Ethers.js integration

## Database Schema

### Core Tables

#### Clients
```sql
- id (Primary Key)
- Personal info (firstName, lastName, gender, nationality, birthday, birthPlace)
- Identification (type, numbers, expiration)
- Contact (phone, email)
- Professional (occupation, sector, pepStatus, pepDetails)
- Address (street, building, city, state, country, zipCode)
- Financial (sourceOfWealth, bankAccount, sourceOfFunds, taxId)
- KYC status and dates
- Blockchain (walletAddress, isWhitelisted, whitelistedAt)
- Audit fields (createdAt, updatedAt, createdBy, updatedBy)
```

#### Client Documents
```sql
- Document metadata and verification status
- File information and storage paths
- Verification history and notes
```

#### Client Transactions
```sql
- Blockchain transaction history
- Token operations and status
- Gas usage and transaction details
```

## API Endpoints

### Client Management
- `GET /api/clients` - List clients with pagination and filtering
- `POST /api/clients` - Create new client
- `GET /api/clients/[id]` - Get client details
- `PUT /api/clients/[id]` - Update client information
- `DELETE /api/clients/[id]` - Delete client

### KYC Management
- `GET /api/clients/[id]/kyc` - Get KYC details
- `PUT /api/clients/[id]/kyc` - Update KYC status

### Whitelist Management
- `GET /api/clients/[id]/whitelist` - Get whitelist status
- `PUT /api/clients/[id]/whitelist` - Update whitelist status

### Analytics
- `GET /api/clients/stats` - Get client statistics

## Setup Instructions

### 1. Database Setup
Follow the [Database Setup Guide](./DATABASE_SETUP.md) to configure PostgreSQL.

### 2. Environment Configuration
```bash
cp .env.example .env.local
```

Update `.env.local` with your configuration:
```env
DATABASE_URL="postgresql://user:password@localhost:5432/tokendev_clients"
NEXTAUTH_SECRET="your-secret-key"
CONTRACT_ADMIN_PRIVATE_KEY="your-private-key"
```

### 3. Install Dependencies
```bash
npm install
```

### 4. Initialize Database
```bash
npm run db:generate
npm run db:push
npm run db:seed  # Optional: Add sample data
```

### 5. Start Development Server
```bash
npm run dev
```

## Usage Guide

### Adding New Clients

1. Navigate to the Client Management section
2. Click "Add New Client"
3. Fill out the comprehensive form with all required information:
   - Personal details
   - Identification documents
   - Contact information
   - Professional information
   - Address details
   - Financial information
4. Submit the form to create the client profile

### Managing KYC Status

1. View client details
2. Navigate to KYC section
3. Update status based on document verification:
   - **Pending**: Initial status for new clients
   - **In Review**: Documents submitted and under review
   - **Approved**: KYC completed successfully
   - **Rejected**: KYC failed verification
   - **Expired**: KYC approval has expired
4. Add notes explaining status changes

### Whitelist Management

1. Ensure client has approved KYC status
2. Navigate to whitelist section
3. Add wallet address
4. Enable whitelist status
5. System will update both database and blockchain contract

### Searching and Filtering

Use the advanced search and filtering options:
- **Text Search**: Search by name, email, phone, or tax ID
- **KYC Status Filter**: Filter by approval status
- **Whitelist Filter**: Show only whitelisted/non-whitelisted clients
- **Sorting**: Sort by creation date, name, or status

## Security Considerations

### Data Protection
- All sensitive data is encrypted at rest
- API endpoints require proper authentication
- Input validation using Zod schemas
- SQL injection protection via Prisma ORM

### Compliance Features
- Complete audit trail for all changes
- Document verification workflow
- PEP status monitoring
- GDPR compliance ready

### Access Control
- Role-based access control
- Secure API key management
- Environment variable protection

## Integration with Blockchain

### Whitelist Integration
The system integrates with your existing ERC-3643 token contracts:

```typescript
// Example: Adding client to whitelist
const client = await updateWhitelistStatus({
  clientId: 'client-id',
  walletAddress: '0x...',
  isWhitelisted: true
});
```

### Transaction Monitoring
Track all client blockchain activities:
- Token minting
- Transfers
- Freeze/unfreeze operations
- Whitelist changes

## Development

### Adding New Fields
1. Update Prisma schema in `prisma/schema.prisma`
2. Run `npm run db:push` to update database
3. Update validation schemas in `src/lib/validations/client.ts`
4. Update API routes and components as needed

### Custom Validation
Add custom validation rules in the Zod schemas:

```typescript
// Example: Custom age validation
birthday: z.string().refine((date) => {
  const age = calculateAge(new Date(date));
  return age >= 18 && age <= 120;
}, 'Must be between 18 and 120 years old')
```

### Database Migrations
For production deployments:

```bash
npm run db:migrate
```

## Monitoring and Maintenance

### Database Monitoring
- Monitor query performance
- Set up automated backups
- Regular security updates

### Application Monitoring
- API response times
- Error rates and logging
- User activity tracking

## Support

For technical support or questions:
1. Check the troubleshooting section in DATABASE_SETUP.md
2. Review API documentation
3. Check application logs for error details

## Future Enhancements

- Document upload and storage
- Email notifications for KYC status changes
- Advanced reporting and analytics
- Multi-language support
- Mobile application
- Integration with external KYC providers
