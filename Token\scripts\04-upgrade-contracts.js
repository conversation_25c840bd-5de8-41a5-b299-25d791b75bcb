// <PERSON>ript to upgrade the contract implementations

const { ethers, upgrades } = require("hardhat");

async function main() {
  try {
    // Get contract address to upgrade from environment variables
    const contractAddress = process.env.CONTRACT_ADDRESS;
    if (!contractAddress) {
      throw new Error("CONTRACT_ADDRESS environment variable not set");
    }

    // Get contract type from environment variables (token or whitelist)
    const contractType = process.env.CONTRACT_TYPE;
    if (!contractType || (contractType !== "token" && contractType !== "whitelist" && contractType !== "factory")) {
      throw new Error("CONTRACT_TYPE environment variable not set or invalid. Valid types: token, whitelist, factory");
    }

    // Normalize the contract address to ensure proper checksum
    const normalizedContractAddress = ethers.getAddress(contractAddress.toLowerCase());
    console.log(`Upgrading ${contractType} contract at ${normalizedContractAddress}...`);

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`Executor: ${deployer.address}`);

    // Check if proxy is imported
    console.log("Checking OpenZeppelin storage...");
    try {
      const implAddress = await upgrades.erc1967.getImplementationAddress(normalizedContractAddress);
      console.log(`Current implementation address: ${implAddress}`);
    } catch (error) {
      console.log(`Error checking implementation: ${error.message}`);
    }

    // Force import the proxy if needed
    try {
      console.log("Attempting to force import proxy before upgrading...");
      let contractFactory;
      
      switch (contractType.toLowerCase()) {
        case "token":
          contractFactory = await ethers.getContractFactory("SecurityToken");
          break;
        case "whitelist":
          contractFactory = await ethers.getContractFactory("Whitelist");
          break;
        case "factory":
          contractFactory = await ethers.getContractFactory("SecurityTokenFactory");
          break;
      }
      
      const importedContract = await upgrades.forceImport(normalizedContractAddress, contractFactory, { kind: "uups" });
      console.log(`Successfully imported proxy at ${await importedContract.getAddress()}`);
    } catch (importError) {
      console.log(`Import failed (may be already imported): ${importError.message}`);
    }

    // Define upgrade options with unsafe flags to bypass compatibility checks
    const upgradeOptions = { 
      kind: "uups",
      unsafeAllow: ["delegatecall", "constructor", "state-variable-immutable", "state-variable-assignment"],
      unsafeAllowCustomTypes: true,
      unsafeAllowLinkedLibraries: true
    };
    console.log(`Using upgrade options: ${JSON.stringify(upgradeOptions)}`);

    let upgradedContract;

    switch (contractType.toLowerCase()) {
      case "token":
        // Deploy a new SecurityToken implementation
        const SecurityToken = await ethers.getContractFactory("SecurityToken");
        console.log("Preparing to upgrade token...");
        upgradedContract = await upgrades.upgradeProxy(normalizedContractAddress, SecurityToken, upgradeOptions);
        console.log("Waiting for deployment...");
        await upgradedContract.waitForDeployment();
        
        console.log(`SecurityToken upgraded at: ${await upgradedContract.getAddress()}`);
        
        // Log some information about the upgraded token
        console.log(`Token name: ${await upgradedContract.name()}`);
        console.log(`Token symbol: ${await upgradedContract.symbol()}`);
        console.log(`Token max supply: ${ethers.formatEther(await upgradedContract.maxSupply())} tokens`);
        break;
      
      case "whitelist":
        // Deploy a new Whitelist implementation
        const Whitelist = await ethers.getContractFactory("Whitelist");
        upgradedContract = await upgrades.upgradeProxy(normalizedContractAddress, Whitelist, upgradeOptions);
        await upgradedContract.waitForDeployment();
        
        console.log(`Whitelist upgraded at: ${await upgradedContract.getAddress()}`);
        break;
      
      case "factory":
        // For factory, we need to deploy new implementations and update the factory
        console.log("Deploying new SecurityToken implementation...");
        const SecurityTokenImpl = await ethers.getContractFactory("SecurityToken");
        const newTokenImpl = await SecurityTokenImpl.deploy();
        await newTokenImpl.waitForDeployment();
        
        console.log("Deploying new Whitelist implementation...");
        const WhitelistImpl = await ethers.getContractFactory("Whitelist");
        const newWhitelistImpl = await WhitelistImpl.deploy();
        await newWhitelistImpl.waitForDeployment();
        
        // Connect to the factory
        const factory = await ethers.getContractAt("SecurityTokenFactory", normalizedContractAddress);
        
        // Update the implementations
        console.log("Updating factory implementations...");
        const tx = await factory.updateImplementations(
          await newTokenImpl.getAddress(),
          await newWhitelistImpl.getAddress()
        );
        
        await tx.wait();
        
        console.log(`SecurityTokenFactory implementation updated with:`);
        console.log(`- New token implementation: ${await newTokenImpl.getAddress()}`);
        console.log(`- New whitelist implementation: ${await newWhitelistImpl.getAddress()}`);
        break;
    }

    console.log("Upgrade completed!");
  } catch (error) {
    console.error("Error during upgrade:", error);
    
    // Add more detailed error analysis
    if (error.message && error.message.includes("invalid BytesLike value")) {
      console.error("BytesLike error detected. This typically happens when:");
      console.error("1. There's a storage layout mismatch between versions");
      console.error("2. The contract has incompatible changes in state variables");
      console.error("3. The contract initialization is incorrect");
    }
    
    throw error;  // Re-throw to exit with error code
  }
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });