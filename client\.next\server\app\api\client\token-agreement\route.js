/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/token-agreement/route";
exports.ids = ["app/api/client/token-agreement/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Ftoken-agreement%2Froute&page=%2Fapi%2Fclient%2Ftoken-agreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Ftoken-agreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Ftoken-agreement%2Froute&page=%2Fapi%2Fclient%2Ftoken-agreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Ftoken-agreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_token_agreement_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/token-agreement/route.ts */ \"(rsc)/./src/app/api/client/token-agreement/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/token-agreement/route\",\n        pathname: \"/api/client/token-agreement\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/token-agreement/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\token-agreement\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_token_agreement_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Ftoken-agreement%2Froute&page=%2Fapi%2Fclient%2Ftoken-agreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Ftoken-agreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/token-agreement/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/client/token-agreement/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const tokenAddress = searchParams.get('tokenAddress');\n        const tokenSymbol = searchParams.get('tokenSymbol');\n        if (!tokenAddress && !tokenSymbol) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address or symbol required'\n            }, {\n                status: 400\n            });\n        }\n        // TODO: Query database for token-specific agreement acceptance\n        // Example structure:\n        // SELECT * FROM token_agreements \n        // WHERE user_email = ? AND (token_address = ? OR token_symbol = ?)\n        const mockAgreementStatus = {\n            accepted: false,\n            acceptedAt: null,\n            tokenAddress: tokenAddress,\n            tokenSymbol: tokenSymbol,\n            agreementVersion: '1.0'\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(mockAgreementStatus);\n    } catch (error) {\n        console.error('Error fetching token agreement status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch token agreement status'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const userEmail = session.user.email;\n        // Try to call admin panel API to save token agreement\n        const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';\n        const requestData = {\n            ...body,\n            clientEmail: userEmail\n        };\n        try {\n            const response = await fetch(`${adminApiUrl}/token-agreements`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log('💾 Saved token agreement via admin API:', {\n                    userEmail,\n                    tokenAddress: body.tokenAddress,\n                    tokenSymbol: body.tokenSymbol,\n                    accepted: body.accepted\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n            } else {\n                console.warn('⚠️ Admin API not available for saving agreement, using fallback');\n            }\n        } catch (error) {\n            console.warn('⚠️ Admin API error for saving agreement, using fallback:', error);\n        }\n        // Fallback: Log the agreement and return success\n        const acceptedAt = new Date().toISOString();\n        console.log('💾 Fallback: Logging token agreement acceptance:', {\n            userEmail,\n            tokenAddress: body.tokenAddress,\n            tokenSymbol: body.tokenSymbol,\n            accepted: body.accepted,\n            acceptedAt,\n            data: requestData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Token agreement acceptance saved successfully (fallback mode)',\n            data: {\n                accepted: true,\n                acceptedAt,\n                tokenAddress: body.tokenAddress,\n                tokenSymbol: body.tokenSymbol,\n                agreementVersion: body.agreementVersion || '1.0'\n            }\n        });\n    } catch (error) {\n        console.error('Error saving token agreement acceptance:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save token agreement acceptance'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/token-agreement/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Ftoken-agreement%2Froute&page=%2Fapi%2Fclient%2Ftoken-agreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Ftoken-agreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();