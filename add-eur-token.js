// Add a token with EUR currency for testing
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3000/api';

async function addEurToken() {
  console.log('=== Adding EUR Token for Currency Testing ===');
  
  const eurToken = {
    address: '0xEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE',
    name: 'European Real Estate Token',
    symbol: 'EURT',
    decimals: 2,
    maxSupply: '500000',
    totalSupply: '0',
    tokenType: 'realestate',
    tokenPrice: '50 EUR',
    currency: 'EUR',
    bonusTiers: 'Early Bird: 10%, Standard: 5%',
    whitelistAddress: '******************************************',
    adminAddress: '******************************************',
    hasKYC: true,
    network: 'amoy',
    deploymentNotes: 'European real estate token with EUR pricing'
  };
  
  try {
    const response = await fetch(`${ADMIN_API_URL}/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(eurToken),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ EUR token added successfully!');
      console.log(`Token ID: ${data.id}`);
      console.log(`Currency: ${data.currency}`);
      console.log(`Price: ${data.tokenPrice}`);
    } else {
      console.log(`❌ Failed to add EUR token: ${data.error}`);
    }
  } catch (error) {
    console.error('Error adding EUR token:', error.message);
  }
}

async function addEthToken() {
  console.log('\n=== Adding ETH Token for Currency Testing ===');
  
  const ethToken = {
    address: '******************************************',
    name: 'Ethereum DeFi Fund',
    symbol: 'ETHF',
    decimals: 18,
    maxSupply: '100000',
    totalSupply: '0',
    tokenType: 'fund',
    tokenPrice: '0.1 ETH',
    currency: 'ETH',
    bonusTiers: 'Whale: 15%, Dolphin: 10%, Fish: 5%',
    whitelistAddress: '******************************************',
    adminAddress: '******************************************',
    hasKYC: true,
    network: 'amoy',
    deploymentNotes: 'DeFi fund token priced in ETH'
  };
  
  try {
    const response = await fetch(`${ADMIN_API_URL}/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ethToken),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ ETH token added successfully!');
      console.log(`Token ID: ${data.id}`);
      console.log(`Currency: ${data.currency}`);
      console.log(`Price: ${data.tokenPrice}`);
    } else {
      console.log(`❌ Failed to add ETH token: ${data.error}`);
    }
  } catch (error) {
    console.error('Error adding ETH token:', error.message);
  }
}

async function testCurrencyDisplay() {
  console.log('\n=== Testing Currency Display ===');
  
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    const tokens = await response.json();
    
    console.log('All token currencies:');
    tokens.forEach(token => {
      console.log(`- ${token.symbol}: ${token.price} ${token.currency}`);
    });
    
    // Check unique currencies
    const uniqueCurrencies = [...new Set(tokens.map(t => t.currency))];
    console.log('\nUnique currencies:', uniqueCurrencies);
    
  } catch (error) {
    console.error('Error testing currency display:', error.message);
  }
}

async function main() {
  await addEurToken();
  await addEthToken();
  await testCurrencyDisplay();
}

main().catch(console.error);
