const { ethers, upgrades } = require("hardhat");

async function main() {
    console.log("🚀 Deploying ERC-3643 Identity Registry and Compliance System...");
    console.log("================================================================");

    const [deployer] = await ethers.getSigners();
    console.log("Deploying contracts with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

    // Get existing ClaimRegistry address (should be deployed first)
    const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
        throw new Error("CLAIM_REGISTRY_ADDRESS not found in environment variables. Deploy ClaimRegistry first.");
    }
    console.log("Using ClaimRegistry at:", claimRegistryAddress);

    // 1. Deploy IdentityRegistry
    console.log("\n1️⃣ Deploying IdentityRegistry...");
    const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
    
    const identityRegistry = await upgrades.deployProxy(
        IdentityRegistry,
        [
            deployer.address, // admin
            claimRegistryAddress // claim registry
        ],
        {
            initializer: "initialize",
            kind: "uups"
        }
    );
    
    await identityRegistry.waitForDeployment();
    const identityRegistryAddress = await identityRegistry.getAddress();
    console.log("✅ IdentityRegistry deployed to:", identityRegistryAddress);

    // 2. Deploy Compliance
    console.log("\n2️⃣ Deploying Compliance...");
    const Compliance = await ethers.getContractFactory("Compliance");
    
    const compliance = await upgrades.deployProxy(
        Compliance,
        [
            deployer.address, // admin
            identityRegistryAddress // identity registry
        ],
        {
            initializer: "initialize",
            kind: "uups"
        }
    );
    
    await compliance.waitForDeployment();
    const complianceAddress = await compliance.getAddress();
    console.log("✅ Compliance deployed to:", complianceAddress);

    // 3. Verify deployments
    console.log("\n3️⃣ Verifying deployments...");
    
    // Check IdentityRegistry
    try {
        const version = await identityRegistry.hasRole(await identityRegistry.DEFAULT_ADMIN_ROLE(), deployer.address);
        console.log("✅ IdentityRegistry admin role verified:", version);
    } catch (error) {
        console.log("❌ IdentityRegistry verification failed:", error.message);
    }

    // Check Compliance
    try {
        const hasRole = await compliance.hasRole(await compliance.DEFAULT_ADMIN_ROLE(), deployer.address);
        console.log("✅ Compliance admin role verified:", hasRole);
    } catch (error) {
        console.log("❌ Compliance verification failed:", error.message);
    }

    // 4. Set up initial configuration
    console.log("\n4️⃣ Setting up initial configuration...");
    
    // Add some default required claim topics if needed
    try {
        const requiredTopics = await identityRegistry.getRequiredClaimTopics();
        console.log("✅ Required claim topics:", requiredTopics.map(t => t.toString()));
    } catch (error) {
        console.log("❌ Failed to get required claim topics:", error.message);
    }

    // Get default compliance rule
    try {
        const defaultRuleId = ethers.keccak256(ethers.toUtf8Bytes("DEFAULT_RULE"));
        const rule = await compliance.getComplianceRule(defaultRuleId);
        console.log("✅ Default compliance rule:", {
            isActive: rule[0],
            maxHolders: rule[1].toString(),
            maxTokensPerHolder: rule[2].toString(),
            maxTotalSupply: rule[3].toString()
        });
    } catch (error) {
        console.log("❌ Failed to get default compliance rule:", error.message);
    }

    // 5. Display deployment summary
    console.log("\n🎉 Deployment Summary");
    console.log("=====================");
    console.log("IdentityRegistry:", identityRegistryAddress);
    console.log("Compliance:", complianceAddress);
    console.log("ClaimRegistry:", claimRegistryAddress);
    console.log("Admin:", deployer.address);

    // 6. Environment variables for .env.local
    console.log("\n📝 Add these to your .env.local file:");
    console.log("=====================================");
    console.log(`IDENTITY_REGISTRY_ADDRESS=${identityRegistryAddress}`);
    console.log(`COMPLIANCE_ADDRESS=${complianceAddress}`);

    // 7. Next steps
    console.log("\n🔄 Next Steps:");
    console.log("==============");
    console.log("1. Add the environment variables above to your .env.local file");
    console.log("2. Update existing SecurityToken deployments to use new contracts");
    console.log("3. Migrate existing whitelist data to IdentityRegistry");
    console.log("4. Test the new compliance system");

    return {
        identityRegistry: identityRegistryAddress,
        compliance: complianceAddress,
        claimRegistry: claimRegistryAddress
    };
}

// Handle script execution
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ Deployment failed:", error);
            process.exit(1);
        });
}

module.exports = main;
