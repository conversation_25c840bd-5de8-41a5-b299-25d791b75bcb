const fetch = require('node-fetch');

async function testOrdersAPI() {
  console.log('🧪 Testing Orders API');
  console.log('====================');

  const ADMIN_API_URL = 'http://localhost:6677/api';
  const CLIENT_API_URL = 'http://localhost:7788/api';

  try {
    // 1. Test admin panel orders API
    console.log('\n1️⃣ Testing admin panel orders API...');
    
    const adminOrdersResponse = await fetch(`${ADMIN_API_URL}/orders`);
    
    if (!adminOrdersResponse.ok) {
      console.log(`❌ Admin orders API failed: ${adminOrdersResponse.status}`);
      const errorText = await adminOrdersResponse.text();
      console.log('Error:', errorText);
    } else {
      const adminOrdersData = await adminOrdersResponse.json();
      console.log(`✅ Admin orders API working`);
      console.log(`   Orders found: ${adminOrdersData.orders?.length || 0}`);
      console.log(`   Total count: ${adminOrdersData.pagination?.totalCount || 0}`);
      
      if (adminOrdersData.orders && adminOrdersData.orders.length > 0) {
        const firstOrder = adminOrdersData.orders[0];
        console.log(`   Sample order: ${firstOrder.token?.name} - ${firstOrder.status}`);
      }
    }

    // 2. Test client orders API (this will fail without auth, but we can see the error)
    console.log('\n2️⃣ Testing client orders API...');
    
    const clientOrdersResponse = await fetch(`${CLIENT_API_URL}/client-orders`);
    
    if (!clientOrdersResponse.ok) {
      console.log(`⚠️  Client orders API returned: ${clientOrdersResponse.status}`);
      const errorText = await clientOrdersResponse.text();
      if (clientOrdersResponse.status === 401) {
        console.log('   ✅ Expected: Unauthorized (need to be logged in)');
      } else {
        console.log('   Error:', errorText);
      }
    } else {
      const clientOrdersData = await clientOrdersResponse.json();
      console.log(`✅ Client orders API working`);
      console.log(`   Orders found: ${clientOrdersData.orders?.length || 0}`);
    }

    // 3. Test specific client orders (using a known client ID)
    console.log('\n3️⃣ Testing admin orders with client filter...');
    
    // First, get a client ID from the database
    const clientsResponse = await fetch(`${ADMIN_API_URL}/clients`);
    if (clientsResponse.ok) {
      const clientsData = await clientsResponse.json();
      if (clientsData.clients && clientsData.clients.length > 0) {
        const firstClient = clientsData.clients[0];
        console.log(`   Testing with client: ${firstClient.firstName} ${firstClient.lastName} (${firstClient.id})`);
        
        const clientOrdersResponse = await fetch(`${ADMIN_API_URL}/orders?clientId=${firstClient.id}`);
        if (clientOrdersResponse.ok) {
          const clientOrdersData = await clientOrdersResponse.json();
          console.log(`   ✅ Client-specific orders: ${clientOrdersData.orders?.length || 0}`);
        } else {
          console.log(`   ❌ Client-specific orders failed: ${clientOrdersResponse.status}`);
        }
      } else {
        console.log('   ⚠️  No clients found in database');
      }
    }

    // 4. Test order status filtering
    console.log('\n4️⃣ Testing order status filtering...');
    
    const statusFilters = ['PENDING_APPROVAL', 'CONFIRMED', 'MINTED', 'CANCELLED'];
    for (const status of statusFilters) {
      const statusResponse = await fetch(`${ADMIN_API_URL}/orders?status=${status}`);
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        console.log(`   ${status}: ${statusData.orders?.length || 0} orders`);
      }
    }

    console.log('\n✅ Orders API testing completed!');
    console.log('\n💡 Next steps:');
    console.log('1. Make sure both admin panel and client are running');
    console.log('2. Sign in to the client portal');
    console.log('3. Place an order from the offers page');
    console.log('4. Check the orders page to see your order');
    console.log('5. Check the admin panel to manage the order');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testOrdersAPI().catch(console.error);
