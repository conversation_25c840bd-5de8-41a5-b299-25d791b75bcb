/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/kyc/update-status/route";
exports.ids = ["app/api/client/kyc/update-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&page=%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&page=%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_kyc_update_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/kyc/update-status/route.ts */ \"(rsc)/./src/app/api/client/kyc/update-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/kyc/update-status/route\",\n        pathname: \"/api/client/kyc/update-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/kyc/update-status/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\kyc\\\\update-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_kyc_update_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&page=%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/kyc/update-status/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/client/kyc/update-status/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Update KYC status in admin panel from client portal\n * POST /api/client/kyc/update-status\n */ async function POST(request) {\n    try {\n        // Check authentication\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)();\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { kycStatus, kycNotes } = body;\n        if (!kycStatus) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'KYC status is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Updating KYC status for user:', {\n            userEmail: session.user.email,\n            kycStatus,\n            kycNotes\n        });\n        // Find client by email in admin panel\n        const adminApiUrl = process.env.ADMIN_API_BASE_URL;\n        try {\n            const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(session.user.email || '')}&limit=1`, {\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!searchResponse.ok) {\n                console.error('Failed to search for client:', searchResponse.status);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to find client profile'\n                }, {\n                    status: 404\n                });\n            }\n            const searchData = await searchResponse.json();\n            const client = searchData.clients?.[0];\n            if (!client) {\n                console.error('Client not found for email:', session.user.email);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Client profile not found'\n                }, {\n                    status: 404\n                });\n            }\n            console.log('Found client for KYC update:', {\n                clientId: client.id,\n                email: client.email,\n                currentKycStatus: client.kycStatus\n            });\n            // Update the client's KYC status\n            const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}/kyc`, {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    kycStatus,\n                    kycNotes: kycNotes || 'KYC status updated via client portal'\n                })\n            });\n            if (!updateResponse.ok) {\n                const errorText = await updateResponse.text();\n                console.error('Failed to update KYC status:', errorText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to update KYC status'\n                }, {\n                    status: 500\n                });\n            }\n            const updatedClient = await updateResponse.json();\n            console.log('Successfully updated client KYC status:', {\n                clientId: updatedClient.id,\n                newKycStatus: updatedClient.kycStatus,\n                kycCompletedAt: updatedClient.kycCompletedAt\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'KYC status updated successfully',\n                client: {\n                    id: updatedClient.id,\n                    kycStatus: updatedClient.kycStatus,\n                    kycCompletedAt: updatedClient.kycCompletedAt,\n                    kycNotes: updatedClient.kycNotes\n                }\n            });\n        } catch (error) {\n            // If admin panel is not running, return graceful error\n            console.log('Admin panel not available for KYC update:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'KYC status will be updated automatically via webhook'\n            }, {\n                status: 202\n            });\n        }\n    } catch (error) {\n        console.error('Error updating KYC status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/kyc/update-status/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&page=%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fkyc%2Fupdate-status%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();