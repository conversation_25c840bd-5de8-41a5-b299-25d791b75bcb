'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { CheckCircleIcon, ClockIcon, ExclamationTriangleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { CountrySelection } from './CountrySelection';
import { TokenAgreement } from './TokenAgreement';
import { QualificationForm } from '../QualificationForm';
import { WalletConnection } from '../WalletConnection';
import { AutomaticKYC } from '../AutomaticKYC';

interface QualificationFlowProps {
  tokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
}

interface QualificationStep {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'current' | 'pending' | 'error';
}

export function QualificationFlow({ tokenAddress, tokenName, tokenSymbol }: QualificationFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [stepData, setStepData] = useState({
    country: '',
    agreementAccepted: false,
    profileCompleted: false,
    walletConnected: false,
    kycCompleted: false,
  });
  const [qualificationStatus, setQualificationStatus] = useState<string>('PENDING');
  const [kycStatus, setKycStatus] = useState<string>('idle');
  const [kycError, setKycError] = useState<string | null>(null);

  // Fetch existing qualification progress
  const { data: qualificationProgress, isLoading } = useQuery({
    queryKey: ['qualification-progress', tokenAddress],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (tokenAddress) params.append('tokenAddress', tokenAddress);
      
      const response = await fetch(`/api/client/qualification-progress?${params.toString()}`);
      if (response.ok) {
        return response.json();
      }
      return null;
    },
  });

  // Fetch client profile
  const { data: profile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: async () => {
      const response = await fetch('/api/client/profile');
      if (response.ok) {
        return response.json();
      }
      return null;
    },
  });

  // Fetch wallet status
  const { data: walletStatus } = useQuery({
    queryKey: ['wallet-status'],
    queryFn: async () => {
      const response = await fetch('/api/client/wallet');
      if (response.ok) {
        return response.json();
      }
      return null;
    },
  });

  // Update step data based on fetched progress
  useEffect(() => {
    if (qualificationProgress) {
      // Try to get more recent data from localStorage first
      const storageKey = `qualification_progress_${tokenAddress}`;
      let localProgress = null;
      try {
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          localProgress = JSON.parse(stored);
          console.log('📱 Found localStorage progress:', localProgress);
        }
      } catch (error) {
        console.error('Error reading localStorage:', error);
      }

      // Use localStorage data if it's more recent, otherwise use API data
      const progressToUse = localProgress || qualificationProgress;

      const newStepData = {
        country: progressToUse.country || '',
        agreementAccepted: progressToUse.agreementAccepted || false,
        profileCompleted: progressToUse.profileCompleted || !!profile,
        walletConnected: progressToUse.walletConnected || !!walletStatus?.verified,
        kycCompleted: progressToUse.kycCompleted || (profile?.kycStatus === 'APPROVED'),
      };

      setStepData(newStepData);

      // Set qualification status
      if (progressToUse.qualificationStatus) {
        setQualificationStatus(progressToUse.qualificationStatus);
      }

      // Set current step based on saved progress or calculate from completion status
      let calculatedStep = progressToUse.currentStep || 0;

      // Allow users to progress through all steps without blocking
      // Only auto-advance to next incomplete step if current step is completed
      if (calculatedStep === 0 && newStepData.country) {
        calculatedStep = 1; // Move to agreement if country is selected
      } else if (calculatedStep === 1 && newStepData.agreementAccepted) {
        calculatedStep = 2; // Move to profile if agreement is accepted
      } else if (calculatedStep === 2 && newStepData.profileCompleted) {
        calculatedStep = 3; // Move to wallet if profile is completed
      } else if (calculatedStep === 3 && newStepData.walletConnected) {
        calculatedStep = 4; // Move to KYC if wallet is connected
      } else if (calculatedStep === 4 && newStepData.kycCompleted) {
        calculatedStep = 5; // All completed
      }

      setCurrentStep(calculatedStep);

      console.log('🔄 Restored qualification state:', {
        stepData: newStepData,
        currentStep: calculatedStep,
        savedProgress: progressToUse,
        source: localProgress ? 'localStorage' : 'API'
      });
    }
  }, [qualificationProgress, profile, walletStatus, tokenAddress]);

  // Function to manually fix qualification progress flags
  const fixQualificationFlags = async () => {
    if (!profile || !tokenAddress) {
      console.error('❌ Missing profile or token address');
      return;
    }

    try {
      console.log('🔧 Attempting to fix qualification progress flags...');

      const response = await fetch('/api/client/qualification-progress', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userEmail: profile.email,
          tokenAddress: tokenAddress,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Qualification progress flags fixed successfully:', result);
        // Reload the page to see the updated data
        window.location.reload();
      } else {
        console.error('❌ Failed to fix qualification progress flags:', response.status);
      }
    } catch (error) {
      console.error('❌ Error fixing qualification progress flags:', error);
    }
  };

  const steps: QualificationStep[] = [
    {
      id: 'country',
      title: 'Country Selection',
      description: 'Select your country of residence for compliance',
      status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending',
    },
    {
      id: 'agreement',
      title: 'Token Agreement',
      description: `Accept the ${tokenName || 'token'} specific investment agreement`,
      status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending',
    },
    {
      id: 'profile',
      title: 'Main Information',
      description: 'Complete your personal and financial information',
      status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending',
    },
    {
      id: 'wallet',
      title: 'Wallet Connection',
      description: 'Connect and verify your cryptocurrency wallet',
      status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending',
    },
    {
      id: 'kyc',
      title: 'KYC Verification',
      description: 'Complete identity verification using Sumsub',
      status: stepData.kycCompleted ? 'completed' : 
              kycStatus === 'failed' ? 'error' : 
              currentStep === 4 ? 'current' : 'pending',
    },
  ];

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'current':
        return <ClockIcon className="h-6 w-6 text-blue-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />;
      default:
        return <div className="h-6 w-6 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'current': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Function to save qualification progress
  const saveProgress = async (updatedStepData: any, newCurrentStep: number) => {
    try {
      // Ensure we have the correct completion flags based on current state
      // For country: check if it was explicitly completed OR if there's a country value
      const countryCompleted = updatedStepData.countryCompleted === true ||
                              (updatedStepData.country && updatedStepData.country !== '') ||
                              (stepData.countryCompleted === true) ||
                              (stepData.country && stepData.country !== '');

      const agreementAccepted = updatedStepData.agreementAccepted === true || stepData.agreementAccepted === true;
      const profileCompleted = updatedStepData.profileCompleted === true || stepData.profileCompleted === true || !!profile;
      const walletConnected = updatedStepData.walletConnected === true || stepData.walletConnected === true || !!profile?.walletAddress;
      const kycCompleted = updatedStepData.kycCompleted === true || stepData.kycCompleted === true || profile?.kycStatus === 'APPROVED';

      // Calculate completed steps based on actual step completion flags
      const stepCompletionFlags = [
        countryCompleted,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted,
      ];
      const actualCompletedSteps = stepCompletionFlags.filter(Boolean).length;

      const progressData = {
        ...updatedStepData,
        // Ensure all completion flags are explicitly set
        countryCompleted,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted,
        // Preserve country value if it exists
        country: updatedStepData.country || stepData.country || '',
        tokenAddress,
        currentStep: newCurrentStep,
        completedSteps: actualCompletedSteps,
      };

      console.log('💾 Saving progress to database:', progressData);
      console.log('🔍 Step completion flags:', {
        countryCompleted,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted,
        actualCompletedSteps
      });

      // Save to backend database via admin panel API
      const response = await fetch('/api/client/qualification-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(progressData),
      });

      if (!response.ok) {
        throw new Error('Failed to save progress');
      }

      const result = await response.json();
      console.log('✅ Progress saved successfully to database:', result);
    } catch (error) {
      console.error('❌ Error saving progress:', error);
      // Don't block the user flow if saving fails
    }
  };

  // Step completion handlers
  const handleCountryComplete = async (country: string) => {
    const updatedStepData = { ...stepData, country, countryCompleted: true };
    setStepData(updatedStepData);
    setCurrentStep(1);

    // Save progress
    await saveProgress(updatedStepData, 1);
  };

  const handleAgreementComplete = async () => {
    // First save the token agreement
    try {
      const response = await fetch('/api/client/token-agreement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tokenAddress,
          tokenSymbol,
          accepted: true,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save agreement');
      }

      console.log('✅ Token agreement saved successfully');
    } catch (error) {
      console.error('❌ Error saving token agreement:', error);
    }

    // Update step data and progress
    const updatedStepData = { ...stepData, agreementAccepted: true };
    setStepData(updatedStepData);
    setCurrentStep(2);

    // Save progress
    await saveProgress(updatedStepData, 2);
  };

  const handleProfileComplete = async () => {
    const updatedStepData = { ...stepData, profileCompleted: true };
    setStepData(updatedStepData);
    setCurrentStep(3);

    // Save progress
    await saveProgress(updatedStepData, 3);
  };

  const handleWalletComplete = async () => {
    const updatedStepData = { ...stepData, walletConnected: true };
    setStepData(updatedStepData);
    setCurrentStep(4);

    // Save progress
    await saveProgress(updatedStepData, 4);
  };

  const handleKYCStatusChange = async (status: string, error?: string) => {
    setKycStatus(status);
    if (error) {
      setKycError(error);
    } else {
      setKycError(null);
    }

    if (status === 'completed') {
      const updatedStepData = { ...stepData, kycCompleted: true };
      setStepData(updatedStepData);
      setCurrentStep(5);

      // Save progress
      await saveProgress(updatedStepData, 5);
    }
  };

  // Step navigation functions
  const canNavigateToStep = (stepIndex: number) => {
    // Users can always navigate to completed steps or the next incomplete step
    if (stepIndex === 0) return true; // Country selection always available
    if (stepIndex === 1) return stepData.country !== ''; // Agreement if country selected
    if (stepIndex === 2) return stepData.agreementAccepted; // Profile if agreement accepted
    if (stepIndex === 3) return stepData.profileCompleted; // Wallet if profile completed
    if (stepIndex === 4) return stepData.walletConnected; // KYC if wallet connected
    if (stepIndex === 5) return stepData.kycCompleted; // Completion if KYC done
    return false;
  };

  const handleStepClick = (stepIndex: number) => {
    if (canNavigateToStep(stepIndex)) {
      setCurrentStep(stepIndex);
      // Force save with correct completion flags based on current state
      const updatedData = {
        ...stepData,
        // Ensure all completion flags are set based on current state
        countryCompleted: stepData.countryCompleted || (stepData.country && stepData.country !== '') || completedSteps >= 1,
        agreementAccepted: stepData.agreementAccepted || completedSteps >= 2,
        profileCompleted: stepData.profileCompleted || !!profile || completedSteps >= 3,
        walletConnected: stepData.walletConnected || !!profile?.walletAddress || completedSteps >= 4,
        kycCompleted: stepData.kycCompleted || profile?.kycStatus === 'APPROVED' || completedSteps >= 5,
      };
      console.log('🔧 Forcing save with correct completion flags:', updatedData);
      saveProgress(updatedData, stepIndex);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {tokenName ? `${tokenName} Qualification` : 'Token Qualification'}
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          Complete the following steps to qualify for {tokenName || 'token'} investment
        </p>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-3 mb-8">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>

        <p className="text-sm text-gray-500">
          {completedSteps} of {totalSteps} steps completed
        </p>
      </div>

      {/* Steps Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        {steps.map((step, index) => (
          <div
            key={step.id}
            onClick={() => handleStepClick(index)}
            className={`p-4 rounded-lg border text-center transition-all duration-200 ${getStepColor(step.status)} ${
              canNavigateToStep(index)
                ? 'cursor-pointer hover:shadow-md hover:scale-105'
                : 'cursor-not-allowed opacity-60'
            }`}
          >
            <div className="flex justify-center mb-2">
              {getStepIcon(step.status)}
            </div>
            <h3 className="text-sm font-semibold mb-1">{step.title}</h3>
            <p className="text-xs">{step.description}</p>
            {canNavigateToStep(index) && index !== currentStep && (
              <p className="text-xs mt-1 font-medium">Click to navigate</p>
            )}
          </div>
        ))}
      </div>

      {/* Current Step Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {currentStep === 0 && (
          <CountrySelection
            onComplete={handleCountryComplete}
            selectedCountry={stepData.country}
            isCompleted={stepData.country !== ''}
          />
        )}

        {currentStep === 1 && (
          <TokenAgreement
            onComplete={handleAgreementComplete}
            tokenName={tokenName}
            tokenSymbol={tokenSymbol}
            isCompleted={stepData.agreementAccepted}
          />
        )}

        {currentStep === 2 && (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Main Information</h2>
              <p className="text-gray-600">
                Please provide your complete personal and financial information.
              </p>
            </div>
            <QualificationForm
              onComplete={handleProfileComplete}
              existingProfile={profile}
            />
          </div>
        )}

        {currentStep === 3 && (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Wallet Connection</h2>
              <p className="text-gray-600">
                Connect your cryptocurrency wallet using Reown (WalletConnect).
              </p>
            </div>
            <WalletConnection onWalletConnected={handleWalletComplete} />
          </div>
        )}

        {currentStep === 4 && (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">KYC Verification</h2>
              <p className="text-gray-600">
                Complete your identity verification using Sumsub.
              </p>
            </div>
            <AutomaticKYC
              onStatusChange={handleKYCStatusChange}
              existingProfile={profile}
            />
          </div>
        )}

        {currentStep === 5 && (
          <div className="text-center py-12">
            {(qualificationStatus === 'APPROVED' || qualificationStatus === 'FORCE_APPROVED') ? (
              <>
                <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Qualification Approved!
                </h2>
                <p className="text-gray-600 mb-6">
                  Congratulations! Your qualification for {tokenName || 'this token'} has been approved.
                  You are now eligible to invest in this token.
                </p>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 max-w-md mx-auto">
                  <div className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
                    <div className="text-left">
                      <p className="text-sm font-medium text-green-800">Approved for Investment</p>
                      <p className="text-xs text-green-700">
                        You can now proceed to invest in this token through the platform.
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : qualificationStatus === 'REJECTED' ? (
              <>
                <XCircleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Qualification Rejected
                </h2>
                <p className="text-gray-600 mb-6">
                  Unfortunately, your qualification for {tokenName || 'this token'} has been rejected.
                  Please contact support for more information.
                </p>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 max-w-md mx-auto">
                  <div className="flex items-center">
                    <XCircleIcon className="h-5 w-5 text-red-600 mr-2" />
                    <div className="text-left">
                      <p className="text-sm font-medium text-red-800">Application Rejected</p>
                      <p className="text-xs text-red-700">
                        Please review your information and contact support if needed.
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Qualification Submitted!
                </h2>
                <p className="text-gray-600 mb-6">
                  You have successfully completed all qualification steps for {tokenName || 'this token'}.
                  Your application is now pending admin review and approval.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-md mx-auto">
                  <div className="flex items-center">
                    <ClockIcon className="h-5 w-5 text-yellow-600 mr-2" />
                    <div className="text-left">
                      <p className="text-sm font-medium text-yellow-800">Pending Admin Approval</p>
                      <p className="text-xs text-yellow-700">
                        An administrator will review your qualification and approve you for token investment.
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => window.location.href = '/'}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Return to Dashboard
              </button>
              <button
                onClick={fixQualificationFlags}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
              >
                Fix Progress Flags
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
