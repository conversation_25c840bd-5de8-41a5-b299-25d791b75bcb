// API Token Deployment Script
// This script is designed to be called from an API or admin panel
// It will deploy a token with robust error handling

const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

// Import the robust deployment function
const robustDeploy = require('./robust-deploy-token');

async function main() {
  try {
    // Log start of deployment
    console.log("\n=== API TOKEN DEPLOYMENT STARTED ===");
    
    // Verify required environment variables
    const requiredVars = ['FACTORY_ADDRESS', 'TOKEN_NAME', 'TOKEN_SYMBOL', 'MAX_SUPPLY'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
    
    // Set adjusted gas values for Amoy testnet
    if (!process.env.GAS_LIMIT) {
      process.env.GAS_LIMIT = "5000000"; // 5M gas (reduced from 3M)
      console.log(`Setting default GAS_LIMIT: ${process.env.GAS_LIMIT}`);
    }
    
    if (!process.env.GAS_PRICE) {
      process.env.GAS_PRICE = "50"; // 50 gwei (reduced from 200)
      console.log(`Setting default GAS_PRICE: ${process.env.GAS_PRICE} gwei`);
    }
    
    // Set a better RPC URL for Amoy if not specified
    if (!process.env.AMOY_RPC_URL) {
      process.env.AMOY_RPC_URL = "https://polygon-amoy.blockpi.network/v1/rpc/public";
      console.log(`Using alternative RPC endpoint: ${process.env.AMOY_RPC_URL}`);
    }
    
    // Log key parameters
    console.log(`Deploying token with name: ${process.env.TOKEN_NAME}`);
    console.log(`Symbol: ${process.env.TOKEN_SYMBOL}`);
    console.log(`Max Supply: ${process.env.MAX_SUPPLY}`);
    
    // Call the robust deployment function
    const result = await robustDeploy();
    
    // Create a JSON file with the deployment result
    const outputPath = path.join(__dirname, '../deployment-results.json');
    const deploymentResult = {
      tokenAddress: result.tokenAddress,
      whitelistAddress: result.whitelistAddress,
      tokenName: process.env.TOKEN_NAME,
      tokenSymbol: process.env.TOKEN_SYMBOL,
      maxSupply: process.env.MAX_SUPPLY,
      transactionHash: result.transactionHash || "",
      status: result.status || "success",
      timestamp: new Date().toISOString(),
      deployer: result.deployer
    };
    
    // Save the result
    if (fs.existsSync(outputPath)) {
      // Append to existing results
      let existingResults = [];
      try {
        const fileContent = fs.readFileSync(outputPath, 'utf8');
        existingResults = JSON.parse(fileContent);
        if (!Array.isArray(existingResults)) {
          existingResults = [existingResults];
        }
      } catch (e) {
        console.warn("Could not read existing deployment results, creating new file");
      }
      
      existingResults.push(deploymentResult);
      fs.writeFileSync(outputPath, JSON.stringify(existingResults, null, 2));
    } else {
      // Create new file
      fs.writeFileSync(outputPath, JSON.stringify([deploymentResult], null, 2));
    }
    
    console.log("\n=== API TOKEN DEPLOYMENT COMPLETED ===");
    console.log(`Token Address: ${result.tokenAddress}`);
    console.log(`Whitelist Address: ${result.whitelistAddress}`);
    console.log(`Transaction Hash: ${result.transactionHash || "Unknown"}`);
    
    // Return the result in a format that can be easily consumed by an API
    return deploymentResult;
  } catch (error) {
    // Log the error
    console.error("\n=== API TOKEN DEPLOYMENT FAILED ===");
    console.error(`Error: ${error.message}`);
    
    // Create an error result
    const errorResult = {
      error: true,
      message: error.message,
      details: error.toString(),
      timestamp: new Date().toISOString()
    };
    
    // Save the error to a file
    const errorPath = path.join(__dirname, '../deployment-errors.json');
    let errors = [];
    
    if (fs.existsSync(errorPath)) {
      try {
        const fileContent = fs.readFileSync(errorPath, 'utf8');
        errors = JSON.parse(fileContent);
        if (!Array.isArray(errors)) {
          errors = [errors];
        }
      } catch (e) {
        console.warn("Could not read existing error log, creating new file");
      }
    }
    
    errors.push(errorResult);
    fs.writeFileSync(errorPath, JSON.stringify(errors, null, 2));
    
    // Throw the error to be caught by the calling code
    throw error;
  }
}

// Execute the script if called directly
if (require.main === module) {
  main()
    .then((result) => {
      console.log("API Deployment result:", result);
      process.exit(0);
    })
    .catch((error) => {
      console.error("API Deployment failed:", error);
      process.exit(1);
    });
} else {
  // Export for use in other scripts or APIs
  module.exports = main;
} 