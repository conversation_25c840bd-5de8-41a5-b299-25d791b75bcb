// We require the Hardhat Runtime Environment explicitly here. This is optional
// but useful for running the script in a standalone fashion through `node <script>`.
//
// When running the script with `npx hardhat run <script>` you'll find the Hardhat
// Runtime Environment's members available in the global scope.
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  // Get signers and network information
  const [deployer] = await hre.ethers.getSigners();
  const networkName = hre.network.name;

  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Network:", networkName);
  // In ethers v6, getBalance() returns a bigint
  const balance = await hre.ethers.provider.getBalance(deployer.address);
  console.log("Account balance:", balance.toString());

  try {
    // Deploy the SecurityTokenFactory
    console.log("Deploying SecurityTokenFactory...");
    console.log("⚠️  Note: This is a large contract deployment and may take several minutes and cost significant gas.");

    const SecurityTokenFactory = await hre.ethers.getContractFactory("SecurityTokenFactory");

    // Estimate gas for deployment
    try {
      const deploymentData = SecurityTokenFactory.getDeployTransaction(deployer.address);
      const gasEstimate = await deployer.estimateGas(deploymentData);
      console.log("Estimated gas for deployment:", gasEstimate.toString());

      // Get current gas price
      const gasPrice = await deployer.provider.getGasPrice();
      console.log("Current gas price:", hre.ethers.formatUnits(gasPrice, 'gwei'), "gwei");

      // Calculate estimated cost
      const estimatedCost = gasEstimate * gasPrice;
      console.log("Estimated deployment cost:", hre.ethers.formatEther(estimatedCost), "MATIC");

      // Check if user has enough balance
      const balance = await deployer.provider.getBalance(deployer.address);
      console.log("Account balance:", hre.ethers.formatEther(balance), "MATIC");

      if (balance < estimatedCost * 2n) { // 2x buffer for safety
        console.error("❌ Insufficient balance for deployment!");
        console.error("Required (with buffer):", hre.ethers.formatEther(estimatedCost * 2n), "MATIC");
        console.error("Available:", hre.ethers.formatEther(balance), "MATIC");
        process.exit(1);
      }

    } catch (error) {
      console.warn("⚠️  Could not estimate gas, proceeding with deployment...");
    }

    // Deploy with manual gas settings to avoid Hardhat's 1 ether cap
    const deploymentOptions = {
      gasLimit: ********, // Reduced gas limit
      gasPrice: hre.ethers.parseUnits('30', 'gwei'), // Manual gas price (30 gwei)
    };

    console.log("Deploying with gas limit:", deploymentOptions.gasLimit);
    console.log("Deploying with gas price:", hre.ethers.formatUnits(deploymentOptions.gasPrice, 'gwei'), "gwei");

    const estimatedCost = BigInt(deploymentOptions.gasLimit) * deploymentOptions.gasPrice;
    console.log("Max deployment cost:", hre.ethers.formatEther(estimatedCost), "MATIC");

    const factory = await SecurityTokenFactory.deploy(deployer.address, deploymentOptions);
    // In ethers v6, we use waitForDeployment instead of deployed()
    await factory.waitForDeployment();

    // In ethers v6, contract address is accessed via getAddress()
    const factoryAddress = await factory.getAddress();
    console.log("SecurityTokenFactory deployed to:", factoryAddress);

    // Get implementation addresses
    const securityTokenImpl = await factory.securityTokenImplementation();
    const whitelistImpl = await factory.whitelistImplementation();
    const whitelistWithKYCImpl = await factory.whitelistWithKYCImplementation();

    console.log("SecurityToken implementation:", securityTokenImpl);
    console.log("Whitelist implementation:", whitelistImpl);
    console.log("WhitelistWithKYC implementation:", whitelistWithKYCImpl);

    // Save deployment information
    const deploymentInfo = {
      network: networkName,
      factory: factoryAddress,
      implementations: {
        securityToken: securityTokenImpl,
        whitelist: whitelistImpl,
        whitelistWithKYC: whitelistWithKYCImpl
      },
      timestamp: new Date().toISOString()
    };

    // Create a deployment file if it doesn't exist
    const deploymentsDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir);
    }

    const deploymentFile = path.join(deploymentsDir, `${networkName}.json`);
    fs.writeFileSync(
      deploymentFile,
      JSON.stringify(deploymentInfo, null, 2)
    );

    console.log(`Deployment information saved to ${deploymentFile}`);

    // Add additional deployers if needed
    // For example:
    // await factory.addDeployer("0xAdditionalDeployerAddress");

    console.log("Deployment completed successfully");

  } catch (error) {
    console.error("Error during deployment:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });