'use client';

import { useState, useEffect } from 'react';
import { CheckCircleIcon, ClockIcon, XCircleIcon, UserIcon, EyeIcon } from '@heroicons/react/24/outline';

interface QualificationProgress {
  id: string;
  client: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    kycStatus: string;
    walletAddress?: string;
  };
  token?: {
    id: string;
    name: string;
    symbol: string;
    address: string;
  };
  countrySelected: boolean;
  countryValue?: string;
  agreementAccepted: boolean;
  profileCompleted: boolean;
  walletConnected: boolean;
  kycCompleted: boolean;
  currentStep: number;
  completedSteps: number;
  qualificationStatus: 'PENDING' | 'APPROVED' | 'REJECTED' | 'FORCE_APPROVED';
  approvedBy?: string;
  approvedAt?: string;
  rejectedReason?: string;
  createdAt: string;
  updatedAt: string;
}

export default function QualificationsPage() {
  const [qualifications, setQualifications] = useState<QualificationProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL');
  const [selectedQualification, setSelectedQualification] = useState<QualificationProgress | null>(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchQualifications();
  }, []);

  const fetchQualifications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/qualifications');
      if (response.ok) {
        const data = await response.json();
        setQualifications(data.qualifications || []);
      }
    } catch (error) {
      console.error('Error fetching qualifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (qualificationId: string, forceApprove = false) => {
    try {
      const response = await fetch(`/api/qualifications/${qualificationId}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ forceApprove }),
      });

      if (response.ok) {
        await fetchQualifications();
        setShowModal(false);
      } else {
        alert('Failed to approve qualification');
      }
    } catch (error) {
      console.error('Error approving qualification:', error);
      alert('Error approving qualification');
    }
  };

  const handleReject = async (qualificationId: string, reason: string) => {
    try {
      const response = await fetch(`/api/qualifications/${qualificationId}/reject`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason }),
      });

      if (response.ok) {
        await fetchQualifications();
        setShowModal(false);
      } else {
        alert('Failed to reject qualification');
      }
    } catch (error) {
      console.error('Error rejecting qualification:', error);
      alert('Error rejecting qualification');
    }
  };

  const filteredQualifications = qualifications.filter(q => {
    if (filter === 'ALL') return true;
    return q.qualificationStatus === filter;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
      case 'FORCE_APPROVED':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'REJECTED':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'FORCE_APPROVED':
        return 'bg-blue-100 text-blue-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const isQualificationComplete = (q: QualificationProgress) => {
    return q.countrySelected && q.agreementAccepted && q.profileCompleted && 
           q.walletConnected && q.kycCompleted;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Client Qualifications</h1>
          <p className="text-gray-600">Review and approve client qualification applications</p>
        </div>
        <button
          onClick={fetchQualifications}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        {['ALL', 'PENDING', 'APPROVED', 'REJECTED'].map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status as any)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === status
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {status} ({qualifications.filter(q => status === 'ALL' || q.qualificationStatus === status).length})
          </button>
        ))}
      </div>

      {/* Qualifications Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Token
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Progress
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Submitted
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredQualifications.map((qualification) => (
              <tr key={qualification.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <UserIcon className="h-8 w-8 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {qualification.client.firstName} {qualification.client.lastName}
                      </div>
                      <div className="text-sm text-gray-500">{qualification.client.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {qualification.token ? (
                    <div>
                      <div className="text-sm font-medium text-gray-900">{qualification.token.name}</div>
                      <div className="text-sm text-gray-500">{qualification.token.symbol}</div>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">Global</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(qualification.completedSteps / 5) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600">
                      {qualification.completedSteps}/5
                    </span>
                  </div>
                  {isQualificationComplete(qualification) && (
                    <div className="text-xs text-green-600 mt-1">All steps completed</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getStatusIcon(qualification.qualificationStatus)}
                    <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(qualification.qualificationStatus)}`}>
                      {qualification.qualificationStatus.replace('_', ' ')}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(qualification.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => {
                      setSelectedQualification(qualification);
                      setShowModal(true);
                    }}
                    className="text-blue-600 hover:text-blue-900 flex items-center"
                  >
                    <EyeIcon className="h-4 w-4 mr-1" />
                    Review
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredQualifications.length === 0 && (
          <div className="text-center py-12">
            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No qualifications found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'ALL' ? 'No qualification applications yet.' : `No ${filter.toLowerCase()} qualifications.`}
            </p>
          </div>
        )}
      </div>

      {/* Review Modal */}
      {showModal && selectedQualification && (
        <QualificationReviewModal
          qualification={selectedQualification}
          onClose={() => {
            setShowModal(false);
            setSelectedQualification(null);
          }}
          onApprove={(forceApprove) => handleApprove(selectedQualification.id, forceApprove)}
          onReject={(reason) => handleReject(selectedQualification.id, reason)}
        />
      )}
    </div>
  );
}

// Qualification Review Modal Component
function QualificationReviewModal({
  qualification,
  onClose,
  onApprove,
  onReject,
}: {
  qualification: QualificationProgress;
  onClose: () => void;
  onApprove: (forceApprove: boolean) => void;
  onReject: (reason: string) => void;
}) {
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectForm, setShowRejectForm] = useState(false);

  const isComplete = qualification.countrySelected && qualification.agreementAccepted &&
                    qualification.profileCompleted && qualification.walletConnected &&
                    qualification.kycCompleted;

  const getStepStatus = (completed: boolean) => {
    return completed ? (
      <CheckCircleIcon className="h-5 w-5 text-green-500" />
    ) : (
      <XCircleIcon className="h-5 w-5 text-red-500" />
    );
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              Qualification Review - {qualification.client.firstName} {qualification.client.lastName}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircleIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Client Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Client Information</h4>
              <div className="space-y-2 text-sm">
                <div><strong>Name:</strong> {qualification.client.firstName} {qualification.client.lastName}</div>
                <div><strong>Email:</strong> {qualification.client.email}</div>
                <div><strong>KYC Status:</strong> {qualification.client.kycStatus}</div>
                <div><strong>Wallet:</strong> {qualification.client.walletAddress || 'Not connected'}</div>
                <div><strong>Country:</strong> {qualification.countryValue || 'Not selected'}</div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Token Information</h4>
              {qualification.token ? (
                <div className="space-y-2 text-sm">
                  <div><strong>Name:</strong> {qualification.token.name}</div>
                  <div><strong>Symbol:</strong> {qualification.token.symbol}</div>
                  <div><strong>Address:</strong> {qualification.token.address}</div>
                </div>
              ) : (
                <div className="text-sm text-gray-500">Global qualification (not token-specific)</div>
              )}
            </div>
          </div>

          {/* Qualification Steps */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Qualification Steps</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm">1. Country Selection</span>
                <div className="flex items-center">
                  {getStepStatus(qualification.countrySelected)}
                  <span className="ml-2 text-sm text-gray-600">
                    {qualification.countrySelected ? qualification.countryValue : 'Not completed'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm">2. Agreement Acceptance</span>
                {getStepStatus(qualification.agreementAccepted)}
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm">3. Profile Completion</span>
                {getStepStatus(qualification.profileCompleted)}
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm">4. Wallet Connection</span>
                {getStepStatus(qualification.walletConnected)}
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm">5. KYC Verification</span>
                {getStepStatus(qualification.kycCompleted)}
              </div>
            </div>
          </div>

          {/* Current Status */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Current Status</h4>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(qualification.qualificationStatus)}`}>
                {qualification.qualificationStatus.replace('_', ' ')}
              </span>
              {qualification.approvedBy && (
                <span className="text-sm text-gray-600">
                  by {qualification.approvedBy} on {new Date(qualification.approvedAt!).toLocaleDateString()}
                </span>
              )}
            </div>
            {qualification.rejectedReason && (
              <div className="mt-2 p-3 bg-red-50 rounded-lg">
                <p className="text-sm text-red-800"><strong>Rejection Reason:</strong> {qualification.rejectedReason}</p>
              </div>
            )}
          </div>

          {/* Actions */}
          {qualification.qualificationStatus === 'PENDING' && (
            <div className="flex justify-end space-x-4">
              {!showRejectForm ? (
                <>
                  <button
                    onClick={() => setShowRejectForm(true)}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    Reject
                  </button>
                  {!isComplete && (
                    <button
                      onClick={() => onApprove(true)}
                      className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
                    >
                      Force Approve
                    </button>
                  )}
                  <button
                    onClick={() => onApprove(false)}
                    disabled={!isComplete}
                    className={`px-4 py-2 rounded-lg ${
                      isComplete
                        ? 'bg-green-600 text-white hover:bg-green-700'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    Approve
                  </button>
                </>
              ) : (
                <div className="w-full">
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Enter reason for rejection..."
                    className="w-full p-3 border border-gray-300 rounded-lg mb-3"
                    rows={3}
                  />
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => {
                        setShowRejectForm(false);
                        setRejectionReason('');
                      }}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => {
                        if (rejectionReason.trim()) {
                          onReject(rejectionReason.trim());
                        }
                      }}
                      disabled={!rejectionReason.trim()}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                      Confirm Rejection
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
