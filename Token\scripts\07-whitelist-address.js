const hre = require("hardhat");

async function main() {
  try {
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Whitelisting address with the account:", deployer.address);
    console.log("Network:", networkName);

    // Get parameters from environment variables
    const tokenAddress = process.env.TOKEN_ADDRESS;
    const addressToWhitelist = process.env.ADDRESS_TO_WHITELIST;
    const action = process.env.ACTION || "add";

    if (!tokenAddress) {
      console.error("Please set TOKEN_ADDRESS environment variable");
      process.exit(1);
    }

    if (!addressToWhitelist) {
      console.error("Please set ADDRESS_TO_WHITELIST environment variable");
      process.exit(1);
    }

    console.log("Token address:", tokenAddress);
    console.log("Address to whitelist:", addressToWhitelist);
    console.log("Action:", action);

    // Connect to the token contract
    const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);

    // Get the whitelist contract address
    const whitelistAddress = await token.identityRegistry();
    console.log("Whitelist address:", whitelistAddress);

    if (whitelistAddress === hre.ethers.ZeroAddress) {
      console.error("No whitelist contract found for this token");
      process.exit(1);
    }

    // Connect to the whitelist contract
    const whitelist = await hre.ethers.getContractAt("Whitelist", whitelistAddress);

    // Check roles
    const AGENT_ROLE = await whitelist.AGENT_ROLE();
    const hasAgentRole = await whitelist.hasRole(AGENT_ROLE, deployer.address);

    console.log("User has AGENT_ROLE:", hasAgentRole);

    if (!hasAgentRole) {
      console.error("You don't have AGENT_ROLE on the whitelist contract");
      console.error("Please grant AGENT_ROLE first using the role management script");
      process.exit(1);
    }

    // Check current status
    const isCurrentlyWhitelisted = await whitelist.isWhitelisted(addressToWhitelist);
    console.log("Currently whitelisted:", isCurrentlyWhitelisted);

    if (action === "add") {
      if (isCurrentlyWhitelisted) {
        console.log("Address is already whitelisted!");
        return;
      }

      console.log("Adding address to whitelist...");
      
      // Use optimized gas settings for Amoy testnet
      const gasLimit = BigInt(200000);
      const gasPrice = hre.ethers.parseUnits("50", "gwei");
      
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", hre.ethers.formatUnits(gasPrice, "gwei"), "gwei");

      const tx = await whitelist.addToWhitelist(addressToWhitelist, {
        gasLimit,
        gasPrice
      });

      console.log("Transaction hash:", tx.hash);
      console.log("Waiting for confirmation...");

      await tx.wait();
      console.log("✅ Address successfully added to whitelist!");

      // Verify
      const isNowWhitelisted = await whitelist.isWhitelisted(addressToWhitelist);
      console.log("Verification - now whitelisted:", isNowWhitelisted);

    } else if (action === "remove") {
      if (!isCurrentlyWhitelisted) {
        console.log("Address is not whitelisted!");
        return;
      }

      console.log("Removing address from whitelist...");
      
      // Use optimized gas settings
      const gasLimit = BigInt(200000);
      const gasPrice = hre.ethers.parseUnits("50", "gwei");

      const tx = await whitelist.removeFromWhitelist(addressToWhitelist, {
        gasLimit,
        gasPrice
      });

      console.log("Transaction hash:", tx.hash);
      console.log("Waiting for confirmation...");

      await tx.wait();
      console.log("✅ Address successfully removed from whitelist!");

      // Verify
      const isNowWhitelisted = await whitelist.isWhitelisted(addressToWhitelist);
      console.log("Verification - now whitelisted:", isNowWhitelisted);

    } else if (action === "check") {
      console.log("=== WHITELIST STATUS ===");
      console.log("Address:", addressToWhitelist);
      console.log("Whitelisted:", isCurrentlyWhitelisted);
      
      try {
        const isFrozen = await whitelist.isFrozen(addressToWhitelist);
        console.log("Frozen:", isFrozen);
      } catch (err) {
        console.log("Frozen status: Could not check (function may not exist)");
      }

    } else {
      console.error("Invalid action. Use 'add', 'remove', or 'check'");
      process.exit(1);
    }

  } catch (error) {
    console.error("Error managing whitelist:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
