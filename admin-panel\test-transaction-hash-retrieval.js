const { PrismaClient } = require('@prisma/client');

async function testTransactionHashRetrieval() {
  console.log('🧪 Testing Transaction Hash Retrieval');
  console.log('=====================================');

  const prisma = new PrismaClient();

  try {
    // 1. Check if there are any orders with transaction hashes
    console.log('1️⃣ Checking for orders with transaction hashes...');
    
    const ordersWithTx = await prisma.order.findMany({
      where: {
        transactionHash: {
          not: null
        }
      },
      select: {
        id: true,
        status: true,
        transactionHash: true,
        blockNumber: true,
        client: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        },
        token: {
          select: {
            name: true,
            symbol: true
          }
        }
      }
    });

    console.log(`Found ${ordersWithTx.length} orders with transaction hashes:`);
    ordersWithTx.forEach((order, index) => {
      console.log(`  ${index + 1}. ${order.client.firstName} ${order.client.lastName} - ${order.token.name}`);
      console.log(`     Status: ${order.status}`);
      console.log(`     Tx Hash: ${order.transactionHash}`);
      console.log(`     Block: ${order.blockNumber || 'N/A'}`);
      console.log('');
    });

    // 2. Test the API endpoint
    console.log('2️⃣ Testing orders API endpoint...');
    
    try {
      const response = await fetch('http://localhost:6677/api/orders?limit=5');
      if (response.ok) {
        const data = await response.json();
        console.log(`API returned ${data.orders.length} orders`);
        
        const ordersWithTxFromAPI = data.orders.filter(order => order.transactionHash);
        console.log(`${ordersWithTxFromAPI.length} orders have transaction hashes in API response`);
        
        ordersWithTxFromAPI.forEach((order, index) => {
          console.log(`  ${index + 1}. Order ${order.id.substring(0, 8)}... - Status: ${order.status}`);
          console.log(`     Tx Hash: ${order.transactionHash}`);
          console.log(`     Block: ${order.blockNumber || 'N/A'}`);
        });
      } else {
        console.log('❌ API request failed:', response.status);
      }
    } catch (error) {
      console.log('❌ API request error:', error.message);
    }

    // 3. Summary
    console.log('3️⃣ Summary:');
    if (ordersWithTx.length > 0) {
      console.log('✅ Transaction hashes are being stored in database');
      console.log('✅ Database queries can retrieve transaction hashes');
      console.log('💡 If API doesn\'t show transaction hashes, check the API select statements');
    } else {
      console.log('⚠️  No orders with transaction hashes found');
      console.log('💡 Try minting some tokens to create orders with transaction hashes');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testTransactionHashRetrieval().catch(console.error);
