const fetch = require('node-fetch');

async function debugAdminWhitelist() {
  console.log('🔍 Debugging Admin Panel Whitelist API');
  console.log('======================================');

  const YOUR_WALLET = '******************************************';
  const TOKEN_ADDRESS = '******************************************';
  const ADMIN_API_URL = 'http://localhost:6677';

  try {
    // 1. Test the batch whitelist check API directly
    console.log('1️⃣ Testing batch whitelist check API...');
    
    const batchResponse = await fetch(`${ADMIN_API_URL}/api/whitelist/check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: YOUR_WALLET,
        tokenAddresses: [TOKEN_ADDRESS]
      })
    });

    if (!batchResponse.ok) {
      console.log(`❌ Batch API failed: ${batchResponse.status}`);
      const errorText = await batchResponse.text();
      console.log('Error:', errorText);
    } else {
      const batchData = await batchResponse.json();
      console.log('✅ Batch API response:');
      console.log(JSON.stringify(batchData, null, 2));
      
      const tokenResult = batchData.tokens?.[0];
      if (tokenResult) {
        console.log(`\n📊 Token Result:`);
        console.log(`   Token: ${tokenResult.tokenSymbol}`);
        console.log(`   Whitelisted: ${tokenResult.isWhitelisted ? '✅ YES' : '❌ NO'}`);
        console.log(`   Blockchain Checked: ${tokenResult.blockchainChecked ? '✅ YES' : '❌ NO'}`);
        console.log(`   Approval Status: ${tokenResult.approvalStatus}`);
      }
    }

    // 2. Test the single token whitelist check API
    console.log('\n2️⃣ Testing single token whitelist check API...');
    
    const singleResponse = await fetch(`${ADMIN_API_URL}/api/whitelist/check?walletAddress=${YOUR_WALLET}&tokenAddress=${TOKEN_ADDRESS}`);
    
    if (!singleResponse.ok) {
      console.log(`❌ Single API failed: ${singleResponse.status}`);
      const errorText = await singleResponse.text();
      console.log('Error:', errorText);
    } else {
      const singleData = await singleResponse.json();
      console.log('✅ Single API response:');
      console.log(JSON.stringify(singleData, null, 2));
    }

    // 3. Check what the client API is calling
    console.log('\n3️⃣ Testing what client API calls...');
    
    // First get tokens to see what addresses are being used
    const tokensResponse = await fetch(`${ADMIN_API_URL}/api/tokens?source=database`);
    if (tokensResponse.ok) {
      const tokens = await tokensResponse.json();
      const orderToken = tokens.find(t => t.symbol === 'Order_01');
      
      if (orderToken) {
        console.log(`📋 Order_01 token in database:`);
        console.log(`   Address: ${orderToken.address}`);
        console.log(`   Whitelist Address: ${orderToken.whitelistAddress}`);
        console.log(`   Case matches: ${orderToken.address === TOKEN_ADDRESS ? '✅ YES' : '❌ NO'}`);
        console.log(`   Case insensitive match: ${orderToken.address.toLowerCase() === TOKEN_ADDRESS.toLowerCase() ? '✅ YES' : '❌ NO'}`);
        
        // Test with the exact address from database
        console.log(`\n   Testing with database address: ${orderToken.address}`);
        const dbAddressResponse = await fetch(`${ADMIN_API_URL}/api/whitelist/check`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            walletAddress: YOUR_WALLET,
            tokenAddresses: [orderToken.address]
          })
        });
        
        if (dbAddressResponse.ok) {
          const dbAddressData = await dbAddressResponse.json();
          const dbTokenResult = dbAddressData.tokens?.[0];
          console.log(`   Result with DB address: ${dbTokenResult?.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED'}`);
        }
      } else {
        console.log('❌ Order_01 token not found in database');
      }
    }

    // 4. Check admin panel logs
    console.log('\n4️⃣ Check admin panel console logs for:');
    console.log('   - "Blockchain check for Order_01: true/false"');
    console.log('   - Any error messages about RPC or contract calls');
    console.log('   - Network connection issues');

    console.log('\n💡 Next steps:');
    console.log('1. Check admin panel console for blockchain check logs');
    console.log('2. Verify AMOY_RPC_URL is working in admin panel .env');
    console.log('3. Check if the whitelist contract ABI is correct');
    console.log('4. Verify the contract address is correct');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugAdminWhitelist().catch(console.error);
