// Test script to verify decimals fix
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3002/api';

async function testDecimalsFix() {
  console.log('=== Testing Decimals Fix ===');
  
  const testToken = {
    address: '******************************************',
    name: 'Test Zero Decimals Token',
    symbol: 'TZD',
    decimals: 0, // This should be saved as 0, not 18
    maxSupply: '1000000',
    totalSupply: '0',
    tokenType: 'commodity',
    tokenPrice: '10 USD',
    currency: 'USD',
    network: 'amoy',
    deploymentNotes: 'Test token with 0 decimals'
  };
  
  try {
    console.log('Creating token with decimals: 0');
    
    const response = await fetch(`${ADMIN_API_URL}/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testToken),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Token created successfully!');
      console.log(`Token ID: ${data.id}`);
      console.log(`Decimals saved as: ${data.decimals}`);
      
      if (data.decimals === 0) {
        console.log('✅ Decimals fix is working correctly!');
      } else {
        console.log('❌ Decimals fix is NOT working - expected 0, got', data.decimals);
      }
    } else {
      console.log(`❌ Failed to create token: ${data.error}`);
    }
  } catch (error) {
    console.error('Error testing decimals fix:', error.message);
  }
}

async function testWithDifferentDecimals() {
  console.log('\n=== Testing Different Decimal Values ===');
  
  const testCases = [
    { decimals: 0, symbol: 'T0D', name: 'Zero Decimals' },
    { decimals: 6, symbol: 'T6D', name: 'Six Decimals' },
    { decimals: 18, symbol: 'T18D', name: 'Eighteen Decimals' }
  ];
  
  for (const testCase of testCases) {
    const testToken = {
      address: `0x${testCase.decimals.toString().padStart(40, '0')}`,
      name: testCase.name,
      symbol: testCase.symbol,
      decimals: testCase.decimals,
      maxSupply: '1000000',
      totalSupply: '0',
      tokenType: 'test',
      tokenPrice: '1 USD',
      currency: 'USD',
      network: 'amoy',
      deploymentNotes: `Test token with ${testCase.decimals} decimals`
    };
    
    try {
      const response = await fetch(`${ADMIN_API_URL}/tokens`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testToken),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        const isCorrect = data.decimals === testCase.decimals;
        console.log(`${isCorrect ? '✅' : '❌'} ${testCase.name}: Expected ${testCase.decimals}, got ${data.decimals}`);
      } else {
        console.log(`❌ Failed to create ${testCase.name}: ${data.error}`);
      }
    } catch (error) {
      console.error(`Error testing ${testCase.name}:`, error.message);
    }
  }
}

async function main() {
  await testDecimalsFix();
  await testWithDifferentDecimals();
}

main().catch(console.error);
