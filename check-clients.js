// Check existing clients and their whitelist status
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3000/api';

async function checkClients() {
  console.log('=== Checking Existing Clients ===');
  
  try {
    const response = await fetch(`${ADMIN_API_URL}/clients`);
    
    if (!response.ok) {
      console.log(`API returned ${response.status}. Clients endpoint might not exist yet.`);
      return;
    }
    
    const clients = await response.json();
    
    console.log(`Found ${clients.length} clients`);
    
    if (clients.length > 0) {
      console.log('\nClient details:');
      clients.forEach((client, index) => {
        console.log(`${index + 1}. ${client.firstName} ${client.lastName}`);
        console.log(`   Email: ${client.email}`);
        console.log(`   Wallet: ${client.walletAddress || 'Not connected'}`);
        console.log(`   KYC Status: ${client.kycStatus}`);
        console.log(`   Whitelisted: ${client.isWhitelisted}`);
        console.log('');
      });
    } else {
      console.log('No clients found in database');
    }
    
  } catch (error) {
    console.error('Error checking clients:', error.message);
  }
}

async function checkTokenApprovals() {
  console.log('=== Checking Token Approvals ===');
  
  try {
    // This endpoint might not exist yet, but let's try
    const response = await fetch(`${ADMIN_API_URL}/token-approvals`);
    
    if (!response.ok) {
      console.log('Token approvals endpoint not available yet');
      return;
    }
    
    const approvals = await response.json();
    console.log(`Found ${approvals.length} token approvals`);
    
  } catch (error) {
    console.log('Token approvals endpoint not available yet');
  }
}

async function main() {
  await checkClients();
  await checkTokenApprovals();
}

main().catch(console.error);
