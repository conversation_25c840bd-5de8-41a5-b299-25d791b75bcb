import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import { prisma } from '@/lib/prisma';

// Contract ABIs (simplified for the functions we need)
const IdentityRegistryABI = [
  "function registerIdentity(address investor, uint16 country) external",
  "function addTo<PERSON>hitelist(address investor) external",
  "function removeFrom<PERSON><PERSON>elist(address investor) external",
  "function approveKyc(address investor) external",
  "function revokeKyc(address investor) external",
  "function isVerified(address investor) external view returns (bool)",
  "function isWhitelisted(address investor) external view returns (bool)",
  "function isKycApproved(address investor) external view returns (bool)",
  "function investorCountry(address investor) external view returns (uint16)",
  "function isFrozen(address investor) external view returns (bool)",
  "function freezeAddress(address investor) external",
  "function unfreezeAddress(address investor) external",
  "function batchAddToWhitelist(address[] calldata investors) external",
  "function batchApproveKyc(address[] calldata investors) external",
  "function getVerifiedAddressCount() external view returns (uint256)"
];

const ClaimRegistryABI = [
  "function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external",
  "function revokeClaim(address subject, uint256 topic) external",
  "function hasValidClaim(address subject, uint256 topic) external view returns (bool)",
  "function getClaimsByAddress(address subject) external view returns (tuple(uint256 topic, address issuer, bytes signature, bytes data, string uri, uint256 validUntil, bool revoked)[])",
  "function getTotalClaims() external view returns (uint256)",
  "function getClaimCount(address subject) external view returns (uint256)"
];

// Country code mapping (ISO-3166 numeric)
const COUNTRY_CODES: { [key: string]: number } = {
  'US': 840, 'USA': 840, 'United States': 840,
  'CA': 124, 'Canada': 124,
  'GB': 826, 'UK': 826, 'United Kingdom': 826,
  'DE': 276, 'Germany': 276,
  'FR': 250, 'France': 250,
  'IT': 380, 'Italy': 380,
  'ES': 724, 'Spain': 724,
  'NL': 528, 'Netherlands': 528,
  'CH': 756, 'Switzerland': 756,
  'AU': 36, 'Australia': 36,
  'JP': 392, 'Japan': 392,
  'SG': 702, 'Singapore': 702,
};

// Tokeny Claim Topics (using Tokeny-style Topic IDs)
const CLAIM_TOPICS = {
  KYC: 10101010000001,
  AML: 10101010000002,
  IDENTITY: 10101010000003,
  QUALIFICATION: 10101010000004,
  ACCREDITATION: 10101010000005,
  RESIDENCE: 10101010000006,
  TOKEN_ISSUER: 10101010000007
};

// Tokeny-style claim data format
function generateTokenyClaim(country: string, claimType: string): string {
  const now = new Date();
  const timestamp = now.getFullYear().toString().slice(-2) + // YY
                   (now.getMonth() + 1).toString().padStart(2, '0') + // MM
                   now.getDate().toString().padStart(2, '0') + // DD
                   now.getHours().toString().padStart(2, '0') + // HH
                   now.getMinutes().toString().padStart(2, '0') + // MM
                   now.getSeconds().toString().padStart(2, '0'); // SS

  const countryCode = getCountryCode(country).toString().padStart(3, '0');

  // Additional data based on claim type
  let additionalData = '';
  switch (claimType) {
    case 'KYC':
      additionalData = '001'; // KYC level 1
      break;
    case 'QUALIFICATION':
      additionalData = '002'; // Qualified investor
      break;
    case 'TOKEN_ISSUER':
      additionalData = '003'; // Token issuer
      break;
    default:
      additionalData = '000';
  }

  return timestamp + countryCode + additionalData;
}

function getProvider() {
  return new ethers.JsonRpcProvider(process.env.AMOY_RPC_URL);
}

function getSigner() {
  const provider = getProvider();
  return new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY!, provider);
}

function getIdentityRegistryContract() {
  const signer = getSigner();
  return new ethers.Contract(
    process.env.IDENTITY_REGISTRY_ADDRESS!,
    IdentityRegistryABI,
    signer
  );
}

function getClaimRegistryContract() {
  const signer = getSigner();
  return new ethers.Contract(
    process.env.CLAIM_REGISTRY_ADDRESS!,
    ClaimRegistryABI,
    signer
  );
}

function getCountryCode(country: string): number {
  return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA
}

// GET /api/identity?address=0x... - Get identity status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');

    if (!address) {
      return NextResponse.json(
        { error: 'address parameter is required' },
        { status: 400 }
      );
    }

    const identityRegistry = getIdentityRegistryContract();
    const claimRegistry = getClaimRegistryContract();

    // Get identity status from blockchain
    const [isVerified, isWhitelisted, isKycApproved, country, isFrozen] = await Promise.all([
      identityRegistry.isVerified(address),
      identityRegistry.isWhitelisted(address),
      identityRegistry.isKycApproved(address),
      identityRegistry.investorCountry(address),
      identityRegistry.isFrozen(address)
    ]);

    // Get claims
    let claims = [];
    try {
      // First try to get claim count to see if address has any claims
      const claimCount = await claimRegistry.getClaimCount(address);
      console.log(`Address ${address} has ${claimCount} claims`);

      if (claimCount > 0) {
        claims = await claimRegistry.getClaimsByAddress(address);
      }
    } catch (error) {
      console.warn('Could not fetch claims:', error);
      // Try individual claim checks for common topics
      try {
        const commonTopics = [10101010000001, 10101010000004, 10101010000007]; // KYC, Qualification, Token Issuer (Tokeny-style)
        for (const topic of commonTopics) {
          const hasValidClaim = await claimRegistry.hasValidClaim(address, topic);
          if (hasValidClaim) {
            claims.push({
              topic,
              issuer: '******************************************',
              signature: '0x',
              data: '0x',
              uri: `Topic ${topic} claim`,
              validUntil: 0,
              revoked: false
            });
          }
        }
      } catch (fallbackError) {
        console.warn('Fallback claim check also failed:', fallbackError);
      }
    }

    // Get client data from database
    const client = await prisma.client.findFirst({
      where: {
        walletAddress: {
          equals: address,
          mode: 'insensitive'
        }
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        nationality: true,
        kycStatus: true,
        isWhitelisted: true
      }
    });

    return NextResponse.json({
      address,
      blockchain: {
        isVerified,
        isWhitelisted,
        isKycApproved,
        country: country.toString(),
        isFrozen,
        claims: claims.length
      },
      database: {
        exists: !!client,
        kycStatus: client?.kycStatus || 'PENDING',
        isWhitelisted: client?.isWhitelisted || false,
        nationality: client?.nationality
      },
      client: client || null
    });

  } catch (error) {
    console.error('Error fetching identity status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch identity status' },
      { status: 500 }
    );
  }
}

// POST /api/identity - Register identity or update status
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, address, country, clientId } = body;

    if (!action || !address) {
      return NextResponse.json(
        { error: 'action and address are required' },
        { status: 400 }
      );
    }

    const identityRegistry = getIdentityRegistryContract();
    const claimRegistry = getClaimRegistryContract();
    let txHash = '';

    switch (action) {
      case 'register':
        {
          const countryCode = country ? getCountryCode(country) : 840; // Default to USA
          const tx = await identityRegistry.registerIdentity(address, countryCode);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Identity registered: ${address} (country: ${countryCode})`);
        }
        break;

      case 'whitelist':
        {
          const tx = await identityRegistry.addToWhitelist(address);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Added to whitelist: ${address}`);

          // Update database
          if (clientId) {
            await prisma.client.update({
              where: { id: clientId },
              data: { isWhitelisted: true }
            });
          }
        }
        break;

      case 'unwhitelist':
        {
          const tx = await identityRegistry.removeFromWhitelist(address);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Removed from whitelist: ${address}`);

          // Update database
          if (clientId) {
            await prisma.client.update({
              where: { id: clientId },
              data: { isWhitelisted: false }
            });
          }
        }
        break;

      case 'approve_kyc':
        {
          const tx = await identityRegistry.approveKyc(address);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ KYC approved: ${address}`);

          // Issue Tokeny-style KYC claim
          try {
            const kycClaimValue = generateTokenyClaim('US', 'KYC'); // Default to US
            const claimData = ethers.AbiCoder.defaultAbiCoder().encode(
              ["string", "string", "uint256"],
              [kycClaimValue, "KYC_APPROVED", Math.floor(Date.now() / 1000)]
            );
            const claimTx = await claimRegistry.issueClaim(
              address,
              CLAIM_TOPICS.KYC,
              "0x", // empty signature
              claimData,
              `KYC:${kycClaimValue}`, // URI with Tokeny format
              0 // never expires
            );
            await claimTx.wait();
            console.log(`✅ KYC claim issued: ${address} with value: ${kycClaimValue}`);
          } catch (error) {
            console.warn('Could not issue KYC claim:', error);
          }

          // Update database
          if (clientId) {
            await prisma.client.update({
              where: { id: clientId },
              data: { kycStatus: 'APPROVED' }
            });
          }
        }
        break;

      case 'revoke_kyc':
        {
          const tx = await identityRegistry.revokeKyc(address);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ KYC revoked: ${address}`);

          // Revoke KYC claim
          try {
            const claimTx = await claimRegistry.revokeClaim(address, CLAIM_TOPICS.KYC);
            await claimTx.wait();
            console.log(`✅ KYC claim revoked: ${address}`);
          } catch (error) {
            console.warn('Could not revoke KYC claim:', error);
          }

          // Update database
          if (clientId) {
            await prisma.client.update({
              where: { id: clientId },
              data: { kycStatus: 'REJECTED' }
            });
          }
        }
        break;

      case 'freeze':
        {
          const tx = await identityRegistry.freezeAddress(address);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Address frozen: ${address}`);
        }
        break;

      case 'unfreeze':
        {
          const tx = await identityRegistry.unfreezeAddress(address);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Address unfrozen: ${address}`);
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      action,
      address,
      txHash,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error processing identity action:', error);
    return NextResponse.json(
      { error: `Failed to ${body.action}: ${error.message}` },
      { status: 500 }
    );
  }
}
