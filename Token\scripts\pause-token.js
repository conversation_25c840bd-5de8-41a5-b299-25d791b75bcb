// Special script for pausing tokens on problematic networks like <PERSON>oy
const { ethers } = require("hardhat");

async function main() {
  // Get token address from environment variables or command line
  const tokenAddress = process.env.TOKEN_ADDRESS;
  if (!tokenAddress) {
    throw new Error("TOKEN_ADDRESS environment variable not set");
  }

  console.log(`Attempting to pause token at address: ${tokenAddress}`);

  // Get the signer
  const [signer] = await ethers.getSigners();
  console.log(`Using account: ${signer.address}`);

  try {
    // Connect to the token contract
    const token = await ethers.getContractAt("SecurityToken", tokenAddress);
    
    // Check if token is already paused
    try {
      const isPaused = await token.paused();
      console.log(`Current token pause status: ${isPaused ? "PAUSED" : "NOT PAUSED"}`);
      
      if (isPaused) {
        console.log("Token is already paused. No action needed.");
        return;
      }
    } catch (error) {
      console.log("Could not check pause status, continuing anyway...");
    }

    // Create a raw transaction for pause
    // Function selector for pause() is 0x8456cb59
    const pauseSelector = "0x8456cb59";
    
    // Set gas parameters with very high values to ensure it goes through
    const gasLimit = process.env.GAS_LIMIT ? parseInt(process.env.GAS_LIMIT) : 500000;
    
    // Get current nonce
    const nonce = await signer.getNonce();
    console.log(`Using nonce: ${nonce}`);
    
    // Form transaction
    const tx = {
      to: tokenAddress,
      data: pauseSelector,
      gasLimit: gasLimit,
      nonce: nonce
    };
    
    console.log("Sending raw pause transaction...");
    console.log(`Gas limit: ${gasLimit}`);
    
    // Send transaction
    const txResponse = await signer.sendTransaction(tx);
    console.log(`Transaction sent! Hash: ${txResponse.hash}`);
    
    // Wait for transaction to be mined
    console.log("Waiting for transaction confirmation...");
    const receipt = await txResponse.wait();
    console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
    console.log("Token successfully paused!");
  } catch (error) {
    console.error("Error pausing token:");
    console.error(error);
    
    // Provide additional troubleshooting guidance
    console.log("\nTroubleshooting Tips:");
    console.log("1. Make sure you have administrator rights on the token contract");
    console.log("2. Try with a higher gas limit:");
    console.log(`   $env:GAS_LIMIT="1000000"`);
    console.log("3. Try using a different RPC endpoint by updating hardhat.config.js");
    console.log("4. The Amoy testnet may be experiencing issues - try again later");
    
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 