// Check currencies in token data
const fetch = require('node-fetch');

async function checkCurrencies() {
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    const tokens = await response.json();
    
    console.log('Token currencies:');
    tokens.forEach(token => {
      console.log(`- ${token.symbol}: ${token.currency} (price: ${token.price})`);
    });
    
    // Check unique currencies
    const uniqueCurrencies = [...new Set(tokens.map(t => t.currency))];
    console.log('\nUnique currencies found:', uniqueCurrencies);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkCurrencies();
