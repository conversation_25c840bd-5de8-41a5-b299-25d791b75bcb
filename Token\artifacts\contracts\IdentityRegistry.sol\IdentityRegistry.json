{"_format": "hh-sol-artifact-1", "contractName": "IdentityRegistry", "sourceName": "contracts/IdentityRegistry.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldRegistry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newRegistry", "type": "address"}], "name": "ClaimRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "CountryRestricted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "CountryUnrestricted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "IdentityRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "IdentityRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "country", "type": "uint16"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "IdentityUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "RequiredClaimTopicAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "RequiredClaimTopicRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COMPLIANCE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "addRequiredClaimTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "approveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchAdd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchApproveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchFreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRemoveFrom<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRevokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchUnfreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "canTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimRegistry", "outputs": [{"internalType": "contract ClaimRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "freezeAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "getCountryInvestorCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "getIdentity", "outputs": [{"components": [{"internalType": "bool", "name": "isVerified", "type": "bool"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "isKycApproved", "type": "bool"}, {"internalType": "bool", "name": "isFrozen", "type": "bool"}, {"internalType": "uint16", "name": "country", "type": "uint16"}, {"internalType": "uint256", "name": "registeredAt", "type": "uint256"}, {"internalType": "uint256", "name": "updatedAt", "type": "uint256"}], "internalType": "struct IdentityRegistry.Identity", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRequiredClaimTopics", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getVerifiedAddressByIndex", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getVerifiedAddressCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "start", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getVerifiedAddresses", "outputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "_claimRegistry", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "investorCountry", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "isCountryRestricted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isKycApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "registerIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "removeIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTopic", "type": "uint256"}], "name": "removeRequiredClaimTopic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "restrictCountry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "revokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "unfreeze<PERSON>ddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "unrestrictCountry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newClaimRegistry", "type": "address"}], "name": "updateClaimRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "updateIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "0x60a0806040523461002a5730608052612ec0908161003082396080518181816114a501526116830152f35b600080fdfe608060408181526004908136101561001657600080fd5b600092833560e01c90816301ffc9a71461233c57508063045fb8881461227c578063062d3bd71461224157806308f45a581461220f5780631082e4c9146120fd578063122831911461202a5780631a03a5ac14611f7757806322459e1814611f3c578063248a9ca314611f045780632db6fa3614611e165780632f2ff15d14611dee5780632fea7b8114611cf457806336568abe14611cad5780633af32abf14611c6d5780633bec79b214611c4757806343359b0614611bcc5780634670dffe14611b04578063485cc9551461186e5780634f1ef28614611609578063503e72bf146115ea57806351e946d51461150957806352d1902d1461149057806357c07da914611427578063594b49a8146113595780635992b56d146112975780636c85cf6714611260578063784cb314146111735780637d4730ec146110dc5780637e42683b146110895780638ab1d68114610fb857806391d1485414610f645780639cb4733814610ea3578063a217fddf14610e88578063a878e15614610e5f578063ad3cb1cc14610dd3578063b9209e3314610d96578063bc4c855214610d5f578063bfb8054714610c96578063c1f2f1aa14610b39578063c5a80fa71461089f578063c789baec146107a2578063c824ecfa14610762578063cbdc20d714610685578063d547741f1461063a578063ddbf12491461055a578063e43252d71461040a578063e5839836146103c65763ff7de8ce1461023457600080fd5b346103c25760203660031901126103c257813590610250612a9d565b818452600760205260ff81852054161561036d57835b60068054808310156103625790849161027e84612659565b939054600394851b1c1461029757505050600101610266565b9193949590926000199283810190811161034f57906102c76102bb6102e093612659565b905490881b1c91612659565b90919082549060031b91821b91600019901b1916179055565b825490811561033c57508101926102f684612659565b81939154921b1b19169055555b81835260076020528220805460ff191690557f7f1e96c0c373272b21004fb177424aa2f7d89a6d332340aea280aa0d2e2c65368280a280f35b634e487b7160e01b885260319052602487fd5b634e487b7160e01b895260118352602489fd5b505050909150610303565b5162461bcd60e51b8152602081840152602a60248201527f4964656e7469747952656769737472793a20636c61696d20746f706963206e6f6044820152691d081c995c5d5a5c995960b21b6064820152608490fd5b8280fd5b8382346104065760203660031901126104065760209160ff9082906001600160a01b036103f1612404565b168152808552205460181c1690519015158152f35b5080fd5b50346103c25760208060031936011261055657610425612404565b61042d612a23565b6001600160a01b03168085528482528285205490939061044f9060ff16612536565b83855284825260ff8386205460081c166105075783855284825260ff8386205460181c166104ba5750839083825252822061010061ff00198254161781556002429101557fa850ae9193f515cbae8d35e8925bd2be26627fc91bce650b8652ed254e9cab038280a280f35b915162461bcd60e51b815291820152602360248201527f4964656e7469747952656769737472793a20616464726573732069732066726f6044820152623d32b760e91b6064820152608490fd5b915162461bcd60e51b815291820152602560248201527f4964656e7469747952656769737472793a20616c72656164792077686974656c6044820152641a5cdd195960da1b6064820152608490fd5b8380fd5b8382346104065761056a36612390565b9190610574612a23565b61057c612b0a565b6105878315156124a0565b835b8381106105a657846001600080516020612e6b8339815191525580f35b6001906001600160a01b036105c46105bf8388876124fc565b612522565b1680875286602052848720805460ff808216908161062c575b506105ec575b50505001610589565b62ff0000191662010000178155426002909101557f58d052b1bc711fbe4d875dbe3001969a01a1e190222ffd02168c01c58dd880658780a28680806105e3565b90508160101c16158a6105dd565b50346103c257806003193601126103c257610681913561067c600161065d61241a565b93838752600080516020612e4b83398151915260205286200154612ad7565b612d23565b5080f35b8382346104065761069536612390565b919061069f612a23565b6106a7612b0a565b6106b28315156124a0565b835b8381106106d157846001600080516020612e6b8339815191525580f35b6001906001600160a01b036106ea6105bf8388876124fc565b1680875286602052848720805460ff8082169081610754575b50610712575b505050016106b4565b63ff00000019166301000000178155426002909101557f90811a8edd3b3c17eeaefffc17f639cc69145d41a359c9843994dc25382036908780a2868080610709565b90508160181c16158a610703565b8382346104065760203660031901126104065760209160ff9082906001600160a01b0361078d612404565b168152808552205460101c1690519015158152f35b50346103c25760203660031901126103c2578135916107bf612a9d565b828452600760205260ff82852054166108465760065490600160401b8210156108335750826102c78260016107f79401600655612659565b81835260076020528220805460ff191660011790557f50e99ab14180451dd0260a9de0faf6d66f1db881d883bf473fa474b3871110de8280a280f35b634e487b7160e01b855260419052602484fd5b6020608492519162461bcd60e51b8352820152602e60248201527f4964656e7469747952656769737472793a20636c61696d20746f70696320616c60448201526d1c9958591e481c995c5d5a5c995960921b6064820152fd5b508290346104065780600319360112610406576108ba612404565b906108c36123f3565b916108cc612a23565b6001600160a01b038116918215610ae05782855260209185835260ff8287205416610a875761ffff80951694858752600284528287205460ff161561091090612594565b8683519161091d83612430565b600183528583019082825285840191838352606085019084825260808601918b835260a08701944286528960c08901974289528d8152808d5220975115159288549051151560081b61ff00169151151560101b62ff0000169251151560181b63ff000000169360ff169063ffffffff191617171717855551166109b990849065ffff0000000082549160201b169065ffff000000001916179055565b516001830155519060020155600354600160401b811015610a7457906109e8826001610a069401600355612622565b90919060018060a01b038084549260031b9316831b921b1916179055565b6003546000198101908111610a61578596847f4dff57f8a1fd916c525a7a89d7885c5ffba52da35cb740c4c1f0fa46929a5beb9596975283528187205584865260018252808620610a578154612613565b905551428152a380f35b634e487b7160e01b865260118752602486fd5b634e487b7160e01b875260418852602487fd5b815162461bcd60e51b8152808801849052602d60248201527f4964656e7469747952656769737472793a206964656e7469747920616c72656160448201526c191e481c9959da5cdd195c9959609a1b6064820152608490fd5b5162461bcd60e51b8152602081870152602e60248201527f4964656e7469747952656769737472793a2063616e6e6f74207265676973746560448201526d72207a65726f206164647265737360901b6064820152608490fd5b50919034610406576020806003193601126103c257610b56612404565b93610b5f612a23565b6001600160a01b0394851680855284835281852054909390610b839060ff16612536565b83855284835261ffff82862054841c16855260018352818520610ba681546125f0565b905583855280835281852054600354600019979190888101908111610c83578083918303610c54575b505050600354968715610c41577ff06dfe9062e9317cd357560ea667d65a9b032f1a674c04e99653e894e145c8fa9596970190610c0b82612622565b909182549160031b1b1916905560035584865282528481812055848252846002828220828155826001820155015551428152a280f35b634e487b7160e01b875260318352602487fd5b610c5d90612622565b90549060031b1c16610c72816109e884612622565b875282855283872055388181610bcf565b634e487b7160e01b885260118452602488fd5b50346103c25760203660031901126103c257610cb0612404565b610cb8612a23565b6001600160a01b031680845260208490528184205490929060181c60ff1615610d1d575081835282602052822063ff0000001981541681556002429101557fc3776b472ebf54114339eec9e4dc924e7ce307a97f5c1ee72b6d474e6e5e8b7c8280a280f35b6020606492519162461bcd60e51b8352820152601c60248201527f4964656e7469747952656769737472793a206e6f742066726f7a656e000000006044820152fd5b8382346104065760203660031901126104065760ff8160209361ffff610d836123e2565b1681526002855220541690519015158152f35b8382346104065760203660031901126104065760209160ff9082906001600160a01b03610dc1612404565b16815280855220541690519015158152f35b5090346103c257826003193601126103c2578151908282019082821067ffffffffffffffff8311176108335750825260058152602090640352e302e360dc1b6020820152825193849260208452825192836020860152825b848110610e4957505050828201840152601f01601f19168101030190f35b8181018301518882018801528795508201610e2b565b83823461040657816003193601126104065760055490516001600160a01b039091168152602090f35b83823461040657816003193601126104065751908152602090f35b83823461040657610eb336612390565b9190610ebd612a23565b610ec5612b0a565b610ed08315156124a0565b835b838110610eef57846001600080516020612e6b8339815191525580f35b6001906001600160a01b03610f086105bf8388876124fc565b1680875286602052848720805460ff8160101c16610f2a575b50505001610ed2565b62ff0000191681556002429101557ff1c17763556df78bc208351a44a3321884a234d46c5f9a055d0bf24740f5745c8780a2868080610f21565b5090346103c257816003193601126103c2578160209360ff92610f8561241a565b90358252600080516020612e4b83398151915286528282206001600160a01b039091168252855220549151911615158152f35b50346103c25760203660031901126103c257610fd2612404565b610fda612a23565b6001600160a01b031680845260208490528184205490929060081c60ff161561103d575081835282602052822061ff001981541681556002429101557fcdd2e9b91a56913d370075169cefa1602ba36be5301664f752192bb1709df7578280a280f35b6020608492519162461bcd60e51b8352820152602160248201527f4964656e7469747952656769737472793a206e6f742077686974656c697374656044820152601960fa1b6064820152fd5b8382346104065760203660031901126104065760209161ffff9082906001600160a01b036110b5612404565b168082528186526110cb60ff8484205416612536565b81528085522054831c169051908152f35b509134611170576020366003190112611170575080359060035482101561111f5760208361110984612622565b60018060a01b0391549060031b1c169051908152f35b608490602084519162461bcd60e51b8352820152602560248201527f4964656e7469747952656769737472793a20696e646578206f7574206f6620626044820152646f756e647360d81b6064820152fd5b80fd5b50346103c25760203660031901126103c25761118d612404565b611195612a23565b60018060a01b031691828452836020526111b460ff8386205416612536565b8284528360205260ff8285205460101c1661120f57508183528260205282206201000062ff0000198254161781556002429101557f58d052b1bc711fbe4d875dbe3001969a01a1e190222ffd02168c01c58dd880658280a280f35b6020608492519162461bcd60e51b8352820152602660248201527f4964656e7469747952656769737472793a20616c7265616479204b59432061706044820152651c1c9bdd995960d21b6064820152fd5b83823461040657806003193601126104065760209061128e611280612404565b61128861241a565b906128f4565b90519015158152f35b838234610406576112a736612390565b91906112b1612a23565b6112b9612b0a565b6112c48315156124a0565b835b8381106112e357846001600080516020612e6b8339815191525580f35b6001906001600160a01b036112fc6105bf8388876124fc565b1680875286602052848720805460ff8160181c1661131e575b505050016112c6565b63ff000000191681556002429101557fc3776b472ebf54114339eec9e4dc924e7ce307a97f5c1ee72b6d474e6e5e8b7c8780a2868080611315565b50346103c25760203660031901126103c25761ffff6113766123e2565b61137e612a9d565b1691828452600260205260ff82852054166113d0575081835260026020528220805460ff191660011790557f8167b56997f0a49901e10854844c55dab2f8c1d04a345683d858cb73a9904d718280a280f35b6020608492519162461bcd60e51b8352820152602c60248201527f4964656e7469747952656769737472793a20636f756e74727920616c7265616460448201526b1e481c995cdd1c9a58dd195960a21b6064820152fd5b5090346103c257816003193601126103c2576114479060243590356127fd565b815192839260208080860192818752855180945286019401925b82811061147057505050500390f35b83516001600160a01b031685528695509381019392810192600101611461565b508234611170578060031936011261117057507f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031630036114fc57602090517f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8152f35b5163703e46dd60e11b8152fd5b50346103c25760208060031936011261055657611524612404565b61152c612a23565b6001600160a01b03168085528482528285205490939061154e9060ff16612536565b83855284825260ff8386205460181c166115a957508390838252528220630100000063ff000000198254161781556002429101557f90811a8edd3b3c17eeaefffc17f639cc69145d41a359c9843994dc25382036908280a280f35b915162461bcd60e51b815291820181905260248201527f4964656e7469747952656769737472793a20616c72656164792066726f7a656e6044820152606490fd5b8382346104065781600319360112610406576020906003549051908152f35b50806003193601126103c25761161d612404565b90602493843567ffffffffffffffff81116104065736602382011215610406578085013561164a81612484565b9461165785519687612462565b81865260209182870193368a838301011161186a578186928b8693018737880101526001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000811630811490811561183c575b5061182c576116bc612a9d565b81169585516352d1902d60e01b815283818a818b5afa8691816117f9575b506116f6575050505050505191634c9c8ce360e01b8352820152fd5b9088888894938c7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc918281036117e45750853b156117d0575080546001600160a01b031916821790558451889392917fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b8580a28251156117b25750506106819582915190845af4913d156117a8573d61179a61179182612484565b92519283612462565b81528581943d92013e612de7565b5060609250612de7565b9550955050505050346117c457505080f35b63b398979f60e01b8152fd5b8651634c9c8ce360e01b8152808501849052fd5b8751632a87526960e21b815280860191909152fd5b9091508481813d8311611825575b6118118183612462565b81010312611821575190386116da565b8680fd5b503d611807565b855163703e46dd60e11b81528890fd5b9050817f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc54161415386116af565b8580fd5b5090346103c257816003193601126103c257611888612404565b61189061241a565b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a009283549260ff84871c16159267ffffffffffffffff851694851580611afd575b60018097149081611af3575b159081611aea575b50611ada5767ffffffffffffffff1981168617875584611abb575b506001600160a01b039082821615611a6157169061196390611923831515612766565b61192b612da6565b611933612da6565b61193b612da6565b85600080516020612e6b83398151915255611954612da6565b61195d81612b3a565b50612bda565b506bffffffffffffffffffffffff60a01b6005541617600555600654600160401b9081811015611a4e57808561199c9201600655612659565b919080549260031b600019938488831b921b191617905560065490811015611a4e5780856119cd9201600655612659565b819291549060031b9184831b921b191617905582865260076020528486209060ff1991848382541617905586528285872091825416179055611a0d578380f35b7fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29260209268ff000000000000000019815416905551908152a13880808380f35b634e487b7160e01b885260418352602488fd5b875162461bcd60e51b8152602081860152602e60248201527f4964656e7469747952656769737472793a2061646d696e2063616e6e6f74206260448201526d65207a65726f206164647265737360901b6064820152608490fd5b68ffffffffffffffffff19166801000000000000000117865538611900565b875163f92ee8a960e01b81528490fd5b905015386118e5565b303b1591506118dd565b50846118d1565b50346103c25760203660031901126103c25761ffff611b216123e2565b611b29612a9d565b1691828452600260205260ff828520541615611b79575081835260026020528220805460ff191690557f36dc0db8018c1313d02e9ec8640b1718e43cbfa13fa2ae9ad3d8688aee46364d8280a280f35b6020608492519162461bcd60e51b8352820152602860248201527f4964656e7469747952656769737472793a20636f756e747279206e6f742072656044820152671cdd1c9a58dd195960c21b6064820152fd5b833461117057602036600319011261117057611be6612404565b611bee612a9d565b6001600160a01b0390811690611c05821515612766565b600554826bffffffffffffffffffffffff60a01b821617600555167f3d3924a743f428521fe93cb2097c3ac3112a67671bc6afe7d6bc16fefc03ff698380a380f35b8382346104065760203660031901126104065760209061128e611c68612404565b612690565b8382346104065760203660031901126104065760209160ff9082906001600160a01b03611c98612404565b168152808552205460081c1690519015158152f35b50829034610406578060031936011261040657611cc861241a565b90336001600160a01b03831603611ce55750610681919235612d23565b5163334bd91960e11b81528390fd5b83823461040657602090816003193601126103c2578060e093611d15612404565b8160c08451611d2381612430565b828152828882015282868201528260608201528260808201528260a0820152015260018060a01b0316815280845220815191611d5e83612430565b815460ff81161515948585528085019260ff8360081c161515845280860160ff8460101c1615158152606087019260ff8560181c161515845261ffff958660808a0196831c16865260c0600260018a01549960a08c019a8b52015499019889528351998a5251151590890152511515908701525115156060860152511660808401525160a08301525160c0820152f35b50346103c257806003193601126103c2576106819135611e11600161065d61241a565b612c9c565b83823461040657611e2636612390565b9190611e30612a23565b611e38612b0a565b611e438315156124a0565b835b838110611e6257846001600080516020612e6b8339815191525580f35b6001906001600160a01b03611e7b6105bf8388876124fc565b1680875286602052848720805460ff8082169081611ef5575b81611ee7575b50611ea9575b50505001611e45565b61ff001916610100178155426002909101557fa850ae9193f515cbae8d35e8925bd2be26627fc91bce650b8652ed254e9cab038780a2868080611ea0565b90508160181c16158a611e9a565b8091508260081c161590611e94565b5090346103c25760203660031901126103c25781602093600192358152600080516020612e4b83398151915285522001549051908152f35b838234610406578160031936011261040657602090517fcab5a0bfe0b79d2c4b1c2e02599fa044d115b7511f9659307cb42769509677098152f35b5082346111705780600319360112611170579080519182906006549182855260208095018093600684527ff652222313e28459528d920b65115c16c04f3efc82aaedc97be59f3f377c0d3f90845b8181106120165750505081611fdb910382612462565b83519485948186019282875251809352850193925b828110611fff57505050500390f35b835185528695509381019392810192600101611ff0565b825484529288019260019283019201611fc5565b50346103c25760203660031901126103c257612044612404565b61204c612a23565b6001600160a01b031680845260208490528184205490929060101c60ff16156120b0575081835282602052822062ff00001981541681556002429101557ff1c17763556df78bc208351a44a3321884a234d46c5f9a055d0bf24740f5745c8280a280f35b6020608492519162461bcd60e51b8352820152602260248201527f4964656e7469747952656769737472793a206e6f74204b594320617070726f76604482015261195960f21b6064820152fd5b838234610406578060031936011261040657612117612404565b907fc6e61adbbeea91c3fd926f72939eb0f3cfbe0056b3d4584c47cd26ee5b72a0006121416123f3565b9261214a612a23565b6001600160a01b03168085526020858152838620549193909161216f9060ff16612536565b61ffff94858116958688526002845261218e60ff848a20541615612594565b85885287845282882054841c168681036121de575b508487528683526121d082882091829065ffff0000000082549160201b169065ffff000000001916179055565b60024291015551428152a380f35b8752600183528187206121f181546125f0565b9055858752600183528187206122078154612613565b9055876121a3565b838234610406576020366003190112610406578060209261ffff6122316123e2565b1681526001845220549051908152f35b838234610406578160031936011261040657602090517f442a94f1a1fac79af32856af2a64f63648cfa2ef3b98610a5bb7cbec4cee69858152f35b8382346104065761228c36612390565b9190612296612a23565b61229e612b0a565b6122a98315156124a0565b835b8381106122c857846001600080516020612e6b8339815191525580f35b6001906001600160a01b036122e16105bf8388876124fc565b1680875286602052848720805460ff8160081c16612303575b505050016122ab565b61ff00191681556002429101557fcdd2e9b91a56913d370075169cefa1602ba36be5301664f752192bb1709df7578780a28680806122fa565b849084346103c25760203660031901126103c2573563ffffffff60e01b81168091036103c25760209250637965db0b60e01b811490811561237f575b5015158152f35b6301ffc9a760e01b14905083612378565b9060206003198301126123dd5760043567ffffffffffffffff928382116123dd57806023830112156123dd5781600401359384116123dd5760248460051b830101116123dd576024019190565b600080fd5b6004359061ffff821682036123dd57565b6024359061ffff821682036123dd57565b600435906001600160a01b03821682036123dd57565b602435906001600160a01b03821682036123dd57565b60e0810190811067ffffffffffffffff82111761244c57604052565b634e487b7160e01b600052604160045260246000fd5b90601f8019910116810190811067ffffffffffffffff82111761244c57604052565b67ffffffffffffffff811161244c57601f01601f191660200190565b156124a757565b60405162461bcd60e51b815260206004820152602760248201527f4964656e7469747952656769737472793a20656d7074792061646472657373656044820152667320617272617960c81b6064820152608490fd5b919081101561250c5760051b0190565b634e487b7160e01b600052603260045260246000fd5b356001600160a01b03811681036123dd5790565b1561253d57565b60405162461bcd60e51b815260206004820152602960248201527f4964656e7469747952656769737472793a206964656e74697479206e6f7420726044820152681959da5cdd195c995960ba1b6064820152608490fd5b1561259b57565b60405162461bcd60e51b815260206004820152602760248201527f4964656e7469747952656769737472793a20636f756e747279206973207265736044820152661d1c9a58dd195960ca1b6064820152608490fd5b80156125fd576000190190565b634e487b7160e01b600052601160045260246000fd5b60001981146125fd5760010190565b60035481101561250c5760036000527fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b0190600090565b60065481101561250c5760066000527ff652222313e28459528d920b65115c16c04f3efc82aaedc97be59f3f377c0d3f0190600090565b600654801561275f5760055490916001600160a01b03918216919060005b8481106126bf575050505050600190565b6126c881612659565b905490604091825191632c52e7ab60e01b8352868616600484015260031b1c6024820152602080826044818a5afa928315612755575060009261271f575b505015612715576001016126ae565b5050505050600090565b81813d831161274e575b6127338183612462565b81010312610406575190811515820361117057503880612706565b503d612729565b513d6000823e3d90fd5b5050600190565b1561276d57565b60405162461bcd60e51b815260206004820152603760248201527f4964656e7469747952656769737472793a20636c61696d20726567697374727960448201527f2063616e6e6f74206265207a65726f20616464726573730000000000000000006064820152608490fd5b67ffffffffffffffff811161244c5760051b60200190565b919082039182116125fd57565b60038054909392818310156128c6578201908183116125fd578082116128be575b5061282982826127f0565b9161284c612836846127d8565b936128446040519586612462565b8085526127d8565b602090601f190136858301378395825b84811061286b57505050505050565b61287481612622565b905490831b1c6001600160a01b03169061288e85826127f0565b9187518310156128a9578460019360051b890101520161285c565b60246000634e487b7160e01b81526032600452fd5b90503861281e565b50505090506040516020810181811067ffffffffffffffff82111761244c5760405260008152600036813790565b9060018060a01b03918281169060009382855260209085825260ff93604093858589205416158015612a11575b6129de57818852858589205460081c161580156129fc575b6129de57818852858589205460181c1680156129e8575b6129de5761295d90612690565b1580156129ce575b6129c557865285825261ffff908184882054841c168752600283528484882054169586156129a1575b50505050505061299e5750600190565b90565b16865285825282862054821c1685526002905283205416905038808080808061298e565b50505050505090565b506129d886612690565b15612965565b5050505050505090565b508287168852858589205460181c16612950565b508287168852858589205460081c1615612939565b50828716885285858920541615612921565b3360009081527f81cf8a8d68c3bf977ff8d17e8ef6f30dbea1776614c7d2258c5ab1b17338333f60205260409020547fcab5a0bfe0b79d2c4b1c2e02599fa044d115b7511f9659307cb42769509677099060ff1615612a7f5750565b6044906040519063e2517d3f60e01b82523360048301526024820152fd5b3360009081527fb7db2dd08fcb62d0c9e08c51941cae53c267786a0b75803fb7960902fc8ef97d602052604081205460ff1615612a7f5750565b80600052600080516020612e4b83398151915260205260406000203360005260205260ff6040600020541615612a7f5750565b600080516020612e6b8339815191526002815414612b285760029055565b604051633ee5aeb560e01b8152600490fd5b6001600160a01b031660008181527fb7db2dd08fcb62d0c9e08c51941cae53c267786a0b75803fb7960902fc8ef97d6020526040812054909190600080516020612e4b8339815191529060ff16612bd5578280526020526040822081835260205260408220600160ff1982541617905533917f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d8180a4600190565b505090565b6001600160a01b031660008181527f81cf8a8d68c3bf977ff8d17e8ef6f30dbea1776614c7d2258c5ab1b17338333f60205260408120549091907fcab5a0bfe0b79d2c4b1c2e02599fa044d115b7511f9659307cb427695096770990600080516020612e4b8339815191529060ff16612c96578184526020526040832082845260205260408320600160ff198254161790557f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d339380a4600190565b50505090565b90600091808352600080516020612e4b83398151915280602052604084209260018060a01b03169283855260205260ff60408520541615600014612c96578184526020526040832082845260205260408320600160ff198254161790557f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d339380a4600190565b90600091808352600080516020612e4b83398151915280602052604084209260018060a01b03169283855260205260ff604085205416600014612c9657818452602052604083208284526020526040832060ff1981541690557ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b339380a4600190565b60ff7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a005460401c1615612dd557565b604051631afcd79f60e31b8152600490fd5b90612e0e5750805115612dfc57805190602001fd5b60405163d6bda27560e01b8152600490fd5b81511580612e41575b612e1f575090565b604051639996b31560e01b81526001600160a01b039091166004820152602490fd5b50803b15612e1756fe02dd7bc7dec4dceedda775e58dd541e08a116c6c53815c0bd028192f7b6268009b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00a2646970667358221220fe7d635ac0cc959edb5351608e983b3106ab9b65bad4d3290959356d41ca5b4664736f6c63430008160033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}