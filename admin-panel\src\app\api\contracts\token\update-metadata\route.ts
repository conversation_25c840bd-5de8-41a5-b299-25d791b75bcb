import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenABI from '../../../../../contracts/SecurityToken.json';

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002, // Default to Amoy
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      tokenAddress, 
      tokenPrice,
      bonusTiers,
      network = 'amoy' 
    } = body;
    
    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      );
    }
    
    if (!tokenPrice && !bonusTiers) {
      return NextResponse.json(
        { error: 'At least one of tokenPrice or bonusTiers must be provided' },
        { status: 400 }
      );
    }
    
    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials.'
        },
        { status: 422 }
      );
    }
    
    // Validate address
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format provided' },
        { status: 400 }
      );
    }
    
    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;
    
    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);
    
    // Connect to the network
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });
    
    // Ensure the network is connected
    const network_details = await provider.getNetwork();
    console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);
    
    // Create wallet and connect to provider
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    console.log(`Wallet address: ${wallet.address}`);
    
    // Connect to the token contract
    const tokenContract = new ethers.Contract(
      tokenAddress,
      SecurityTokenABI.abi,
      wallet
    );
    
    // Fetch current metadata if not provided
    let currentTokenPrice = tokenPrice;
    let currentBonusTiers = bonusTiers;
    
    if (!currentTokenPrice) {
      currentTokenPrice = await tokenContract.tokenPrice();
      console.log(`Using current token price: ${currentTokenPrice}`);
    }
    
    if (!currentBonusTiers) {
      currentBonusTiers = await tokenContract.bonusTiers();
      console.log(`Using current bonus tiers: ${currentBonusTiers}`);
    }
    
    // Update token metadata
    console.log(`Updating token metadata...`);
    console.log(`Token price: ${currentTokenPrice}`);
    console.log(`Bonus tiers: ${currentBonusTiers}`);
    
    try {
      // Call the updateTokenMetadata function
      const tx = await tokenContract.updateTokenMetadata(currentTokenPrice, currentBonusTiers);
      console.log(`Transaction hash: ${tx.hash}`);
      
      // Wait for the transaction to be mined
      const receipt = await tx.wait();
      
      return NextResponse.json({
        success: true,
        action: 'updateTokenMetadata',
        txHash: tx.hash,
        blockNumber: receipt.blockNumber,
        tokenPrice: currentTokenPrice,
        bonusTiers: currentBonusTiers
      });
      
    } catch (txError: any) {
      console.error('Transaction error:', txError);
      
      // Check if the issue might be a permissions problem
      try {
        const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
        const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);
        
        if (!hasAdminRole) {
          return NextResponse.json({
            success: false,
            error: "The connected wallet doesn't have the DEFAULT_ADMIN_ROLE required for updating metadata",
            details: `Please grant DEFAULT_ADMIN_ROLE to ${wallet.address} on the token contract`
          }, { status: 403 });
        }
      } catch (roleCheckError) {
        console.error('Role check error:', roleCheckError);
      }
      
      throw txError; // Re-throw to be caught by the outer catch
    }
    
  } catch (error: any) {
    console.error('Error updating token metadata:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
} 