/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/qualification-progress/route";
exports.ids = ["app/api/qualification-progress/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/qualification-progress/route.ts */ \"(rsc)/./src/app/api/qualification-progress/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/qualification-progress/route\",\n        pathname: \"/api/qualification-progress\",\n        filename: \"route\",\n        bundlePath: \"app/api/qualification-progress/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\qualification-progress\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/qualification-progress/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/qualification-progress/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const clientEmail = searchParams.get('clientEmail');\n        const tokenAddress = searchParams.get('tokenAddress');\n        const clientId = searchParams.get('clientId');\n        if (!clientEmail && !clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client email or ID required'\n            }, {\n                status: 400\n            });\n        }\n        // Find client first\n        let client;\n        if (clientId) {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n                where: {\n                    id: clientId\n                }\n            });\n        } else {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n                where: {\n                    email: clientEmail\n                }\n            });\n        }\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find qualification progress\n        const whereClause = {\n            clientId: client.id\n        };\n        if (tokenAddress) {\n            // Find token first\n            const token = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findFirst({\n                where: {\n                    address: tokenAddress\n                }\n            });\n            if (token) {\n                whereClause.tokenId = token.id;\n            } else {\n                // If token not found, return default progress\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    country: '',\n                    countryCompleted: false,\n                    agreementAccepted: false,\n                    profileCompleted: !!client,\n                    walletConnected: !!client.walletAddress,\n                    kycCompleted: client.kycStatus === 'APPROVED',\n                    currentStep: 0,\n                    completedSteps: 0,\n                    tokenAddress: tokenAddress,\n                    clientEmail: client.email,\n                    lastUpdated: new Date().toISOString()\n                });\n            }\n        }\n        const progress = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.findFirst({\n            where: whereClause,\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        if (!progress) {\n            // Return default progress based on client status\n            const defaultProgress = {\n                country: '',\n                countryCompleted: false,\n                agreementAccepted: false,\n                profileCompleted: !!client,\n                walletConnected: !!client.walletAddress,\n                kycCompleted: client.kycStatus === 'APPROVED',\n                currentStep: 0,\n                completedSteps: 0,\n                tokenAddress: tokenAddress,\n                clientEmail: client.email,\n                lastUpdated: new Date().toISOString()\n            };\n            // Calculate current step based on completion status\n            if (defaultProgress.kycCompleted) {\n                defaultProgress.currentStep = 5;\n                defaultProgress.completedSteps = 5;\n            } else if (defaultProgress.walletConnected) {\n                defaultProgress.currentStep = 4;\n                defaultProgress.completedSteps = 4;\n            } else if (defaultProgress.profileCompleted) {\n                defaultProgress.currentStep = 3;\n                defaultProgress.completedSteps = 3;\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(defaultProgress);\n        }\n        // Return existing progress\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            country: progress.countryValue || '',\n            countryCompleted: progress.countrySelected,\n            agreementAccepted: progress.agreementAccepted,\n            profileCompleted: progress.profileCompleted,\n            walletConnected: progress.walletConnected,\n            kycCompleted: progress.kycCompleted,\n            currentStep: progress.currentStep,\n            completedSteps: progress.completedSteps,\n            tokenAddress: progress.token?.address || tokenAddress,\n            clientEmail: progress.client.email,\n            lastUpdated: progress.updatedAt.toISOString()\n        });\n    } catch (error) {\n        console.error('Error fetching qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { clientEmail, clientId, tokenAddress, country, countryCompleted, agreementAccepted, profileCompleted, walletConnected, kycCompleted, currentStep, completedSteps } = body;\n        if (!clientEmail && !clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client email or ID required'\n            }, {\n                status: 400\n            });\n        }\n        // Find client\n        let client;\n        if (clientId) {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n                where: {\n                    id: clientId\n                }\n            });\n        } else {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n                where: {\n                    email: clientEmail\n                }\n            });\n        }\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find token if provided\n        let token = null;\n        if (tokenAddress) {\n            token = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findFirst({\n                where: {\n                    address: tokenAddress\n                }\n            });\n        }\n        // Create or update qualification progress\n        const progressData = {\n            clientId: client.id,\n            tokenId: token?.id,\n            countrySelected: countryCompleted || false,\n            countryValue: country || '',\n            agreementAccepted: agreementAccepted || false,\n            profileCompleted: profileCompleted || false,\n            walletConnected: walletConnected || false,\n            kycCompleted: kycCompleted || false,\n            currentStep: currentStep || 0,\n            completedSteps: completedSteps || 0,\n            // Set completion timestamps\n            countryCompletedAt: countryCompleted ? new Date() : null,\n            agreementCompletedAt: agreementAccepted ? new Date() : null,\n            profileCompletedAt: profileCompleted ? new Date() : null,\n            walletCompletedAt: walletConnected ? new Date() : null,\n            kycCompletedAt: kycCompleted ? new Date() : null\n        };\n        const whereClause = {\n            clientId: client.id\n        };\n        if (token) {\n            whereClause.tokenId = token.id;\n        } else if (!tokenAddress) {\n            // For global progress (no specific token)\n            whereClause.tokenId = null;\n        }\n        const progress = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.upsert({\n            where: {\n                clientId_tokenId: whereClause\n            },\n            update: progressData,\n            create: progressData,\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        console.log('💾 Saved qualification progress to database:', {\n            clientEmail: client.email,\n            tokenAddress: token?.address || 'global',\n            currentStep: progressData.currentStep,\n            completedSteps: progressData.completedSteps\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Qualification progress saved successfully',\n            data: {\n                id: progress.id,\n                currentStep: progress.currentStep,\n                completedSteps: progress.completedSteps,\n                lastUpdated: progress.updatedAt.toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error saving qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/qualification-progress/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();