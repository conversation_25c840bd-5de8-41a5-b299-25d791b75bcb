const { ethers, upgrades } = require("hardhat");
const fs = require("fs");

async function main() {
  const proxyAddress = "******************************************";
  
  // Get the implementation address
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
  console.log("Implementation address:", implementationAddress);
  
  // Get the bytecode of the implementation contract
  const provider = ethers.provider;
  const bytecode = await provider.getCode(implementationAddress);
  console.log("Bytecode length:", bytecode.length);
  
  // Save the bytecode to a file for reference
  fs.writeFileSync("implementation-bytecode.txt", bytecode);
  console.log("Bytecode saved to implementation-bytecode.txt");
  
  // Get the current implementation contract
  const whitelist = await ethers.getContractAt("WhitelistV2", implementationAddress);
  
  // Try to call a function from the implementation contract
  try {
    // Get the AGENT_ROLE constant
    const AGENT_ROLE = await whitelist.AGENT_ROLE();
    console.log("AGENT_ROLE:", AGENT_ROLE);
    
    // Try to access the KYC functions
    try {
      // Check if the function exists in the contract
      const functionFragment = whitelist.interface.getFunction("isKycApproved");
      console.log("isKycApproved function exists in ABI:", !!functionFragment);
      
      // Try to call the function directly on the implementation (this might fail)
      console.log("Trying to call isKycApproved directly on implementation...");
      const testAddress = "******************************************"; // Example address
      try {
        const isApproved = await whitelist.isKycApproved(testAddress);
        console.log(`Address ${testAddress} KYC approved: ${isApproved}`);
      } catch (callError) {
        console.log("Error calling isKycApproved on implementation:", callError.message);
      }
    } catch (error) {
      console.log("Error accessing isKycApproved function:", error.message);
    }
  } catch (error) {
    console.error("Error interacting with implementation contract:", error.message);
  }
  
  // Try to interact with the proxy contract
  try {
    console.log("\nTrying to interact with the proxy contract...");
    const whitelistProxy = await ethers.getContractAt("WhitelistV2", proxyAddress);
    
    // Try to call a function through the proxy
    try {
      const AGENT_ROLE = await whitelistProxy.AGENT_ROLE();
      console.log("AGENT_ROLE from proxy:", AGENT_ROLE);
      
      // Try to call the KYC function through the proxy
      try {
        const testAddress = "******************************************"; // Example address
        const isApproved = await whitelistProxy.isKycApproved(testAddress);
        console.log(`Address ${testAddress} KYC approved through proxy: ${isApproved}`);
      } catch (kycError) {
        console.log("Error calling isKycApproved through proxy:", kycError.message);
      }
    } catch (proxyError) {
      console.log("Error calling function through proxy:", proxyError.message);
    }
  } catch (error) {
    console.error("Error interacting with proxy contract:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 