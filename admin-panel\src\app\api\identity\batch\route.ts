import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import { prisma } from '@/lib/prisma';

// Contract ABIs (simplified for batch operations)
const IdentityRegistryABI = [
  "function batchAddToWhitelist(address[] calldata investors) external",
  "function batchApproveKyc(address[] calldata investors) external",
  "function batchRemoveFromWhitelist(address[] calldata investors) external",
  "function batchRevokeKyc(address[] calldata investors) external"
];

const ClaimRegistryABI = [
  "function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external"
];

// Claim topics (Tokeny-style Topic IDs)
const CLAIM_TOPICS = {
  KYC: 10101010000001,
  QUALIFICATION: 10101010000004
};

function getSigner() {
  const provider = new ethers.JsonRpcProvider(process.env.AMOY_RPC_URL);
  return new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY!, provider);
}

function getIdentityRegistryContract() {
  const signer = getSigner();
  return new ethers.Contract(
    process.env.IDENTITY_REGISTRY_ADDRESS!,
    IdentityRegistryABI,
    signer
  );
}

function getClaimRegistryContract() {
  const signer = getSigner();
  return new ethers.Contract(
    process.env.CLAIM_REGISTRY_ADDRESS!,
    ClaimRegistryABI,
    signer
  );
}

// POST /api/identity/batch - Batch operations for multiple addresses
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, addresses, clientIds } = body;

    if (!action || !addresses || !Array.isArray(addresses)) {
      return NextResponse.json(
        { error: 'action and addresses array are required' },
        { status: 400 }
      );
    }

    if (addresses.length === 0) {
      return NextResponse.json(
        { error: 'addresses array cannot be empty' },
        { status: 400 }
      );
    }

    if (addresses.length > 50) {
      return NextResponse.json(
        { error: 'Maximum 50 addresses allowed per batch' },
        { status: 400 }
      );
    }

    const identityRegistry = getIdentityRegistryContract();
    const claimRegistry = getClaimRegistryContract();
    let txHash = '';
    let results = [];

    console.log(`🔄 Processing batch ${action} for ${addresses.length} addresses`);

    switch (action) {
      case 'batch_whitelist':
        {
          const tx = await identityRegistry.batchAddToWhitelist(addresses);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Batch whitelisted ${addresses.length} addresses`);

          // Update database for all clients
          if (clientIds && Array.isArray(clientIds)) {
            await prisma.client.updateMany({
              where: { id: { in: clientIds } },
              data: { isWhitelisted: true }
            });
          }

          results = addresses.map(addr => ({ address: addr, success: true }));
        }
        break;

      case 'batch_unwhitelist':
        {
          const tx = await identityRegistry.batchRemoveFromWhitelist(addresses);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Batch removed ${addresses.length} addresses from whitelist`);

          // Update database for all clients
          if (clientIds && Array.isArray(clientIds)) {
            await prisma.client.updateMany({
              where: { id: { in: clientIds } },
              data: { isWhitelisted: false }
            });
          }

          results = addresses.map(addr => ({ address: addr, success: true }));
        }
        break;

      case 'batch_approve_kyc':
        {
          const tx = await identityRegistry.batchApproveKyc(addresses);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Batch KYC approved ${addresses.length} addresses`);

          // Issue KYC claims for all addresses
          const claimData = ethers.AbiCoder.defaultAbiCoder().encode(
            ["string", "uint256"],
            ["KYC_APPROVED", Math.floor(Date.now() / 1000)]
          );

          const claimResults = [];
          for (const address of addresses) {
            try {
              const claimTx = await claimRegistry.issueClaim(
                address,
                CLAIM_TOPICS.KYC,
                "0x", // empty signature
                claimData,
                "", // empty URI
                0 // never expires
              );
              await claimTx.wait();
              claimResults.push({ address, claimIssued: true });
              console.log(`✅ KYC claim issued for: ${address}`);
            } catch (error) {
              console.warn(`Could not issue KYC claim for ${address}:`, error);
              claimResults.push({ address, claimIssued: false, error: error.message });
            }
          }

          // Update database for all clients
          if (clientIds && Array.isArray(clientIds)) {
            await prisma.client.updateMany({
              where: { id: { in: clientIds } },
              data: { kycStatus: 'APPROVED' }
            });
          }

          results = addresses.map(addr => ({ 
            address: addr, 
            success: true,
            claimIssued: claimResults.find(c => c.address === addr)?.claimIssued || false
          }));
        }
        break;

      case 'batch_revoke_kyc':
        {
          const tx = await identityRegistry.batchRevokeKyc(addresses);
          await tx.wait();
          txHash = tx.hash;
          console.log(`✅ Batch KYC revoked ${addresses.length} addresses`);

          // Update database for all clients
          if (clientIds && Array.isArray(clientIds)) {
            await prisma.client.updateMany({
              where: { id: { in: clientIds } },
              data: { kycStatus: 'REJECTED' }
            });
          }

          results = addresses.map(addr => ({ address: addr, success: true }));
        }
        break;

      case 'batch_issue_qualification_claims':
        {
          const claimData = ethers.AbiCoder.defaultAbiCoder().encode(
            ["string", "uint256"],
            ["QUALIFIED", Math.floor(Date.now() / 1000)]
          );

          const claimResults = [];
          for (const address of addresses) {
            try {
              const claimTx = await claimRegistry.issueClaim(
                address,
                CLAIM_TOPICS.QUALIFICATION,
                "0x", // empty signature
                claimData,
                "", // empty URI
                0 // never expires
              );
              await claimTx.wait();
              claimResults.push({ address, success: true, txHash: claimTx.hash });
              console.log(`✅ Qualification claim issued for: ${address}`);
            } catch (error) {
              console.warn(`Could not issue qualification claim for ${address}:`, error);
              claimResults.push({ address, success: false, error: error.message });
            }
          }

          results = claimResults;
          txHash = 'multiple'; // Multiple transactions
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid batch action' },
          { status: 400 }
        );
    }

    // Log the batch operation
    console.log(`🎉 Batch operation completed:`);
    console.log(`   Action: ${action}`);
    console.log(`   Addresses: ${addresses.length}`);
    console.log(`   Transaction: ${txHash}`);
    console.log(`   Success rate: ${results.filter(r => r.success).length}/${results.length}`);

    return NextResponse.json({
      success: true,
      action,
      addressCount: addresses.length,
      txHash,
      results,
      timestamp: new Date().toISOString(),
      summary: {
        total: addresses.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    });

  } catch (error) {
    console.error('Error processing batch identity operation:', error);
    return NextResponse.json(
      { error: `Failed to process batch ${body.action}: ${error.message}` },
      { status: 500 }
    );
  }
}

// GET /api/identity/batch/status - Get batch operation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const addresses = searchParams.get('addresses')?.split(',') || [];

    if (addresses.length === 0) {
      return NextResponse.json(
        { error: 'addresses parameter is required' },
        { status: 400 }
      );
    }

    const identityRegistry = getIdentityRegistryContract();

    // Check status for all addresses
    const statusChecks = addresses.map(async (address) => {
      try {
        const [isVerified, isWhitelisted, isKycApproved] = await Promise.all([
          identityRegistry.isVerified(address),
          identityRegistry.isWhitelisted(address),
          identityRegistry.isKycApproved(address)
        ]);

        return {
          address,
          isVerified,
          isWhitelisted,
          isKycApproved,
          success: true
        };
      } catch (error) {
        return {
          address,
          success: false,
          error: error.message
        };
      }
    });

    const results = await Promise.all(statusChecks);

    return NextResponse.json({
      addresses: addresses.length,
      results,
      summary: {
        verified: results.filter(r => r.success && r.isVerified).length,
        whitelisted: results.filter(r => r.success && r.isWhitelisted).length,
        kycApproved: results.filter(r => r.success && r.isKycApproved).length,
        errors: results.filter(r => !r.success).length
      }
    });

  } catch (error) {
    console.error('Error checking batch status:', error);
    return NextResponse.json(
      { error: 'Failed to check batch status' },
      { status: 500 }
    );
  }
}
