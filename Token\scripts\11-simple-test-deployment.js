const { ethers, upgrades } = require("hardhat");

async function main() {
    console.log("🧪 Simple ERC-3643 Test Deployment...");
    console.log("=====================================");

    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

    // Use existing ClaimRegistry if available
    let claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS || "******************************************";
    console.log("Using ClaimRegistry:", claimRegistryAddress);

    try {
        // Step 1: Deploy IdentityRegistry with lower gas settings
        console.log("\n1️⃣ Deploying IdentityRegistry...");
        const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
        
        const identityRegistry = await upgrades.deployProxy(
            IdentityRegistry,
            [deployer.address, claimRegistryAddress],
            {
                initializer: "initialize",
                kind: "uups",
                gasLimit: 8000000, // Lower gas limit
                gasPrice: ethers.parseUnits('50', 'gwei') // Fixed gas price
            }
        );
        
        await identityRegistry.waitForDeployment();
        const identityRegistryAddress = await identityRegistry.getAddress();
        console.log("✅ IdentityRegistry deployed to:", identityRegistryAddress);

        // Step 2: Deploy Compliance with lower gas settings
        console.log("\n2️⃣ Deploying Compliance...");
        const Compliance = await ethers.getContractFactory("Compliance");
        
        const compliance = await upgrades.deployProxy(
            Compliance,
            [deployer.address, identityRegistryAddress],
            {
                initializer: "initialize",
                kind: "uups",
                gasLimit: 8000000, // Lower gas limit
                gasPrice: ethers.parseUnits('50', 'gwei') // Fixed gas price
            }
        );
        
        await compliance.waitForDeployment();
        const complianceAddress = await compliance.getAddress();
        console.log("✅ Compliance deployed to:", complianceAddress);

        // Step 3: Test basic functionality
        console.log("\n3️⃣ Testing basic functionality...");
        
        // Register deployer identity
        const tx1 = await identityRegistry.registerIdentity(deployer.address, 840); // USA
        await tx1.wait();
        console.log("✅ Deployer identity registered");

        // Verify registration
        const isVerified = await identityRegistry.isVerified(deployer.address);
        console.log("✅ Verification status:", isVerified);

        // Add to whitelist
        const tx2 = await identityRegistry.addToWhitelist(deployer.address);
        await tx2.wait();
        console.log("✅ Deployer added to whitelist");

        // Approve KYC
        const tx3 = await identityRegistry.approveKyc(deployer.address);
        await tx3.wait();
        console.log("✅ Deployer KYC approved");

        // Test compliance
        const canTransfer = await compliance.canTransfer(deployer.address, deployer.address, 100);
        console.log("✅ Can transfer test:", canTransfer);

        // Step 4: Deploy a simple SecurityToken (non-upgradeable for testing)
        console.log("\n4️⃣ Deploying simple SecurityToken...");
        
        try {
            const SecurityToken = await ethers.getContractFactory("SecurityToken");
            
            const securityToken = await upgrades.deployProxy(
                SecurityToken,
                [
                    "Simple Test Token",
                    "SIMPLE",
                    0, // decimals
                    ethers.parseUnits("100000", 0), // smaller max supply
                    identityRegistryAddress,
                    complianceAddress,
                    deployer.address,
                    "1 USD",
                    "No tiers",
                    "Simple test token",
                    ""
                ],
                {
                    initializer: "initialize",
                    kind: "uups",
                    gasLimit: 10000000, // Higher for token
                    gasPrice: ethers.parseUnits('30', 'gwei') // Lower gas price
                }
            );
            
            await securityToken.waitForDeployment();
            const tokenAddress = await securityToken.getAddress();
            console.log("✅ SecurityToken deployed to:", tokenAddress);

            // Test minting
            await securityToken.mint(deployer.address, 1000);
            const balance = await securityToken.balanceOf(deployer.address);
            console.log("✅ Minted tokens, balance:", balance.toString());

        } catch (error) {
            console.log("⚠️ SecurityToken deployment failed (gas too high):", error.message);
            console.log("💡 The IdentityRegistry and Compliance contracts are working!");
        }

        // Summary
        console.log("\n🎉 Deployment Summary");
        console.log("=====================");
        console.log("ClaimRegistry:", claimRegistryAddress);
        console.log("IdentityRegistry:", identityRegistryAddress);
        console.log("Compliance:", complianceAddress);

        console.log("\n📝 Environment Variables:");
        console.log("=========================");
        console.log(`CLAIM_REGISTRY_ADDRESS=${claimRegistryAddress}`);
        console.log(`IDENTITY_REGISTRY_ADDRESS=${identityRegistryAddress}`);
        console.log(`COMPLIANCE_ADDRESS=${complianceAddress}`);

        return {
            claimRegistry: claimRegistryAddress,
            identityRegistry: identityRegistryAddress,
            compliance: complianceAddress
        };

    } catch (error) {
        console.error("❌ Deployment failed:", error.message);
        throw error;
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ Script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
