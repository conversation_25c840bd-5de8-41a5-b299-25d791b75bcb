// Setup your existing user with wallet and token approvals
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setupYourUser() {
  console.log('=== Setting Up Your User ===');
  
  const yourWallet = '******************************************';
  const yourEmail = '<EMAIL>';
  
  try {
    // Find your existing user
    let user = await prisma.client.findFirst({
      where: { email: yourEmail }
    });
    
    if (!user) {
      console.log(`❌ User with email ${yourEmail} not found`);
      console.log('Available users:');
      const allUsers = await prisma.client.findMany({
        select: { id: true, email: true, walletAddress: true }
      });
      allUsers.forEach(u => {
        console.log(`   - ${u.email}: ${u.walletAddress || 'No wallet'}`);
      });
      return;
    }
    
    console.log('✅ Found your user:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Current Wallet: ${user.walletAddress || 'None'}`);
    console.log(`   KYC Status: ${user.kycStatus}`);
    
    // Update user with your wallet address
    user = await prisma.client.update({
      where: { id: user.id },
      data: {
        walletAddress: yourWallet,
        walletSignature: '0xabcdef1234567890...',
        walletVerifiedAt: new Date(),
        isWhitelisted: true,
        whitelistedAt: new Date(),
        kycStatus: 'APPROVED',
        kycCompletedAt: new Date()
      }
    });
    
    console.log('\n✅ Updated your user:');
    console.log(`   Email: ${user.email}`);
    console.log(`   Wallet: ${user.walletAddress}`);
    console.log(`   KYC Status: ${user.kycStatus}`);
    console.log(`   Global Whitelisted: ${user.isWhitelisted}`);
    
    // Delete existing token approvals
    const deletedApprovals = await prisma.tokenClientApproval.deleteMany({
      where: { clientId: user.id }
    });
    console.log(`\n🗑️  Deleted ${deletedApprovals.count} existing token approvals`);
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { id: true, name: true, symbol: true, address: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens found in database');
      return user;
    }

    console.log(`\n📋 Found ${tokens.length} tokens, creating approvals...`);

    // Whitelist the main tokens you mentioned you're already whitelisted for
    const tokensToWhitelist = ['AUG019', 'AUG01Z', 'TZD', 'EURT', 'ETHF'];

    for (const token of tokens) {
      const shouldWhitelist = tokensToWhitelist.includes(token.symbol);

      await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: user.id,
          approvalStatus: shouldWhitelist ? 'APPROVED' : 'PENDING',
          kycApproved: true,
          whitelistApproved: shouldWhitelist,
          approvedBy: shouldWhitelist ? '<EMAIL>' : null,
          approvedAt: shouldWhitelist ? new Date() : null,
          notes: shouldWhitelist ? 'Whitelisted for your wallet' : 'Pending approval'
        }
      });

      const status = shouldWhitelist ? '✅ WHITELISTED' : '⏳ PENDING';
      console.log(`   ${token.symbol.padEnd(10)} | ${status}`);
    }

    const whitelistedCount = tokensToWhitelist.length;
    console.log(`\n✅ Token approvals created: ${whitelistedCount} whitelisted, ${tokens.length - whitelistedCount} pending`);
    
    return user;
    
  } catch (error) {
    console.error('Error setting up user:', error);
    throw error;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Admin Panel Whitelist API:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${symbol.padEnd(10)} | ${status}`);
      });
      
      const whitelistedCount = data.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nAdmin API Summary: ${whitelistedCount}/${data.tokens.length} tokens whitelisted`);
      
    } else {
      console.log('❌ Admin whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing admin whitelist API:', error);
  }
}

async function testClientTokensAPI(walletAddress) {
  console.log('\n=== Testing Client Tokens API ===');
  
  try {
    const fetch = require('node-fetch');
    
    const response = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(walletAddress)}`);
    
    if (response.ok) {
      const tokens = await response.json();
      console.log(`Client tokens API returned ${tokens.length} tokens`);
      
      console.log('\nClient API token whitelist status:');
      tokens.forEach(token => {
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${token.symbol.padEnd(10)} | ${status} | ${token.price} ${token.currency}`);
      });
      
      const whitelistedCount = tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nClient API Summary: ${whitelistedCount}/${tokens.length} tokens whitelisted`);
      
    } else {
      console.log('❌ Client tokens API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing client tokens API:', error);
  }
}

async function main() {
  const yourWallet = '******************************************';
  const yourEmail = '<EMAIL>';
  
  try {
    // Setup your user
    const user = await setupYourUser();
    
    if (!user) {
      console.log('❌ Could not find or update user');
      return;
    }
    
    // Test both APIs
    await testWhitelistAPI(yourWallet);
    await testClientTokensAPI(yourWallet);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\n🎯 READY TO TEST:');
    console.log('1. Open client app: http://localhost:3003/offers');
    console.log(`2. Login with email: ${yourEmail}`);
    console.log('3. Connect wallet: ******************************************');
    console.log('4. You should see:');
    console.log('   - Proper Navbar with wallet connection');
    console.log('   - Green WHITELISTED tags on approved tokens');
    console.log('   - Wallet connect button in header and bottom-right');
    
    console.log('\n📋 WHITELISTED TOKENS FOR YOUR WALLET:');
    console.log('   - AUG019 (Augment_019)');
    console.log('   - AUG01Z (Augment_01z)');
    console.log('   - TZD (Test Zero Decimals Token)');
    console.log('   - EURT (European Real Estate Token)');
    console.log('   - ETHF (Ethereum DeFi Fund)');
    
    console.log('\n🔧 FIXES IMPLEMENTED:');
    console.log('   ✅ Offers page now uses proper Navbar (same as other pages)');
    console.log('   ✅ Wallet connection integrated into offers page');
    console.log('   ✅ KYC modal available on offers page');
    console.log('   ✅ Consistent header across all pages');
    console.log('   ✅ Your existing user updated with wallet address');
    
  } catch (error) {
    console.error('Error in main:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
