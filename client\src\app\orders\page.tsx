'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { Navbar } from '@/components/Navbar';
import { OrderCard } from '@/components/OrderCard';
import { useApiClient } from '@/lib/api-client';


interface Order {
  id: string;
  status: 'PENDING_APPROVAL' | 'CONFIRMED' | 'MINTED' | 'CANCELLED';
  tokensOrdered: string;
  tokensConfirmed: string;
  amountToPay: string;
  confirmedPayment: string;
  tokenPrice: string;
  paymentReference: string;
  createdAt: string;
  updatedAt: string;
  token: {
    id: string;
    name: string;
    symbol: string;
    address: string;
    tokenPrice: string;
    currency: string;
  };
}

interface OrdersResponse {
  success: boolean;
  orders: Order[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

export default function OrdersPage() {
  const { user, isLoading: authLoading } = useUser();
  const apiClient = useApiClient();

  const [selectedStatus, setSelectedStatus] = useState<string>('ALL');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch client profile
  const { data: clientProfile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: () => apiClient.getClientProfile(),
    enabled: !!user,
  });

  // Fetch orders
  const { data: ordersData, isLoading: ordersLoading, error, refetch } = useQuery<OrdersResponse>({
    queryKey: ['client-orders', selectedStatus, currentPage],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      });

      if (selectedStatus !== 'ALL') {
        params.append('status', selectedStatus);
      }

      const response = await fetch(`/api/client-orders?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      return response.json();
    },
    enabled: !!user && !!clientProfile,
  });



  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Required</h1>
            <p className="text-gray-600 mb-6">Please sign in to view your orders.</p>
            <div className="space-y-4">
              <a
                href="/api/auth/login"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center"
              >
                Sign In with Auth0
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar
        user={user}
        clientProfile={clientProfile}
        onGetQualified={() => {}}
        onLogout={undefined}
        useMockAuth={false}
      />

      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
              <p className="text-gray-600 mt-1">Track your token purchase orders and their status</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => refetch()}
                disabled={ordersLoading}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                {ordersLoading ? 'Refreshing...' : 'Refresh'}
              </button>
              <Link
                href="/offers"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Browse Tokens
              </Link>
            </div>
          </div>

          {/* Summary Statistics */}
          {ordersData?.orders && ordersData.orders.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {(() => {
                  const statusCounts = ordersData.orders.reduce((acc, order) => {
                    acc[order.status] = (acc[order.status] || 0) + 1;
                    return acc;
                  }, {} as Record<string, number>);

                  return [
                    { status: 'PENDING_APPROVAL', label: 'Pending', color: 'text-yellow-600', bgColor: 'bg-yellow-50' },
                    { status: 'CONFIRMED', label: 'Confirmed', color: 'text-blue-600', bgColor: 'bg-blue-50' },
                    { status: 'MINTED', label: 'Minted', color: 'text-green-600', bgColor: 'bg-green-50' },
                    { status: 'CANCELLED', label: 'Cancelled', color: 'text-red-600', bgColor: 'bg-red-50' },
                  ].map(({ status, label, color, bgColor }) => (
                    <div key={status} className={`${bgColor} rounded-lg p-4 text-center`}>
                      <div className={`text-2xl font-bold ${color}`}>
                        {statusCounts[status] || 0}
                      </div>
                      <div className="text-sm text-gray-600">{label}</div>
                    </div>
                  ));
                })()}
              </div>
            </div>
          )}

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <label htmlFor="status-filter" className="text-sm font-medium text-gray-700">
                  Filter by Status:
                </label>
                <select
                  id="status-filter"
                  value={selectedStatus}
                  onChange={(e) => {
                    setSelectedStatus(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="ALL">All Orders</option>
                  <option value="PENDING_APPROVAL">Pending Approval</option>
                  <option value="CONFIRMED">Confirmed</option>
                  <option value="MINTED">Minted</option>
                  <option value="CANCELLED">Cancelled</option>
                </select>
              </div>
              <div className="text-sm text-gray-500">
                {ordersData?.pagination && (
                  <>Showing {ordersData.orders.length} of {ordersData.pagination.totalCount} orders</>
                )}
              </div>
            </div>
          </div>

          {/* Orders Content */}
          {ordersLoading ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading your orders...</p>
            </div>
          ) : error ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <p className="text-red-600">Error loading orders. Please try again.</p>
              <button
                onClick={() => refetch()}
                className="mt-4 text-blue-600 hover:text-blue-800 font-medium"
              >
                Retry
              </button>
            </div>
          ) : !ordersData?.orders || ordersData.orders.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Found</h3>
              <p className="text-gray-600 mb-6">
                {selectedStatus === 'ALL'
                  ? "You haven't placed any orders yet."
                  : `No orders found with status: ${selectedStatus}`
                }
              </p>
              <Link
                href="/offers"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Browse Available Tokens
              </Link>
            </div>
          ) : (
            <>
              {/* Orders List */}
              <div className="space-y-4">
                {ordersData.orders.map((order) => (
                  <OrderCard
                    key={order.id}
                    order={order}
                    onRefresh={() => refetch()}
                  />
                ))}
              </div>

              {/* Pagination */}
              {ordersData.pagination.totalPages > 1 && (
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Showing page {ordersData.pagination.page} of {ordersData.pagination.totalPages}
                      ({ordersData.pagination.totalCount} total orders)
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === ordersData.pagination.totalPages}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
