const { ethers } = require("hardhat");

async function checkToken(tokenAddress) {
  console.log(`🔍 Checking token: ${tokenAddress}`);
  console.log("=" + "=".repeat(50));

  try {
    const SecurityTokenABI = require("../admin-panel/src/contracts/SecurityToken.json");
    const provider = new ethers.JsonRpcProvider("https://rpc-amoy.polygon.technology");
    const contract = new ethers.Contract(tokenAddress, SecurityTokenABI.abi, provider);

    // Check basic info
    try {
      const name = await contract.name();
      const symbol = await contract.symbol();
      console.log(`📋 Token: ${name} (${symbol})`);
    } catch (error) {
      console.log(`❌ Invalid token address or not a token contract`);
      return false;
    }

    // Check advanced transfer controls
    let hasAdvancedControls = true;
    
    try {
      await contract.conditionalTransfersEnabled();
      console.log(`✅ Conditional Transfers: Supported`);
    } catch (error) {
      console.log(`❌ Conditional Transfers: Not supported`);
      hasAdvancedControls = false;
    }

    try {
      await contract.transferWhitelistEnabled();
      console.log(`✅ Transfer Whitelisting: Supported`);
    } catch (error) {
      console.log(`❌ Transfer Whitelisting: Not supported`);
      hasAdvancedControls = false;
    }

    try {
      await contract.transferFeesEnabled();
      console.log(`✅ Transfer Fees: Supported`);
    } catch (error) {
      console.log(`❌ Transfer Fees: Not supported`);
      hasAdvancedControls = false;
    }

    console.log("");
    if (hasAdvancedControls) {
      console.log(`🎉 This token SUPPORTS advanced transfer controls!`);
      console.log(`🌐 Use it here: http://localhost:7788/transfer-controls?token=${tokenAddress}`);
    } else {
      console.log(`⚠️  This token does NOT support advanced transfer controls.`);
      console.log(`💡 Create a new token through the admin panel to get these features.`);
    }

    return hasAdvancedControls;

  } catch (error) {
    console.log(`❌ Error checking token: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log("🔍 Quick Token Compatibility Check\n");

  // Check the tokens mentioned in the errors
  const tokensToCheck = [
    "0x523765c9D801669dbb61EEB5DEdEd6F1cd82a79F", // First error
    "0x2b0387c16fe4eab806aa1318ba6f30f8edbc63cc", // Second error
    "0x2383c365E12a6EaC592714819ebdB9173164387c", // Known working token
    "0x7544A3072FAA793e3f89048C31b794f171779544", // Advanced Control Token
  ];

  const supportedTokens = [];
  const unsupportedTokens = [];

  for (const tokenAddress of tokensToCheck) {
    const isSupported = await checkToken(tokenAddress);
    if (isSupported) {
      supportedTokens.push(tokenAddress);
    } else {
      unsupportedTokens.push(tokenAddress);
    }
    console.log("");
  }

  console.log("📊 SUMMARY");
  console.log("=" + "=".repeat(20));
  
  if (supportedTokens.length > 0) {
    console.log("✅ Tokens WITH advanced transfer controls:");
    supportedTokens.forEach(token => {
      console.log(`   ${token}`);
    });
  }

  if (unsupportedTokens.length > 0) {
    console.log("\n❌ Tokens WITHOUT advanced transfer controls:");
    unsupportedTokens.forEach(token => {
      console.log(`   ${token}`);
    });
  }

  console.log("\n🎯 RECOMMENDATION:");
  if (supportedTokens.length > 0) {
    console.log(`Use one of the supported tokens above, or create a new token through:`);
    console.log(`http://localhost:7788/create-token`);
  } else {
    console.log(`Create a new token through the admin panel to get advanced features:`);
    console.log(`http://localhost:7788/create-token`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Check failed:", error);
    process.exit(1);
  });
