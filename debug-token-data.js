const fetch = require('node-fetch');

async function debugTokenData() {
  console.log('🔍 Debugging Token Data Issues');
  console.log('===============================');

  const CLIENT_API_URL = 'http://localhost:7788';
  const ADMIN_API_URL = 'http://localhost:6677';
  const TEST_WALLET = '******************************************'; // Your wallet

  try {
    // 1. Check admin panel tokens
    console.log('1️⃣ Fetching tokens from admin panel...');
    const adminResponse = await fetch(`${ADMIN_API_URL}/api/tokens?source=database`);
    
    if (!adminResponse.ok) {
      console.log(`❌ Admin API failed: ${adminResponse.status}`);
      return;
    }
    
    const adminTokens = await adminResponse.json();
    console.log(`✅ Admin panel returned ${adminTokens.length} tokens`);
    
    if (adminTokens.length > 0) {
      const firstToken = adminTokens[0];
      console.log('\n📊 First token from admin panel:');
      console.log(`   Name: ${firstToken.name}`);
      console.log(`   Symbol: ${firstToken.symbol}`);
      console.log(`   Address: ${firstToken.address}`);
      console.log(`   Token Price: ${firstToken.tokenPrice}`);
      console.log(`   Currency: ${firstToken.currency}`);
      console.log(`   Max Supply: ${firstToken.maxSupply}`);
    }

    // 2. Check client API tokens
    console.log('\n2️⃣ Fetching tokens from client API...');
    const clientResponse = await fetch(`${CLIENT_API_URL}/api/tokens`);
    
    if (!clientResponse.ok) {
      console.log(`❌ Client API failed: ${clientResponse.status}`);
      return;
    }
    
    const clientTokens = await clientResponse.json();
    console.log(`✅ Client API returned ${clientTokens.length} tokens`);
    
    if (clientTokens.length > 0) {
      const firstToken = clientTokens[0];
      console.log('\n📊 First token from client API:');
      console.log(`   Name: ${firstToken.name}`);
      console.log(`   Symbol: ${firstToken.symbol}`);
      console.log(`   Address: ${firstToken.address}`);
      console.log(`   Price: ${firstToken.price} (type: ${typeof firstToken.price})`);
      console.log(`   Currency: ${firstToken.currency}`);
      console.log(`   Max Supply: ${firstToken.maxSupply}`);
      console.log(`   Is Whitelisted: ${firstToken.isWhitelisted}`);
      
      // Test price parsing
      const priceNum = Number(firstToken.price);
      console.log(`   Price as number: ${priceNum} (isNaN: ${isNaN(priceNum)})`);
    }

    // 3. Check client API with test wallet
    console.log('\n3️⃣ Fetching tokens with test wallet...');
    const clientWithWalletResponse = await fetch(`${CLIENT_API_URL}/api/tokens?testWallet=${TEST_WALLET}`);
    
    if (!clientWithWalletResponse.ok) {
      console.log(`❌ Client API with wallet failed: ${clientWithWalletResponse.status}`);
      return;
    }
    
    const clientWithWalletTokens = await clientWithWalletResponse.json();
    console.log(`✅ Client API with wallet returned ${clientWithWalletTokens.length} tokens`);
    
    if (clientWithWalletTokens.length > 0) {
      const firstToken = clientWithWalletTokens[0];
      console.log('\n📊 First token with wallet check:');
      console.log(`   Name: ${firstToken.name}`);
      console.log(`   Symbol: ${firstToken.symbol}`);
      console.log(`   Is Whitelisted: ${firstToken.isWhitelisted}`);
    }

    // 4. Check whitelist API directly
    console.log('\n4️⃣ Checking whitelist API directly...');
    const whitelistResponse = await fetch(`${ADMIN_API_URL}/api/whitelist/check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: TEST_WALLET,
        tokenAddresses: adminTokens.map(t => t.address)
      })
    });
    
    if (!whitelistResponse.ok) {
      console.log(`❌ Whitelist API failed: ${whitelistResponse.status}`);
      const errorText = await whitelistResponse.text();
      console.log(`Error: ${errorText}`);
      return;
    }
    
    const whitelistData = await whitelistResponse.json();
    console.log(`✅ Whitelist API returned data for ${whitelistData.tokens?.length || 0} tokens`);
    
    if (whitelistData.tokens && whitelistData.tokens.length > 0) {
      console.log('\n📊 Whitelist status for first few tokens:');
      whitelistData.tokens.slice(0, 3).forEach(token => {
        console.log(`   ${token.tokenSymbol}: ${token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED'}`);
      });
    }

    // 5. Test price formatting
    console.log('\n5️⃣ Testing price formatting...');
    
    function formatPrice(price, currency) {
      const numPrice = parseFloat(price);
      console.log(`   Input: price="${price}", currency="${currency}"`);
      console.log(`   Parsed: ${numPrice} (isNaN: ${isNaN(numPrice)})`);
    
      // Handle crypto currencies that don't have standard currency codes
      const cryptoCurrencies = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI'];
      if (cryptoCurrencies.includes(currency.toUpperCase())) {
        const result = `${numPrice} ${currency.toUpperCase()}`;
        console.log(`   Crypto format result: ${result}`);
        return result;
      }
    
      // Handle standard fiat currencies
      const supportedCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
      const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';
    
      try {
        const result = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currencyCode,
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(numPrice);
        console.log(`   Fiat format result: ${result}`);
        return result;
      } catch (error) {
        console.log(`   Formatting error: ${error.message}`);
        return `${numPrice} ${currency}`;
      }
    }

    if (clientTokens.length > 0) {
      const testToken = clientTokens[0];
      console.log(`\n   Testing with token: ${testToken.symbol}`);
      formatPrice(testToken.price, testToken.currency);
      
      // Test calculation
      const orderAmount = 5555;
      const tokenPrice = Number(testToken.price);
      const total = orderAmount * tokenPrice;
      console.log(`   Calculation test: ${orderAmount} * ${tokenPrice} = ${total}`);
      console.log(`   Total formatted: ${formatPrice(total.toString(), testToken.currency)}`);
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
  }
}

// Run the debug
debugTokenData().catch(console.error);
