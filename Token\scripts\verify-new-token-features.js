const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Verifying New Token Features from Factory...");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);

  // Factory address
  const factoryAddress = "******************************************";
  console.log("Factory address:", factoryAddress);

  // Get factory contract
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  // Deploy a test token through the factory
  console.log("\n🚀 Deploying test token through factory...");
  
  const tokenName = "Factory Test Token";
  const tokenSymbol = "FTT" + Date.now(); // Unique symbol
  const decimals = 0;
  const maxSupply = ethers.parseUnits("1000000", 0);
  const admin = deployer.address;
  const tokenPrice = "100 USD";
  const bonusTiers = "Tier 1: 10%";
  const tokenDetails = "Test token deployed through upgraded factory";
  const tokenImageUrl = "";

  try {
    const deployTx = await factory.deploySecurityToken(
      tokenName,
      tokenSymbol,
      decimals,
      maxSupply,
      admin,
      tokenPrice,
      bonusTiers,
      tokenDetails,
      tokenImageUrl
    );

    console.log("Deploy transaction hash:", deployTx.hash);
    const receipt = await deployTx.wait();
    console.log("✅ Transaction confirmed!");

    // Find the TokenDeployed event
    const tokenDeployedEvent = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (!tokenDeployedEvent) {
      console.log("❌ Could not find TokenDeployed event");
      return;
    }

    const parsed = factory.interface.parseLog(tokenDeployedEvent);
    const newTokenAddress = parsed.args.tokenAddress;
    const whitelistAddress = parsed.args.identityRegistryAddress;

    console.log("✅ New token deployed successfully!");
    console.log("Token address:", newTokenAddress);
    console.log("Whitelist address:", whitelistAddress);

    // Test the new token for advanced features
    console.log("\n🔍 Testing advanced transfer controls...");
    
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const token = SecurityToken.attach(newTokenAddress);

    // Test basic info
    const name = await token.name();
    const symbol = await token.symbol();
    const tokenDecimals = await token.decimals();
    const totalSupply = await token.totalSupply();

    console.log("📋 Token Info:");
    console.log("   Name:", name);
    console.log("   Symbol:", symbol);
    console.log("   Decimals:", tokenDecimals);
    console.log("   Total Supply:", ethers.formatUnits(totalSupply, tokenDecimals));

    // Test advanced transfer controls
    console.log("\n🔒 Advanced Transfer Controls:");
    
    try {
      const conditionalTransfersEnabled = await token.conditionalTransfersEnabled();
      console.log("   ✅ Conditional Transfers:", conditionalTransfersEnabled ? "Enabled" : "Disabled");
    } catch (error) {
      console.log("   ❌ Conditional Transfers: Not supported");
    }

    try {
      const transferWhitelistEnabled = await token.transferWhitelistEnabled();
      console.log("   ✅ Transfer Whitelisting:", transferWhitelistEnabled ? "Enabled" : "Disabled");
    } catch (error) {
      console.log("   ❌ Transfer Whitelisting: Not supported");
    }

    try {
      const transferFeesEnabled = await token.transferFeesEnabled();
      const [feePercentage, feeCollector] = await token.getTransferFeeConfig();
      console.log("   ✅ Transfer Fees:", transferFeesEnabled ? `Enabled (${Number(feePercentage) / 100}%)` : "Disabled");
      if (transferFeesEnabled) {
        console.log("   ✅ Fee Collector:", feeCollector);
      }
    } catch (error) {
      console.log("   ❌ Transfer Fees: Not supported");
    }

    // Test other features
    console.log("\n🛡️ Other Features:");
    
    try {
      const paused = await token.paused();
      console.log("   ✅ Pausable:", paused ? "Paused" : "Active");
    } catch (error) {
      console.log("   ❌ Pausable: Not supported");
    }

    try {
      const maxSupplyValue = await token.maxSupply();
      console.log("   ✅ Max Supply:", ethers.formatUnits(maxSupplyValue, tokenDecimals));
    } catch (error) {
      console.log("   ❌ Max Supply: Not supported");
    }

    try {
      const version = await token.version();
      console.log("   ✅ Version:", version);
    } catch (error) {
      console.log("   ❌ Version: Not supported");
    }

    // Summary
    console.log("\n📊 VERIFICATION SUMMARY");
    console.log("=======================");
    
    let hasAdvancedControls = false;
    try {
      await token.conditionalTransfersEnabled();
      await token.transferWhitelistEnabled();
      await token.transferFeesEnabled();
      hasAdvancedControls = true;
    } catch (error) {
      hasAdvancedControls = false;
    }

    if (hasAdvancedControls) {
      console.log("🎉 SUCCESS: Token has advanced transfer controls!");
      console.log("✅ Factory upgrade was successful");
      console.log("✅ All new tokens will have advanced features");
      console.log("");
      console.log("🌐 Test this token in admin panel:");
      console.log(`   Token Details: http://localhost:7788/tokens/${newTokenAddress}`);
      console.log(`   Transfer Controls: http://localhost:7788/transfer-controls?token=${newTokenAddress}`);
      console.log("");
      console.log("🚀 You can now create tokens through the admin panel with advanced features!");
    } else {
      console.log("❌ FAILED: Token does not have advanced transfer controls");
      console.log("⚠️ Factory upgrade may have failed");
    }

  } catch (error) {
    console.log("❌ Deployment failed:", error.message);
    
    if (error.message.includes("token with this symbol already exists")) {
      console.log("💡 Try again - the symbol was already used");
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Verification failed:", error);
    process.exit(1);
  });
