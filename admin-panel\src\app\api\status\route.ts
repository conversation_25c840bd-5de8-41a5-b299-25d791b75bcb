import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Test database connection
    let dbStatus = 'disconnected';
    let clientCount = 0;
    
    try {
      clientCount = await prisma.client.count();
      dbStatus = 'connected';
    } catch (error) {
      console.error('Database connection error:', error);
    }

    // Get environment info
    const envInfo = {
      nodeEnv: process.env.NODE_ENV,
      adminApiBaseUrl: process.env.ADMIN_API_BASE_URL,
      databaseUrl: process.env.DATABASE_URL ? 'configured' : 'not configured',
    };

    const status = {
      status: 'running',
      timestamp: new Date().toISOString(),
      database: {
        status: dbStatus,
        clientCount: clientCount,
      },
      environment: envInfo,
      cors: {
        allowedOrigin: 'http://localhost:7788',
        allowedMethods: 'GET, POST, PUT, DELETE, OPTIONS',
        allowedHeaders: 'Content-Type, Authorization',
      },
    };

    return NextResponse.json(status);
  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
