# Decimals Configuration Usage Guide

## Overview

The ERC-3643 token deployment system now supports configurable decimals (0-18) according to ERC-3643 standards. This allows you to create tokens with different decimal precision based on your use case.

## Common Use Cases

- **0 decimals**: Whole unit tokens (shares, certificates, NFT-like tokens)
- **6 decimals**: USDC-like stablecoins
- **8 decimals**: Bitcoin-like precision
- **18 decimals**: Ethereum standard (default)

## Usage Methods

### 1. Using Deployment Scripts

#### Environment Variables (Recommended)
```bash
# Windows PowerShell
$env:TOKEN_NAME="My Custom Token"
$env:TOKEN_SYMBOL="MCT"
$env:TOKEN_DECIMALS="6"
npx hardhat run scripts/02-deploy-token.js --network <network>

# Linux/Mac
TOKEN_NAME="My Custom Token" TOKEN_SYMBOL="MCT" TOKEN_DECIMALS="6" npx hardhat run scripts/02-deploy-token.js --network <network>
```

#### Direct Script Modification
Edit `scripts/02-deploy-token.js` and change:
```javascript
const tokenDecimals = 6; // Set your desired decimals here
```

### 2. Using Factory Contract Directly

```javascript
// Deploy with 6 decimals
await factory.deploySecurityToken(
  "USDC-like Token",
  "USDC",
  6,                    // 6 decimals
  1000000,              // max supply
  adminAddress,
  "1 USD",
  "No tiers",
  "Stablecoin with 6 decimals"
);

// Deploy with 0 decimals (whole units only)
await factory.deploySecurityToken(
  "Share Certificate",
  "CERT",
  0,                    // 0 decimals
  10000,                // max supply
  adminAddress,
  "100 USD",
  "Early: 10%, Late: 5%",
  "Company share certificates"
);
```

### 3. Using with KYC

```javascript
await factory.deploySecurityTokenWithOptions(
  "Premium Token",
  "PREM",
  8,                    // 8 decimals
  21000000,             // max supply
  adminAddress,
  "50 USD",
  "VIP: 15%, Standard: 5%",
  "Premium token with KYC",
  true                  // enable KYC
);
```

## Validation Rules

- **Range**: 0 ≤ decimals ≤ 18
- **Type**: Must be uint8
- **Required**: Parameter is mandatory (no default in contract)

## Examples by Use Case

### Stablecoin (6 decimals)
```javascript
// 1 token = 1.000000 (6 decimal places)
// Suitable for USD-pegged tokens
const decimals = 6;
const maxSupply = 1000000; // 1M tokens
```

### Share Certificates (0 decimals)
```javascript
// 1 token = 1 (no fractional shares)
// Suitable for company shares, certificates
const decimals = 0;
const maxSupply = 10000; // 10K shares
```

### Utility Token (18 decimals)
```javascript
// 1 token = 1.000000000000000000 (18 decimal places)
// Standard Ethereum precision
const decimals = 18;
const maxSupply = ethers.parseEther("1000000"); // 1M tokens
```

## Testing

Run the comprehensive test suite:
```bash
npx hardhat test test/SecurityTokenFactory.test.js
```

Run the simple decimals test:
```bash
npx hardhat run scripts/simple-test.js --network hardhat
```

## Important Notes

1. **Immutable**: Decimals cannot be changed after token deployment
2. **Display**: Frontend applications should use the `decimals()` function to properly display token amounts
3. **Calculations**: Always account for decimals when performing token amount calculations
4. **Compatibility**: All existing functionality remains unchanged - decimals is an additional parameter

## Migration from Previous Version

If you have existing deployment scripts, you need to add the decimals parameter:

**Before:**
```javascript
await factory.deploySecurityToken(name, symbol, maxSupply, admin, price, tiers, details);
```

**After:**
```javascript
await factory.deploySecurityToken(name, symbol, decimals, maxSupply, admin, price, tiers, details);
```

The default behavior (18 decimals) can be maintained by passing `18` as the decimals parameter.
