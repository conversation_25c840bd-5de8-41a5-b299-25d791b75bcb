// Direct transaction script for Amoy testnet issues
const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

// Function selectors
const SELECTORS = {
  pause: "0x8456cb59",
  unpause: "0x3f4ba83a",
  mint: "0x40c10f19" // mint(address,uint256)
};

async function main() {
  // Get the operation type
  const operation = process.env.OPERATION;
  if (!operation || !["pause", "unpause", "mint"].includes(operation.toLowerCase())) {
    throw new Error("OPERATION environment variable not set or invalid. Use 'pause', 'unpause', or 'mint'");
  }

  // Get token address
  const tokenAddress = process.env.TOKEN_ADDRESS;
  if (!tokenAddress) {
    throw new Error("TOKEN_ADDRESS environment variable not set");
  }

  // For mint operation, we need additional parameters
  let mintTo, mintAmount, mintData;
  if (operation.toLowerCase() === "mint") {
    mintTo = process.env.TO_ADDRESS;
    if (!mintTo || !ethers.isAddress(mintTo)) {
      throw new Error("TO_ADDRESS environment variable not set or invalid");
    }

    mintAmount = process.env.AMOUNT;
    if (!mintAmount) {
      throw new Error("AMOUNT environment variable not set");
    }
    
    // Encode mint function data: mint(address,uint256)
    // Pad the address to 32 bytes
    const paddedAddress = ethers.zeroPadValue(mintTo, 32);
    // Convert amount to wei and pad to 32 bytes
    const amountWei = ethers.parseEther(mintAmount);
    const paddedAmount = ethers.zeroPadValue(ethers.toBeHex(amountWei), 32);
    
    // Combine function selector with encoded parameters
    mintData = SELECTORS.mint + paddedAddress.substring(2) + paddedAmount.substring(2);
  }

  // Gas parameters - set explicitly high values
  const gasPrice = process.env.GAS_PRICE ? ethers.parseUnits(process.env.GAS_PRICE, "gwei") : ethers.parseUnits("100", "gwei");
  const gasLimit = process.env.GAS_LIMIT ? BigInt(process.env.GAS_LIMIT) : BigInt(1000000); // Default 1M gas

  // Log operation details
  console.log(`\n=== DIRECT TRANSACTION TO AMOY TESTNET ===`);
  console.log(`Operation: ${operation.toUpperCase()}`);
  console.log(`Token Address: ${tokenAddress}`);
  if (operation.toLowerCase() === "mint") {
    console.log(`Mint To: ${mintTo}`);
    console.log(`Mint Amount: ${mintAmount}`);
  }
  console.log(`Gas Price: ${ethers.formatUnits(gasPrice, "gwei")} gwei`);
  console.log(`Gas Limit: ${gasLimit.toString()}`);

  try {
    // Create wallet from private key
    const privateKey = process.env.PRIVATE_KEY;
    if (!privateKey) {
      throw new Error("PRIVATE_KEY environment variable not set");
    }

    // Use provider from hardhat config
    const provider = ethers.provider;
    const wallet = new ethers.Wallet(privateKey, provider);
    console.log(`Using wallet address: ${wallet.address}`);

    // Get current nonce
    const nonce = await wallet.getNonce();
    console.log(`Current nonce: ${nonce}`);

    // Determine which function data to use
    let data;
    switch(operation.toLowerCase()) {
      case "pause":
        data = SELECTORS.pause;
        break;
      case "unpause":
        data = SELECTORS.unpause;
        break;
      case "mint":
        data = mintData;
        break;
    }

    // Create transaction object with EXPLICITLY set gas parameters
    const tx = {
      to: tokenAddress,
      data: data,
      gasLimit: gasLimit,
      gasPrice: gasPrice,
      nonce: nonce,
      chainId: (await provider.getNetwork()).chainId
    };

    console.log("\nSending transaction...");
    
    // Sign and send the transaction
    const signedTx = await wallet.signTransaction(tx);
    const txResponse = await provider.broadcastTransaction(signedTx);
    
    console.log(`Transaction sent! Hash: ${txResponse.hash}`);
    console.log("Waiting for confirmation...");

    // Save transaction details to a file for reference
    const txDetails = {
      operation: operation,
      hash: txResponse.hash,
      to: tokenAddress,
      from: wallet.address,
      data: data,
      gasLimit: gasLimit.toString(),
      gasPrice: ethers.formatUnits(gasPrice, "gwei"),
      nonce: nonce,
      timestamp: new Date().toISOString()
    };

    const txLogPath = path.join(__dirname, '../transaction-logs.json');
    let txLogs = [];
    
    if (fs.existsSync(txLogPath)) {
      const fileContent = fs.readFileSync(txLogPath, 'utf8');
      try {
        txLogs = JSON.parse(fileContent);
      } catch (e) {
        console.warn("Couldn't parse existing transaction logs, creating new file");
      }
    }
    
    txLogs.push(txDetails);
    fs.writeFileSync(txLogPath, JSON.stringify(txLogs, null, 2));
    
    // Wait for transaction to be mined
    const receipt = await provider.waitForTransaction(txResponse.hash);
    
    if (receipt && receipt.status === 1) {
      console.log(`\n✅ SUCCESS! Transaction confirmed in block ${receipt.blockNumber}`);
      console.log(`${operation.toUpperCase()} operation completed successfully`);
    } else {
      console.log(`\n❌ FAILED! Transaction was mined but may have failed`);
    }
    
    return receipt;
  } catch (error) {
    console.error("\n❌ ERROR during transaction:");
    if (error.reason) console.error(`Reason: ${error.reason}`);
    console.error(error);
    
    console.log("\n=== TROUBLESHOOTING SUGGESTIONS ===");
    console.log("1. Try with even higher gas values:");
    console.log(`   $env:GAS_LIMIT="2000000"`);
    console.log(`   $env:GAS_PRICE="200"`);
    console.log("2. Try a different RPC endpoint:");
    console.log(`   $env:AMOY_RPC_URL="https://polygon-amoy.blockpi.network/v1/rpc/public"`);
    console.log("3. Check if your private key has enough funds for gas fees");
    console.log("4. If using mint, verify the recipient is whitelisted");
    console.log("5. The Amoy testnet may be experiencing issues, try again later");
    
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 