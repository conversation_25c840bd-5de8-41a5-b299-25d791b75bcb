// We require the Hardhat Runtime Environment explicitly here. This is optional
// but useful for running the script in a standalone fashion through `node <script>`.
//
// When running the script with `npx hardhat run <script>` you'll find the Hardhat
// Runtime Environment's members available in the global scope.
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    // Get signers and network information
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;
    
    console.log("Deploying token with the account:", deployer.address);
    console.log("Network:", networkName);
    
    // Load factory address from deployment file
    const deploymentsDir = path.join(__dirname, "../deployments");
    const deploymentFile = path.join(deploymentsDir, `${networkName}.json`);
    
    if (!fs.existsSync(deploymentFile)) {
      console.error(`Deployment file not found for network ${networkName}`);
      console.error("Please run the factory deployment script first");
      process.exit(1);
    }
    
    const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));
    const factoryAddress = deploymentInfo.factory;
    
    console.log("Using factory at:", factoryAddress);
    
    // Load the factory contract
    const factory = await hre.ethers.getContractAt("SecurityTokenFactory", factoryAddress);
    
    // Token parameters
    const tokenName = process.env.TOKEN_NAME || "Test Security Token";
    const tokenSymbol = process.env.TOKEN_SYMBOL || "TST";
    const maxSupply = process.env.MAX_SUPPLY || hre.ethers.parseEther("1000000");
    const adminAddress = process.env.ADMIN_ADDRESS || deployer.address;
    const tokenPrice = process.env.TOKEN_PRICE || "10 USD";
    const bonusTiers = process.env.BONUS_TIERS || "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%";
    const tokenDetails = process.env.TOKEN_DETAILS || "Test security token for development";
    const withKYC = process.env.WITH_KYC === "true" || false;
    
    console.log("Deploying token with parameters:");
    console.log("Name:", tokenName);
    console.log("Symbol:", tokenSymbol);
    console.log("Max Supply:", maxSupply.toString());
    console.log("Admin:", adminAddress);
    console.log("With KYC:", withKYC);
    
    // Deploy the token
    console.log("Deploying token...");
    
    // For local testing, we will deploy the token directly
    const SecurityToken = await hre.ethers.getContractFactory("SecurityToken");
    const Whitelist = await hre.ethers.getContractFactory(withKYC ? "WhitelistWithKYC" : "Whitelist");
    
    // Deploy Whitelist first
    console.log("Deploying whitelist...");
    const whitelist = await hre.upgrades.deployProxy(
      Whitelist,
      [adminAddress], // constructor args for initialize function
      { initializer: 'initializeWithAgent' }
    );
    await whitelist.waitForDeployment();
    
    const identityRegistryAddress = await whitelist.getAddress();
    console.log("Whitelist deployed to:", identityRegistryAddress);
    
    // Now deploy token
    console.log("Deploying token...");
    const token = await hre.upgrades.deployProxy(
      SecurityToken,
      [
        tokenName,
        tokenSymbol,
        maxSupply,
        identityRegistryAddress,
        adminAddress,
        tokenPrice,
        bonusTiers,
        tokenDetails
      ],
      { initializer: 'initialize' }
    );
    await token.waitForDeployment();
    
    const tokenAddress = await token.getAddress();
    console.log("Token deployed to:", tokenAddress);
    
    // Save token deployment information
    const tokenInfo = {
      network: networkName,
      name: tokenName,
      symbol: tokenSymbol,
      address: tokenAddress,
      identityRegistry: identityRegistryAddress,
      admin: adminAddress,
      maxSupply: maxSupply.toString(),
      withKYC: withKYC,
      timestamp: new Date().toISOString()
    };
    
    // Create tokens directory if it doesn't exist
    const tokensDir = path.join(__dirname, "../tokens");
    if (!fs.existsSync(tokensDir)) {
      fs.mkdirSync(tokensDir);
    }
    
    // Save to a file named by the token symbol
    const tokenFile = path.join(tokensDir, `${tokenSymbol}.json`);
    fs.writeFileSync(
      tokenFile,
      JSON.stringify(tokenInfo, null, 2)
    );
    console.log(`Token information saved to ${tokenFile}`);
    
    // Update an index file with all tokens
    const indexFile = path.join(tokensDir, "index.json");
    let tokenIndex = {};
    
    if (fs.existsSync(indexFile)) {
      tokenIndex = JSON.parse(fs.readFileSync(indexFile, "utf8"));
    }
    
    tokenIndex[tokenSymbol] = {
      name: tokenName,
      address: tokenAddress,
      identityRegistry: identityRegistryAddress,
      network: networkName,
      withKYC: withKYC
    };
    
    fs.writeFileSync(
      indexFile,
      JSON.stringify(tokenIndex, null, 2)
    );
    
    console.log("Token index updated");
    console.log("Deployment completed successfully");
    
  } catch (error) {
    console.error("Error during token deployment:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });