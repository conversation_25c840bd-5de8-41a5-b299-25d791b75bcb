// Check current database state
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabaseState() {
  console.log('=== Current Database State ===');
  
  try {
    // Check all users
    const users = await prisma.client.findMany({
      select: { 
        id: true, 
        email: true, 
        walletAddress: true,
        kycStatus: true,
        isWhitelisted: true
      }
    });
    
    console.log('All users:');
    users.forEach(user => {
      console.log(`   - ${user.email}: ${user.walletAddress || 'No wallet'} (KYC: ${user.kycStatus}, Whitelisted: ${user.isWhitelisted})`);
    });
    
    // Check your specific user
    const yourUser = await prisma.client.findFirst({
      where: { email: '<EMAIL>' },
      include: {
        tokenApprovals: {
          include: {
            token: {
              select: { symbol: true, address: true }
            }
          }
        }
      }
    });
    
    if (yourUser) {
      console.log('\n✅ Your user details:');
      console.log(`   Email: ${yourUser.email}`);
      console.log(`   Wallet: ${yourUser.walletAddress}`);
      console.log(`   KYC Status: ${yourUser.kycStatus}`);
      console.log(`   Global Whitelisted: ${yourUser.isWhitelisted}`);
      console.log(`   Token Approvals: ${yourUser.tokenApprovals.length}`);
      
      if (yourUser.tokenApprovals.length > 0) {
        console.log('\n   Token approval details:');
        yourUser.tokenApprovals.forEach(approval => {
          const status = approval.whitelistApproved ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
          console.log(`     ${approval.token.symbol}: ${status}`);
        });
      }
    } else {
      console.log('\n❌ Your user not found');
    }
    
    // Check token approvals for your desired wallet
    const desiredWallet = '******************************************';
    const userWithDesiredWallet = await prisma.client.findFirst({
      where: { 
        walletAddress: {
          equals: desiredWallet,
          mode: 'insensitive'
        }
      },
      include: {
        tokenApprovals: {
          include: {
            token: {
              select: { symbol: true }
            }
          }
        }
      }
    });
    
    if (userWithDesiredWallet) {
      console.log(`\n✅ User with desired wallet ${desiredWallet}:`);
      console.log(`   Email: ${userWithDesiredWallet.email}`);
      console.log(`   Token Approvals: ${userWithDesiredWallet.tokenApprovals.length}`);
      
      const whitelistedTokens = userWithDesiredWallet.tokenApprovals.filter(a => a.whitelistApproved);
      console.log(`   Whitelisted Tokens: ${whitelistedTokens.length}`);
      
      whitelistedTokens.forEach(approval => {
        console.log(`     - ${approval.token.symbol}`);
      });
    } else {
      console.log(`\n❌ No user found with wallet ${desiredWallet}`);
    }
    
  } catch (error) {
    console.error('Error checking database state:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseState().catch(console.error);
