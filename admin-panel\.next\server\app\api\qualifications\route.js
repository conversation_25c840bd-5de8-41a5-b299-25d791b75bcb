/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/qualifications/route";
exports.ids = ["app/api/qualifications/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2Froute&page=%2Fapi%2Fqualifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2Froute&page=%2Fapi%2Fqualifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_qualifications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/qualifications/route.ts */ \"(rsc)/./src/app/api/qualifications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/qualifications/route\",\n        pathname: \"/api/qualifications\",\n        filename: \"route\",\n        bundlePath: \"app/api/qualifications/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\qualifications\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_qualifications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2Froute&page=%2Fapi%2Fqualifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/qualifications/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/qualifications/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status');\n        const tokenId = searchParams.get('tokenId');\n        const clientId = searchParams.get('clientId');\n        // Build where clause\n        const whereClause = {};\n        if (status && status !== 'ALL') {\n            whereClause.qualificationStatus = status;\n        }\n        if (tokenId) {\n            whereClause.tokenId = tokenId;\n        }\n        if (clientId) {\n            whereClause.clientId = clientId;\n        }\n        // Fetch qualifications with related data\n        const qualifications = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.findMany({\n            where: whereClause,\n            include: {\n                client: {\n                    select: {\n                        id: true,\n                        firstName: true,\n                        lastName: true,\n                        email: true,\n                        kycStatus: true,\n                        walletAddress: true,\n                        phoneNumber: true,\n                        nationality: true\n                    }\n                },\n                token: {\n                    select: {\n                        id: true,\n                        name: true,\n                        symbol: true,\n                        address: true\n                    }\n                }\n            },\n            orderBy: [\n                {\n                    qualificationStatus: 'asc'\n                },\n                {\n                    updatedAt: 'desc'\n                }\n            ]\n        });\n        // Calculate summary statistics\n        const summary = {\n            total: qualifications.length,\n            pending: qualifications.filter((q)=>q.qualificationStatus === 'PENDING').length,\n            approved: qualifications.filter((q)=>q.qualificationStatus === 'APPROVED').length,\n            rejected: qualifications.filter((q)=>q.qualificationStatus === 'REJECTED').length,\n            forceApproved: qualifications.filter((q)=>q.qualificationStatus === 'FORCE_APPROVED').length\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            qualifications,\n            summary\n        });\n    } catch (error) {\n        console.error('Error fetching qualifications:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch qualifications'\n        }, {\n            status: 500\n        });\n    }\n}\n// Create a new qualification progress entry (for testing)\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { clientId, tokenId, countrySelected = false, countryValue, agreementAccepted = false, profileCompleted = false, walletConnected = false, kycCompleted = false, currentStep = 0, completedSteps = 0 } = body;\n        if (!clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if qualification already exists\n        const existing = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.findFirst({\n            where: {\n                clientId,\n                tokenId: tokenId || null\n            }\n        });\n        if (existing) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Qualification already exists for this client and token'\n            }, {\n                status: 400\n            });\n        }\n        // Create new qualification progress\n        const qualification = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.create({\n            data: {\n                clientId,\n                tokenId,\n                countrySelected,\n                countryValue,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                currentStep,\n                completedSteps,\n                qualificationStatus: 'PENDING'\n            },\n            include: {\n                client: {\n                    select: {\n                        id: true,\n                        firstName: true,\n                        lastName: true,\n                        email: true,\n                        kycStatus: true,\n                        walletAddress: true\n                    }\n                },\n                token: {\n                    select: {\n                        id: true,\n                        name: true,\n                        symbol: true,\n                        address: true\n                    }\n                }\n            }\n        });\n        console.log('Created qualification progress:', {\n            id: qualification.id,\n            clientEmail: qualification.client.email,\n            tokenName: qualification.token?.name || 'Global',\n            status: qualification.qualificationStatus\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Qualification progress created successfully',\n            qualification\n        });\n    } catch (error) {\n        console.error('Error creating qualification:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create qualification'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/qualifications/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualifications%2Froute&page=%2Fapi%2Fqualifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualifications%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();