import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

/**
 * Sumsub Webhook Handler
 * Receives verification status updates from Sumsub
 */

const SUMSUB_SECRET = process.env.SUMSUB_SECRET!;

export async function POST(request: NextRequest) {
  try {
    // Get the raw request body
    const rawBody = await request.text();

    // Get headers
    const signature = request.headers.get('x-payload-digest');
    const webhookSecret = SUMSUB_SECRET;

    if (!signature || !webhookSecret) {
      console.error('Missing webhook verification data:', {
        hasSignature: !!signature,
        hasSecret: !!webhookSecret
      });
      return NextResponse.json(
        { error: 'Unauthorized - missing verification data' },
        { status: 401 }
      );
    }

    // Verify webhook signature
    if (!verifyWebhookSignature(rawBody, signature, webhookSecret)) {
      console.error('Invalid webhook signature');
      return NextResponse.json(
        { error: 'Unauthorized - invalid signature' },
        { status: 401 }
      );
    }

    // Parse the webhook payload
    const webhookData = JSON.parse(rawBody);
    const { applicantId, inspectionId, correlationId, externalUserId, type, reviewStatus, reviewResult } = webhookData;

    console.log('Received Sumsub webhook:', {
      applicantId,
      inspectionId,
      correlationId,
      externalUserId,
      type,
      reviewStatus,
      reviewResult: reviewResult?.reviewAnswer
    });

    // Process the webhook based on type and status
    await processWebhookData(webhookData);

    return NextResponse.json({
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Verify webhook signature using HMAC SHA256
 */
function verifyWebhookSignature(
  requestBody: string,
  signature: string,
  secretKey: string
): boolean {
  try {
    // Calculate expected signature
    const expectedSignature = crypto
      .createHmac('sha256', secretKey)
      .update(requestBody)
      .digest('hex');

    // Compare signatures using constant-time comparison
    const expectedBuffer = Buffer.from(expectedSignature, 'hex');
    const providedBuffer = Buffer.from(signature.replace('sha256=', ''), 'hex');

    if (expectedBuffer.length !== providedBuffer.length) {
      return false;
    }

    return crypto.timingSafeEqual(expectedBuffer, providedBuffer);
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

/**
 * Process webhook data and update database
 */
async function processWebhookData(webhookData: any) {
  const { applicantId, externalUserId, type, reviewStatus, reviewResult, applicantType } = webhookData;

  try {
    console.log('Processing Sumsub webhook data:', {
      applicantId,
      externalUserId,
      type,
      reviewStatus,
      reviewAnswer: reviewResult?.reviewAnswer,
      applicantType
    });

    // Map Sumsub status to internal KYC status
    const kycStatus = mapSumsubStatusToKycStatus(reviewStatus, reviewResult?.reviewAnswer);

    // If verification is completed, get detailed applicant data for comparison
    if (reviewStatus === 'completed' && reviewResult?.reviewAnswer === 'GREEN') {
      await processApprovedVerification(applicantId, externalUserId);
    }

    // Update KYC status in admin panel database
    await updateKycStatus({
      userId: externalUserId,
      applicantId: applicantId,
      status: kycStatus,
      reviewStatus: reviewStatus,
      reviewResult: reviewResult,
      rawWebhookData: webhookData
    });

  } catch (error) {
    console.error('Error processing webhook data:', error);
    throw error;
  }
}

/**
 * Process approved verification and compare data
 */
async function processApprovedVerification(applicantId: string, externalUserId: string) {
  try {
    // Get detailed applicant data from Sumsub
    const applicantData = await getSumsubApplicantData(applicantId);

    if (applicantData) {
      // Extract comparison data from metadata
      const qualificationMetadata = applicantData.metadata?.find(
        (m: any) => m.key === 'qualification_data'
      );

      let expectedData = null;
      if (qualificationMetadata) {
        try {
          const metadataValue = JSON.parse(qualificationMetadata.value);
          expectedData = metadataValue.expected_data;
        } catch (error) {
          console.warn('Could not parse qualification metadata:', error);
        }
      }

      // Compare extracted data with expected data
      const comparisonResult = compareKycData(applicantData, expectedData);

      console.log('Sumsub KYC Data Comparison:', {
        applicantId,
        externalUserId,
        hasExpectedData: !!expectedData,
        comparisonResult
      });

      // TODO: Store comparison results
      /*
      await updateUserProfile({
        userId: externalUserId,
        firstName: applicantData.info?.firstName,
        lastName: applicantData.info?.lastName,
        dateOfBirth: applicantData.info?.dob,
        nationality: applicantData.info?.country,
        isVerified: true,
        kycComparisonResult: comparisonResult,
        sumsubApplicantId: applicantId
      });
      */
    }
  } catch (error) {
    console.error('Error processing approved verification:', error);
  }
}

/**
 * Get detailed applicant data from Sumsub
 */
async function getSumsubApplicantData(applicantId: string) {
  try {
    const SUMSUB_BASE_URL = process.env.SUMSUB_BASE_URL!;
    const SUMSUB_TOKEN = process.env.SUMSUB_TOKEN!;
    const SUMSUB_SECRET = process.env.SUMSUB_SECRET!;

    // Generate signature for API call
    const timestamp = Math.floor(Date.now() / 1000);
    const url = `/resources/applicants/${applicantId}/one`;
    const message = timestamp + 'GET' + url + '';
    const signature = require('crypto').createHmac('sha256', SUMSUB_SECRET).update(message).digest('hex');

    const headers = {
      'Accept': 'application/json',
      'X-App-Token': SUMSUB_TOKEN,
      'X-App-Access-Sig': signature,
      'X-App-Access-Ts': timestamp.toString(),
    };

    const response = await fetch(`${SUMSUB_BASE_URL}${url}`, {
      method: 'GET',
      headers: headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to get Sumsub applicant data:', errorText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting Sumsub applicant data:', error);
    return null;
  }
}

/**
 * Compare extracted KYC data with expected user data
 */
function compareKycData(applicantData: any, expectedData: any) {
  if (!expectedData) {
    return {
      hasComparison: false,
      message: 'No expected data provided for comparison'
    };
  }

  const comparisons = [];
  const mismatches = [];

  // Helper function to normalize strings for comparison
  const normalize = (str: string | null | undefined) => {
    if (!str) return '';
    return str.toLowerCase().trim().replace(/\s+/g, ' ');
  };

  // Helper function to normalize dates
  const normalizeDate = (date: string | null | undefined) => {
    if (!date) return '';
    try {
      return new Date(date).toISOString().split('T')[0]; // YYYY-MM-DD format
    } catch {
      return date?.toString() || '';
    }
  };

  const info = applicantData.info || {};

  // Compare first name
  if (expectedData.first_name && info.firstName) {
    const expected = normalize(expectedData.first_name);
    const actual = normalize(info.firstName);
    const matches = expected === actual;
    comparisons.push({
      field: 'first_name',
      expected: expectedData.first_name,
      actual: info.firstName,
      matches
    });
    if (!matches) mismatches.push('first_name');
  }

  // Compare last name
  if (expectedData.last_name && info.lastName) {
    const expected = normalize(expectedData.last_name);
    const actual = normalize(info.lastName);
    const matches = expected === actual;
    comparisons.push({
      field: 'last_name',
      expected: expectedData.last_name,
      actual: info.lastName,
      matches
    });
    if (!matches) mismatches.push('last_name');
  }

  // Compare date of birth
  if (expectedData.date_of_birth && info.dob) {
    const expected = normalizeDate(expectedData.date_of_birth);
    const actual = normalizeDate(info.dob);
    const matches = expected === actual;
    comparisons.push({
      field: 'date_of_birth',
      expected: expectedData.date_of_birth,
      actual: info.dob,
      matches
    });
    if (!matches) mismatches.push('date_of_birth');
  }

  // Compare nationality/country
  if (expectedData.nationality && info.country) {
    const expected = normalize(expectedData.nationality);
    const actual = normalize(info.country);
    const matches = expected === actual;
    comparisons.push({
      field: 'nationality',
      expected: expectedData.nationality,
      actual: info.country,
      matches
    });
    if (!matches) mismatches.push('nationality');
  }

  const totalComparisons = comparisons.length;
  const matchingFields = comparisons.filter(c => c.matches).length;
  const matchPercentage = totalComparisons > 0 ? (matchingFields / totalComparisons) * 100 : 0;

  return {
    hasComparison: true,
    totalComparisons,
    matchingFields,
    matchPercentage: Math.round(matchPercentage),
    mismatches,
    comparisons,
    isFullMatch: mismatches.length === 0 && totalComparisons > 0,
    summary: `${matchingFields}/${totalComparisons} fields match (${Math.round(matchPercentage)}%)`
  };
}

/**
 * Map Sumsub verification status to internal KYC status
 */
function mapSumsubStatusToKycStatus(reviewStatus: string, reviewAnswer?: string): string {
  switch (reviewStatus) {
    case 'init':
      return 'PENDING';
    case 'pending':
      return 'IN_REVIEW';
    case 'completed':
      switch (reviewAnswer) {
        case 'GREEN':
          return 'APPROVED';
        case 'RED':
          return 'REJECTED';
        case 'YELLOW':
          return 'IN_REVIEW';
        default:
          return 'IN_REVIEW';
      }
    default:
      return 'IN_REVIEW';
  }
}

/**
 * Update KYC status in admin panel database
 */
async function updateKycStatus({
  userId,
  applicantId,
  status,
  reviewStatus,
  reviewResult,
  rawWebhookData
}: {
  userId: string;
  applicantId: string;
  status: string;
  reviewStatus: string;
  reviewResult: any;
  rawWebhookData: any;
}) {
  try {
    console.log('Updating KYC status in admin panel:', {
      userId,
      applicantId,
      status,
      reviewStatus,
      reviewAnswer: reviewResult?.reviewAnswer
    });

    // Find client by Auth0 user ID (which should be their email)
    const adminApiUrl = process.env.ADMIN_API_BASE_URL!;

    // First, search for the client by email (userId is the Auth0 sub which should be email)
    const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userId)}&limit=1`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!searchResponse.ok) {
      console.error('Failed to search for client:', searchResponse.status);
      return;
    }

    const searchData = await searchResponse.json();
    const client = searchData.clients?.[0];

    if (!client) {
      console.error('Client not found for userId:', userId);
      return;
    }

    console.log('Found client for KYC update:', {
      clientId: client.id,
      email: client.email,
      currentKycStatus: client.kycStatus
    });

    // Prepare KYC notes with verification details
    let kycNotes = `Sumsub verification completed automatically.`;
    if (reviewResult?.reviewAnswer) {
      kycNotes += ` Result: ${reviewResult.reviewAnswer}`;
    }
    if (reviewStatus) {
      kycNotes += ` Status: ${reviewStatus}`;
    }
    if (applicantId) {
      kycNotes += ` Applicant ID: ${applicantId}`;
    }

    // Update the client's KYC status
    const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}/kyc`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        kycStatus: status,
        kycNotes: kycNotes,
      }),
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      console.error('Failed to update KYC status:', errorText);
      return;
    }

    const updatedClient = await updateResponse.json();
    console.log('Successfully updated client KYC status:', {
      clientId: updatedClient.id,
      newKycStatus: updatedClient.kycStatus,
      kycCompletedAt: updatedClient.kycCompletedAt
    });

  } catch (error) {
    console.error('Error updating KYC status in admin panel:', error);
    // Don't throw error to avoid webhook retry loops
  }
}

// Export for testing
export { verifyWebhookSignature, mapSumsubStatusToKycStatus, compareKycData, updateKycStatus };
