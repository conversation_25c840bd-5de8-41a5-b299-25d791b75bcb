/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/qualification/page";
exports.ids = ["app/qualification/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fqualification%2Fpage&page=%2Fqualification%2Fpage&appPaths=%2Fqualification%2Fpage&pagePath=private-next-app-dir%2Fqualification%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fqualification%2Fpage&page=%2Fqualification%2Fpage&appPaths=%2Fqualification%2Fpage&pagePath=private-next-app-dir%2Fqualification%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/qualification/page.tsx */ \"(rsc)/./src/app/qualification/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'qualification',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/qualification/page\",\n        pathname: \"/qualification\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fqualification%2Fpage&page=%2Fqualification%2Fpage&appPaths=%2Fqualification%2Fpage&pagePath=private-next-app-dir%2Fqualification%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/index.js */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AppLayout.tsx */ \"(rsc)/./src/components/AppLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(rsc)/./src/components/providers/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/MockAuthProvider.tsx */ \"(rsc)/./src/components/providers/MockAuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ReactQueryProvider.tsx */ \"(rsc)/./src/components/providers/ReactQueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WalletProvider.tsx */ \"(rsc)/./src/components/WalletProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cqualification%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cqualification%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/qualification/page.tsx */ \"(rsc)/./src/app/qualification/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNxdWFsaWZpY2F0aW9uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUEyRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZ2l0aHViXFxcXHRva2VuZGV2LW5ld3Jvb1xcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxxdWFsaWZpY2F0aW9uXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cqualification%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_providers_ReactQueryProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/ReactQueryProvider */ \"(rsc)/./src/components/providers/ReactQueryProvider.tsx\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(rsc)/./src/components/providers/MockAuthProvider.tsx\");\n/* harmony import */ var _components_WalletProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/WalletProvider */ \"(rsc)/./src/components/WalletProvider.tsx\");\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AppLayout */ \"(rsc)/./src/components/AppLayout.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"TokenDev Client Portal\",\n    description: \"Secure token management and KYC compliance platform\",\n    keywords: [\n        \"tokens\",\n        \"blockchain\",\n        \"KYC\",\n        \"compliance\",\n        \"investment\"\n    ]\n};\nfunction RootLayout({ children }) {\n    const useMockAuth = \"false\" === 'true';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className)} antialiased`,\n            children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.MockAuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ReactQueryProvider__WEBPACK_IMPORTED_MODULE_3__.ReactQueryProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletProvider__WEBPACK_IMPORTED_MODULE_6__.WalletProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_7__.AppLayout, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ReactQueryProvider__WEBPACK_IMPORTED_MODULE_3__.ReactQueryProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WalletProvider__WEBPACK_IMPORTED_MODULE_6__.WalletProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_7__.AppLayout, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/qualification/page.tsx":
/*!****************************************!*\
  !*** ./src/app/qualification/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\app\\qualification\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/AppLayout.tsx":
/*!**************************************!*\
  !*** ./src/components/AppLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppLayout: () => (/* binding */ AppLayout)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AppLayout = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\AppLayout.tsx",
"AppLayout",
);

/***/ }),

/***/ "(rsc)/./src/components/WalletProvider.tsx":
/*!*******************************************!*\
  !*** ./src/components/WalletProvider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WalletProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WalletProvider() from the server but WalletProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\WalletProvider.tsx",
"WalletProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\AuthProvider.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/MockAuthProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/MockAuthProvider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MockAuthProvider: () => (/* binding */ MockAuthProvider),
/* harmony export */   useMockUser: () => (/* binding */ useMockUser)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const MockAuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call MockAuthProvider() from the server but MockAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\MockAuthProvider.tsx",
"MockAuthProvider",
);const useMockUser = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useMockUser() from the server but useMockUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\MockAuthProvider.tsx",
"useMockUser",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/ReactQueryProvider.tsx":
/*!*********************************************************!*\
  !*** ./src/components/providers/ReactQueryProvider.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ReactQueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\ReactQueryProvider.tsx",
"ReactQueryProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@auth0/nextjs-auth0/dist/client/index.js */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AppLayout.tsx */ \"(ssr)/./src/components/AppLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/MockAuthProvider.tsx */ \"(ssr)/./src/components/providers/MockAuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ReactQueryProvider.tsx */ \"(ssr)/./src/components/providers/ReactQueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WalletProvider.tsx */ \"(ssr)/./src/components/WalletProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5C%40auth0%5C%5Cnextjs-auth0%5C%5Cdist%5C%5Cclient%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CAppLayout.tsx%22%2C%22ids%22%3A%5B%22AppLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CMockAuthProvider.tsx%22%2C%22ids%22%3A%5B%22MockAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CReactQueryProvider.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CWalletProvider.tsx%22%2C%22ids%22%3A%5B%22WalletProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cqualification%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cqualification%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/qualification/page.tsx */ \"(ssr)/./src/app/qualification/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNxdWFsaWZpY2F0aW9uJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUEyRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZ2l0aHViXFxcXHRva2VuZGV2LW5ld3Jvb1xcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxxdWFsaWZpY2F0aW9uXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cqualification%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/qualification/page.tsx":
/*!****************************************!*\
  !*** ./src/app/qualification/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_QualificationForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/QualificationForm */ \"(ssr)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(ssr)/./src/components/providers/MockAuthProvider.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction QualificationPage() {\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const mockAuth = useMockAuth ? (0,_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_3__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationPage.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"QualificationPage.useQuery\"],\n        enabled: !!user\n    });\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null // AppLayout handles authentication\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"My Profile\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"View and edit your qualification details and personal information\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QualificationForm__WEBPACK_IMPORTED_MODULE_2__.QualificationForm, {\n                        onComplete: ()=>{\n                            // Refresh the page to show updated data\n                            window.location.reload();\n                        },\n                        existingProfile: clientProfile\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\qualification\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QualificationPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/qualification/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AppLayout.tsx":
/*!**************************************!*\
  !*** ./src/components/AppLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./src/components/Navbar.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* harmony import */ var _providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/MockAuthProvider */ \"(ssr)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\n\n\nfunction AppLayout({ children }) {\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const mockAuth = useMockAuth ? (0,_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"AppLayout.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"AppLayout.useQuery\"],\n        enabled: !!user\n    });\n    // Loading state\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    // Not authenticated - show login screen\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"TokenDev Client Portal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Complete your KYC qualification to access investment opportunities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: mockAuth.login,\n                                    disabled: mockAuth.isLoading,\n                                    className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                    children: mockAuth.isLoading ? 'Signing In...' : 'Sign In (Demo)'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/api/auth/login\",\n                                    className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center\",\n                                    children: \"Sign In with Auth0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"New to TokenDev?\",\n                                        ' ',\n                                        useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: mockAuth.login,\n                                            className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                            children: \"Create an account (Demo)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/api/auth/login?screen_hint=signup\",\n                                            className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                            children: \"Create an account with Auth0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 pt-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"\\uD83D\\uDD12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: \"Secure\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: \"KYC Compliant\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: \"Real-time\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    // Authenticated - show app layout\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                user: user,\n                clientProfile: clientProfile,\n                onLogout: useMockAuth ? mockAuth.logout : undefined,\n                useMockAuth: useMockAuth\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-[calc(100vh-4rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                        user: user,\n                        onLogout: useMockAuth ? mockAuth.logout : undefined,\n                        useMockAuth: useMockAuth\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\AppLayout.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transition/transition.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChevronDownIcon,ClipboardDocumentCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Header,Navbar auto */ \n\n\n\n\nfunction Header({ user, clientProfile, onLogout, useMockAuth }) {\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"TokenDev\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu, {\n                            as: \"div\",\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Button, {\n                                        className: \"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open user menu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    user.picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-8 w-8 rounded-full\",\n                                                        src: user.picture,\n                                                        alt: user.name || 'User avatar'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden md:block text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: user.name || 'User'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 54,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 57,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Transition, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"transition ease-out duration-100\",\n                                    enterFrom: \"transform opacity-0 scale-95\",\n                                    enterTo: \"transform opacity-100 scale-100\",\n                                    leave: \"transition ease-in duration-75\",\n                                    leaveFrom: \"transform opacity-100 scale-100\",\n                                    leaveTo: \"transform opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Items, {\n                                        className: \"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-3 border-b border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: user.name || 'User'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    clientProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-2 h-2 rounded-full mr-2 ${clientProfile.kycStatus === 'APPROVED' ? 'bg-green-500' : clientProfile.kycStatus === 'IN_REVIEW' ? 'bg-yellow-500' : clientProfile.kycStatus === 'REJECTED' ? 'bg-red-500' : 'bg-gray-400'}`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"KYC: \",\n                                                                    clientProfile.kycStatus.replace('_', ' ')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Item, {\n                                                children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/qualification\",\n                                                        className: `${active ? 'bg-gray-100' : ''} flex items-center px-4 py-2 text-sm text-gray-700`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            clientProfile ? 'View Profile & Qualification' : 'Complete Qualification'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-100\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Menu.Item, {\n                                                children: ({ active })=>useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onLogout,\n                                                        className: `${active ? 'bg-gray-100' : ''} flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Sign out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `/api/auth/logout?returnTo=${\"http://localhost:7788\" || 0}`,\n                                                        className: `${active ? 'bg-gray-100' : ''} flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChevronDownIcon_ClipboardDocumentCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Sign out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n// Keep the old Navbar component for backward compatibility during transition\nfunction Navbar({ user, clientProfile, onGetQualified, onLogout, useMockAuth }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n        user: user,\n        clientProfile: clientProfile,\n        onLogout: onLogout,\n        useMockAuth: useMockAuth\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 160,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/QualificationForm.tsx":
/*!**********************************************!*\
  !*** ./src/components/QualificationForm.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationForm: () => (/* binding */ QualificationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ QualificationForm auto */ \n\n\n\nfunction QualificationForm({ onComplete, existingProfile }) {\n    // Helper function to format date for input\n    const formatDateForInput = (dateString)=>{\n        if (!dateString) return '';\n        try {\n            return new Date(dateString).toISOString().split('T')[0];\n        } catch  {\n            return '';\n        }\n    };\n    // Check if form can be edited\n    const canEdit = !existingProfile?.kycStatus || existingProfile.kycStatus === 'REJECTED' || existingProfile.kycStatus === 'EXPIRED';\n    // Initialize form data with empty values first\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        gender: '',\n        nationality: '',\n        birthday: '',\n        birthPlace: '',\n        identificationType: '',\n        passportNumber: '',\n        idCardNumber: '',\n        documentExpiration: '',\n        phoneNumber: '',\n        email: '',\n        occupation: '',\n        sectorOfActivity: '',\n        pepStatus: existingProfile?.pepStatus || 'NOT_PEP',\n        pepDetails: existingProfile?.pepDetails || '',\n        street: existingProfile?.street || '',\n        buildingNumber: existingProfile?.buildingNumber || '',\n        city: existingProfile?.city || '',\n        state: existingProfile?.state || '',\n        country: existingProfile?.country || '',\n        zipCode: existingProfile?.zipCode || '',\n        sourceOfWealth: existingProfile?.sourceOfWealth || '',\n        bankAccountNumber: existingProfile?.bankAccountNumber || '',\n        sourceOfFunds: existingProfile?.sourceOfFunds || '',\n        taxIdentificationNumber: existingProfile?.taxIdentificationNumber || ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.useApiClient)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // Update form data when existingProfile changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationForm.useEffect\": ()=>{\n            if (existingProfile) {\n                console.log('Updating form data with existing profile:', existingProfile);\n                setFormData({\n                    firstName: existingProfile.firstName || '',\n                    lastName: existingProfile.lastName || '',\n                    gender: existingProfile.gender || '',\n                    nationality: existingProfile.nationality || '',\n                    birthday: formatDateForInput(existingProfile.birthday),\n                    birthPlace: existingProfile.birthPlace || '',\n                    identificationType: existingProfile.identificationType || '',\n                    passportNumber: existingProfile.passportNumber || '',\n                    idCardNumber: existingProfile.idCardNumber || '',\n                    documentExpiration: formatDateForInput(existingProfile.documentExpiration),\n                    phoneNumber: existingProfile.phoneNumber || '',\n                    email: existingProfile.email || '',\n                    occupation: existingProfile.occupation || '',\n                    sectorOfActivity: existingProfile.sectorOfActivity || '',\n                    pepStatus: existingProfile.pepStatus || 'NOT_PEP',\n                    pepDetails: existingProfile.pepDetails || '',\n                    street: existingProfile.street || '',\n                    buildingNumber: existingProfile.buildingNumber || '',\n                    city: existingProfile.city || '',\n                    state: existingProfile.state || '',\n                    country: existingProfile.country || '',\n                    zipCode: existingProfile.zipCode || '',\n                    sourceOfWealth: existingProfile.sourceOfWealth || '',\n                    bankAccountNumber: existingProfile.bankAccountNumber || '',\n                    sourceOfFunds: existingProfile.sourceOfFunds || '',\n                    taxIdentificationNumber: existingProfile.taxIdentificationNumber || ''\n                });\n            }\n        }\n    }[\"QualificationForm.useEffect\"], [\n        existingProfile\n    ]);\n    const submitQualificationMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"QualificationForm.useMutation[submitQualificationMutation]\": async (data)=>{\n                if (existingProfile) {\n                    return apiClient.updateClientProfile(data);\n                } else {\n                    return apiClient.createClientProfile(data);\n                }\n            }\n        }[\"QualificationForm.useMutation[submitQualificationMutation]\"],\n        onSuccess: {\n            \"QualificationForm.useMutation[submitQualificationMutation]\": ()=>{\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'client-profile'\n                    ]\n                });\n                onComplete();\n            }\n        }[\"QualificationForm.useMutation[submitQualificationMutation]\"],\n        onError: {\n            \"QualificationForm.useMutation[submitQualificationMutation]\": (error)=>{\n                setErrors({\n                    general: error.message || 'Failed to submit qualification application'\n                });\n            }\n        }[\"QualificationForm.useMutation[submitQualificationMutation]\"]\n    });\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required field validations\n        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';\n        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';\n        if (!formData.gender) newErrors.gender = 'Gender is required';\n        if (!formData.nationality.trim()) newErrors.nationality = 'Nationality is required';\n        if (!formData.birthday) newErrors.birthday = 'Birthday is required';\n        if (!formData.birthPlace.trim()) newErrors.birthPlace = 'Birth place is required';\n        if (!formData.identificationType) newErrors.identificationType = 'Identification type is required';\n        if (!formData.documentExpiration) newErrors.documentExpiration = 'Document expiration is required';\n        if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';\n        if (!formData.occupation.trim()) newErrors.occupation = 'Occupation is required';\n        if (!formData.sectorOfActivity.trim()) newErrors.sectorOfActivity = 'Sector of activity is required';\n        if (!formData.street.trim()) newErrors.street = 'Street is required';\n        if (!formData.buildingNumber.trim()) newErrors.buildingNumber = 'Building number is required';\n        if (!formData.city.trim()) newErrors.city = 'City is required';\n        if (!formData.country.trim()) newErrors.country = 'Country is required';\n        if (!formData.zipCode.trim()) newErrors.zipCode = 'Zip code is required';\n        if (!formData.sourceOfWealth.trim()) newErrors.sourceOfWealth = 'Source of wealth is required';\n        if (!formData.bankAccountNumber.trim()) newErrors.bankAccountNumber = 'Bank account number is required';\n        if (!formData.sourceOfFunds.trim()) newErrors.sourceOfFunds = 'Source of funds is required';\n        if (!formData.taxIdentificationNumber.trim()) newErrors.taxIdentificationNumber = 'Tax identification number is required';\n        // Validate birthday (must be 18-120 years old)\n        if (formData.birthday) {\n            const birthDate = new Date(formData.birthday);\n            const today = new Date();\n            const age = today.getFullYear() - birthDate.getFullYear();\n            if (age < 18 || age > 120) {\n                newErrors.birthday = 'Must be between 18 and 120 years old';\n            }\n        }\n        // Validate document expiration (must be in the future)\n        if (formData.documentExpiration) {\n            const expirationDate = new Date(formData.documentExpiration);\n            const today = new Date();\n            if (expirationDate <= today) {\n                newErrors.documentExpiration = 'Document expiration must be in the future';\n            }\n        }\n        // Validate identification document number based on type\n        if (formData.identificationType === 'PASSPORT' && !formData.passportNumber.trim()) {\n            newErrors.passportNumber = 'Passport number is required';\n        }\n        if ((formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE') && !formData.idCardNumber.trim()) {\n            newErrors.idCardNumber = 'ID card number is required';\n        }\n        return newErrors;\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        const validationErrors = validateForm();\n        if (Object.keys(validationErrors).length > 0) {\n            setErrors(validationErrors);\n            return;\n        }\n        setErrors({});\n        submitQualificationMutation.mutate(formData);\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    // Get status display info\n    const getStatusInfo = ()=>{\n        if (!existingProfile?.kycStatus) return null;\n        const statusConfig = {\n            PENDING: {\n                color: 'yellow',\n                text: 'Pending Review',\n                description: 'Your qualification application is waiting to be reviewed.'\n            },\n            IN_REVIEW: {\n                color: 'blue',\n                text: 'Under Review',\n                description: 'Your qualification application is currently being reviewed by our team.'\n            },\n            APPROVED: {\n                color: 'green',\n                text: 'Approved',\n                description: 'Your qualification application has been approved.'\n            },\n            REJECTED: {\n                color: 'red',\n                text: 'Rejected',\n                description: 'Your qualification application was rejected. Please review the feedback and resubmit.'\n            },\n            EXPIRED: {\n                color: 'gray',\n                text: 'Expired',\n                description: 'Your qualification approval has expired. Please resubmit your application.'\n            }\n        };\n        return statusConfig[existingProfile.kycStatus];\n    };\n    const statusInfo = getStatusInfo();\n    // Helper function to get input styling\n    const getInputClassName = (hasError = false)=>{\n        const baseClasses = \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\";\n        if (!canEdit) {\n            return `${baseClasses} bg-gray-50 border-gray-200 cursor-not-allowed`;\n        }\n        return `${baseClasses} ${hasError ? 'border-red-500' : 'border-gray-300'}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            statusInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `p-4 rounded-lg border ${statusInfo.color === 'green' ? 'bg-green-50 border-green-200' : statusInfo.color === 'red' ? 'bg-red-50 border-red-200' : statusInfo.color === 'blue' ? 'bg-blue-50 border-blue-200' : statusInfo.color === 'yellow' ? 'bg-yellow-50 border-yellow-200' : 'bg-gray-50 border-gray-200'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `inline-flex px-3 py-1 text-sm font-semibold rounded-full ${statusInfo.color === 'green' ? 'text-green-600 bg-green-100' : statusInfo.color === 'red' ? 'text-red-600 bg-red-100' : statusInfo.color === 'blue' ? 'text-blue-600 bg-blue-100' : statusInfo.color === 'yellow' ? 'text-yellow-600 bg-yellow-100' : 'text-gray-600 bg-gray-100'}`,\n                                        children: statusInfo.text\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: statusInfo.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            existingProfile?.kycNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Admin Notes:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    existingProfile.kycNotes\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    !canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Note:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this),\n                            \" Your qualification application cannot be edited in its current status.\",\n                            existingProfile?.kycStatus === 'APPROVED' && ' Your application has been approved.',\n                            existingProfile?.kycStatus === 'PENDING' && ' Your application is pending review.',\n                            existingProfile?.kycStatus === 'IN_REVIEW' && ' Your application is currently under review.'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: existingProfile ? 'Qualification Information' : 'Complete Your Qualification'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: canEdit ? 'Please provide your information to qualify for security token investment.' : 'View your qualification application details.'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                        children: errors.general\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-4\",\n                                        children: \"Personal Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"First Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"firstName\",\n                                                        value: formData.firstName,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.firstName)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.firstName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Last Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"lastName\",\n                                                        value: formData.lastName,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.lastName)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.lastName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Gender *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"gender\",\n                                                        value: formData.gender,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        disabled: !canEdit,\n                                                        className: getInputClassName(!!errors.gender),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Gender\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MALE\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FEMALE\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PREFER_NOT_TO_SAY\",\n                                                                children: \"Prefer not to say\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.gender\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Nationality *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"nationality\",\n                                                        value: formData.nationality,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.nationality)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.nationality\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Birthday *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        name: \"birthday\",\n                                                        value: formData.birthday,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.birthday)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.birthday && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.birthday\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Birth Place *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"birthPlace\",\n                                                        value: formData.birthPlace,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.birthPlace)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.birthPlace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.birthPlace\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-4\",\n                                        children: \"Identification & Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Identification Type *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"identificationType\",\n                                                        value: formData.identificationType,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        disabled: !canEdit,\n                                                        className: getInputClassName(!!errors.identificationType),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PASSPORT\",\n                                                                children: \"Passport\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"ID_CARD\",\n                                                                children: \"ID Card\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DRIVERS_LICENSE\",\n                                                                children: \"Driver's License\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.identificationType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.identificationType\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.identificationType === 'PASSPORT' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Passport Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"passportNumber\",\n                                                        value: formData.passportNumber,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.passportNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.passportNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.passportNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            (formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE' || formData.identificationType === 'OTHER') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"ID Card Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"idCardNumber\",\n                                                        value: formData.idCardNumber,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.idCardNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.idCardNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.idCardNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Document Expiration *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        name: \"documentExpiration\",\n                                                        value: formData.documentExpiration,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.documentExpiration)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.documentExpiration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.documentExpiration\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Phone Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        name: \"phoneNumber\",\n                                                        value: formData.phoneNumber,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.phoneNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.phoneNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.phoneNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email,\n                                                        onChange: handleChange,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.email)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-4\",\n                                        children: \"Professional & Address Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Occupation *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"occupation\",\n                                                        value: formData.occupation,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.occupation)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.occupation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.occupation\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Sector of Activity *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"sectorOfActivity\",\n                                                        value: formData.sectorOfActivity,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.sectorOfActivity)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.sectorOfActivity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.sectorOfActivity\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Street Address *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"street\",\n                                                        value: formData.street,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.street)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.street && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.street\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Building Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"buildingNumber\",\n                                                        value: formData.buildingNumber,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.buildingNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.buildingNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.buildingNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"City *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"city\",\n                                                        value: formData.city,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.city)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.city\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"State/Province\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"state\",\n                                                        value: formData.state,\n                                                        onChange: handleChange,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.state)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.state && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.state\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Country *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"country\",\n                                                        value: formData.country,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.country)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.country && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.country\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Zip Code *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"zipCode\",\n                                                        value: formData.zipCode,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.zipCode)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.zipCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.zipCode\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-4\",\n                                        children: \"Financial Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Source of Wealth *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"sourceOfWealth\",\n                                                        value: formData.sourceOfWealth,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.sourceOfWealth)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.sourceOfWealth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.sourceOfWealth\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Source of Funds *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"sourceOfFunds\",\n                                                        value: formData.sourceOfFunds,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.sourceOfFunds)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.sourceOfFunds && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.sourceOfFunds\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Bank Account Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"bankAccountNumber\",\n                                                        value: formData.bankAccountNumber,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.bankAccountNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.bankAccountNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.bankAccountNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Tax Identification Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"taxIdentificationNumber\",\n                                                        value: formData.taxIdentificationNumber,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        readOnly: !canEdit,\n                                                        className: getInputClassName(!!errors.taxIdentificationNumber)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.taxIdentificationNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.taxIdentificationNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 mb-4\",\n                                        children: \"Political Exposure\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Politically Exposed Person (PEP) Status *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"pepStatus\",\n                                                        value: formData.pepStatus,\n                                                        onChange: handleChange,\n                                                        required: canEdit,\n                                                        disabled: !canEdit,\n                                                        className: getInputClassName(!!errors.pepStatus),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NOT_PEP\",\n                                                                children: \"Not a PEP\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PEP\",\n                                                                children: \"PEP\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FAMILY_MEMBER\",\n                                                                children: \"Family member of PEP\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CLOSE_ASSOCIATE\",\n                                                                children: \"Close associate of PEP\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.pepStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.pepStatus\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.pepStatus !== 'NOT_PEP' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"PEP Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"pepDetails\",\n                                                        value: formData.pepDetails,\n                                                        onChange: handleChange,\n                                                        readOnly: !canEdit,\n                                                        rows: 3,\n                                                        className: getInputClassName(!!errors.pepDetails),\n                                                        placeholder: \"Please provide details about your political exposure...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.pepDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: errors.pepDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 11\n                            }, this),\n                            canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: submitQualificationMutation.isPending,\n                                    className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: submitQualificationMutation.isPending ? 'Submitting...' : 'Complete Qualification'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\QualificationForm.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9RdWFsaWZpY2F0aW9uRm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ3dCO0FBQ0E7QUFPN0QsU0FBU0ssa0JBQWtCLEVBQUVDLFVBQVUsRUFBRUMsZUFBZSxFQUEwQjtJQUN2RiwyQ0FBMkM7SUFDM0MsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCLElBQUksQ0FBQ0EsWUFBWSxPQUFPO1FBQ3hCLElBQUk7WUFDRixPQUFPLElBQUlDLEtBQUtELFlBQVlFLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ3pELEVBQUUsT0FBTTtZQUNOLE9BQU87UUFDVDtJQUNGO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1DLFVBQVUsQ0FBQ04saUJBQWlCTyxhQUNsQlAsZ0JBQWdCTyxTQUFTLEtBQUssY0FDOUJQLGdCQUFnQk8sU0FBUyxLQUFLO0lBRTlDLCtDQUErQztJQUMvQyxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2hCLCtDQUFRQSxDQUFDO1FBQ3ZDaUIsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsb0JBQW9CO1FBQ3BCQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZEMsb0JBQW9CO1FBQ3BCQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsWUFBWTtRQUNaQyxrQkFBa0I7UUFDbEJDLFdBQVd4QixpQkFBaUJ3QixhQUFhO1FBQ3pDQyxZQUFZekIsaUJBQWlCeUIsY0FBYztRQUMzQ0MsUUFBUTFCLGlCQUFpQjBCLFVBQVU7UUFDbkNDLGdCQUFnQjNCLGlCQUFpQjJCLGtCQUFrQjtRQUNuREMsTUFBTTVCLGlCQUFpQjRCLFFBQVE7UUFDL0JDLE9BQU83QixpQkFBaUI2QixTQUFTO1FBQ2pDQyxTQUFTOUIsaUJBQWlCOEIsV0FBVztRQUNyQ0MsU0FBUy9CLGlCQUFpQitCLFdBQVc7UUFDckNDLGdCQUFnQmhDLGlCQUFpQmdDLGtCQUFrQjtRQUNuREMsbUJBQW1CakMsaUJBQWlCaUMscUJBQXFCO1FBQ3pEQyxlQUFlbEMsaUJBQWlCa0MsaUJBQWlCO1FBQ2pEQyx5QkFBeUJuQyxpQkFBaUJtQywyQkFBMkI7SUFDdkU7SUFFQSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBRzVDLCtDQUFRQSxDQUF5QixDQUFDO0lBQzlELE1BQU02QyxZQUFZekMsNkRBQVlBO0lBQzlCLE1BQU0wQyxjQUFjM0MscUVBQWNBO0lBRWxDLGdEQUFnRDtJQUNoREYsZ0RBQVNBO3VDQUFDO1lBQ1IsSUFBSU0saUJBQWlCO2dCQUNuQndDLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkN6QztnQkFDekRTLFlBQVk7b0JBQ1ZDLFdBQVdWLGdCQUFnQlUsU0FBUyxJQUFJO29CQUN4Q0MsVUFBVVgsZ0JBQWdCVyxRQUFRLElBQUk7b0JBQ3RDQyxRQUFRWixnQkFBZ0JZLE1BQU0sSUFBSTtvQkFDbENDLGFBQWFiLGdCQUFnQmEsV0FBVyxJQUFJO29CQUM1Q0MsVUFBVWIsbUJBQW1CRCxnQkFBZ0JjLFFBQVE7b0JBQ3JEQyxZQUFZZixnQkFBZ0JlLFVBQVUsSUFBSTtvQkFDMUNDLG9CQUFvQmhCLGdCQUFnQmdCLGtCQUFrQixJQUFJO29CQUMxREMsZ0JBQWdCakIsZ0JBQWdCaUIsY0FBYyxJQUFJO29CQUNsREMsY0FBY2xCLGdCQUFnQmtCLFlBQVksSUFBSTtvQkFDOUNDLG9CQUFvQmxCLG1CQUFtQkQsZ0JBQWdCbUIsa0JBQWtCO29CQUN6RUMsYUFBYXBCLGdCQUFnQm9CLFdBQVcsSUFBSTtvQkFDNUNDLE9BQU9yQixnQkFBZ0JxQixLQUFLLElBQUk7b0JBQ2hDQyxZQUFZdEIsZ0JBQWdCc0IsVUFBVSxJQUFJO29CQUMxQ0Msa0JBQWtCdkIsZ0JBQWdCdUIsZ0JBQWdCLElBQUk7b0JBQ3REQyxXQUFXeEIsZ0JBQWdCd0IsU0FBUyxJQUFJO29CQUN4Q0MsWUFBWXpCLGdCQUFnQnlCLFVBQVUsSUFBSTtvQkFDMUNDLFFBQVExQixnQkFBZ0IwQixNQUFNLElBQUk7b0JBQ2xDQyxnQkFBZ0IzQixnQkFBZ0IyQixjQUFjLElBQUk7b0JBQ2xEQyxNQUFNNUIsZ0JBQWdCNEIsSUFBSSxJQUFJO29CQUM5QkMsT0FBTzdCLGdCQUFnQjZCLEtBQUssSUFBSTtvQkFDaENDLFNBQVM5QixnQkFBZ0I4QixPQUFPLElBQUk7b0JBQ3BDQyxTQUFTL0IsZ0JBQWdCK0IsT0FBTyxJQUFJO29CQUNwQ0MsZ0JBQWdCaEMsZ0JBQWdCZ0MsY0FBYyxJQUFJO29CQUNsREMsbUJBQW1CakMsZ0JBQWdCaUMsaUJBQWlCLElBQUk7b0JBQ3hEQyxlQUFlbEMsZ0JBQWdCa0MsYUFBYSxJQUFJO29CQUNoREMseUJBQXlCbkMsZ0JBQWdCbUMsdUJBQXVCLElBQUk7Z0JBQ3RFO1lBQ0Y7UUFDRjtzQ0FBRztRQUFDbkM7S0FBZ0I7SUFFcEIsTUFBTTBDLDhCQUE4Qi9DLGtFQUFXQSxDQUFDO1FBQzlDZ0QsVUFBVTswRUFBRSxPQUFPQztnQkFDakIsSUFBSTVDLGlCQUFpQjtvQkFDbkIsT0FBT3NDLFVBQVVPLG1CQUFtQixDQUFDRDtnQkFDdkMsT0FBTztvQkFDTCxPQUFPTixVQUFVUSxtQkFBbUIsQ0FBQ0Y7Z0JBQ3ZDO1lBQ0Y7O1FBQ0FHLFNBQVM7MEVBQUU7Z0JBQ1RSLFlBQVlTLGlCQUFpQixDQUFDO29CQUFFQyxVQUFVO3dCQUFDO3FCQUFpQjtnQkFBQztnQkFDN0RsRDtZQUNGOztRQUNBbUQsT0FBTzswRUFBRSxDQUFDQztnQkFDUmQsVUFBVTtvQkFBRWUsU0FBU0QsTUFBTUUsT0FBTyxJQUFJO2dCQUE2QztZQUNyRjs7SUFDRjtJQUVBLE1BQU1DLGVBQWU7UUFDbkIsTUFBTUMsWUFBb0MsQ0FBQztRQUUzQyw2QkFBNkI7UUFDN0IsSUFBSSxDQUFDL0MsU0FBU0UsU0FBUyxDQUFDOEMsSUFBSSxJQUFJRCxVQUFVN0MsU0FBUyxHQUFHO1FBQ3RELElBQUksQ0FBQ0YsU0FBU0csUUFBUSxDQUFDNkMsSUFBSSxJQUFJRCxVQUFVNUMsUUFBUSxHQUFHO1FBQ3BELElBQUksQ0FBQ0gsU0FBU0ksTUFBTSxFQUFFMkMsVUFBVTNDLE1BQU0sR0FBRztRQUN6QyxJQUFJLENBQUNKLFNBQVNLLFdBQVcsQ0FBQzJDLElBQUksSUFBSUQsVUFBVTFDLFdBQVcsR0FBRztRQUMxRCxJQUFJLENBQUNMLFNBQVNNLFFBQVEsRUFBRXlDLFVBQVV6QyxRQUFRLEdBQUc7UUFDN0MsSUFBSSxDQUFDTixTQUFTTyxVQUFVLENBQUN5QyxJQUFJLElBQUlELFVBQVV4QyxVQUFVLEdBQUc7UUFDeEQsSUFBSSxDQUFDUCxTQUFTUSxrQkFBa0IsRUFBRXVDLFVBQVV2QyxrQkFBa0IsR0FBRztRQUNqRSxJQUFJLENBQUNSLFNBQVNXLGtCQUFrQixFQUFFb0MsVUFBVXBDLGtCQUFrQixHQUFHO1FBQ2pFLElBQUksQ0FBQ1gsU0FBU1ksV0FBVyxDQUFDb0MsSUFBSSxJQUFJRCxVQUFVbkMsV0FBVyxHQUFHO1FBQzFELElBQUksQ0FBQ1osU0FBU2MsVUFBVSxDQUFDa0MsSUFBSSxJQUFJRCxVQUFVakMsVUFBVSxHQUFHO1FBQ3hELElBQUksQ0FBQ2QsU0FBU2UsZ0JBQWdCLENBQUNpQyxJQUFJLElBQUlELFVBQVVoQyxnQkFBZ0IsR0FBRztRQUNwRSxJQUFJLENBQUNmLFNBQVNrQixNQUFNLENBQUM4QixJQUFJLElBQUlELFVBQVU3QixNQUFNLEdBQUc7UUFDaEQsSUFBSSxDQUFDbEIsU0FBU21CLGNBQWMsQ0FBQzZCLElBQUksSUFBSUQsVUFBVTVCLGNBQWMsR0FBRztRQUNoRSxJQUFJLENBQUNuQixTQUFTb0IsSUFBSSxDQUFDNEIsSUFBSSxJQUFJRCxVQUFVM0IsSUFBSSxHQUFHO1FBQzVDLElBQUksQ0FBQ3BCLFNBQVNzQixPQUFPLENBQUMwQixJQUFJLElBQUlELFVBQVV6QixPQUFPLEdBQUc7UUFDbEQsSUFBSSxDQUFDdEIsU0FBU3VCLE9BQU8sQ0FBQ3lCLElBQUksSUFBSUQsVUFBVXhCLE9BQU8sR0FBRztRQUNsRCxJQUFJLENBQUN2QixTQUFTd0IsY0FBYyxDQUFDd0IsSUFBSSxJQUFJRCxVQUFVdkIsY0FBYyxHQUFHO1FBQ2hFLElBQUksQ0FBQ3hCLFNBQVN5QixpQkFBaUIsQ0FBQ3VCLElBQUksSUFBSUQsVUFBVXRCLGlCQUFpQixHQUFHO1FBQ3RFLElBQUksQ0FBQ3pCLFNBQVMwQixhQUFhLENBQUNzQixJQUFJLElBQUlELFVBQVVyQixhQUFhLEdBQUc7UUFDOUQsSUFBSSxDQUFDMUIsU0FBUzJCLHVCQUF1QixDQUFDcUIsSUFBSSxJQUFJRCxVQUFVcEIsdUJBQXVCLEdBQUc7UUFFbEYsK0NBQStDO1FBQy9DLElBQUkzQixTQUFTTSxRQUFRLEVBQUU7WUFDckIsTUFBTTJDLFlBQVksSUFBSXRELEtBQUtLLFNBQVNNLFFBQVE7WUFDNUMsTUFBTTRDLFFBQVEsSUFBSXZEO1lBQ2xCLE1BQU13RCxNQUFNRCxNQUFNRSxXQUFXLEtBQUtILFVBQVVHLFdBQVc7WUFDdkQsSUFBSUQsTUFBTSxNQUFNQSxNQUFNLEtBQUs7Z0JBQ3pCSixVQUFVekMsUUFBUSxHQUFHO1lBQ3ZCO1FBQ0Y7UUFFQSx1REFBdUQ7UUFDdkQsSUFBSU4sU0FBU1csa0JBQWtCLEVBQUU7WUFDL0IsTUFBTTBDLGlCQUFpQixJQUFJMUQsS0FBS0ssU0FBU1csa0JBQWtCO1lBQzNELE1BQU11QyxRQUFRLElBQUl2RDtZQUNsQixJQUFJMEQsa0JBQWtCSCxPQUFPO2dCQUMzQkgsVUFBVXBDLGtCQUFrQixHQUFHO1lBQ2pDO1FBQ0Y7UUFFQSx3REFBd0Q7UUFDeEQsSUFBSVgsU0FBU1Esa0JBQWtCLEtBQUssY0FBYyxDQUFDUixTQUFTUyxjQUFjLENBQUN1QyxJQUFJLElBQUk7WUFDakZELFVBQVV0QyxjQUFjLEdBQUc7UUFDN0I7UUFDQSxJQUFJLENBQUNULFNBQVNRLGtCQUFrQixLQUFLLGFBQWFSLFNBQVNRLGtCQUFrQixLQUFLLGlCQUFnQixLQUFNLENBQUNSLFNBQVNVLFlBQVksQ0FBQ3NDLElBQUksSUFBSTtZQUNySUQsVUFBVXJDLFlBQVksR0FBRztRQUMzQjtRQUVBLE9BQU9xQztJQUNUO0lBRUEsTUFBTU8sZUFBZSxDQUFDQztRQUNwQkEsRUFBRUMsY0FBYztRQUVoQixNQUFNQyxtQkFBbUJYO1FBQ3pCLElBQUlZLE9BQU9DLElBQUksQ0FBQ0Ysa0JBQWtCRyxNQUFNLEdBQUcsR0FBRztZQUM1Qy9CLFVBQVU0QjtZQUNWO1FBQ0Y7UUFFQTVCLFVBQVUsQ0FBQztRQUNYSyw0QkFBNEIyQixNQUFNLENBQUM3RDtJQUNyQztJQUVBLE1BQU04RCxlQUFlLENBQUNQO1FBQ3BCLE1BQU0sRUFBRVEsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR1QsRUFBRVUsTUFBTTtRQUNoQ2hFLFlBQVlpRSxDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ0gsS0FBSyxFQUFFQztZQUFNO1FBQzlDLElBQUlwQyxNQUFNLENBQUNtQyxLQUFLLEVBQUU7WUFDaEJsQyxVQUFVcUMsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNILEtBQUssRUFBRTtnQkFBRztRQUMzQztJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1JLGdCQUFnQjtRQUNwQixJQUFJLENBQUMzRSxpQkFBaUJPLFdBQVcsT0FBTztRQUV4QyxNQUFNcUUsZUFBZTtZQUNuQkMsU0FBUztnQkFBRUMsT0FBTztnQkFBVUMsTUFBTTtnQkFBa0JDLGFBQWE7WUFBNEQ7WUFDN0hDLFdBQVc7Z0JBQUVILE9BQU87Z0JBQVFDLE1BQU07Z0JBQWdCQyxhQUFhO1lBQTBFO1lBQ3pJRSxVQUFVO2dCQUFFSixPQUFPO2dCQUFTQyxNQUFNO2dCQUFZQyxhQUFhO1lBQW9EO1lBQy9HRyxVQUFVO2dCQUFFTCxPQUFPO2dCQUFPQyxNQUFNO2dCQUFZQyxhQUFhO1lBQXdGO1lBQ2pKSSxTQUFTO2dCQUFFTixPQUFPO2dCQUFRQyxNQUFNO2dCQUFXQyxhQUFhO1lBQTZFO1FBQ3ZJO1FBRUEsT0FBT0osWUFBWSxDQUFDNUUsZ0JBQWdCTyxTQUFTLENBQThCO0lBQzdFO0lBRUEsTUFBTThFLGFBQWFWO0lBRW5CLHVDQUF1QztJQUN2QyxNQUFNVyxvQkFBb0IsQ0FBQ0MsV0FBVyxLQUFLO1FBQ3pDLE1BQU1DLGNBQWM7UUFDcEIsSUFBSSxDQUFDbEYsU0FBUztZQUNaLE9BQU8sR0FBR2tGLFlBQVksOENBQThDLENBQUM7UUFDdkU7UUFDQSxPQUFPLEdBQUdBLFlBQVksQ0FBQyxFQUFFRCxXQUFXLG1CQUFtQixtQkFBbUI7SUFDNUU7SUFFQSxxQkFDRSw4REFBQ0U7UUFBSUMsV0FBVTs7WUFFWkwsNEJBQ0MsOERBQUNJO2dCQUFJQyxXQUFXLENBQUMsc0JBQXNCLEVBQ3JDTCxXQUFXUCxLQUFLLEtBQUssVUFBVSxpQ0FDL0JPLFdBQVdQLEtBQUssS0FBSyxRQUFRLDZCQUM3Qk8sV0FBV1AsS0FBSyxLQUFLLFNBQVMsK0JBQzlCTyxXQUFXUCxLQUFLLEtBQUssV0FBVyxtQ0FDaEMsOEJBQ0E7O2tDQUNBLDhEQUFDVzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUtELFdBQVcsQ0FBQyx5REFBeUQsRUFDekVMLFdBQVdQLEtBQUssS0FBSyxVQUFVLGdDQUMvQk8sV0FBV1AsS0FBSyxLQUFLLFFBQVEsNEJBQzdCTyxXQUFXUCxLQUFLLEtBQUssU0FBUyw4QkFDOUJPLFdBQVdQLEtBQUssS0FBSyxXQUFXLGtDQUNoQyw2QkFDQTtrREFDQ08sV0FBV04sSUFBSTs7Ozs7O2tEQUVsQiw4REFBQ1k7d0NBQUtELFdBQVU7a0RBQXlCTCxXQUFXTCxXQUFXOzs7Ozs7Ozs7Ozs7NEJBRWhFaEYsaUJBQWlCNEYsMEJBQ2hCLDhEQUFDSDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNHO2tEQUFPOzs7Ozs7b0NBQXFCO29DQUFFN0YsZ0JBQWdCNEYsUUFBUTs7Ozs7Ozs7Ozs7OztvQkFJNUQsQ0FBQ3RGLHlCQUNBLDhEQUFDbUY7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRzswQ0FBTzs7Ozs7OzRCQUFjOzRCQUNyQjdGLGlCQUFpQk8sY0FBYyxjQUFjOzRCQUM3Q1AsaUJBQWlCTyxjQUFjLGFBQWE7NEJBQzVDUCxpQkFBaUJPLGNBQWMsZUFBZTs7Ozs7Ozs7Ozs7OzswQkFPdkQsOERBQUNrRjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0k7Z0NBQUdKLFdBQVU7MENBQ1gxRixrQkFBa0IsOEJBQThCOzs7Ozs7MENBRW5ELDhEQUFDK0Y7Z0NBQUVMLFdBQVU7MENBQ1ZwRixVQUFVLDhFQUE4RTs7Ozs7Ozs7Ozs7O29CQUk1RjhCLE9BQU9nQixPQUFPLGtCQUNiLDhEQUFDcUM7d0JBQUlDLFdBQVU7a0NBQ1p0RCxPQUFPZ0IsT0FBTzs7Ozs7O2tDQUluQiw4REFBQzRDO3dCQUFLQyxVQUFVbkM7d0JBQWM0QixXQUFVOzswQ0FFdEMsOERBQUNEOztrREFDQyw4REFBQ1M7d0NBQUdSLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTRSxTQUFTO3dEQUN6QjRGLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU8xQixTQUFTOzs7Ozs7b0RBRWhEMEIsT0FBTzFCLFNBQVMsa0JBQ2YsOERBQUNxRjt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPMUIsU0FBUzs7Ozs7Ozs7Ozs7OzBEQUk5RCw4REFBQytFOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTRyxRQUFRO3dEQUN4QjJGLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU96QixRQUFROzs7Ozs7b0RBRS9DeUIsT0FBT3pCLFFBQVEsa0JBQ2QsOERBQUNvRjt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPekIsUUFBUTs7Ozs7Ozs7Ozs7OzBEQUk3RCw4REFBQzhFOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDZTt3REFDQ2xDLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTSSxNQUFNO3dEQUN0QjBGLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVm9HLFVBQVUsQ0FBQ3BHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU94QixNQUFNOzswRUFFNUMsOERBQUMrRjtnRUFBT25DLE9BQU07MEVBQUc7Ozs7OzswRUFDakIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQU87Ozs7OzswRUFDckIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQVM7Ozs7OzswRUFDdkIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQVE7Ozs7OzswRUFDdEIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQW9COzs7Ozs7Ozs7Ozs7b0RBRW5DcEMsT0FBT3hCLE1BQU0sa0JBQ1osOERBQUNtRjt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPeEIsTUFBTTs7Ozs7Ozs7Ozs7OzBEQUkzRCw4REFBQzZFOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTSyxXQUFXO3dEQUMzQnlGLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU92QixXQUFXOzs7Ozs7b0RBRWxEdUIsT0FBT3ZCLFdBQVcsa0JBQ2pCLDhEQUFDa0Y7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT3ZCLFdBQVc7Ozs7Ozs7Ozs7OzswREFJaEUsOERBQUM0RTs7a0VBQ0MsOERBQUNVO3dEQUFNVCxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ1U7d0RBQ0NDLE1BQUs7d0RBQ0w5QixNQUFLO3dEQUNMQyxPQUFPaEUsU0FBU00sUUFBUTt3REFDeEJ3RixVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPdEIsUUFBUTs7Ozs7O29EQUUvQ3NCLE9BQU90QixRQUFRLGtCQUNkLDhEQUFDaUY7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT3RCLFFBQVE7Ozs7Ozs7Ozs7OzswREFJN0QsOERBQUMyRTs7a0VBQ0MsOERBQUNVO3dEQUFNVCxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ1U7d0RBQ0NDLE1BQUs7d0RBQ0w5QixNQUFLO3dEQUNMQyxPQUFPaEUsU0FBU08sVUFBVTt3REFDMUJ1RixVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPckIsVUFBVTs7Ozs7O29EQUVqRHFCLE9BQU9yQixVQUFVLGtCQUNoQiw4REFBQ2dGO3dEQUFFTCxXQUFVO2tFQUE2QnRELE9BQU9yQixVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT25FLDhEQUFDMEU7O2tEQUNDLDhEQUFDUzt3Q0FBR1IsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNlO3dEQUNDbEMsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVNRLGtCQUFrQjt3REFDbENzRixVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZvRyxVQUFVLENBQUNwRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPcEIsa0JBQWtCOzswRUFFeEQsOERBQUMyRjtnRUFBT25DLE9BQU07MEVBQUc7Ozs7OzswRUFDakIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQVc7Ozs7OzswRUFDekIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQVU7Ozs7OzswRUFDeEIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQWtCOzs7Ozs7MEVBQ2hDLDhEQUFDbUM7Z0VBQU9uQyxPQUFNOzBFQUFROzs7Ozs7Ozs7Ozs7b0RBRXZCcEMsT0FBT3BCLGtCQUFrQixrQkFDeEIsOERBQUMrRTt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPcEIsa0JBQWtCOzs7Ozs7Ozs7Ozs7NENBSXRFUixTQUFTUSxrQkFBa0IsS0FBSyw0QkFDL0IsOERBQUN5RTs7a0VBQ0MsOERBQUNVO3dEQUFNVCxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ1U7d0RBQ0NDLE1BQUs7d0RBQ0w5QixNQUFLO3dEQUNMQyxPQUFPaEUsU0FBU1MsY0FBYzt3REFDOUJxRixVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPbkIsY0FBYzs7Ozs7O29EQUVyRG1CLE9BQU9uQixjQUFjLGtCQUNwQiw4REFBQzhFO3dEQUFFTCxXQUFVO2tFQUE2QnRELE9BQU9uQixjQUFjOzs7Ozs7Ozs7Ozs7NENBS25FVCxDQUFBQSxTQUFTUSxrQkFBa0IsS0FBSyxhQUFhUixTQUFTUSxrQkFBa0IsS0FBSyxxQkFBcUJSLFNBQVNRLGtCQUFrQixLQUFLLE9BQU0sbUJBQ3hJLDhEQUFDeUU7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNVO3dEQUNDQyxNQUFLO3dEQUNMOUIsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVNVLFlBQVk7d0RBQzVCb0YsVUFBVWhDO3dEQUNWaUMsVUFBVWpHO3dEQUNWa0csVUFBVSxDQUFDbEc7d0RBQ1hvRixXQUFXSixrQkFBa0IsQ0FBQyxDQUFDbEQsT0FBT2xCLFlBQVk7Ozs7OztvREFFbkRrQixPQUFPbEIsWUFBWSxrQkFDbEIsOERBQUM2RTt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPbEIsWUFBWTs7Ozs7Ozs7Ozs7OzBEQUtuRSw4REFBQ3VFOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTVyxrQkFBa0I7d0RBQ2xDbUYsVUFBVWhDO3dEQUNWaUMsVUFBVWpHO3dEQUNWa0csVUFBVSxDQUFDbEc7d0RBQ1hvRixXQUFXSixrQkFBa0IsQ0FBQyxDQUFDbEQsT0FBT2pCLGtCQUFrQjs7Ozs7O29EQUV6RGlCLE9BQU9qQixrQkFBa0Isa0JBQ3hCLDhEQUFDNEU7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT2pCLGtCQUFrQjs7Ozs7Ozs7Ozs7OzBEQUl2RSw4REFBQ3NFOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTWSxXQUFXO3dEQUMzQmtGLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU9oQixXQUFXOzs7Ozs7b0RBRWxEZ0IsT0FBT2hCLFdBQVcsa0JBQ2pCLDhEQUFDMkU7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT2hCLFdBQVc7Ozs7Ozs7Ozs7OzswREFJaEUsOERBQUNxRTs7a0VBQ0MsOERBQUNVO3dEQUFNVCxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ1U7d0RBQ0NDLE1BQUs7d0RBQ0w5QixNQUFLO3dEQUNMQyxPQUFPaEUsU0FBU2EsS0FBSzt3REFDckJpRixVQUFVaEM7d0RBQ1ZrQyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPZixLQUFLOzs7Ozs7b0RBRTVDZSxPQUFPZixLQUFLLGtCQUNYLDhEQUFDMEU7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT2YsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU85RCw4REFBQ29FOztrREFDQyw4REFBQ1M7d0NBQUdSLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTYyxVQUFVO3dEQUMxQmdGLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU9kLFVBQVU7Ozs7OztvREFFakRjLE9BQU9kLFVBQVUsa0JBQ2hCLDhEQUFDeUU7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT2QsVUFBVTs7Ozs7Ozs7Ozs7OzBEQUkvRCw4REFBQ21FOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTZSxnQkFBZ0I7d0RBQ2hDK0UsVUFBVWhDO3dEQUNWaUMsVUFBVWpHO3dEQUNWa0csVUFBVSxDQUFDbEc7d0RBQ1hvRixXQUFXSixrQkFBa0IsQ0FBQyxDQUFDbEQsT0FBT2IsZ0JBQWdCOzs7Ozs7b0RBRXZEYSxPQUFPYixnQkFBZ0Isa0JBQ3RCLDhEQUFDd0U7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT2IsZ0JBQWdCOzs7Ozs7Ozs7Ozs7MERBSXJFLDhEQUFDa0U7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNVO3dEQUNDQyxNQUFLO3dEQUNMOUIsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVNrQixNQUFNO3dEQUN0QjRFLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU9WLE1BQU07Ozs7OztvREFFN0NVLE9BQU9WLE1BQU0sa0JBQ1osOERBQUNxRTt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPVixNQUFNOzs7Ozs7Ozs7Ozs7MERBSTNELDhEQUFDK0Q7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNVO3dEQUNDQyxNQUFLO3dEQUNMOUIsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVNtQixjQUFjO3dEQUM5QjJFLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVmtHLFVBQVUsQ0FBQ2xHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU9ULGNBQWM7Ozs7OztvREFFckRTLE9BQU9ULGNBQWMsa0JBQ3BCLDhEQUFDb0U7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT1QsY0FBYzs7Ozs7Ozs7Ozs7OzBEQUluRSw4REFBQzhEOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTb0IsSUFBSTt3REFDcEIwRSxVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPUixJQUFJOzs7Ozs7b0RBRTNDUSxPQUFPUixJQUFJLGtCQUNWLDhEQUFDbUU7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT1IsSUFBSTs7Ozs7Ozs7Ozs7OzBEQUl6RCw4REFBQzZEOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTcUIsS0FBSzt3REFDckJ5RSxVQUFVaEM7d0RBQ1ZrQyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPUCxLQUFLOzs7Ozs7b0RBRTVDTyxPQUFPUCxLQUFLLGtCQUNYLDhEQUFDa0U7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT1AsS0FBSzs7Ozs7Ozs7Ozs7OzBEQUkxRCw4REFBQzREOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTc0IsT0FBTzt3REFDdkJ3RSxVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPTixPQUFPOzs7Ozs7b0RBRTlDTSxPQUFPTixPQUFPLGtCQUNiLDhEQUFDaUU7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT04sT0FBTzs7Ozs7Ozs7Ozs7OzBEQUk1RCw4REFBQzJEOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTdUIsT0FBTzt3REFDdkJ1RSxVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPTCxPQUFPOzs7Ozs7b0RBRTlDSyxPQUFPTCxPQUFPLGtCQUNiLDhEQUFDZ0U7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT0wsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9oRSw4REFBQzBEOztrREFDQyw4REFBQ1M7d0NBQUdSLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDVTt3REFDQ0MsTUFBSzt3REFDTDlCLE1BQUs7d0RBQ0xDLE9BQU9oRSxTQUFTd0IsY0FBYzt3REFDOUJzRSxVQUFVaEM7d0RBQ1ZpQyxVQUFVakc7d0RBQ1ZrRyxVQUFVLENBQUNsRzt3REFDWG9GLFdBQVdKLGtCQUFrQixDQUFDLENBQUNsRCxPQUFPSixjQUFjOzs7Ozs7b0RBRXJESSxPQUFPSixjQUFjLGtCQUNwQiw4REFBQytEO3dEQUFFTCxXQUFVO2tFQUE2QnRELE9BQU9KLGNBQWM7Ozs7Ozs7Ozs7OzswREFJbkUsOERBQUN5RDs7a0VBQ0MsOERBQUNVO3dEQUFNVCxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ1U7d0RBQ0NDLE1BQUs7d0RBQ0w5QixNQUFLO3dEQUNMQyxPQUFPaEUsU0FBUzBCLGFBQWE7d0RBQzdCb0UsVUFBVWhDO3dEQUNWaUMsVUFBVWpHO3dEQUNWa0csVUFBVSxDQUFDbEc7d0RBQ1hvRixXQUFXSixrQkFBa0IsQ0FBQyxDQUFDbEQsT0FBT0YsYUFBYTs7Ozs7O29EQUVwREUsT0FBT0YsYUFBYSxrQkFDbkIsOERBQUM2RDt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPRixhQUFhOzs7Ozs7Ozs7Ozs7MERBSWxFLDhEQUFDdUQ7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNVO3dEQUNDQyxNQUFLO3dEQUNMOUIsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVN5QixpQkFBaUI7d0RBQ2pDcUUsVUFBVWhDO3dEQUNWaUMsVUFBVWpHO3dEQUNWa0csVUFBVSxDQUFDbEc7d0RBQ1hvRixXQUFXSixrQkFBa0IsQ0FBQyxDQUFDbEQsT0FBT0gsaUJBQWlCOzs7Ozs7b0RBRXhERyxPQUFPSCxpQkFBaUIsa0JBQ3ZCLDhEQUFDOEQ7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT0gsaUJBQWlCOzs7Ozs7Ozs7Ozs7MERBSXRFLDhEQUFDd0Q7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNVO3dEQUNDQyxNQUFLO3dEQUNMOUIsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVMyQix1QkFBdUI7d0RBQ3ZDbUUsVUFBVWhDO3dEQUNWaUMsVUFBVWpHO3dEQUNWa0csVUFBVSxDQUFDbEc7d0RBQ1hvRixXQUFXSixrQkFBa0IsQ0FBQyxDQUFDbEQsT0FBT0QsdUJBQXVCOzs7Ozs7b0RBRTlEQyxPQUFPRCx1QkFBdUIsa0JBQzdCLDhEQUFDNEQ7d0RBQUVMLFdBQVU7a0VBQTZCdEQsT0FBT0QsdUJBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT2hGLDhEQUFDc0Q7O2tEQUNDLDhEQUFDUzt3Q0FBR1IsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7O2tFQUNDLDhEQUFDVTt3REFBTVQsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNlO3dEQUNDbEMsTUFBSzt3REFDTEMsT0FBT2hFLFNBQVNnQixTQUFTO3dEQUN6QjhFLFVBQVVoQzt3REFDVmlDLFVBQVVqRzt3REFDVm9HLFVBQVUsQ0FBQ3BHO3dEQUNYb0YsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU9aLFNBQVM7OzBFQUUvQyw4REFBQ21GO2dFQUFPbkMsT0FBTTswRUFBVTs7Ozs7OzBFQUN4Qiw4REFBQ21DO2dFQUFPbkMsT0FBTTswRUFBTTs7Ozs7OzBFQUNwQiw4REFBQ21DO2dFQUFPbkMsT0FBTTswRUFBZ0I7Ozs7OzswRUFDOUIsOERBQUNtQztnRUFBT25DLE9BQU07MEVBQWtCOzs7Ozs7Ozs7Ozs7b0RBRWpDcEMsT0FBT1osU0FBUyxrQkFDZiw4REFBQ3VFO3dEQUFFTCxXQUFVO2tFQUE2QnRELE9BQU9aLFNBQVM7Ozs7Ozs7Ozs7Ozs0Q0FJN0RoQixTQUFTZ0IsU0FBUyxLQUFLLDJCQUN0Qiw4REFBQ2lFOztrRUFDQyw4REFBQ1U7d0RBQU1ULFdBQVU7a0VBQStDOzs7Ozs7a0VBR2hFLDhEQUFDa0I7d0RBQ0NyQyxNQUFLO3dEQUNMQyxPQUFPaEUsU0FBU2lCLFVBQVU7d0RBQzFCNkUsVUFBVWhDO3dEQUNWa0MsVUFBVSxDQUFDbEc7d0RBQ1h1RyxNQUFNO3dEQUNObkIsV0FBV0osa0JBQWtCLENBQUMsQ0FBQ2xELE9BQU9YLFVBQVU7d0RBQ2hEcUYsYUFBWTs7Ozs7O29EQUViMUUsT0FBT1gsVUFBVSxrQkFDaEIsOERBQUNzRTt3REFBRUwsV0FBVTtrRUFBNkJ0RCxPQUFPWCxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBUXBFbkIseUJBQ0MsOERBQUNtRjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3FCO29DQUNDVixNQUFLO29DQUNMSyxVQUFVaEUsNEJBQTRCc0UsU0FBUztvQ0FDL0N0QixXQUFVOzhDQUVUaEQsNEJBQTRCc0UsU0FBUyxHQUFHLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRM0UiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxzcmNcXGNvbXBvbmVudHNcXFF1YWxpZmljYXRpb25Gb3JtLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VNdXRhdGlvbiwgdXNlUXVlcnlDbGllbnQgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknO1xuaW1wb3J0IHsgdXNlQXBpQ2xpZW50LCB0eXBlIENsaWVudFByb2ZpbGUgfSBmcm9tICdAL2xpYi9hcGktY2xpZW50JztcblxuaW50ZXJmYWNlIFF1YWxpZmljYXRpb25Gb3JtUHJvcHMge1xuICBvbkNvbXBsZXRlOiAoKSA9PiB2b2lkO1xuICBleGlzdGluZ1Byb2ZpbGU/OiBDbGllbnRQcm9maWxlIHwgbnVsbDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFF1YWxpZmljYXRpb25Gb3JtKHsgb25Db21wbGV0ZSwgZXhpc3RpbmdQcm9maWxlIH06IFF1YWxpZmljYXRpb25Gb3JtUHJvcHMpIHtcbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGZvcm1hdCBkYXRlIGZvciBpbnB1dFxuICBjb25zdCBmb3JtYXREYXRlRm9ySW5wdXQgPSAoZGF0ZVN0cmluZzogc3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZCkgPT4ge1xuICAgIGlmICghZGF0ZVN0cmluZykgcmV0dXJuICcnO1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuICcnO1xuICAgIH1cbiAgfTtcblxuICAvLyBDaGVjayBpZiBmb3JtIGNhbiBiZSBlZGl0ZWRcbiAgY29uc3QgY2FuRWRpdCA9ICFleGlzdGluZ1Byb2ZpbGU/Lmt5Y1N0YXR1cyB8fFxuICAgICAgICAgICAgICAgICAgZXhpc3RpbmdQcm9maWxlLmt5Y1N0YXR1cyA9PT0gJ1JFSkVDVEVEJyB8fFxuICAgICAgICAgICAgICAgICAgZXhpc3RpbmdQcm9maWxlLmt5Y1N0YXR1cyA9PT0gJ0VYUElSRUQnO1xuXG4gIC8vIEluaXRpYWxpemUgZm9ybSBkYXRhIHdpdGggZW1wdHkgdmFsdWVzIGZpcnN0XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIGZpcnN0TmFtZTogJycsXG4gICAgbGFzdE5hbWU6ICcnLFxuICAgIGdlbmRlcjogJycsXG4gICAgbmF0aW9uYWxpdHk6ICcnLFxuICAgIGJpcnRoZGF5OiAnJyxcbiAgICBiaXJ0aFBsYWNlOiAnJyxcbiAgICBpZGVudGlmaWNhdGlvblR5cGU6ICcnLFxuICAgIHBhc3Nwb3J0TnVtYmVyOiAnJyxcbiAgICBpZENhcmROdW1iZXI6ICcnLFxuICAgIGRvY3VtZW50RXhwaXJhdGlvbjogJycsXG4gICAgcGhvbmVOdW1iZXI6ICcnLFxuICAgIGVtYWlsOiAnJyxcbiAgICBvY2N1cGF0aW9uOiAnJyxcbiAgICBzZWN0b3JPZkFjdGl2aXR5OiAnJyxcbiAgICBwZXBTdGF0dXM6IGV4aXN0aW5nUHJvZmlsZT8ucGVwU3RhdHVzIHx8ICdOT1RfUEVQJyxcbiAgICBwZXBEZXRhaWxzOiBleGlzdGluZ1Byb2ZpbGU/LnBlcERldGFpbHMgfHwgJycsXG4gICAgc3RyZWV0OiBleGlzdGluZ1Byb2ZpbGU/LnN0cmVldCB8fCAnJyxcbiAgICBidWlsZGluZ051bWJlcjogZXhpc3RpbmdQcm9maWxlPy5idWlsZGluZ051bWJlciB8fCAnJyxcbiAgICBjaXR5OiBleGlzdGluZ1Byb2ZpbGU/LmNpdHkgfHwgJycsXG4gICAgc3RhdGU6IGV4aXN0aW5nUHJvZmlsZT8uc3RhdGUgfHwgJycsXG4gICAgY291bnRyeTogZXhpc3RpbmdQcm9maWxlPy5jb3VudHJ5IHx8ICcnLFxuICAgIHppcENvZGU6IGV4aXN0aW5nUHJvZmlsZT8uemlwQ29kZSB8fCAnJyxcbiAgICBzb3VyY2VPZldlYWx0aDogZXhpc3RpbmdQcm9maWxlPy5zb3VyY2VPZldlYWx0aCB8fCAnJyxcbiAgICBiYW5rQWNjb3VudE51bWJlcjogZXhpc3RpbmdQcm9maWxlPy5iYW5rQWNjb3VudE51bWJlciB8fCAnJyxcbiAgICBzb3VyY2VPZkZ1bmRzOiBleGlzdGluZ1Byb2ZpbGU/LnNvdXJjZU9mRnVuZHMgfHwgJycsXG4gICAgdGF4SWRlbnRpZmljYXRpb25OdW1iZXI6IGV4aXN0aW5nUHJvZmlsZT8udGF4SWRlbnRpZmljYXRpb25OdW1iZXIgfHwgJycsXG4gIH0pO1xuXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+Pih7fSk7XG4gIGNvbnN0IGFwaUNsaWVudCA9IHVzZUFwaUNsaWVudCgpO1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KCk7XG5cbiAgLy8gVXBkYXRlIGZvcm0gZGF0YSB3aGVuIGV4aXN0aW5nUHJvZmlsZSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGV4aXN0aW5nUHJvZmlsZSkge1xuICAgICAgY29uc29sZS5sb2coJ1VwZGF0aW5nIGZvcm0gZGF0YSB3aXRoIGV4aXN0aW5nIHByb2ZpbGU6JywgZXhpc3RpbmdQcm9maWxlKTtcbiAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgZmlyc3ROYW1lOiBleGlzdGluZ1Byb2ZpbGUuZmlyc3ROYW1lIHx8ICcnLFxuICAgICAgICBsYXN0TmFtZTogZXhpc3RpbmdQcm9maWxlLmxhc3ROYW1lIHx8ICcnLFxuICAgICAgICBnZW5kZXI6IGV4aXN0aW5nUHJvZmlsZS5nZW5kZXIgfHwgJycsXG4gICAgICAgIG5hdGlvbmFsaXR5OiBleGlzdGluZ1Byb2ZpbGUubmF0aW9uYWxpdHkgfHwgJycsXG4gICAgICAgIGJpcnRoZGF5OiBmb3JtYXREYXRlRm9ySW5wdXQoZXhpc3RpbmdQcm9maWxlLmJpcnRoZGF5KSxcbiAgICAgICAgYmlydGhQbGFjZTogZXhpc3RpbmdQcm9maWxlLmJpcnRoUGxhY2UgfHwgJycsXG4gICAgICAgIGlkZW50aWZpY2F0aW9uVHlwZTogZXhpc3RpbmdQcm9maWxlLmlkZW50aWZpY2F0aW9uVHlwZSB8fCAnJyxcbiAgICAgICAgcGFzc3BvcnROdW1iZXI6IGV4aXN0aW5nUHJvZmlsZS5wYXNzcG9ydE51bWJlciB8fCAnJyxcbiAgICAgICAgaWRDYXJkTnVtYmVyOiBleGlzdGluZ1Byb2ZpbGUuaWRDYXJkTnVtYmVyIHx8ICcnLFxuICAgICAgICBkb2N1bWVudEV4cGlyYXRpb246IGZvcm1hdERhdGVGb3JJbnB1dChleGlzdGluZ1Byb2ZpbGUuZG9jdW1lbnRFeHBpcmF0aW9uKSxcbiAgICAgICAgcGhvbmVOdW1iZXI6IGV4aXN0aW5nUHJvZmlsZS5waG9uZU51bWJlciB8fCAnJyxcbiAgICAgICAgZW1haWw6IGV4aXN0aW5nUHJvZmlsZS5lbWFpbCB8fCAnJyxcbiAgICAgICAgb2NjdXBhdGlvbjogZXhpc3RpbmdQcm9maWxlLm9jY3VwYXRpb24gfHwgJycsXG4gICAgICAgIHNlY3Rvck9mQWN0aXZpdHk6IGV4aXN0aW5nUHJvZmlsZS5zZWN0b3JPZkFjdGl2aXR5IHx8ICcnLFxuICAgICAgICBwZXBTdGF0dXM6IGV4aXN0aW5nUHJvZmlsZS5wZXBTdGF0dXMgfHwgJ05PVF9QRVAnLFxuICAgICAgICBwZXBEZXRhaWxzOiBleGlzdGluZ1Byb2ZpbGUucGVwRGV0YWlscyB8fCAnJyxcbiAgICAgICAgc3RyZWV0OiBleGlzdGluZ1Byb2ZpbGUuc3RyZWV0IHx8ICcnLFxuICAgICAgICBidWlsZGluZ051bWJlcjogZXhpc3RpbmdQcm9maWxlLmJ1aWxkaW5nTnVtYmVyIHx8ICcnLFxuICAgICAgICBjaXR5OiBleGlzdGluZ1Byb2ZpbGUuY2l0eSB8fCAnJyxcbiAgICAgICAgc3RhdGU6IGV4aXN0aW5nUHJvZmlsZS5zdGF0ZSB8fCAnJyxcbiAgICAgICAgY291bnRyeTogZXhpc3RpbmdQcm9maWxlLmNvdW50cnkgfHwgJycsXG4gICAgICAgIHppcENvZGU6IGV4aXN0aW5nUHJvZmlsZS56aXBDb2RlIHx8ICcnLFxuICAgICAgICBzb3VyY2VPZldlYWx0aDogZXhpc3RpbmdQcm9maWxlLnNvdXJjZU9mV2VhbHRoIHx8ICcnLFxuICAgICAgICBiYW5rQWNjb3VudE51bWJlcjogZXhpc3RpbmdQcm9maWxlLmJhbmtBY2NvdW50TnVtYmVyIHx8ICcnLFxuICAgICAgICBzb3VyY2VPZkZ1bmRzOiBleGlzdGluZ1Byb2ZpbGUuc291cmNlT2ZGdW5kcyB8fCAnJyxcbiAgICAgICAgdGF4SWRlbnRpZmljYXRpb25OdW1iZXI6IGV4aXN0aW5nUHJvZmlsZS50YXhJZGVudGlmaWNhdGlvbk51bWJlciB8fCAnJyxcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2V4aXN0aW5nUHJvZmlsZV0pO1xuXG4gIGNvbnN0IHN1Ym1pdFF1YWxpZmljYXRpb25NdXRhdGlvbiA9IHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiBhc3luYyAoZGF0YTogdHlwZW9mIGZvcm1EYXRhKSA9PiB7XG4gICAgICBpZiAoZXhpc3RpbmdQcm9maWxlKSB7XG4gICAgICAgIHJldHVybiBhcGlDbGllbnQudXBkYXRlQ2xpZW50UHJvZmlsZShkYXRhKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBhcGlDbGllbnQuY3JlYXRlQ2xpZW50UHJvZmlsZShkYXRhKTtcbiAgICAgIH1cbiAgICB9LFxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydjbGllbnQtcHJvZmlsZSddIH0pO1xuICAgICAgb25Db21wbGV0ZSgpO1xuICAgIH0sXG4gICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcbiAgICAgIHNldEVycm9ycyh7IGdlbmVyYWw6IGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBzdWJtaXQgcXVhbGlmaWNhdGlvbiBhcHBsaWNhdGlvbicgfSk7XG4gICAgfSxcbiAgfSk7XG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCkgPT4ge1xuICAgIGNvbnN0IG5ld0Vycm9yczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9O1xuXG4gICAgLy8gUmVxdWlyZWQgZmllbGQgdmFsaWRhdGlvbnNcbiAgICBpZiAoIWZvcm1EYXRhLmZpcnN0TmFtZS50cmltKCkpIG5ld0Vycm9ycy5maXJzdE5hbWUgPSAnRmlyc3QgbmFtZSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5sYXN0TmFtZS50cmltKCkpIG5ld0Vycm9ycy5sYXN0TmFtZSA9ICdMYXN0IG5hbWUgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuZ2VuZGVyKSBuZXdFcnJvcnMuZ2VuZGVyID0gJ0dlbmRlciBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5uYXRpb25hbGl0eS50cmltKCkpIG5ld0Vycm9ycy5uYXRpb25hbGl0eSA9ICdOYXRpb25hbGl0eSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5iaXJ0aGRheSkgbmV3RXJyb3JzLmJpcnRoZGF5ID0gJ0JpcnRoZGF5IGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLmJpcnRoUGxhY2UudHJpbSgpKSBuZXdFcnJvcnMuYmlydGhQbGFjZSA9ICdCaXJ0aCBwbGFjZSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5pZGVudGlmaWNhdGlvblR5cGUpIG5ld0Vycm9ycy5pZGVudGlmaWNhdGlvblR5cGUgPSAnSWRlbnRpZmljYXRpb24gdHlwZSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5kb2N1bWVudEV4cGlyYXRpb24pIG5ld0Vycm9ycy5kb2N1bWVudEV4cGlyYXRpb24gPSAnRG9jdW1lbnQgZXhwaXJhdGlvbiBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5waG9uZU51bWJlci50cmltKCkpIG5ld0Vycm9ycy5waG9uZU51bWJlciA9ICdQaG9uZSBudW1iZXIgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEub2NjdXBhdGlvbi50cmltKCkpIG5ld0Vycm9ycy5vY2N1cGF0aW9uID0gJ09jY3VwYXRpb24gaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuc2VjdG9yT2ZBY3Rpdml0eS50cmltKCkpIG5ld0Vycm9ycy5zZWN0b3JPZkFjdGl2aXR5ID0gJ1NlY3RvciBvZiBhY3Rpdml0eSBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5zdHJlZXQudHJpbSgpKSBuZXdFcnJvcnMuc3RyZWV0ID0gJ1N0cmVldCBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5idWlsZGluZ051bWJlci50cmltKCkpIG5ld0Vycm9ycy5idWlsZGluZ051bWJlciA9ICdCdWlsZGluZyBudW1iZXIgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuY2l0eS50cmltKCkpIG5ld0Vycm9ycy5jaXR5ID0gJ0NpdHkgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuY291bnRyeS50cmltKCkpIG5ld0Vycm9ycy5jb3VudHJ5ID0gJ0NvdW50cnkgaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuemlwQ29kZS50cmltKCkpIG5ld0Vycm9ycy56aXBDb2RlID0gJ1ppcCBjb2RlIGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLnNvdXJjZU9mV2VhbHRoLnRyaW0oKSkgbmV3RXJyb3JzLnNvdXJjZU9mV2VhbHRoID0gJ1NvdXJjZSBvZiB3ZWFsdGggaXMgcmVxdWlyZWQnO1xuICAgIGlmICghZm9ybURhdGEuYmFua0FjY291bnROdW1iZXIudHJpbSgpKSBuZXdFcnJvcnMuYmFua0FjY291bnROdW1iZXIgPSAnQmFuayBhY2NvdW50IG51bWJlciBpcyByZXF1aXJlZCc7XG4gICAgaWYgKCFmb3JtRGF0YS5zb3VyY2VPZkZ1bmRzLnRyaW0oKSkgbmV3RXJyb3JzLnNvdXJjZU9mRnVuZHMgPSAnU291cmNlIG9mIGZ1bmRzIGlzIHJlcXVpcmVkJztcbiAgICBpZiAoIWZvcm1EYXRhLnRheElkZW50aWZpY2F0aW9uTnVtYmVyLnRyaW0oKSkgbmV3RXJyb3JzLnRheElkZW50aWZpY2F0aW9uTnVtYmVyID0gJ1RheCBpZGVudGlmaWNhdGlvbiBudW1iZXIgaXMgcmVxdWlyZWQnO1xuXG4gICAgLy8gVmFsaWRhdGUgYmlydGhkYXkgKG11c3QgYmUgMTgtMTIwIHllYXJzIG9sZClcbiAgICBpZiAoZm9ybURhdGEuYmlydGhkYXkpIHtcbiAgICAgIGNvbnN0IGJpcnRoRGF0ZSA9IG5ldyBEYXRlKGZvcm1EYXRhLmJpcnRoZGF5KTtcbiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcbiAgICAgIGNvbnN0IGFnZSA9IHRvZGF5LmdldEZ1bGxZZWFyKCkgLSBiaXJ0aERhdGUuZ2V0RnVsbFllYXIoKTtcbiAgICAgIGlmIChhZ2UgPCAxOCB8fCBhZ2UgPiAxMjApIHtcbiAgICAgICAgbmV3RXJyb3JzLmJpcnRoZGF5ID0gJ011c3QgYmUgYmV0d2VlbiAxOCBhbmQgMTIwIHllYXJzIG9sZCc7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZG9jdW1lbnQgZXhwaXJhdGlvbiAobXVzdCBiZSBpbiB0aGUgZnV0dXJlKVxuICAgIGlmIChmb3JtRGF0YS5kb2N1bWVudEV4cGlyYXRpb24pIHtcbiAgICAgIGNvbnN0IGV4cGlyYXRpb25EYXRlID0gbmV3IERhdGUoZm9ybURhdGEuZG9jdW1lbnRFeHBpcmF0aW9uKTtcbiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcbiAgICAgIGlmIChleHBpcmF0aW9uRGF0ZSA8PSB0b2RheSkge1xuICAgICAgICBuZXdFcnJvcnMuZG9jdW1lbnRFeHBpcmF0aW9uID0gJ0RvY3VtZW50IGV4cGlyYXRpb24gbXVzdCBiZSBpbiB0aGUgZnV0dXJlJztcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBpZGVudGlmaWNhdGlvbiBkb2N1bWVudCBudW1iZXIgYmFzZWQgb24gdHlwZVxuICAgIGlmIChmb3JtRGF0YS5pZGVudGlmaWNhdGlvblR5cGUgPT09ICdQQVNTUE9SVCcgJiYgIWZvcm1EYXRhLnBhc3Nwb3J0TnVtYmVyLnRyaW0oKSkge1xuICAgICAgbmV3RXJyb3JzLnBhc3Nwb3J0TnVtYmVyID0gJ1Bhc3Nwb3J0IG51bWJlciBpcyByZXF1aXJlZCc7XG4gICAgfVxuICAgIGlmICgoZm9ybURhdGEuaWRlbnRpZmljYXRpb25UeXBlID09PSAnSURfQ0FSRCcgfHwgZm9ybURhdGEuaWRlbnRpZmljYXRpb25UeXBlID09PSAnRFJJVkVSU19MSUNFTlNFJykgJiYgIWZvcm1EYXRhLmlkQ2FyZE51bWJlci50cmltKCkpIHtcbiAgICAgIG5ld0Vycm9ycy5pZENhcmROdW1iZXIgPSAnSUQgY2FyZCBudW1iZXIgaXMgcmVxdWlyZWQnO1xuICAgIH1cblxuICAgIHJldHVybiBuZXdFcnJvcnM7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcblxuICAgIGNvbnN0IHZhbGlkYXRpb25FcnJvcnMgPSB2YWxpZGF0ZUZvcm0oKTtcbiAgICBpZiAoT2JqZWN0LmtleXModmFsaWRhdGlvbkVycm9ycykubGVuZ3RoID4gMCkge1xuICAgICAgc2V0RXJyb3JzKHZhbGlkYXRpb25FcnJvcnMpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldEVycm9ycyh7fSk7XG4gICAgc3VibWl0UXVhbGlmaWNhdGlvbk11dGF0aW9uLm11dGF0ZShmb3JtRGF0YSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSB9ID0gZS50YXJnZXQ7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbbmFtZV06IHZhbHVlIH0pKTtcbiAgICBpZiAoZXJyb3JzW25hbWVdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbbmFtZV06ICcnIH0pKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gR2V0IHN0YXR1cyBkaXNwbGF5IGluZm9cbiAgY29uc3QgZ2V0U3RhdHVzSW5mbyA9ICgpID0+IHtcbiAgICBpZiAoIWV4aXN0aW5nUHJvZmlsZT8ua3ljU3RhdHVzKSByZXR1cm4gbnVsbDtcblxuICAgIGNvbnN0IHN0YXR1c0NvbmZpZyA9IHtcbiAgICAgIFBFTkRJTkc6IHsgY29sb3I6ICd5ZWxsb3cnLCB0ZXh0OiAnUGVuZGluZyBSZXZpZXcnLCBkZXNjcmlwdGlvbjogJ1lvdXIgcXVhbGlmaWNhdGlvbiBhcHBsaWNhdGlvbiBpcyB3YWl0aW5nIHRvIGJlIHJldmlld2VkLicgfSxcbiAgICAgIElOX1JFVklFVzogeyBjb2xvcjogJ2JsdWUnLCB0ZXh0OiAnVW5kZXIgUmV2aWV3JywgZGVzY3JpcHRpb246ICdZb3VyIHF1YWxpZmljYXRpb24gYXBwbGljYXRpb24gaXMgY3VycmVudGx5IGJlaW5nIHJldmlld2VkIGJ5IG91ciB0ZWFtLicgfSxcbiAgICAgIEFQUFJPVkVEOiB7IGNvbG9yOiAnZ3JlZW4nLCB0ZXh0OiAnQXBwcm92ZWQnLCBkZXNjcmlwdGlvbjogJ1lvdXIgcXVhbGlmaWNhdGlvbiBhcHBsaWNhdGlvbiBoYXMgYmVlbiBhcHByb3ZlZC4nIH0sXG4gICAgICBSRUpFQ1RFRDogeyBjb2xvcjogJ3JlZCcsIHRleHQ6ICdSZWplY3RlZCcsIGRlc2NyaXB0aW9uOiAnWW91ciBxdWFsaWZpY2F0aW9uIGFwcGxpY2F0aW9uIHdhcyByZWplY3RlZC4gUGxlYXNlIHJldmlldyB0aGUgZmVlZGJhY2sgYW5kIHJlc3VibWl0LicgfSxcbiAgICAgIEVYUElSRUQ6IHsgY29sb3I6ICdncmF5JywgdGV4dDogJ0V4cGlyZWQnLCBkZXNjcmlwdGlvbjogJ1lvdXIgcXVhbGlmaWNhdGlvbiBhcHByb3ZhbCBoYXMgZXhwaXJlZC4gUGxlYXNlIHJlc3VibWl0IHlvdXIgYXBwbGljYXRpb24uJyB9LFxuICAgIH07XG5cbiAgICByZXR1cm4gc3RhdHVzQ29uZmlnW2V4aXN0aW5nUHJvZmlsZS5reWNTdGF0dXMgYXMga2V5b2YgdHlwZW9mIHN0YXR1c0NvbmZpZ107XG4gIH07XG5cbiAgY29uc3Qgc3RhdHVzSW5mbyA9IGdldFN0YXR1c0luZm8oKTtcblxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IGlucHV0IHN0eWxpbmdcbiAgY29uc3QgZ2V0SW5wdXRDbGFzc05hbWUgPSAoaGFzRXJyb3IgPSBmYWxzZSkgPT4ge1xuICAgIGNvbnN0IGJhc2VDbGFzc2VzID0gXCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiO1xuICAgIGlmICghY2FuRWRpdCkge1xuICAgICAgcmV0dXJuIGAke2Jhc2VDbGFzc2VzfSBiZy1ncmF5LTUwIGJvcmRlci1ncmF5LTIwMCBjdXJzb3Itbm90LWFsbG93ZWRgO1xuICAgIH1cbiAgICByZXR1cm4gYCR7YmFzZUNsYXNzZXN9ICR7aGFzRXJyb3IgPyAnYm9yZGVyLXJlZC01MDAnIDogJ2JvcmRlci1ncmF5LTMwMCd9YDtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogU3RhdHVzIERpc3BsYXkgKi99XG4gICAgICB7c3RhdHVzSW5mbyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgYm9yZGVyICR7XG4gICAgICAgICAgc3RhdHVzSW5mby5jb2xvciA9PT0gJ2dyZWVuJyA/ICdiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJyA6XG4gICAgICAgICAgc3RhdHVzSW5mby5jb2xvciA9PT0gJ3JlZCcgPyAnYmctcmVkLTUwIGJvcmRlci1yZWQtMjAwJyA6XG4gICAgICAgICAgc3RhdHVzSW5mby5jb2xvciA9PT0gJ2JsdWUnID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJyA6XG4gICAgICAgICAgc3RhdHVzSW5mby5jb2xvciA9PT0gJ3llbGxvdycgPyAnYmcteWVsbG93LTUwIGJvcmRlci15ZWxsb3ctMjAwJyA6XG4gICAgICAgICAgJ2JnLWdyYXktNTAgYm9yZGVyLWdyYXktMjAwJ1xuICAgICAgICB9YH0+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IHB4LTMgcHktMSB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgc3RhdHVzSW5mby5jb2xvciA9PT0gJ2dyZWVuJyA/ICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi0xMDAnIDpcbiAgICAgICAgICAgICAgICBzdGF0dXNJbmZvLmNvbG9yID09PSAncmVkJyA/ICd0ZXh0LXJlZC02MDAgYmctcmVkLTEwMCcgOlxuICAgICAgICAgICAgICAgIHN0YXR1c0luZm8uY29sb3IgPT09ICdibHVlJyA/ICd0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtMTAwJyA6XG4gICAgICAgICAgICAgICAgc3RhdHVzSW5mby5jb2xvciA9PT0gJ3llbGxvdycgPyAndGV4dC15ZWxsb3ctNjAwIGJnLXllbGxvdy0xMDAnIDpcbiAgICAgICAgICAgICAgICAndGV4dC1ncmF5LTYwMCBiZy1ncmF5LTEwMCdcbiAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgIHtzdGF0dXNJbmZvLnRleHR9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3N0YXR1c0luZm8uZGVzY3JpcHRpb259PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7ZXhpc3RpbmdQcm9maWxlPy5reWNOb3RlcyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgPHN0cm9uZz5BZG1pbiBOb3Rlczo8L3N0cm9uZz4ge2V4aXN0aW5nUHJvZmlsZS5reWNOb3Rlc31cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHshY2FuRWRpdCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxzdHJvbmc+Tm90ZTo8L3N0cm9uZz4gWW91ciBxdWFsaWZpY2F0aW9uIGFwcGxpY2F0aW9uIGNhbm5vdCBiZSBlZGl0ZWQgaW4gaXRzIGN1cnJlbnQgc3RhdHVzLlxuICAgICAgICAgICAgICB7ZXhpc3RpbmdQcm9maWxlPy5reWNTdGF0dXMgPT09ICdBUFBST1ZFRCcgJiYgJyBZb3VyIGFwcGxpY2F0aW9uIGhhcyBiZWVuIGFwcHJvdmVkLid9XG4gICAgICAgICAgICAgIHtleGlzdGluZ1Byb2ZpbGU/Lmt5Y1N0YXR1cyA9PT0gJ1BFTkRJTkcnICYmICcgWW91ciBhcHBsaWNhdGlvbiBpcyBwZW5kaW5nIHJldmlldy4nfVxuICAgICAgICAgICAgICB7ZXhpc3RpbmdQcm9maWxlPy5reWNTdGF0dXMgPT09ICdJTl9SRVZJRVcnICYmICcgWW91ciBhcHBsaWNhdGlvbiBpcyBjdXJyZW50bHkgdW5kZXIgcmV2aWV3Lid9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBGb3JtICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICB7ZXhpc3RpbmdQcm9maWxlID8gJ1F1YWxpZmljYXRpb24gSW5mb3JtYXRpb24nIDogJ0NvbXBsZXRlIFlvdXIgUXVhbGlmaWNhdGlvbid9XG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICB7Y2FuRWRpdCA/ICdQbGVhc2UgcHJvdmlkZSB5b3VyIGluZm9ybWF0aW9uIHRvIHF1YWxpZnkgZm9yIHNlY3VyaXR5IHRva2VuIGludmVzdG1lbnQuJyA6ICdWaWV3IHlvdXIgcXVhbGlmaWNhdGlvbiBhcHBsaWNhdGlvbiBkZXRhaWxzLid9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7ZXJyb3JzLmdlbmVyYWwgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBiZy1yZWQtMTAwIGJvcmRlciBib3JkZXItcmVkLTQwMCB0ZXh0LXJlZC03MDAgcHgtNCBweS0zIHJvdW5kZWRcIj5cbiAgICAgICAgICAgIHtlcnJvcnMuZ2VuZXJhbH1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICB7LyogUGVyc29uYWwgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlBlcnNvbmFsIEluZm9ybWF0aW9uPC9oND5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgRmlyc3QgTmFtZSAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJmaXJzdE5hbWVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmZpcnN0TmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuZmlyc3ROYW1lKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuZmlyc3ROYW1lICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmZpcnN0TmFtZX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIExhc3QgTmFtZSAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJsYXN0TmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubGFzdE5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ9e2NhbkVkaXR9XG4gICAgICAgICAgICAgICAgICByZWFkT25seT17IWNhbkVkaXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2dldElucHV0Q2xhc3NOYW1lKCEhZXJyb3JzLmxhc3ROYW1lKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMubGFzdE5hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMubGFzdE5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICBHZW5kZXIgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgbmFtZT1cImdlbmRlclwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZ2VuZGVyfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXtjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5nZW5kZXIpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3QgR2VuZGVyPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTUFMRVwiPk1hbGU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJGRU1BTEVcIj5GZW1hbGU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJPVEhFUlwiPk90aGVyPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUFJFRkVSX05PVF9UT19TQVlcIj5QcmVmZXIgbm90IHRvIHNheTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuZ2VuZGVyICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmdlbmRlcn08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIE5hdGlvbmFsaXR5ICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cIm5hdGlvbmFsaXR5XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYXRpb25hbGl0eX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMubmF0aW9uYWxpdHkpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5uYXRpb25hbGl0eSAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5uYXRpb25hbGl0eX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIEJpcnRoZGF5ICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImJpcnRoZGF5XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5iaXJ0aGRheX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuYmlydGhkYXkpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5iaXJ0aGRheSAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5iaXJ0aGRheX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIEJpcnRoIFBsYWNlICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImJpcnRoUGxhY2VcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmJpcnRoUGxhY2V9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ9e2NhbkVkaXR9XG4gICAgICAgICAgICAgICAgICByZWFkT25seT17IWNhbkVkaXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2dldElucHV0Q2xhc3NOYW1lKCEhZXJyb3JzLmJpcnRoUGxhY2UpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5iaXJ0aFBsYWNlICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmJpcnRoUGxhY2V9PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogSWRlbnRpZmljYXRpb24gJiBDb250YWN0ICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1tZCBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5JZGVudGlmaWNhdGlvbiAmIENvbnRhY3Q8L2g0PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICBJZGVudGlmaWNhdGlvbiBUeXBlICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJpZGVudGlmaWNhdGlvblR5cGVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmlkZW50aWZpY2F0aW9uVHlwZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuaWRlbnRpZmljYXRpb25UeXBlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+U2VsZWN0IFR5cGU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQQVNTUE9SVFwiPlBhc3Nwb3J0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSURfQ0FSRFwiPklEIENhcmQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJEUklWRVJTX0xJQ0VOU0VcIj5Ecml2ZXIncyBMaWNlbnNlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiT1RIRVJcIj5PdGhlcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuaWRlbnRpZmljYXRpb25UeXBlICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmlkZW50aWZpY2F0aW9uVHlwZX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAge2Zvcm1EYXRhLmlkZW50aWZpY2F0aW9uVHlwZSA9PT0gJ1BBU1NQT1JUJyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICBQYXNzcG9ydCBOdW1iZXIgKlxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXNzcG9ydE51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXNzcG9ydE51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ9e2NhbkVkaXR9XG4gICAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5wYXNzcG9ydE51bWJlcil9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5wYXNzcG9ydE51bWJlciAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnBhc3Nwb3J0TnVtYmVyfTwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgeyhmb3JtRGF0YS5pZGVudGlmaWNhdGlvblR5cGUgPT09ICdJRF9DQVJEJyB8fCBmb3JtRGF0YS5pZGVudGlmaWNhdGlvblR5cGUgPT09ICdEUklWRVJTX0xJQ0VOU0UnIHx8IGZvcm1EYXRhLmlkZW50aWZpY2F0aW9uVHlwZSA9PT0gJ09USEVSJykgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgSUQgQ2FyZCBOdW1iZXIgKlxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJpZENhcmROdW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuaWRDYXJkTnVtYmVyfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2dldElucHV0Q2xhc3NOYW1lKCEhZXJyb3JzLmlkQ2FyZE51bWJlcil9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5pZENhcmROdW1iZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5pZENhcmROdW1iZXJ9PC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgRG9jdW1lbnQgRXhwaXJhdGlvbiAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJkb2N1bWVudEV4cGlyYXRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRvY3VtZW50RXhwaXJhdGlvbn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuZG9jdW1lbnRFeHBpcmF0aW9uKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuZG9jdW1lbnRFeHBpcmF0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmRvY3VtZW50RXhwaXJhdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIFBob25lIE51bWJlciAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cInBob25lTnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5waG9uZU51bWJlcn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMucGhvbmVOdW1iZXIpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5waG9uZU51bWJlciAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5waG9uZU51bWJlcn08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIEVtYWlsIEFkZHJlc3NcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1haWx9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5lbWFpbCl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLmVtYWlsICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmVtYWlsfTwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFByb2Zlc3Npb25hbCAmIEFkZHJlc3MgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlByb2Zlc3Npb25hbCAmIEFkZHJlc3MgSW5mb3JtYXRpb248L2g0PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICBPY2N1cGF0aW9uICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cIm9jY3VwYXRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm9jY3VwYXRpb259XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ9e2NhbkVkaXR9XG4gICAgICAgICAgICAgICAgICByZWFkT25seT17IWNhbkVkaXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2dldElucHV0Q2xhc3NOYW1lKCEhZXJyb3JzLm9jY3VwYXRpb24pfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5vY2N1cGF0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLm9jY3VwYXRpb259PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICBTZWN0b3Igb2YgQWN0aXZpdHkgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwic2VjdG9yT2ZBY3Rpdml0eVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc2VjdG9yT2ZBY3Rpdml0eX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuc2VjdG9yT2ZBY3Rpdml0eSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnNlY3Rvck9mQWN0aXZpdHkgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuc2VjdG9yT2ZBY3Rpdml0eX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIFN0cmVldCBBZGRyZXNzICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cInN0cmVldFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RyZWV0fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXtjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5zdHJlZXQpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5zdHJlZXQgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuc3RyZWV0fTwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgQnVpbGRpbmcgTnVtYmVyICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImJ1aWxkaW5nTnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5idWlsZGluZ051bWJlcn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuYnVpbGRpbmdOdW1iZXIpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5idWlsZGluZ051bWJlciAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5idWlsZGluZ051bWJlcn08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIENpdHkgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiY2l0eVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY2l0eX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuY2l0eSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLmNpdHkgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuY2l0eX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIFN0YXRlL1Byb3ZpbmNlXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJzdGF0ZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RhdGV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5zdGF0ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnN0YXRlICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnN0YXRlfTwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgQ291bnRyeSAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJjb3VudHJ5XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb3VudHJ5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXtjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5jb3VudHJ5KX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuY291bnRyeSAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5jb3VudHJ5fTwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgWmlwIENvZGUgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiemlwQ29kZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuemlwQ29kZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuemlwQ29kZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnppcENvZGUgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuemlwQ29kZX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBGaW5hbmNpYWwgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPkZpbmFuY2lhbCBJbmZvcm1hdGlvbjwvaDQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIFNvdXJjZSBvZiBXZWFsdGggKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwic291cmNlT2ZXZWFsdGhcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNvdXJjZU9mV2VhbHRofVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXtjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy5zb3VyY2VPZldlYWx0aCl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnNvdXJjZU9mV2VhbHRoICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnNvdXJjZU9mV2VhbHRofTwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgU291cmNlIG9mIEZ1bmRzICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cInNvdXJjZU9mRnVuZHNcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNvdXJjZU9mRnVuZHN9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ9e2NhbkVkaXR9XG4gICAgICAgICAgICAgICAgICByZWFkT25seT17IWNhbkVkaXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2dldElucHV0Q2xhc3NOYW1lKCEhZXJyb3JzLnNvdXJjZU9mRnVuZHMpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5zb3VyY2VPZkZ1bmRzICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnNvdXJjZU9mRnVuZHN9PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICBCYW5rIEFjY291bnQgTnVtYmVyICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImJhbmtBY2NvdW50TnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5iYW5rQWNjb3VudE51bWJlcn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMuYmFua0FjY291bnROdW1iZXIpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5iYW5rQWNjb3VudE51bWJlciAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5iYW5rQWNjb3VudE51bWJlcn08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIFRheCBJZGVudGlmaWNhdGlvbiBOdW1iZXIgKlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwidGF4SWRlbnRpZmljYXRpb25OdW1iZXJcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRheElkZW50aWZpY2F0aW9uTnVtYmVyfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkPXtjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgcmVhZE9ubHk9eyFjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRJbnB1dENsYXNzTmFtZSghIWVycm9ycy50YXhJZGVudGlmaWNhdGlvbk51bWJlcil9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnRheElkZW50aWZpY2F0aW9uTnVtYmVyICYmIChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnRheElkZW50aWZpY2F0aW9uTnVtYmVyfTwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFBFUCBTdGF0dXMgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlBvbGl0aWNhbCBFeHBvc3VyZTwvaDQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgUG9saXRpY2FsbHkgRXhwb3NlZCBQZXJzb24gKFBFUCkgU3RhdHVzICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJwZXBTdGF0dXNcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlcFN0YXR1c31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZD17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMucGVwU3RhdHVzKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTk9UX1BFUFwiPk5vdCBhIFBFUDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBFUFwiPlBFUDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkZBTUlMWV9NRU1CRVJcIj5GYW1pbHkgbWVtYmVyIG9mIFBFUDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNMT1NFX0FTU09DSUFURVwiPkNsb3NlIGFzc29jaWF0ZSBvZiBQRVA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnBlcFN0YXR1cyAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5wZXBTdGF0dXN9PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHtmb3JtRGF0YS5wZXBTdGF0dXMgIT09ICdOT1RfUEVQJyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICBQRVAgRGV0YWlsc1xuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGVwRGV0YWlsc1wiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZXBEZXRhaWxzfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICByZWFkT25seT17IWNhbkVkaXR9XG4gICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Z2V0SW5wdXRDbGFzc05hbWUoISFlcnJvcnMucGVwRGV0YWlscyl9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUGxlYXNlIHByb3ZpZGUgZGV0YWlscyBhYm91dCB5b3VyIHBvbGl0aWNhbCBleHBvc3VyZS4uLlwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5wZXBEZXRhaWxzICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMucGVwRGV0YWlsc308L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3VibWl0IEJ1dHRvbiAqL31cbiAgICAgICAgICB7Y2FuRWRpdCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmRcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdWJtaXRRdWFsaWZpY2F0aW9uTXV0YXRpb24uaXNQZW5kaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtNiBweS0yIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3N1Ym1pdFF1YWxpZmljYXRpb25NdXRhdGlvbi5pc1BlbmRpbmcgPyAnU3VibWl0dGluZy4uLicgOiAnQ29tcGxldGUgUXVhbGlmaWNhdGlvbid9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9mb3JtPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VNdXRhdGlvbiIsInVzZVF1ZXJ5Q2xpZW50IiwidXNlQXBpQ2xpZW50IiwiUXVhbGlmaWNhdGlvbkZvcm0iLCJvbkNvbXBsZXRlIiwiZXhpc3RpbmdQcm9maWxlIiwiZm9ybWF0RGF0ZUZvcklucHV0IiwiZGF0ZVN0cmluZyIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwiY2FuRWRpdCIsImt5Y1N0YXR1cyIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImdlbmRlciIsIm5hdGlvbmFsaXR5IiwiYmlydGhkYXkiLCJiaXJ0aFBsYWNlIiwiaWRlbnRpZmljYXRpb25UeXBlIiwicGFzc3BvcnROdW1iZXIiLCJpZENhcmROdW1iZXIiLCJkb2N1bWVudEV4cGlyYXRpb24iLCJwaG9uZU51bWJlciIsImVtYWlsIiwib2NjdXBhdGlvbiIsInNlY3Rvck9mQWN0aXZpdHkiLCJwZXBTdGF0dXMiLCJwZXBEZXRhaWxzIiwic3RyZWV0IiwiYnVpbGRpbmdOdW1iZXIiLCJjaXR5Iiwic3RhdGUiLCJjb3VudHJ5IiwiemlwQ29kZSIsInNvdXJjZU9mV2VhbHRoIiwiYmFua0FjY291bnROdW1iZXIiLCJzb3VyY2VPZkZ1bmRzIiwidGF4SWRlbnRpZmljYXRpb25OdW1iZXIiLCJlcnJvcnMiLCJzZXRFcnJvcnMiLCJhcGlDbGllbnQiLCJxdWVyeUNsaWVudCIsImNvbnNvbGUiLCJsb2ciLCJzdWJtaXRRdWFsaWZpY2F0aW9uTXV0YXRpb24iLCJtdXRhdGlvbkZuIiwiZGF0YSIsInVwZGF0ZUNsaWVudFByb2ZpbGUiLCJjcmVhdGVDbGllbnRQcm9maWxlIiwib25TdWNjZXNzIiwiaW52YWxpZGF0ZVF1ZXJpZXMiLCJxdWVyeUtleSIsIm9uRXJyb3IiLCJlcnJvciIsImdlbmVyYWwiLCJtZXNzYWdlIiwidmFsaWRhdGVGb3JtIiwibmV3RXJyb3JzIiwidHJpbSIsImJpcnRoRGF0ZSIsInRvZGF5IiwiYWdlIiwiZ2V0RnVsbFllYXIiLCJleHBpcmF0aW9uRGF0ZSIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInZhbGlkYXRpb25FcnJvcnMiLCJPYmplY3QiLCJrZXlzIiwibGVuZ3RoIiwibXV0YXRlIiwiaGFuZGxlQ2hhbmdlIiwibmFtZSIsInZhbHVlIiwidGFyZ2V0IiwicHJldiIsImdldFN0YXR1c0luZm8iLCJzdGF0dXNDb25maWciLCJQRU5ESU5HIiwiY29sb3IiLCJ0ZXh0IiwiZGVzY3JpcHRpb24iLCJJTl9SRVZJRVciLCJBUFBST1ZFRCIsIlJFSkVDVEVEIiwiRVhQSVJFRCIsInN0YXR1c0luZm8iLCJnZXRJbnB1dENsYXNzTmFtZSIsImhhc0Vycm9yIiwiYmFzZUNsYXNzZXMiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwia3ljTm90ZXMiLCJzdHJvbmciLCJoMyIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJoNCIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwib25DaGFuZ2UiLCJyZXF1aXJlZCIsInJlYWRPbmx5Iiwic2VsZWN0IiwiZGlzYWJsZWQiLCJvcHRpb24iLCJ0ZXh0YXJlYSIsInJvd3MiLCJwbGFjZWhvbGRlciIsImJ1dHRvbiIsImlzUGVuZGluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/QualificationForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/QuestionMarkCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,HomeIcon,QuestionMarkCircleIcon,ShoppingBagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'Orders',\n        href: '/orders',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Help',\n        href: '/help',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    }\n];\nfunction Sidebar({ user, onLogout, useMockAuth }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    if (!user) {\n        return null;\n    }\n    const SidebarContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-4 py-6 space-y-2\",\n                    children: navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: `group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n                            onClick: ()=>setSidebarOpen(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: `mr-3 h-5 w-5 flex-shrink-0 ${isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-4 border-t border-gray-200\",\n                    children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onLogout,\n                        className: \"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign out\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: `/api/auth/logout?returnTo=${\"http://localhost:7788\" || 0}`,\n                        className: \"group flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign out\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 37,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed top-4 left-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    className: \"bg-white p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 shadow-md\",\n                    onClick: ()=>setSidebarOpen(true),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Open sidebar\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-40 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Close sidebar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_HomeIcon_QuestionMarkCircleIcon_ShoppingBagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-0 flex-1 bg-white border-r border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {}, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDSjtBQUNpQjtBQVFUO0FBU3JDLE1BQU1TLGFBQWE7SUFDakI7UUFBRUMsTUFBTTtRQUFhQyxNQUFNO1FBQUtDLE1BQU1ULHVMQUFRQTtJQUFDO0lBQy9DO1FBQUVPLE1BQU07UUFBVUMsTUFBTTtRQUFXQyxNQUFNUix1TEFBZUE7SUFBQztJQUN6RDtRQUFFTSxNQUFNO1FBQVFDLE1BQU07UUFBU0MsTUFBTVAsdUxBQXNCQTtJQUFDO0NBQzdEO0FBRU0sU0FBU1EsUUFBUSxFQUFFQyxJQUFJLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxFQUFnQjtJQUNuRSxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1tQixXQUFXakIsNERBQVdBO0lBRTVCLElBQUksQ0FBQ1ksTUFBTTtRQUNULE9BQU87SUFDVDtJQUVBLE1BQU1NLGlCQUFpQixrQkFDckIsOERBQUNDO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDWmIsV0FBV2UsR0FBRyxDQUFDLENBQUNDO3dCQUNmLE1BQU1DLFdBQVdQLGFBQWFNLEtBQUtkLElBQUk7d0JBQ3ZDLHFCQUNFLDhEQUFDVixrREFBSUE7NEJBRUhVLE1BQU1jLEtBQUtkLElBQUk7NEJBQ2ZXLFdBQVcsQ0FBQyxtRkFBbUYsRUFDN0ZJLFdBQ0kseURBQ0EsdURBQ0o7NEJBQ0ZDLFNBQVMsSUFBTVQsZUFBZTs7OENBRTlCLDhEQUFDTyxLQUFLYixJQUFJO29DQUNSVSxXQUFXLENBQUMsMkJBQTJCLEVBQ3JDSSxXQUFXLGtCQUFrQiwyQ0FDN0I7Ozs7OztnQ0FFSEQsS0FBS2YsSUFBSTs7MkJBZExlLEtBQUtmLElBQUk7Ozs7O29CQWlCcEI7Ozs7Ozs4QkFJRiw4REFBQ1c7b0JBQUlDLFdBQVU7OEJBQ1pOLDRCQUNDLDhEQUFDWTt3QkFDQ0QsU0FBU1o7d0JBQ1RPLFdBQVU7OzBDQUVWLDhEQUFDaEIsdUxBQXlCQTtnQ0FBQ2dCLFdBQVU7Ozs7Ozs0QkFBeUQ7Ozs7Ozs2Q0FJaEcsOERBQUNPO3dCQUNDbEIsTUFBTSxDQUFDLDBCQUEwQixFQUFFbUIsdUJBQTJDLElBQUlBLENBQTBCLEVBQUU7d0JBQzlHUixXQUFVOzswQ0FFViw4REFBQ2hCLHVMQUF5QkE7Z0NBQUNnQixXQUFVOzs7Ozs7NEJBQXlEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFReEcscUJBQ0U7OzBCQUVFLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ007b0JBQ0NNLE1BQUs7b0JBQ0xaLFdBQVU7b0JBQ1ZLLFNBQVMsSUFBTVQsZUFBZTs7c0NBRTlCLDhEQUFDaUI7NEJBQUtiLFdBQVU7c0NBQVU7Ozs7OztzQ0FDMUIsOERBQUNmLHVMQUFTQTs0QkFBQ2UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLeEJMLDZCQUNDLDhEQUFDSTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO3dCQUEwQ0ssU0FBUyxJQUFNVCxlQUFlOzs7Ozs7a0NBQ3ZGLDhEQUFDRzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDTTtvQ0FDQ00sTUFBSztvQ0FDTFosV0FBVTtvQ0FDVkssU0FBUyxJQUFNVCxlQUFlOztzREFFOUIsOERBQUNpQjs0Q0FBS2IsV0FBVTtzREFBVTs7Ozs7O3NEQUMxQiw4REFBQ2QsdUxBQVNBOzRDQUFDYyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHekIsOERBQUNGOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNUCw4REFBQ0M7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNYiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xcU2lkZWJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBcbiAgSG9tZUljb24sIFxuICBTaG9wcGluZ0JhZ0ljb24sIFxuICBRdWVzdGlvbk1hcmtDaXJjbGVJY29uLCBcbiAgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbixcbiAgQmFyczNJY29uLFxuICBYTWFya0ljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB0eXBlIHsgVXNlclByb2ZpbGUgfSBmcm9tICdAYXV0aDAvbmV4dGpzLWF1dGgwL2NsaWVudCc7XG5cbmludGVyZmFjZSBTaWRlYmFyUHJvcHMge1xuICB1c2VyOiBVc2VyUHJvZmlsZTtcbiAgb25Mb2dvdXQ/OiAoKSA9PiB2b2lkO1xuICB1c2VNb2NrQXV0aD86IGJvb2xlYW47XG59XG5cbmNvbnN0IG5hdmlnYXRpb24gPSBbXG4gIHsgbmFtZTogJ0Rhc2hib2FyZCcsIGhyZWY6ICcvJywgaWNvbjogSG9tZUljb24gfSxcbiAgeyBuYW1lOiAnT3JkZXJzJywgaHJlZjogJy9vcmRlcnMnLCBpY29uOiBTaG9wcGluZ0JhZ0ljb24gfSxcbiAgeyBuYW1lOiAnSGVscCcsIGhyZWY6ICcvaGVscCcsIGljb246IFF1ZXN0aW9uTWFya0NpcmNsZUljb24gfSxcbl07XG5cbmV4cG9ydCBmdW5jdGlvbiBTaWRlYmFyKHsgdXNlciwgb25Mb2dvdXQsIHVzZU1vY2tBdXRoIH06IFNpZGViYXJQcm9wcykge1xuICBjb25zdCBbc2lkZWJhck9wZW4sIHNldFNpZGViYXJPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIGlmICghdXNlcikge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgY29uc3QgU2lkZWJhckNvbnRlbnQgPSAoKSA9PiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxuICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImZsZXgtMSBweC00IHB5LTYgc3BhY2UteS0yXCI+XG4gICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gcGF0aG5hbWUgPT09IGl0ZW0uaHJlZjtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAgZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNzAwIGJvcmRlci1yLTIgYm9yZGVyLWJsdWUtNzAwJ1xuICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpfSAvLyBDbG9zZSBtb2JpbGUgc2lkZWJhciBvbiBuYXZpZ2F0aW9uXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxpdGVtLmljb25cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Btci0zIGgtNSB3LTUgZmxleC1zaHJpbmstMCAke1xuICAgICAgICAgICAgICAgICAgaXNBY3RpdmUgPyAndGV4dC1ibHVlLTcwMCcgOiAndGV4dC1ncmF5LTQwMCBncm91cC1ob3Zlcjp0ZXh0LWdyYXktNTAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICk7XG4gICAgICAgIH0pfVxuICAgICAgPC9uYXY+XG5cbiAgICAgIHsvKiBMb2dvdXQgQnV0dG9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHB5LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIHt1c2VNb2NrQXV0aCA/IChcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkxvZ291dH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGZsZXggdy1mdWxsIGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS0xMDAgaG92ZXI6dGV4dC1ncmF5LTkwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFycm93UmlnaHRPblJlY3RhbmdsZUljb24gY2xhc3NOYW1lPVwibXItMyBoLTUgdy01IHRleHQtZ3JheS00MDAgZ3JvdXAtaG92ZXI6dGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICBTaWduIG91dFxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxhXG4gICAgICAgICAgICBocmVmPXtgL2FwaS9hdXRoL2xvZ291dD9yZXR1cm5Ubz0ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FVVEgwX1JFVFVSTl9UT19VUkwgfHwgcHJvY2Vzcy5lbnYuQVVUSDBfQkFTRV9VUkx9YH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTEwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8QXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiBjbGFzc05hbWU9XCJtci0zIGgtNSB3LTUgdGV4dC1ncmF5LTQwMCBncm91cC1ob3Zlcjp0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgIFNpZ24gb3V0XG4gICAgICAgICAgPC9hPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIE1vYmlsZSBzaWRlYmFyIHRvZ2dsZSBidXR0b24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiBmaXhlZCB0b3AtNCBsZWZ0LTQgei01MFwiPlxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC0yIHJvdW5kZWQtbWQgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNTAwIGhvdmVyOmJnLWdyYXktMTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1pbnNldCBmb2N1czpyaW5nLWJsdWUtNTAwIHNoYWRvdy1tZFwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhck9wZW4odHJ1ZSl9XG4gICAgICAgID5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+T3BlbiBzaWRlYmFyPC9zcGFuPlxuICAgICAgICAgIDxCYXJzM0ljb24gY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2JpbGUgc2lkZWJhciBvdmVybGF5ICovfVxuICAgICAge3NpZGViYXJPcGVuICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpoaWRkZW4gZml4ZWQgaW5zZXQtMCB6LTQwIGZsZXhcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctZ3JheS02MDAgYmctb3BhY2l0eS03NVwiIG9uQ2xpY2s9eygpID0+IHNldFNpZGViYXJPcGVuKGZhbHNlKX0gLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMSBmbGV4IGZsZXgtY29sIG1heC13LXhzIHctZnVsbCBiZy13aGl0ZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCByaWdodC0wIC1tci0xMiBwdC0yXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtMTAgdy0xMCByb3VuZGVkLWZ1bGwgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWluc2V0IGZvY3VzOnJpbmctd2hpdGVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNpZGViYXJPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5DbG9zZSBzaWRlYmFyPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxYTWFya0ljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxTaWRlYmFyQ29udGVudCAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBEZXNrdG9wIHNpZGViYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBsZzpmbGV4IGxnOmZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHctNjRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC0wIGZsZXgtMSBiZy13aGl0ZSBib3JkZXItciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDxTaWRlYmFyQ29udGVudCAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTGluayIsInVzZVBhdGhuYW1lIiwiSG9tZUljb24iLCJTaG9wcGluZ0JhZ0ljb24iLCJRdWVzdGlvbk1hcmtDaXJjbGVJY29uIiwiQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiIsIkJhcnMzSWNvbiIsIlhNYXJrSWNvbiIsIm5hdmlnYXRpb24iLCJuYW1lIiwiaHJlZiIsImljb24iLCJTaWRlYmFyIiwidXNlciIsIm9uTG9nb3V0IiwidXNlTW9ja0F1dGgiLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwicGF0aG5hbWUiLCJTaWRlYmFyQ29udGVudCIsImRpdiIsImNsYXNzTmFtZSIsIm5hdiIsIm1hcCIsIml0ZW0iLCJpc0FjdGl2ZSIsIm9uQ2xpY2siLCJidXR0b24iLCJhIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FVVEgwX1JFVFVSTl9UT19VUkwiLCJBVVRIMF9CQVNFX1VSTCIsInR5cGUiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WalletProvider.tsx":
/*!*******************************************!*\
  !*** ./src/components/WalletProvider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _config_wallet__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/wallet */ \"(ssr)/./src/config/wallet.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletProvider auto */ \n\n\n\n// Set up queryClient\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 60 * 1000,\n            retry: 1\n        }\n    }\n});\nfunction WalletProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_3__.WagmiProvider, {\n        config: _config_wallet__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\WalletProvider.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\WalletProvider.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9XYWxsZXRQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHcUM7QUFDbUM7QUFDaEM7QUFFeEMscUJBQXFCO0FBQ3JCLE1BQU1JLGNBQWMsSUFBSUgsOERBQVdBLENBQUM7SUFDbENJLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLFdBQVcsS0FBSztZQUNoQkMsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQU1PLFNBQVNDLGVBQWUsRUFBRUMsUUFBUSxFQUF1QjtJQUM5RCxxQkFDRSw4REFBQ1YsZ0RBQWFBO1FBQUNHLFFBQVFBLGtEQUFNQTtrQkFDM0IsNEVBQUNELHNFQUFtQkE7WUFBQ1MsUUFBUVA7c0JBQzFCTTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xcV2FsbGV0UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFdhZ21pUHJvdmlkZXIgfSBmcm9tICd3YWdtaSdcbmltcG9ydCB7IFF1ZXJ5Q2xpZW50LCBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5J1xuaW1wb3J0IHsgY29uZmlnIH0gZnJvbSAnQC9jb25maWcvd2FsbGV0J1xuXG4vLyBTZXQgdXAgcXVlcnlDbGllbnRcbmNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICBzdGFsZVRpbWU6IDYwICogMTAwMCwgLy8gMSBtaW51dGVcbiAgICAgIHJldHJ5OiAxLFxuICAgIH0sXG4gIH0sXG59KVxuXG5pbnRlcmZhY2UgV2FsbGV0UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFdhbGxldFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogV2FsbGV0UHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxXYWdtaVByb3ZpZGVyIGNvbmZpZz17Y29uZmlnfT5cbiAgICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICAgPC9XYWdtaVByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiV2FnbWlQcm92aWRlciIsIlF1ZXJ5Q2xpZW50IiwiUXVlcnlDbGllbnRQcm92aWRlciIsImNvbmZpZyIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwicmV0cnkiLCJXYWxsZXRQcm92aWRlciIsImNoaWxkcmVuIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WalletProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\n\n\nfunction AuthProvider({ children }) {\n    const { user, isLoading } = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (user) {\n                // Set the user in the API client for authenticated requests\n                _lib_api_client__WEBPACK_IMPORTED_MODULE_3__.apiClient.setUser({\n                    sub: user.sub,\n                    email: user.email || undefined,\n                    name: user.name || undefined\n                });\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        user\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\AuthProvider.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/MockAuthProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/MockAuthProvider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockAuthProvider: () => (/* binding */ MockAuthProvider),\n/* harmony export */   useMockUser: () => (/* binding */ useMockUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MockAuthProvider,useMockUser auto */ \n\nconst MockAuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MockAuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const login = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setUser({\n                sub: 'mock-user-123',\n                name: 'Demo User',\n                email: '<EMAIL>',\n                picture: 'https://via.placeholder.com/40'\n            });\n            setIsLoading(false);\n        }, 1000);\n    };\n    const logout = ()=>{\n        setUser(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MockAuthContext.Provider, {\n        value: {\n            user,\n            error: undefined,\n            isLoading,\n            login,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\MockAuthProvider.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\nfunction useMockUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MockAuthContext);\n    if (context === undefined) {\n        throw new Error('useMockUser must be used within a MockAuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/MockAuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ReactQueryProvider.tsx":
/*!*********************************************************!*\
  !*** ./src/components/providers/ReactQueryProvider.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ReactQueryProvider auto */ \n\n\nfunction ReactQueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ReactQueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1,\n                        refetchOnWindowFocus: false\n                    }\n                }\n            })\n    }[\"ReactQueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\providers\\\\ReactQueryProvider.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUmVhY3RRdWVyeVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUV5RTtBQUN4QztBQUUxQixTQUFTRyxtQkFBbUIsRUFBRUMsUUFBUSxFQUFpQztJQUM1RSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBO3VDQUM1QixJQUNFLElBQUlGLDhEQUFXQSxDQUFDO2dCQUNkTSxnQkFBZ0I7b0JBQ2RDLFNBQVM7d0JBQ1BDLFdBQVcsS0FBSzt3QkFDaEJDLE9BQU87d0JBQ1BDLHNCQUFzQjtvQkFDeEI7Z0JBQ0Y7WUFDRjs7SUFHSixxQkFDRSw4REFBQ1Qsc0VBQW1CQTtRQUFDVSxRQUFRTjtrQkFDMUJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxSZWFjdFF1ZXJ5UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUXVlcnlDbGllbnQsIFF1ZXJ5Q2xpZW50UHJvdmlkZXIgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWFjdFF1ZXJ5UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBRdWVyeUNsaWVudCh7XG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiB7XG4gICAgICAgICAgcXVlcmllczoge1xuICAgICAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgICAgICByZXRyeTogMSxcbiAgICAgICAgICAgIHJlZmV0Y2hPbldpbmRvd0ZvY3VzOiBmYWxzZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJ1c2VTdGF0ZSIsIlJlYWN0UXVlcnlQcm92aWRlciIsImNoaWxkcmVuIiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJyZXRyeSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ReactQueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/wallet.ts":
/*!******************************!*\
  !*** ./src/config/wallet.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   modal: () => (/* binding */ modal),\n/* harmony export */   projectId: () => (/* binding */ projectId),\n/* harmony export */   wagmiAdapter: () => (/* binding */ wagmiAdapter)\n/* harmony export */ });\n/* harmony import */ var _reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reown/appkit/react */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/react.js\");\n/* harmony import */ var _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @reown/appkit-adapter-wagmi */ \"(ssr)/./node_modules/@reown/appkit-adapter-wagmi/dist/esm/src/client.js\");\n/* harmony import */ var _reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reown/appkit/networks */ \"(ssr)/./node_modules/@reown/appkit/dist/esm/exports/networks.js\");\n\n\n\n// 1. Get projectId from https://cloud.reown.com\nconst projectId = \"5b68ddabca3cc662e86f74a3044a95d4\" || 0;\nif (!projectId) {\n    throw new Error('NEXT_PUBLIC_REOWN_PROJECT_ID is not set');\n}\n// 2. Configure custom networks with RPC URLs\nconst amoyNetwork = {\n    ..._reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.polygonAmoy,\n    rpcUrls: {\n        default: {\n            http: [\n                \"https://rpc-amoy.polygon.technology\"\n            ]\n        },\n        public: {\n            http: [\n                \"https://rpc-amoy.polygon.technology\"\n            ]\n        }\n    }\n};\nconst polygonNetwork = {\n    ..._reown_appkit_networks__WEBPACK_IMPORTED_MODULE_1__.polygon,\n    rpcUrls: {\n        default: {\n            http: [\n                \"https://polygon-rpc.com\"\n            ]\n        },\n        public: {\n            http: [\n                \"https://polygon-rpc.com\"\n            ]\n        }\n    }\n};\n// 3. Set up Wagmi adapter with AMOY as primary network\nconst wagmiAdapter = new _reown_appkit_adapter_wagmi__WEBPACK_IMPORTED_MODULE_2__.WagmiAdapter({\n    networks: [\n        amoyNetwork,\n        polygonNetwork\n    ],\n    projectId,\n    ssr: true\n});\n// 3. Configure the metadata\nconst metadata = {\n    name: 'Security Token Client Portal',\n    description: 'Client portal for ERC-3643 security token qualification',\n    url: process.env.AUTH0_BASE_URL,\n    icons: [\n        'https://avatars.githubusercontent.com/u/179229932'\n    ]\n};\n// 4. Create the modal with AMOY as default network\nconst modal = (0,_reown_appkit_react__WEBPACK_IMPORTED_MODULE_0__.createAppKit)({\n    adapters: [\n        wagmiAdapter\n    ],\n    networks: [\n        amoyNetwork,\n        polygonNetwork\n    ],\n    defaultNetwork: amoyNetwork,\n    metadata,\n    projectId,\n    features: {\n        analytics: true,\n        email: false,\n        socials: [\n            'google',\n            'x',\n            'github',\n            'discord',\n            'apple'\n        ],\n        emailShowWallets: true // Optional - defaults to your Cloud configuration\n    }\n});\nconst config = wagmiAdapter.wagmiConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/wallet.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   useApiClient: () => (/* binding */ useApiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nclass ApiClient {\n    constructor(){\n        this.user = null;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: \"http://localhost:7788/api\",\n            timeout: 10000,\n            withCredentials: true\n        });\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            if (error.response?.status === 401) {\n                // Handle unauthorized access\n                window.location.href = '/api/auth/login';\n            }\n            return Promise.reject(error);\n        });\n    }\n    setUser(user) {\n        this.user = user;\n    }\n    // Client profile API calls\n    async getClientProfile() {\n        try {\n            const response = await this.client.get('/client/profile');\n            return response.data;\n        } catch (error) {\n            if (error?.response?.status === 404) {\n                return null; // Client profile doesn't exist yet\n            }\n            throw error;\n        }\n    }\n    async createClientProfile(profileData) {\n        const response = await this.client.post('/client/profile', profileData);\n        return response.data;\n    }\n    async updateClientProfile(profileData) {\n        const response = await this.client.put('/client/profile', profileData);\n        return response.data;\n    }\n    // KYC-related API calls\n    async submitKYCApplication(kycData) {\n        const response = await this.client.post('/client/kyc', kycData);\n        return response.data;\n    }\n    async getKYCStatus() {\n        const response = await this.client.get('/client/kyc');\n        return response.data;\n    }\n    // Whitelist-related API calls\n    async getWhitelistStatus() {\n        const response = await this.client.get('/client/whitelist');\n        return response.data;\n    }\n    // Generic API call method\n    async request(config) {\n        const response = await this.client.request(config);\n        return response.data;\n    }\n}\n// Create a singleton instance\nconst apiClient = new ApiClient();\n// Hook for React components\nfunction useApiClient() {\n    return apiClient;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api-client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@reown","vendor-chunks/lit-html","vendor-chunks/@lit","vendor-chunks/lit","vendor-chunks/next","vendor-chunks/viem","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@swc","vendor-chunks/has-flag","vendor-chunks/@walletconnect","vendor-chunks/ox","vendor-chunks/@wagmi","vendor-chunks/abitype","vendor-chunks/tr46","vendor-chunks/@floating-ui","vendor-chunks/mime-db","vendor-chunks/zod","vendor-chunks/axios","vendor-chunks/@headlessui","vendor-chunks/@tanstack","vendor-chunks/@react-aria","vendor-chunks/pino","vendor-chunks/node-fetch","vendor-chunks/whatwg-url","vendor-chunks/valtio","vendor-chunks/multiformats","vendor-chunks/@lit-labs","vendor-chunks/tabbable","vendor-chunks/big.js","vendor-chunks/follow-redirects","vendor-chunks/unstorage","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/zustand","vendor-chunks/thread-stream","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/sonic-boom","vendor-chunks/dayjs","vendor-chunks/@heroicons","vendor-chunks/eventemitter3","vendor-chunks/lit-element","vendor-chunks/asynckit","vendor-chunks/detect-browser","vendor-chunks/idb-keyval","vendor-chunks/node-gyp-build","vendor-chunks/pino-std-serializers","vendor-chunks/webidl-conversions","vendor-chunks/combined-stream","vendor-chunks/base-x","vendor-chunks/uint8arrays","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/quick-format-unescaped","vendor-chunks/proxy-compare","vendor-chunks/mipd","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/destr","vendor-chunks/derive-valtio","vendor-chunks/utf-8-validate","vendor-chunks/wagmi","vendor-chunks/@react-stately","vendor-chunks/process-warning","vendor-chunks/es-set-tostringtag","vendor-chunks/atomic-sleep","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/bufferutil","vendor-chunks/on-exit-leak-free","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/bs58","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fqualification%2Fpage&page=%2Fqualification%2Fpage&appPaths=%2Fqualification%2Fpage&pagePath=private-next-app-dir%2Fqualification%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();