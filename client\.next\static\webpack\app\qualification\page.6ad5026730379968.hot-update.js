"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                const newStepData = {\n                    country: qualificationProgress.country || '',\n                    agreementAccepted: qualificationProgress.agreementAccepted || false,\n                    profileCompleted: qualificationProgress.profileCompleted || !!profile,\n                    walletConnected: qualificationProgress.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: qualificationProgress.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = qualificationProgress.currentStep || 0;\n                // Validate and adjust step based on actual completion status\n                if (!newStepData.country) {\n                    calculatedStep = 0; // Country selection\n                } else if (!newStepData.agreementAccepted) {\n                    calculatedStep = 1; // Agreement acceptance\n                } else if (!newStepData.profileCompleted) {\n                    calculatedStep = 2; // Profile completion\n                } else if (!newStepData.walletConnected) {\n                    calculatedStep = 3; // Wallet connection\n                } else if (!newStepData.kycCompleted) {\n                    calculatedStep = 4; // KYC verification\n                } else {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: qualificationProgress\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = (country)=>{\n        setStepData((prev)=>({\n                ...prev,\n                country\n            }));\n        setCurrentStep(1);\n    };\n    const handleAgreementComplete = ()=>{\n        setStepData((prev)=>({\n                ...prev,\n                agreementAccepted: true\n            }));\n        setCurrentStep(2);\n    };\n    const handleProfileComplete = ()=>{\n        setStepData((prev)=>({\n                ...prev,\n                profileCompleted: true\n            }));\n        setCurrentStep(3);\n    };\n    const handleWalletComplete = ()=>{\n        setStepData((prev)=>({\n                ...prev,\n                walletConnected: true\n            }));\n        setCurrentStep(4);\n    };\n    const handleKYCStatusChange = (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            setStepData((prev)=>({\n                    ...prev,\n                    kycCompleted: true\n                }));\n            setCurrentStep(5);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border text-center \".concat(getStepColor(step.status)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Complete!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". You can now proceed with your investment.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});