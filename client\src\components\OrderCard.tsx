'use client';

import { useState } from 'react';
import Link from 'next/link';

interface Order {
  id: string;
  status: 'PENDING_APPROVAL' | 'CONFIRMED' | 'MINTED' | 'CANCELLED';
  tokensOrdered: string;
  tokensConfirmed: string;
  amountToPay: string;
  confirmedPayment: string;
  tokenPrice: string;
  paymentReference: string;
  createdAt: string;
  updatedAt: string;
  token: {
    id: string;
    name: string;
    symbol: string;
    address: string;
    tokenPrice: string;
    currency: string;
  };
}

interface OrderCardProps {
  order: Order;
  onRefresh?: () => void;
}

export function OrderCard({ order, onRefresh }: OrderCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'PENDING_APPROVAL':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'MINTED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING_APPROVAL':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'CONFIRMED':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'MINTED':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'CANCELLED':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: string, currency: string = 'USD') => {
    const numAmount = parseFloat(amount);

    // Handle crypto currencies that don't have standard currency codes
    const cryptoCurrencies = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI'];
    if (cryptoCurrencies.includes(currency.toUpperCase())) {
      return `${numAmount} ${currency.toUpperCase()}`;
    }

    // Handle standard fiat currencies
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(numAmount);
    } catch (error) {
      // Fallback for unsupported currencies
      return `${numAmount} ${currency}`;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {order.token.name}
              </h3>
              <p className="text-sm text-gray-500 font-mono">
                {order.token.symbol}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(order.status)}`}>
              {getStatusIcon(order.status)}
              <span className="ml-1">{order.status.replace('_', ' ')}</span>
            </span>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-gray-500 mb-1">Tokens Ordered</div>
            <div className="font-semibold text-gray-900">{order.tokensOrdered}</div>
          </div>
          <div>
            <div className="text-gray-500 mb-1">Amount to Pay</div>
            <div className="font-semibold text-gray-900">
              {formatCurrency(order.amountToPay, order.token.currency)}
            </div>
          </div>
          <div>
            <div className="text-gray-500 mb-1">Order Date</div>
            <div className="font-semibold text-gray-900">
              {formatDate(order.createdAt)}
            </div>
          </div>
          <div>
            <div className="text-gray-500 mb-1">Reference</div>
            <div className="font-semibold text-gray-900 font-mono text-xs">
              {order.paymentReference}
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      {isExpanded && (
        <div className="border-t border-gray-100 p-4 bg-gray-50">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-500 mb-1">Token Price</div>
                <div className="font-medium">
                  {formatCurrency(order.tokenPrice, order.token.currency)} per token
                </div>
              </div>
              <div>
                <div className="text-gray-500 mb-1">Tokens Confirmed</div>
                <div className="font-medium">{order.tokensConfirmed}</div>
              </div>
              <div>
                <div className="text-gray-500 mb-1">Confirmed Payment</div>
                <div className="font-medium">
                  {formatCurrency(order.confirmedPayment, order.token.currency)}
                </div>
              </div>
              <div>
                <div className="text-gray-500 mb-1">Last Updated</div>
                <div className="font-medium">{formatDate(order.updatedAt)}</div>
              </div>
            </div>

            {/* Token Address */}
            <div>
              <div className="text-gray-500 mb-1">Token Contract</div>
              <div className="font-mono text-xs bg-white p-2 rounded border">
                {order.token.address}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                Order ID: {order.id}
              </div>
              <div className="flex space-x-2">
                <Link
                  href={`/offers/${order.token.address}`}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  View Token
                </Link>
                {onRefresh && (
                  <button
                    onClick={onRefresh}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Refresh
                  </button>
                )}
              </div>
            </div>

            {/* Status-specific information */}
            {order.status === 'PENDING_APPROVAL' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Awaiting Approval
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>Your order is pending admin approval. You will be notified once it's processed.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {order.status === 'CONFIRMED' && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Order Confirmed
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>Your order has been confirmed. Payment processing is in progress.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {order.status === 'MINTED' && (
              <div className="bg-green-50 border border-green-200 rounded-md p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      Tokens Minted
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>Your tokens have been successfully minted and transferred to your wallet.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {order.status === 'CANCELLED' && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Order Cancelled
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>This order has been cancelled. Please contact support if you have questions.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
