const { ethers } = require("hardhat");

async function main() {
  // Get factory address from environment variable or provide it
  const factoryAddress = process.env.FACTORY_ADDRESS;
  
  if (!factoryAddress) {
    console.log("FACTORY_ADDRESS environment variable not set. Please provide the factory address.");
    process.exit(1);
  }
  
  console.log(`Checking tokens deployed by factory at ${factoryAddress}...`);
  
  // Get the network
  const network = await ethers.provider.getNetwork();
  console.log(`Connected to network: ${network.name} (chainId: ${network.chainId})`);
  
  // Get current account
  const [deployer] = await ethers.getSigners();
  console.log(`Using account: ${deployer.address}`);
  
  // Check if the factory exists
  const code = await ethers.provider.getCode(factoryAddress);
  
  if (code === "0x") {
    console.log("No contract deployed at the factory address!");
    return;
  }
  
  // Connect to the factory
  try {
    const factory = await ethers.getContractAt("SecurityTokenFactory", factoryAddress);
    console.log("Connected to factory contract successfully.");
    
    // Try to get token implementation and whitelist implementation
    try {
      const tokenImpl = await factory.tokenImplementation();
      const whitelistImpl = await factory.whitelistImplementation();
      
      console.log(`Token implementation: ${tokenImpl}`);
      console.log(`Whitelist implementation: ${whitelistImpl}`);
    } catch (error) {
      console.log("Failed to get implementations:", error.message);
    }
    
    // Try to get all deployed tokens
    try {
      // First check how many tokens are deployed
      const tokenCount = await factory.getTokenCount();
      console.log(`Total tokens deployed: ${tokenCount}`);
      
      // List all tokens
      console.log("\nDeployed tokens:");
      console.log("----------------");
      
      const bigIntCount = BigInt(tokenCount.toString());
      
      if (bigIntCount === 0n) {
        console.log("No tokens deployed yet.");
      } else {
        for (let i = 0n; i < bigIntCount; i++) {
          try {
            const tokenAddress = await factory.deployedTokens(i);
            console.log(`Token ${i.toString()}: ${tokenAddress}`);
            
            // Check token details
            const token = await ethers.getContractAt("SecurityToken", tokenAddress);
            
            try {
              const name = await token.name();
              const symbol = await token.symbol();
              const whitelistAddress = await token.whitelistAddress();
              
              console.log(`  Name: ${name}`);
              console.log(`  Symbol: ${symbol}`);
              console.log(`  Whitelist: ${whitelistAddress}`);
              
              // Check if the whitelist contract has the proper functions
              try {
                const whitelist = await ethers.getContractAt("Whitelist", whitelistAddress);
                
                // Test a function call to see if the whitelist is working
                const hasAddToWhitelist = await whitelist.hasRole(
                  ethers.keccak256(ethers.toUtf8Bytes("AGENT_ROLE")),
                  deployer.address
                ).catch(() => "function not found");
                
                console.log(`  Whitelist working: ${hasAddToWhitelist !== "function not found"}`);
              } catch (error) {
                console.log(`  Whitelist error: ${error.message}`);
              }
              
              console.log(""); // Add empty line between tokens
            } catch (error) {
              console.log(`  Token read error: ${error.message}`);
            }
          } catch (error) {
            console.log(`Error with token ${i.toString()}: ${error.message}`);
          }
        }
      }
    } catch (error) {
      console.log("Failed to get token count:", error.message);
    }
  } catch (error) {
    console.log("Not a valid SecurityTokenFactory contract:", error.message);
  }
  
  console.log("\nListing complete.");
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 