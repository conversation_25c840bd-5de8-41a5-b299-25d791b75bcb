import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

// GET /api/client/wallet/[address] - Check if a specific wallet address is verified for the current user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { address } = await params;
    const userEmail = session.user.email;

    // Get client profile from admin panel
    const response = await fetch(
      `${process.env.ADMIN_API_BASE_URL}/clients?search=${encodeURIComponent(userEmail)}&limit=1`
    );

    if (!response.ok) {
      throw new Error(`Admin API error: ${response.status}`);
    }

    const data = await response.json();
    const client = data.clients?.[0];

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Check if the provided address matches the client's wallet address
    const isMatching = client.walletAddress?.toLowerCase() === address.toLowerCase();
    const isVerified = isMatching && !!client.walletVerifiedAt;

    return NextResponse.json({
      address: client.walletAddress,
      verified: isVerified,
      verifiedAt: client.walletVerifiedAt,
      isMatching,
    });
  } catch (error) {
    console.error('Error checking wallet status:', error);
    return NextResponse.json(
      { error: 'Failed to check wallet status' },
      { status: 500 }
    );
  }
}
