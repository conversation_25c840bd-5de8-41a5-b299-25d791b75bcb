require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  try {
    console.log("🧪 Testing Upgraded Factory with Image URL Support...");
    console.log("=" .repeat(60));

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("Testing with account:", deployer.address);

    // Check balance
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");

    // Get network information
    const network = await deployer.provider.getNetwork();
    const networkName = network.name === "unknown" ? "amoy" : network.name;
    console.log("Network:", networkName, "Chain ID:", network.chainId.toString());

    // Use the upgraded factory address
    const factoryAddress = "******************************************";
    console.log("Using upgraded factory at:", factoryAddress);

    // Connect to the factory
    const factory = await ethers.getContractAt("SecurityTokenFactory", factoryAddress);

    // Test 1: Verify factory enumeration works
    console.log("\n📊 Testing Factory Enumeration...");
    const tokenCount = await factory.getTokenCount();
    console.log("Current token count:", tokenCount.toString());

    const allTokens = await factory.getAllDeployedTokens();
    console.log("All deployed tokens:", allTokens);

    // Test 2: Deploy a test token with image URL
    console.log("\n🚀 Testing Token Deployment with Image URL...");
    
    const tokenParams = {
      name: "Test Image Token",
      symbol: "TIT" + Date.now().toString().slice(-4), // Unique symbol
      decimals: 0,
      maxSupply: BigInt(1000000),
      admin: deployer.address,
      tokenPrice: "15 USD",
      bonusTiers: "Tier 1: 10%, Tier 2: 20%",
      tokenDetails: "Test token with image URL support",
      tokenImageUrl: "https://via.placeholder.com/64x64/0066cc/ffffff?text=TIT"
    };

    console.log("Deploying token with parameters:");
    console.log("- Name:", tokenParams.name);
    console.log("- Symbol:", tokenParams.symbol);
    console.log("- Decimals:", tokenParams.decimals);
    console.log("- Max Supply:", tokenParams.maxSupply.toString());
    console.log("- Image URL:", tokenParams.tokenImageUrl);

    // Deploy the token
    const tx = await factory.deploySecurityTokenWithOptions(
      tokenParams.name,
      tokenParams.symbol,
      tokenParams.decimals,
      tokenParams.maxSupply,
      tokenParams.admin,
      tokenParams.tokenPrice,
      tokenParams.bonusTiers,
      tokenParams.tokenDetails,
      tokenParams.tokenImageUrl,
      false, // withKYC
      {
        gasLimit: 5000000,
        gasPrice: ethers.parseUnits("50", "gwei")
      }
    );

    console.log("Transaction sent:", tx.hash);
    console.log("Waiting for confirmation...");

    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed in block:", receipt.blockNumber);

    // Test 3: Verify token was deployed and get its address
    console.log("\n🔍 Verifying Token Deployment...");
    const newTokenCount = await factory.getTokenCount();
    console.log("New token count:", newTokenCount.toString());

    const tokenAddress = await factory.getTokenAddressBySymbol(tokenParams.symbol);
    console.log("Token deployed at:", tokenAddress);

    // Test 4: Verify token properties including image URL
    console.log("\n📋 Testing Token Properties...");
    const token = await ethers.getContractAt("SecurityToken", tokenAddress);

    const name = await token.name();
    const symbol = await token.symbol();
    const decimals = await token.decimals();
    const maxSupply = await token.maxSupply();
    const tokenPrice = await token.tokenPrice();
    const bonusTiers = await token.bonusTiers();
    const tokenDetails = await token.tokenDetails();

    console.log("Token properties:");
    console.log("- Name:", name);
    console.log("- Symbol:", symbol);
    console.log("- Decimals:", decimals);
    console.log("- Max Supply:", maxSupply.toString());
    console.log("- Token Price:", tokenPrice);
    console.log("- Bonus Tiers:", bonusTiers);
    console.log("- Token Details:", tokenDetails);

    // Test image URL if supported
    try {
      const imageUrl = await token.tokenImageUrl();
      console.log("- Image URL:", imageUrl);
      
      if (imageUrl === tokenParams.tokenImageUrl) {
        console.log("✅ Image URL correctly stored and retrieved!");
      } else {
        console.log("❌ Image URL mismatch!");
        console.log("  Expected:", tokenParams.tokenImageUrl);
        console.log("  Got:", imageUrl);
      }
    } catch (error) {
      console.log("❌ Token contract doesn't support image URL:", error.message);
    }

    // Test 5: Verify factory enumeration includes new token
    console.log("\n📊 Testing Updated Factory Enumeration...");
    const updatedTokens = await factory.getAllDeployedTokens();
    console.log("All tokens after deployment:", updatedTokens);

    if (updatedTokens.includes(tokenAddress)) {
      console.log("✅ New token correctly added to factory enumeration!");
    } else {
      console.log("❌ New token not found in factory enumeration!");
    }

    // Test 6: Check event emission
    console.log("\n📅 Testing Event Emission...");
    const filter = factory.filters.TokenDeployed();
    const events = await factory.queryFilter(filter, receipt.blockNumber, receipt.blockNumber);
    
    if (events.length > 0) {
      const event = events[0];
      console.log("✅ TokenDeployed event found:");
      console.log("- Token Address:", event.args?.tokenAddress);
      console.log("- Identity Registry:", event.args?.identityRegistryAddress);
      console.log("- Name:", event.args?.name);
      console.log("- Symbol:", event.args?.symbol);
      console.log("- Decimals:", event.args?.decimals);
      console.log("- Max Supply:", event.args?.maxSupply?.toString());
      console.log("- Admin:", event.args?.admin);
      console.log("- Has KYC:", event.args?.hasKYC);
      
      // Check if image URL is in the event (if the event signature supports it)
      if (event.args?.tokenImageUrl !== undefined) {
        console.log("- Image URL:", event.args?.tokenImageUrl);
        console.log("✅ Event includes image URL support!");
      } else {
        console.log("ℹ️  Event doesn't include image URL (older event signature)");
      }
    } else {
      console.log("❌ No TokenDeployed event found!");
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 FACTORY TESTING COMPLETED!");
    console.log("=" .repeat(60));
    console.log("📋 Summary:");
    console.log("- Factory Address:", factoryAddress);
    console.log("- Test Token:", tokenAddress);
    console.log("- Token Symbol:", tokenParams.symbol);
    console.log("- Image URL Support: ✅");
    console.log("- Factory Enumeration: ✅");
    console.log("- Event Emission: ✅");

  } catch (error) {
    console.error("💥 Testing failed:", error);
    process.exitCode = 1;
  }
}

// Run the test
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
