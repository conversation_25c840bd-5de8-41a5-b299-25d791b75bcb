"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard.tsx":
/*!**************************************!*\
  !*** ./src/components/Dashboard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _KYCModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./KYCModal */ \"(app-pages-browser)/./src/components/KYCModal.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/MockAuthProvider */ \"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const mockAuth = useMockAuth ? (0,_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showKYCModal, setShowKYCModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOrderModal, setShowOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTokenForOrder, setSelectedTokenForOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderAmount, setOrderAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmittingOrder, setIsSubmittingOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderError, setOrderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"Dashboard.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"Dashboard.useQuery\"],\n        enabled: !!user\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (user) {\n                fetchTokens();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress,\n        user\n    ]); // Refetch when wallet address changes\n    const fetchTokens = async ()=>{\n        try {\n            setLoading(true);\n            // Construct URL with proper query parameters\n            const params = new URLSearchParams();\n            if (clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress) {\n                params.append('testWallet', clientProfile.walletAddress);\n            }\n            params.append('_t', Date.now().toString());\n            const url = \"/api/tokens?\".concat(params.toString());\n            console.log('Fetching tokens from:', url);\n            const response = await fetch(url);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Token fetch error:', response.status, errorText);\n                throw new Error(\"Failed to fetch tokens: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log('Fetched tokens:', data);\n            setTokens(data);\n        } catch (err) {\n            console.error('Error in fetchTokens:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        const numPrice = parseFloat(price);\n        // Handle crypto currencies that don't have standard currency codes\n        const cryptoCurrencies = [\n            'ETH',\n            'BTC',\n            'USDC',\n            'USDT',\n            'DAI'\n        ];\n        if (cryptoCurrencies.includes(currency.toUpperCase())) {\n            return \"\".concat(numPrice, \" \").concat(currency.toUpperCase());\n        }\n        // Handle standard fiat currencies\n        const supportedCurrencies = [\n            'USD',\n            'EUR',\n            'GBP',\n            'JPY',\n            'CAD',\n            'AUD'\n        ];\n        const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(numPrice);\n    };\n    const formatSupply = function(supply) {\n        let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const numSupply = parseFloat(supply);\n        // Handle very large numbers (like 1000000000000000000000000)\n        if (decimals > 0 && numSupply > 1000000000000) {\n            // This is likely already in wei/smallest unit, convert to human readable\n            const humanReadable = numSupply / Math.pow(10, decimals);\n            return new Intl.NumberFormat('en-US', {\n                maximumFractionDigits: 0\n            }).format(humanReadable);\n        }\n        // For normal numbers or 0 decimals, display as-is\n        return new Intl.NumberFormat('en-US', {\n            maximumFractionDigits: 0\n        }).format(numSupply);\n    };\n    const getCategoryColor = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return 'bg-amber-100 text-amber-800';\n            case 'real estate':\n            case 'realestate':\n                return 'bg-green-100 text-green-800';\n            case 'equity':\n            case 'equities':\n                return 'bg-blue-100 text-blue-800';\n            case 'debt':\n            case 'bonds':\n                return 'bg-purple-100 text-purple-800';\n            case 'fund':\n            case 'funds':\n                return 'bg-indigo-100 text-indigo-800';\n            case 'security':\n            case 'securities':\n                return 'bg-teal-100 text-teal-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getDefaultImage = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return '🏗️';\n            case 'real estate':\n            case 'realestate':\n                return '🏢';\n            case 'equity':\n            case 'equities':\n                return '📈';\n            case 'debt':\n            case 'bonds':\n                return '💰';\n            case 'fund':\n            case 'funds':\n                return '🏦';\n            case 'security':\n            case 'securities':\n                return '🛡️';\n            default:\n                return '🪙';\n        }\n    };\n    if (!user) {\n        return null; // AppLayout handles authentication\n    }\n    const handleOrderSubmit = async ()=>{\n        if (!selectedTokenForOrder || !orderAmount || !(clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.id)) {\n            setOrderError('Missing token, amount, or client information.');\n            return;\n        }\n        // Validate orderAmount is a positive number\n        const amountNumber = parseFloat(orderAmount);\n        if (isNaN(amountNumber) || amountNumber <= 0) {\n            setOrderError('Please enter a valid positive amount.');\n            return;\n        }\n        // Check if amount exceeds max supply\n        if (amountNumber > Number(selectedTokenForOrder.maxSupply)) {\n            setOrderError(\"Cannot order more than \".concat(selectedTokenForOrder.maxSupply, \" tokens\"));\n            return;\n        }\n        // Check if user is whitelisted for this token\n        if (!selectedTokenForOrder.isWhitelisted) {\n            setOrderError(\"You must be whitelisted for this token before placing an order. Please contact support to get whitelisted for \".concat(selectedTokenForOrder.name, \".\"));\n            return;\n        }\n        setIsSubmittingOrder(true);\n        setOrderError(null);\n        try {\n            const response = await fetch('/api/client-orders', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: selectedTokenForOrder.id,\n                    clientId: clientProfile.id,\n                    tokensOrdered: orderAmount\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to submit order');\n            }\n            // Order submitted successfully\n            setShowOrderModal(false);\n            setSelectedTokenForOrder(null);\n            setOrderAmount('');\n            // Show success message with order details\n            const totalAmount = formatPrice((amountNumber * Number(selectedTokenForOrder.price)).toString(), selectedTokenForOrder.currency);\n            alert(\"Order submitted successfully!\\n\\nToken: \".concat(selectedTokenForOrder.name, \"\\nAmount: \").concat(orderAmount, \" tokens\\nTotal: \").concat(totalAmount, \"\\n\\nYou will be notified once it is approved.\"));\n        } catch (err) {\n            console.error('Error submitting order:', err);\n            setOrderError(err.message || 'Failed to submit order. Please try again.');\n        } finally{\n            setIsSubmittingOrder(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Discover and invest in available security tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 mb-1\",\n                                            children: \"Wallet Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    user && clientProfile && tokens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-blue-900 mb-1\",\n                                            children: \"Available Investment Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-700 text-sm\",\n                                            children: [\n                                                \"You have access to \",\n                                                tokens.length,\n                                                \" investment \",\n                                                tokens.length === 1 ? 'opportunity' : 'opportunities'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-900\",\n                                            children: tokens.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Available Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error loading tokens\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: tokens.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83E\\uDE99\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No tokens available\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Check back later for new investment opportunities.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: tokens.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n                                    children: token.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: token.imageUrl,\n                                        alt: token.name,\n                                        className: \"w-24 h-24 object-cover rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 23\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl\",\n                                        children: getDefaultImage(token.category)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                            children: token.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 font-mono\",\n                                                            children: token.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-1 items-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getCategoryColor(token.category)),\n                                                            children: token.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        token.needsQualification ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                            children: \"\\uD83D\\uDD12 NEEDS QUALIFICATION\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 27\n                                                        }, this) : token.hasRequiredClaims ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                                                            children: \"✅ QUALIFIED\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                            children: \"⏳ CHECKING...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: formatPrice(token.price, token.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"per token\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Total Supply\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: formatSupply(token.totalSupply, token.decimals)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Max Supply\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: formatSupply(token.maxSupply, token.decimals)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 21\n                                        }, this),\n                                        token.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4 line-clamp-2\",\n                                            children: token.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 23\n                                        }, this),\n                                        user && clientProfile ? // User is logged in and has profile\n                                        token.needsQualification ? // Token requires qualification\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                // Redirect to qualification page for this specific token\n                                                const params = new URLSearchParams({\n                                                    token: token.address,\n                                                    tokenName: token.name,\n                                                    tokenSymbol: token.symbol\n                                                });\n                                                window.location.href = \"/qualification?\".concat(params.toString());\n                                            },\n                                            className: \"w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium\",\n                                            children: \"\\uD83D\\uDD12 Complete Qualification\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 25\n                                        }, this) : token.hasRequiredClaims ? // User is qualified for this token\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setSelectedTokenForOrder(token);\n                                                setShowOrderModal(true);\n                                            },\n                                            className: \"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                            children: \"\\uD83D\\uDE80 Invest Now\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 25\n                                        }, this) : // Checking qualification status\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            disabled: true,\n                                            className: \"w-full bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium\",\n                                            children: \"⏳ Checking Qualification...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 25\n                                        }, this) : // User not logged in or no profile\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (!user) {\n                                                    // Redirect to login\n                                                    window.location.href = '/api/auth/login';\n                                                } else {\n                                                    // Redirect to token-specific qualification flow\n                                                    const params = new URLSearchParams({\n                                                        token: token.address,\n                                                        tokenName: token.name,\n                                                        tokenSymbol: token.symbol\n                                                    });\n                                                    window.location.href = \"/qualification?\".concat(params.toString());\n                                                }\n                                            },\n                                            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                            children: !user ? 'Sign In to Invest' : 'Complete Initial Qualification'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-3 bg-gray-50 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Network: \",\n                                                    token.network\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Decimals: \",\n                                                    token.decimals\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, token.id, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 17\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false),\n            showKYCModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCModal__WEBPACK_IMPORTED_MODULE_3__.KYCModal, {\n                onClose: ()=>setShowKYCModal(false),\n                existingProfile: clientProfile\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this),\n            showOrderModal && selectedTokenForOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900\",\n                                        children: [\n                                            \"Order \",\n                                            selectedTokenForOrder.name,\n                                            \" (\",\n                                            selectedTokenForOrder.symbol,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Token:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.name,\n                                                            \" (\",\n                                                            selectedTokenForOrder.symbol,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formatPrice(selectedTokenForOrder.price, selectedTokenForOrder.currency),\n                                                            \" per token\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Available:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.maxSupply,\n                                                            \" tokens\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"order-amount\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Number of Tokens to Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"order-amount\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                step: \"1\",\n                                                value: orderAmount,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    setOrderAmount(value);\n                                                    setOrderError(null);\n                                                },\n                                                placeholder: \"Enter amount of tokens\",\n                                                className: \"block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 17\n                                    }, this),\n                                    orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" \",\n                                                    (()=>{\n                                                        const tokenPrice = Number(selectedTokenForOrder.price);\n                                                        const orderQty = Number(orderAmount);\n                                                        if (isNaN(tokenPrice) || isNaN(orderQty)) {\n                                                            return \"Error: Price=\".concat(selectedTokenForOrder.price, \", Qty=\").concat(orderAmount);\n                                                        }\n                                                        const total = orderQty * tokenPrice;\n                                                        return formatPrice(total.toString(), selectedTokenForOrder.currency);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: \"This order will be submitted for admin approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 19\n                                    }, this),\n                                    orderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: orderError\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOrderSubmit,\n                                        disabled: isSubmittingOrder || !orderAmount,\n                                        className: \"w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmittingOrder ? 'Submitting...' : 'Submit Order'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 510,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {}, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"ApMYDulHuTrKf3ifJxh2CXa6xLY=\", false, function() {\n    return [\n        _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser,\n        _providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.useMockUser,\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard.tsx\n"));

/***/ })

});