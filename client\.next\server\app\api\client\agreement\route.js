/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/agreement/route";
exports.ids = ["app/api/client/agreement/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fagreement%2Froute&page=%2Fapi%2Fclient%2Fagreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fagreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fagreement%2Froute&page=%2Fapi%2Fclient%2Fagreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fagreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_agreement_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/agreement/route.ts */ \"(rsc)/./src/app/api/client/agreement/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/agreement/route\",\n        pathname: \"/api/client/agreement\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/agreement/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\agreement\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_agreement_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjbGllbnQlMkZhZ3JlZW1lbnQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmNsaWVudCUyRmFncmVlbWVudCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmNsaWVudCUyRmFncmVlbWVudCUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDZ2l0aHViJTVDdG9rZW5kZXYtbmV3cm9vJTVDY2xpZW50JTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDZ2l0aHViJTVDdG9rZW5kZXYtbmV3cm9vJTVDY2xpZW50JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUMrQjtBQUM1RztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcZ2l0aHViXFxcXHRva2VuZGV2LW5ld3Jvb1xcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2xpZW50XFxcXGFncmVlbWVudFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY2xpZW50L2FncmVlbWVudC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NsaWVudC9hZ3JlZW1lbnRcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NsaWVudC9hZ3JlZW1lbnQvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJEOlxcXFxnaXRodWJcXFxcdG9rZW5kZXYtbmV3cm9vXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjbGllbnRcXFxcYWdyZWVtZW50XFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fagreement%2Froute&page=%2Fapi%2Fclient%2Fagreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fagreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/agreement/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/client/agreement/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Mock database - in production, this would be a real database\nlet agreementAcceptances = {};\n// GET /api/client/agreement - Get agreement acceptance status\nasync function GET(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const userId = session.user.sub;\n        const userEmail = session.user.email;\n        console.log('📋 Fetching agreement status for:', {\n            userId,\n            userEmail\n        });\n        // First try to get from admin panel database\n        try {\n            const adminApiUrl = process.env.ADMIN_API_BASE_URL;\n            const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`, {\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (searchResponse.ok) {\n                const searchData = await searchResponse.json();\n                const client = searchData.clients?.[0];\n                if (client && client.agreementAccepted !== undefined) {\n                    console.log('✅ Found agreement status in admin panel:', client.agreementAccepted);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        accepted: client.agreementAccepted,\n                        acceptedAt: client.agreementAcceptedAt\n                    });\n                }\n            }\n        } catch (adminError) {\n            if (adminError instanceof Error) {\n                console.log('⚠️ Could not fetch from admin panel, falling back to local storage:', adminError.message);\n            } else {\n                console.log('⚠️ Could not fetch from admin panel, falling back to local storage:', adminError);\n            }\n        }\n        // Fallback to local storage\n        const acceptance = agreementAcceptances[userId];\n        if (!acceptance) {\n            console.log('📋 No agreement status found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                accepted: false,\n                acceptedAt: null\n            });\n        }\n        console.log('📋 Found agreement status in local storage:', acceptance.accepted);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            accepted: acceptance.accepted,\n            acceptedAt: acceptance.acceptedAt\n        });\n    } catch (error) {\n        console.error('Error fetching agreement status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/client/agreement - Accept agreement\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { accepted } = body;\n        if (typeof accepted !== 'boolean') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request data'\n            }, {\n                status: 400\n            });\n        }\n        const userId = session.user.sub;\n        const userEmail = session.user.email;\n        console.log('📝 Agreement acceptance request:', {\n            userId,\n            userEmail,\n            accepted\n        });\n        // Store agreement acceptance locally (for immediate response)\n        const acceptedAt = new Date().toISOString();\n        agreementAcceptances[userId] = {\n            userId,\n            accepted,\n            acceptedAt\n        };\n        // Sync with admin panel database\n        try {\n            const adminApiUrl = process.env.ADMIN_API_BASE_URL;\n            console.log('🔄 Syncing agreement acceptance to admin panel:', adminApiUrl);\n            // First, get the client by email to get their ID\n            const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`, {\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (searchResponse.ok) {\n                const searchData = await searchResponse.json();\n                const client = searchData.clients?.[0];\n                if (client) {\n                    console.log('✅ Found client in admin panel:', client.id);\n                    // Update the client's agreement status\n                    const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}`, {\n                        method: 'PUT',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            agreementAccepted: accepted,\n                            agreementAcceptedAt: acceptedAt\n                        })\n                    });\n                    if (updateResponse.ok) {\n                        console.log('✅ Agreement acceptance synced to admin panel');\n                    } else {\n                        console.log('❌ Failed to sync agreement to admin panel:', updateResponse.status);\n                    }\n                } else {\n                    console.log('⚠️ Client not found in admin panel, creating new client record...');\n                    // Create a basic client record with agreement acceptance\n                    const createResponse = await fetch(`${adminApiUrl}/clients`, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            email: userEmail,\n                            firstName: session.user.given_name || 'Unknown',\n                            lastName: session.user.family_name || 'User',\n                            agreementAccepted: accepted,\n                            agreementAcceptedAt: acceptedAt,\n                            // Add minimal required fields for client creation that pass validation\n                            gender: 'PREFER_NOT_TO_SAY',\n                            nationality: 'Unknown',\n                            birthday: new Date('1990-01-01').toISOString().split('T')[0],\n                            birthPlace: 'Unknown',\n                            identificationType: 'PASSPORT',\n                            passportNumber: `TEMP_${Date.now()}`,\n                            documentExpiration: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000 * 10).toISOString().split('T')[0],\n                            phoneNumber: '******-000-0000',\n                            occupation: 'Not provided',\n                            sectorOfActivity: 'Not provided',\n                            pepStatus: 'NOT_PEP',\n                            street: 'Not provided',\n                            buildingNumber: '1',\n                            city: 'Not provided',\n                            country: 'Unknown',\n                            zipCode: '00000',\n                            sourceOfWealth: 'Not provided',\n                            bankAccountNumber: `TEMP_${Date.now()}`,\n                            sourceOfFunds: 'Not provided',\n                            taxIdentificationNumber: `TEMP_${Date.now()}`\n                        })\n                    });\n                    if (createResponse.ok) {\n                        console.log('✅ Created new client record with agreement acceptance');\n                    } else {\n                        const errorText = await createResponse.text();\n                        console.log('❌ Failed to create client record:', createResponse.status, errorText);\n                    }\n                }\n            } else {\n                console.log('❌ Failed to search for client in admin panel:', searchResponse.status);\n            }\n        } catch (syncError) {\n            if (syncError instanceof Error) {\n                console.error('❌ Error syncing with admin panel:', syncError.message);\n            } else {\n                console.error('❌ Error syncing with admin panel:', syncError);\n            }\n        // Don't fail the request if sync fails - agreement is still stored locally\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            accepted: true,\n            acceptedAt: acceptedAt\n        });\n    } catch (error) {\n        console.error('Error accepting agreement:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/agreement/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fagreement%2Froute&page=%2Fapi%2Fclient%2Fagreement%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fagreement%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();