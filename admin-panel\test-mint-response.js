const { PrismaClient } = require('@prisma/client');

async function testMintResponse() {
  console.log('🧪 Testing Mint API Response');
  console.log('=============================');

  const prisma = new PrismaClient();

  try {
    // 1. Find a CONFIRMED order to test with
    console.log('1️⃣ Looking for CONFIRMED orders...');
    
    const confirmedOrders = await prisma.order.findMany({
      where: {
        status: 'CONFIRMED'
      },
      include: {
        client: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        },
        token: {
          select: {
            name: true,
            symbol: true,
            address: true
          }
        }
      },
      take: 1
    });

    if (confirmedOrders.length === 0) {
      console.log('❌ No CONFIRMED orders found');
      console.log('💡 Create an order and approve it first');
      return;
    }

    const order = confirmedOrders[0];
    console.log(`✅ Found CONFIRMED order: ${order.client.firstName} ${order.client.lastName} - ${order.token.name}`);
    console.log(`   Order ID: ${order.id}`);
    console.log(`   Client wallet: ${order.client.walletAddress || 'NOT SET'}`);
    console.log(`   Token address: ${order.token.address}`);
    console.log(`   Tokens to mint: ${order.tokensOrdered}`);

    if (!order.client.walletAddress) {
      console.log('❌ Client has no wallet address - cannot mint');
      console.log('💡 Add a wallet address to the client first');
      return;
    }

    // 2. Test the mint API endpoint (but don't actually mint)
    console.log('\n2️⃣ Testing mint API structure...');
    
    const mintPayload = {
      tokenAddress: order.token.address,
      recipientAddress: order.client.walletAddress,
      amount: order.tokensOrdered,
      orderId: order.id
    };

    console.log('Mint payload:', mintPayload);
    console.log('✅ Mint payload structure is correct');

    // 3. Check if we have any MINTED orders with transaction hashes
    console.log('\n3️⃣ Checking existing MINTED orders...');
    
    const mintedOrders = await prisma.order.findMany({
      where: {
        status: 'MINTED'
      },
      select: {
        id: true,
        transactionHash: true,
        blockNumber: true,
        client: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        token: {
          select: {
            name: true,
            symbol: true
          }
        }
      }
    });

    console.log(`Found ${mintedOrders.length} MINTED orders:`);
    mintedOrders.forEach((order, index) => {
      console.log(`  ${index + 1}. ${order.client.firstName} ${order.client.lastName} - ${order.token.name}`);
      console.log(`     Tx Hash: ${order.transactionHash || 'NULL'}`);
      console.log(`     Block: ${order.blockNumber || 'NULL'}`);
    });

    // 4. Test API endpoint response
    console.log('\n4️⃣ Testing orders API response...');
    
    try {
      const response = await fetch('http://localhost:6677/api/orders?limit=3');
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Orders API is working');
        
        const sampleOrder = data.orders[0];
        if (sampleOrder) {
          console.log('Sample order fields:');
          console.log('  - id:', !!sampleOrder.id);
          console.log('  - status:', !!sampleOrder.status);
          console.log('  - transactionHash:', sampleOrder.transactionHash !== undefined ? 'present' : 'missing');
          console.log('  - blockNumber:', sampleOrder.blockNumber !== undefined ? 'present' : 'missing');
        }
      } else {
        console.log('❌ Orders API failed:', response.status);
      }
    } catch (error) {
      console.log('❌ Orders API error:', error.message);
    }

    console.log('\n💡 To test minting:');
    console.log('1. Go to http://localhost:6677/orders');
    console.log('2. Find the CONFIRMED order');
    console.log('3. Click MINT button');
    console.log('4. Check the popup for transaction hash');
    console.log('5. Refresh page to see Tx button');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testMintResponse().catch(console.error);
