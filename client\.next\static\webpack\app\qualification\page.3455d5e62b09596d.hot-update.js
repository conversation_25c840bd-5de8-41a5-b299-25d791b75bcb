"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [qualificationStatus, setQualificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PENDING');\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                // Try to get more recent data from localStorage first\n                const storageKey = \"qualification_progress_\".concat(tokenAddress);\n                let localProgress = null;\n                try {\n                    const stored = localStorage.getItem(storageKey);\n                    if (stored) {\n                        localProgress = JSON.parse(stored);\n                        console.log('📱 Found localStorage progress:', localProgress);\n                    }\n                } catch (error) {\n                    console.error('Error reading localStorage:', error);\n                }\n                // Use localStorage data if it's more recent, otherwise use API data\n                const progressToUse = localProgress || qualificationProgress;\n                const newStepData = {\n                    country: progressToUse.country || '',\n                    agreementAccepted: progressToUse.agreementAccepted || false,\n                    profileCompleted: progressToUse.profileCompleted || !!profile,\n                    walletConnected: progressToUse.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: progressToUse.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set qualification status\n                if (progressToUse.qualificationStatus) {\n                    setQualificationStatus(progressToUse.qualificationStatus);\n                }\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = progressToUse.currentStep || 0;\n                // Allow users to progress through all steps without blocking\n                // Only auto-advance to next incomplete step if current step is completed\n                if (calculatedStep === 0 && newStepData.country) {\n                    calculatedStep = 1; // Move to agreement if country is selected\n                } else if (calculatedStep === 1 && newStepData.agreementAccepted) {\n                    calculatedStep = 2; // Move to profile if agreement is accepted\n                } else if (calculatedStep === 2 && newStepData.profileCompleted) {\n                    calculatedStep = 3; // Move to wallet if profile is completed\n                } else if (calculatedStep === 3 && newStepData.walletConnected) {\n                    calculatedStep = 4; // Move to KYC if wallet is connected\n                } else if (calculatedStep === 4 && newStepData.kycCompleted) {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: progressToUse,\n                    source: localProgress ? 'localStorage' : 'API'\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus,\n        tokenAddress\n    ]);\n    // Function to manually fix qualification progress flags\n    const fixQualificationFlags = async ()=>{\n        if (!profile || !tokenAddress) {\n            console.error('❌ Missing profile or token address');\n            return;\n        }\n        try {\n            console.log('🔧 Attempting to fix qualification progress flags...');\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userEmail: profile.email,\n                    tokenAddress: tokenAddress\n                })\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log('✅ Qualification progress flags fixed successfully:', result);\n                // Reload the page to see the updated data\n                window.location.reload();\n            } else {\n                console.error('❌ Failed to fix qualification progress flags:', response.status);\n            }\n        } catch (error) {\n            console.error('❌ Error fixing qualification progress flags:', error);\n        }\n    };\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            // Ensure we have the correct completion flags based on current state\n            // For country: check if it was explicitly completed OR if there's a country value\n            const countryCompleted = updatedStepData.countryCompleted === true || updatedStepData.country && updatedStepData.country !== '' || stepData.countryCompleted === true || stepData.country && stepData.country !== '';\n            const agreementAccepted = updatedStepData.agreementAccepted === true || stepData.agreementAccepted === true;\n            const profileCompleted = updatedStepData.profileCompleted === true || stepData.profileCompleted === true || !!profile;\n            const walletConnected = updatedStepData.walletConnected === true || stepData.walletConnected === true || !!(profile === null || profile === void 0 ? void 0 : profile.walletAddress);\n            const kycCompleted = updatedStepData.kycCompleted === true || stepData.kycCompleted === true || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED';\n            // Calculate completed steps based on actual step completion flags\n            const stepCompletionFlags = [\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted\n            ];\n            const actualCompletedSteps = stepCompletionFlags.filter(Boolean).length;\n            const progressData = {\n                ...updatedStepData,\n                // Ensure all completion flags are explicitly set\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                // Preserve country value if it exists\n                country: updatedStepData.country || stepData.country || '',\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: actualCompletedSteps\n            };\n            console.log('💾 Saving progress to database:', progressData);\n            console.log('🔍 Step completion flags:', {\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                actualCompletedSteps\n            });\n            // Save to backend database via admin panel API\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully to database:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    // Step navigation functions\n    const canNavigateToStep = (stepIndex)=>{\n        // Users can always navigate to completed steps or the next incomplete step\n        if (stepIndex === 0) return true; // Country selection always available\n        if (stepIndex === 1) return stepData.country !== ''; // Agreement if country selected\n        if (stepIndex === 2) return stepData.agreementAccepted; // Profile if agreement accepted\n        if (stepIndex === 3) return stepData.profileCompleted; // Wallet if profile completed\n        if (stepIndex === 4) return stepData.walletConnected; // KYC if wallet connected\n        if (stepIndex === 5) return stepData.kycCompleted; // Completion if KYC done\n        return false;\n    };\n    const handleStepClick = (stepIndex)=>{\n        if (canNavigateToStep(stepIndex)) {\n            setCurrentStep(stepIndex);\n            // Force save with correct completion flags based on current state\n            const updatedData = {\n                ...stepData,\n                // Ensure all completion flags are set based on current state\n                countryCompleted: stepData.countryCompleted || stepData.country && stepData.country !== '' || completedSteps >= 1,\n                agreementAccepted: stepData.agreementAccepted || completedSteps >= 2,\n                profileCompleted: stepData.profileCompleted || !!profile || completedSteps >= 3,\n                walletConnected: stepData.walletConnected || !!(profile === null || profile === void 0 ? void 0 : profile.walletAddress) || completedSteps >= 4,\n                kycCompleted: stepData.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED' || completedSteps >= 5\n            };\n            console.log('🔧 Forcing save with correct completion flags:', updatedData);\n            saveProgress(updatedData, stepIndex);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 407,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 406,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleStepClick(index),\n                        className: \"p-4 rounded-lg border text-center transition-all duration-200 \".concat(getStepColor(step.status), \" \").concat(canNavigateToStep(index) ? 'cursor-pointer hover:shadow-md hover:scale-105' : 'cursor-not-allowed opacity-60'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            canNavigateToStep(index) && index !== currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 font-medium\",\n                                children: \"Click to navigate\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            qualificationStatus === 'APPROVED' || qualificationStatus === 'FORCE_APPROVED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Qualification Approved!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: [\n                                            \"Congratulations! Your qualification for \",\n                                            tokenName || 'this token',\n                                            \" has been approved. You are now eligible to invest in this token.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-green-800\",\n                                                            children: \"Approved for Investment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-green-700\",\n                                                            children: \"You can now proceed to invest in this token through the platform.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : qualificationStatus === 'REJECTED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(XCircleIcon, {\n                                        className: \"h-16 w-16 text-red-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Qualification Rejected\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: [\n                                            \"Unfortunately, your qualification for \",\n                                            tokenName || 'this token',\n                                            \" has been rejected. Please contact support for more information.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(XCircleIcon, {\n                                                    className: \"h-5 w-5 text-red-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-red-800\",\n                                                            children: \"Application Rejected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-700\",\n                                                            children: \"Please review your information and contact support if needed.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Qualification Submitted!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: [\n                                            \"You have successfully completed all qualification steps for \",\n                                            tokenName || 'this token',\n                                            \". Your application is now pending admin review and approval.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-yellow-800\",\n                                                            children: \"Pending Admin Approval\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-yellow-700\",\n                                                            children: \"An administrator will review your qualification and approve you for token investment.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = '/',\n                                        className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"Return to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fixQualificationFlags,\n                                        className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: \"Fix Progress Flags\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 465,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 417,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"iWBKLqK5TA8lhoQ0oRF1sD9fiNc=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});