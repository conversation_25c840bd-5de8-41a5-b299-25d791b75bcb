import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const userEmail = session.user.email;

    // Call admin panel API to get qualification progress
    const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';
    const params = new URLSearchParams({
      clientEmail: userEmail,
    });

    if (tokenAddress) {
      params.append('tokenAddress', tokenAddress);
    }

    const response = await fetch(`${adminApiUrl}/qualification-progress?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Admin API responded with status: ${response.status}`);
    }

    const progressData = await response.json();

    console.log('📊 Retrieved qualification progress from admin API:', {
      userEmail,
      tokenAddress,
      currentStep: progressData.currentStep,
      completedSteps: progressData.completedSteps
    });

    return NextResponse.json(progressData);
  } catch (error) {
    console.error('Error fetching qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualification progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const userEmail = session.user.email;

    // Call admin panel API to save qualification progress
    const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';

    const requestData = {
      ...body,
      clientEmail: userEmail,
    };

    const response = await fetch(`${adminApiUrl}/qualification-progress`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`Admin API responded with status: ${response.status}`);
    }

    const result = await response.json();

    console.log('💾 Saved qualification progress via admin API:', {
      userEmail,
      tokenAddress: body.tokenAddress,
      currentStep: body.currentStep,
      completedSteps: body.completedSteps
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error saving qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to save qualification progress' },
      { status: 500 }
    );
  }
}
