import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const userEmail = session.user.email;

    // Get stored qualification progress from localStorage simulation
    // In a real implementation, this would query the database
    const storageKey = `qualification_progress_${userEmail}_${tokenAddress}`;

    // For now, we'll use a simple in-memory storage simulation
    // In production, this should query the actual database
    const defaultProgress = {
      country: '',
      countryCompleted: false,
      agreementAccepted: false,
      profileCompleted: false,
      walletConnected: false,
      kycCompleted: false,
      currentStep: 0,
      completedSteps: 0,
      tokenAddress: tokenAddress,
      userEmail: userEmail,
      lastUpdated: new Date().toISOString(),
    };

    // Check if user has existing profile (affects profileCompleted status)
    try {
      const profileResponse = await fetch(`${process.env.ADMIN_API_URL || 'http://localhost:6677/api'}/clients?search=${encodeURIComponent(userEmail)}&limit=1`);
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        if (profileData.clients && profileData.clients.length > 0) {
          const client = profileData.clients[0];
          defaultProgress.profileCompleted = true;
          defaultProgress.walletConnected = !!client.walletAddress;
          defaultProgress.kycCompleted = client.kycStatus === 'APPROVED';

          // Update current step based on completion status
          if (defaultProgress.kycCompleted) {
            defaultProgress.currentStep = 5; // All completed
            defaultProgress.completedSteps = 5;
          } else if (defaultProgress.walletConnected) {
            defaultProgress.currentStep = 4; // On KYC step
            defaultProgress.completedSteps = 4;
          } else if (defaultProgress.profileCompleted) {
            defaultProgress.currentStep = 3; // On wallet step
            defaultProgress.completedSteps = 3;
          }
        }
      }
    } catch (error) {
      console.error('Error checking profile status:', error);
    }

    return NextResponse.json(defaultProgress);
  } catch (error) {
    console.error('Error fetching qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualification progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      country,
      countryCompleted,
      agreementAccepted,
      profileCompleted,
      walletConnected,
      kycCompleted,
      tokenAddress,
      currentStep,
      completedSteps
    } = body;

    const userEmail = session.user.email;
    const storageKey = `qualification_progress_${userEmail}_${tokenAddress}`;

    // Create progress object
    const progressData = {
      country: country || '',
      countryCompleted: countryCompleted || false,
      agreementAccepted: agreementAccepted || false,
      profileCompleted: profileCompleted || false,
      walletConnected: walletConnected || false,
      kycCompleted: kycCompleted || false,
      currentStep: currentStep || 0,
      completedSteps: completedSteps || 0,
      tokenAddress: tokenAddress,
      userEmail: userEmail,
      lastUpdated: new Date().toISOString(),
    };

    // In a real implementation, this would save to database
    // For now, we'll log the progress and simulate saving
    console.log('💾 Saving qualification progress:', progressData);

    // TODO: Implement actual database save
    // Example structure:
    // INSERT INTO qualification_progress
    // (user_email, token_address, country, country_completed, agreement_accepted,
    //  profile_completed, wallet_connected, kyc_completed, current_step, completed_steps, updated_at)
    // VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    // ON DUPLICATE KEY UPDATE ...

    return NextResponse.json({
      success: true,
      message: 'Qualification progress saved successfully',
      data: progressData
    });
  } catch (error) {
    console.error('Error saving qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to save qualification progress' },
      { status: 500 }
    );
  }
}
