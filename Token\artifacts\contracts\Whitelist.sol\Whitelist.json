{"_format": "hh-sol-artifact-1", "contractName": "Whitelist", "sourceName": "contracts/Whitelist.sol", "abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "approveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchAdd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchApproveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchFreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRemoveFrom<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchRevokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "name": "batchUnfreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "freezeAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initializeWithAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isKycApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "revokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "unfreeze<PERSON>ddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}