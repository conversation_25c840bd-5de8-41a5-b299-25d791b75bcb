const { ethers } = require('ethers');

async function testRPCConnection() {
  console.log('🔗 Testing RPC Connection');
  console.log('=========================');

  const RPC_URL = process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/';
  console.log(`RPC URL: ${RPC_URL}`);

  try {
    // Test basic connection
    console.log('\n1️⃣ Testing basic RPC connection...');
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    
    // Get network info
    const network = await provider.getNetwork();
    console.log(`✅ Connected to network: ${network.name} (Chain ID: ${network.chainId})`);

    // Get latest block
    const blockNumber = await provider.getBlockNumber();
    console.log(`✅ Latest block: ${blockNumber}`);

    // Test contract call
    console.log('\n2️⃣ Testing contract call...');
    const WHITELIST_CONTRACT = '******************************************';
    const YOUR_WALLET = '******************************************';

    try {
      const WhitelistABI = require('./src/contracts/Whitelist.json');
      const whitelistContract = new ethers.Contract(
        WHITELIST_CONTRACT,
        WhitelistABI.abi,
        provider
      );

      console.log(`Contract address: ${WHITELIST_CONTRACT}`);
      console.log(`Checking wallet: ${YOUR_WALLET}`);

      const isWhitelisted = await whitelistContract.isWhitelisted(YOUR_WALLET);
      console.log(`✅ Whitelist status: ${isWhitelisted ? 'WHITELISTED' : 'NOT WHITELISTED'}`);

      // Test other contract functions
      try {
        const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
        console.log(`✅ AGENT_ROLE: ${AGENT_ROLE}`);
      } catch (error) {
        console.log(`❌ Could not get AGENT_ROLE: ${error.message}`);
      }

    } catch (error) {
      console.log(`❌ Contract call failed: ${error.message}`);
    }

    console.log('\n✅ RPC connection is working!');
    console.log('The issue might be in the API code logic, not the connection.');

  } catch (error) {
    console.log(`❌ RPC connection failed: ${error.message}`);
    
    if (error.message.includes('network')) {
      console.log('\n💡 Possible solutions:');
      console.log('1. Check internet connection');
      console.log('2. Try a different RPC URL:');
      console.log('   - https://polygon-amoy.drpc.org');
      console.log('   - https://rpc.ankr.com/polygon_amoy');
      console.log('3. Check if the RPC service is down');
    }
  }
}

testRPCConnection().catch(console.error);
