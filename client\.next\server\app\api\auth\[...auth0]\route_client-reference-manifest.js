globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/auth/[...auth0]/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js":{"*":{"id":"(ssr)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AppLayout.tsx":{"*":{"id":"(ssr)/./src/components/AppLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/AuthProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/MockAuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ReactQueryProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/ReactQueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WalletProvider.tsx":{"*":{"id":"(ssr)/./src/components/WalletProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard.tsx":{"*":{"id":"(ssr)/./src/components/Dashboard.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\github\\tokendev-newroo\\client\\node_modules\\@auth0\\nextjs-auth0\\dist\\client\\index.js":{"id":"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\components\\AppLayout.tsx":{"id":"(app-pages-browser)/./src/components/AppLayout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\AuthProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\MockAuthProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\components\\providers\\ReactQueryProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/ReactQueryProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\components\\WalletProvider.tsx":{"id":"(app-pages-browser)/./src/components/WalletProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\github\\tokendev-newroo\\client\\src\\components\\Dashboard.tsx":{"id":"(app-pages-browser)/./src/components/Dashboard.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false}},"entryCSSFiles":{"D:\\github\\tokendev-newroo\\client\\src\\":[],"D:\\github\\tokendev-newroo\\client\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\github\\tokendev-newroo\\client\\src\\app\\page":[],"D:\\github\\tokendev-newroo\\client\\src\\app\\api\\auth\\[...auth0]\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js":{"*":{"id":"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AppLayout.tsx":{"*":{"id":"(rsc)/./src/components/AppLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/AuthProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/MockAuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ReactQueryProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/ReactQueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WalletProvider.tsx":{"*":{"id":"(rsc)/./src/components/WalletProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Dashboard.tsx":{"*":{"id":"(rsc)/./src/components/Dashboard.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}