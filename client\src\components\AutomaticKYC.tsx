'use client';

import { useState, useEffect, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CheckCircleIcon, ClockIcon, ExclamationTriangleIcon, ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline';
import snsWebSdk from '@sumsub/websdk';

interface AutomaticKYCProps {
  onStatusChange?: (status: string, error?: string) => void;
  existingProfile?: any;
}

interface SessionData {
  session_id: string;
  session_token: string;
  url: string;
  status: string;
  features: string;
}

export function AutomaticKYC({ onStatusChange, existingProfile }: AutomaticKYCProps) {
  const [currentStatus, setCurrentStatus] = useState<'idle' | 'creating' | 'pending' | 'completed' | 'failed'>('idle');
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const sdkContainerRef = useRef<HTMLDivElement>(null);
  const sdkInstanceRef = useRef<any>(null);

  // Create verification session mutation - SUMSUB
  const createSessionMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/kyc/sumsub/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          callback: `${window.location.origin}/qualification`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.details || errorData.error || 'Failed to create verification session';
        const correlationId = errorData.correlationId ? ` (ID: ${errorData.correlationId})` : '';
        throw new Error(`${errorMessage}${correlationId}`);
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (data: SessionData) => {
      setSessionData(data);
      setCurrentStatus('pending');
      onStatusChange?.('pending');

      // Initialize Sumsub SDK with the access token inline
      setTimeout(() => {
        initializeSumsubSDK(data.session_token);
      }, 100); // Small delay to ensure DOM is ready
    },
    onError: (error: Error) => {
      setError(error.message);
      setCurrentStatus('failed');
      onStatusChange?.('failed', error.message);
    },
  });

  // Poll for session status updates
  useEffect(() => {
    if (!sessionData?.session_id || currentStatus !== 'pending') return;

    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/kyc/sumsub/session?sessionId=${sessionData.session_id}`);
        if (response.ok) {
          const result = await response.json();
          const sessionStatus = result.data?.status;

          if (sessionStatus === 'completed' || sessionStatus === 'approved') {
            setCurrentStatus('completed');
            onStatusChange?.('completed');
            clearInterval(pollInterval);
            // Refresh profile data
            queryClient.invalidateQueries({ queryKey: ['client-profile'] });
          } else if (sessionStatus === 'rejected' || sessionStatus === 'declined') {
            setCurrentStatus('failed');
            onStatusChange?.('failed');
            clearInterval(pollInterval);
          }
        }
      } catch (error) {
        console.error('Error polling session status:', error);
      }
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(pollInterval);
  }, [sessionData, currentStatus, onStatusChange, queryClient]);

  const handleStartKYC = () => {
    setCurrentStatus('creating');
    setError(null);
    createSessionMutation.mutate();
  };

  const handleRetry = () => {
    setCurrentStatus('idle');
    setError(null);
    setSessionData(null);
    cleanupSDK();
    handleStartKYC();
  };

  // Auto-start KYC verification when component mounts
  useEffect(() => {
    // Only auto-start if we don't have a session and haven't failed
    if (currentStatus === 'idle' && !sessionData && !error) {
      console.log('Auto-starting KYC verification...');
      // Small delay to ensure component is fully mounted
      const timer = setTimeout(() => {
        handleStartKYC();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, []); // Empty dependency array means this runs once on mount

  // Clean up SDK instance when component unmounts or resets
  const cleanupSDK = () => {
    if (sdkInstanceRef.current) {
      sdkInstanceRef.current.destroy();
      sdkInstanceRef.current = null;
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupSDK();
    };
  }, []);

  const initializeSumsubSDK = (accessToken: string) => {
    if (!sdkContainerRef.current) return;

    // Clean up any existing SDK instance
    if (sdkInstanceRef.current) {
      try {
        sdkInstanceRef.current.destroy();
      } catch (e) {
        console.log('Error destroying previous SDK instance:', e);
      }
    }

    // Initialize the Sumsub SDK
    sdkInstanceRef.current = snsWebSdk
      .init(accessToken, () => {
        // Token refresh callback - you might want to implement token refresh here
        console.log('Token refresh needed');
        return Promise.resolve(accessToken);
      })
      .withConf({
        lang: 'en',
        email: existingProfile?.email,
        phone: existingProfile?.phoneNumber,
        i18n: {
          document: {
            subTitles: {
              IDENTITY: 'Upload a document that proves your identity'
            }
          }
        },
        uiConf: {
          customCss: `
            .sumsub-container {
              font-family: inherit;
            }
          `
        }
      })
      .withOptions({
        addViewportTag: false,
        adaptIframeHeight: true
      })
      .on('idCheck.onStepCompleted', (payload: any) => {
        console.log('Step completed:', payload);
      })
      .on('idCheck.onApplicantLoaded', (payload: any) => {
        console.log('Applicant loaded:', payload);
      })
      .on('idCheck.onApplicantSubmitted', (payload: any) => {
        console.log('Applicant submitted:', payload);
        setCurrentStatus('completed');
        onStatusChange?.('completed');
        // Refresh profile data
        queryClient.invalidateQueries({ queryKey: ['client-profile'] });
        // Update admin panel status
        updateAdminPanelStatus('APPROVED');
      })
      .on('idCheck.onError', (error: any) => {
        console.error('Sumsub SDK error:', error);
        setError(error.message || 'Verification error');
        setCurrentStatus('failed');
        onStatusChange?.('failed', error.message);
      })
      .onMessage((type: string, payload: any) => {
        console.log('Sumsub SDK message:', type, payload);
      })
      .build();

    // Launch the SDK in the container using CSS selector
    sdkInstanceRef.current.launch('#sumsub-websdk-container');
  };

  // Update admin panel KYC status
  const updateAdminPanelStatus = async (status: string) => {
    try {
      console.log('Updating admin panel KYC status to:', status);

      const response = await fetch('/api/client/kyc/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          kycStatus: status,
          kycNotes: 'Sumsub verification completed successfully via client portal',
        }),
      });

      if (response.ok) {
        console.log('Admin panel KYC status updated successfully');
      } else {
        console.error('Failed to update admin panel KYC status:', response.status);
      }
    } catch (error) {
      console.error('Error updating admin panel KYC status:', error);
    }
  };

  const getStatusDisplay = () => {
    switch (currentStatus) {
      case 'creating':
        return {
          icon: <ClockIcon className="h-8 w-8 text-blue-600 animate-pulse" />,
          title: 'Setting up verification...',
          description: 'Please wait while we prepare your identity verification session.',
          color: 'blue'
        };
      case 'pending':
        return {
          icon: <ClockIcon className="h-8 w-8 text-yellow-600 animate-pulse" />,
          title: 'Verification in progress',
          description: 'Complete your identity verification in the popup window.',
          color: 'yellow'
        };
      case 'completed':
        return {
          icon: <CheckCircleIcon className="h-8 w-8 text-green-600" />,
          title: 'Verification completed',
          description: 'Your identity has been successfully verified.',
          color: 'green'
        };
      case 'failed':
        return {
          icon: <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />,
          title: 'Verification failed',
          description: error || 'Identity verification could not be completed.',
          color: 'red'
        };
      default:
        return {
          icon: <ClockIcon className="h-8 w-8 text-blue-600 animate-pulse" />,
          title: 'Initializing verification...',
          description: 'Your identity verification will start automatically.',
          color: 'blue'
        };
    }
  };

  const statusDisplay = getStatusDisplay();

  return (
    <div className="space-y-6">
      {/* Main Status Display */}
      <div className={`p-6 rounded-lg border ${
        statusDisplay.color === 'green' ? 'bg-green-50 border-green-200' :
        statusDisplay.color === 'red' ? 'bg-red-50 border-red-200' :
        statusDisplay.color === 'blue' ? 'bg-blue-50 border-blue-200' :
        statusDisplay.color === 'yellow' ? 'bg-yellow-50 border-yellow-200' :
        'bg-gray-50 border-gray-200'
      }`}>
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            {statusDisplay.icon}
          </div>
          <div className="flex-grow">
            <h3 className="text-lg font-semibold text-gray-900">
              {statusDisplay.title}
            </h3>
            <p className="text-gray-600 mt-1">
              {statusDisplay.description}
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        {currentStatus === 'creating' && (
          <div className="inline-flex items-center px-6 py-3 bg-blue-100 text-blue-800 rounded-lg">
            <ClockIcon className="h-5 w-5 mr-2 animate-spin" />
            Setting up verification...
          </div>
        )}

        {currentStatus === 'pending' && sessionData && (
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-lg">
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            Complete verification below
          </div>
        )}

        {currentStatus === 'failed' && (
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry Verification
          </button>
        )}

        {currentStatus === 'completed' && (
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-lg">
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Verification Complete
            </div>
          </div>
        )}
      </div>

      {/* Inline Sumsub SDK Container */}
      {currentStatus === 'pending' && sessionData && (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Identity Verification</h3>
            <p className="text-sm text-gray-600 mt-1">
              Complete your verification using the form below
            </p>
          </div>
          <div className="p-4">
            <div
              id="sumsub-websdk-container"
              ref={sdkContainerRef}
              className="w-full"
              style={{ minHeight: '500px' }}
            />
          </div>
          <div className="px-4 pb-4">
            <div className="text-xs text-gray-500">
              Session ID: {sessionData.session_id}
            </div>
          </div>
        </div>
      )}

      {/* Session Information */}
      {sessionData && currentStatus !== 'pending' && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Verification Details</h4>
          <div className="text-xs text-gray-600 space-y-1">
            <div>Session ID: {sessionData.session_id}</div>
            <div>Features: {sessionData.features}</div>
            <div>Status: {sessionData.status}</div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">How automatic verification works:</h4>
        <ul className="space-y-1 text-sm text-blue-800">
          <li>• Verification starts automatically when you reach this step</li>
          <li>• The verification form will appear below once ready</li>
          <li>• Follow the instructions to scan your ID document</li>
          <li>• Sumsub will extract and verify your document information</li>
          <li>• The system will compare extracted data with your qualification form</li>
          <li>• Verification status will update automatically when complete</li>
        </ul>
      </div>

      {/* Debug Information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-900 mb-2">Debug Information</h4>
            <div className="text-xs text-yellow-800 space-y-1">
              <div>Current Status: {currentStatus}</div>
              <div>Has Session: {sessionData ? 'Yes' : 'No'}</div>
              <div>Has Error: {error ? 'Yes' : 'No'}</div>
              <div>SDK Instance: {sdkInstanceRef.current ? 'Initialized' : 'Not initialized'}</div>
              <div>SDK Container: {sdkContainerRef.current ? 'Ready' : 'Not ready'}</div>
            </div>
            {sessionData && (
              <div className="mt-3">
                <button
                  onClick={() => updateAdminPanelStatus('APPROVED')}
                  className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 mr-2"
                >
                  Test: Mark as Approved
                </button>
                <button
                  onClick={() => updateAdminPanelStatus('IN_REVIEW')}
                  className="px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700"
                >
                  Test: Mark as In Review
                </button>
              </div>
            )}
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Sumsub KYC Integration</h4>
            <div className="text-sm text-blue-800 space-y-2">
              <p>
                <strong>Data Comparison:</strong> The system will compare your qualification form data
                with the information extracted from your ID document using Sumsub.
              </p>
              <p>
                <strong>Sumsub Features:</strong> Professional-grade document verification with built-in
                test data support for development and comprehensive fraud detection.
              </p>
              <div className="mt-2">
                <strong>Comparison Fields:</strong>
                <ul className="list-disc list-inside text-xs mt-1 space-y-1">
                  <li>First Name & Last Name</li>
                  <li>Date of Birth</li>
                  <li>Nationality/Country</li>
                  <li>Phone Number</li>
                  <li>Document Information</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}
