const { ethers } = require('ethers');

async function checkSpecificWhitelist() {
  console.log('🔍 Checking Specific Whitelist Contract');
  console.log('======================================');

  const YOUR_WALLET = '******************************************';
  const TOKEN_WHITELIST = '******************************************'; // From the API response

  try {
    const provider = new ethers.JsonRpcProvider(process.env.AMOY_RPC_URL);
    console.log(`🔗 Connected to: ${process.env.AMOY_RPC_URL}`);
    console.log(`👤 Your wallet: ${YOUR_WALLET}`);
    console.log(`📋 Token whitelist contract: ${TOKEN_WHITELIST}`);

    // Load whitelist ABI
    const WhitelistABI = require('./src/contracts/Whitelist.json');
    const whitelistContract = new ethers.Contract(
      TOKEN_WHITELIST,
      WhitelistABI.abi,
      provider
    );

    console.log('\n1️⃣ Checking your whitelist status on this contract...');
    
    // Check if you're whitelisted
    const isWhitelisted = await whitelistContract.isWhitelisted(YOUR_WALLET);
    console.log(`   Result: ${isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED'}`);

    if (!isWhitelisted) {
      console.log('\n2️⃣ Checking admin roles...');
      
      try {
        const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
        const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, YOUR_WALLET);
        console.log(`   You have AGENT_ROLE: ${hasAgentRole ? '✅ YES' : '❌ NO'}`);

        const DEFAULT_ADMIN_ROLE = await whitelistContract.DEFAULT_ADMIN_ROLE();
        const hasDefaultAdminRole = await whitelistContract.hasRole(DEFAULT_ADMIN_ROLE, YOUR_WALLET);
        console.log(`   You have DEFAULT_ADMIN_ROLE: ${hasDefaultAdminRole ? '✅ YES' : '❌ NO'}`);

        if (hasAgentRole || hasDefaultAdminRole) {
          console.log('\n3️⃣ You have admin rights! Adding yourself to whitelist...');
          
          const wallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);
          const whitelistWithSigner = new ethers.Contract(
            TOKEN_WHITELIST,
            WhitelistABI.abi,
            wallet
          );

          console.log('   Submitting whitelist transaction...');
          const tx = await whitelistWithSigner.addToWhitelist(YOUR_WALLET, {
            gasLimit: BigInt(300000),
            gasPrice: ethers.parseUnits("100", "gwei")
          });

          console.log(`   Transaction hash: ${tx.hash}`);
          console.log('   Waiting for confirmation...');
          
          await tx.wait();
          console.log('   ✅ Transaction confirmed!');

          // Verify the whitelist status
          const newStatus = await whitelistContract.isWhitelisted(YOUR_WALLET);
          console.log(`   New whitelist status: ${newStatus ? '✅ WHITELISTED' : '❌ STILL NOT WHITELISTED'}`);

          if (newStatus) {
            console.log('\n🎉 SUCCESS! You are now whitelisted for this token!');
            console.log('   Refresh your browser and try placing an order again.');
          }
        } else {
          console.log('\n❌ You don\'t have admin rights on this whitelist contract.');
          console.log('💡 Solutions:');
          console.log('1. Ask the contract owner to grant you AGENT_ROLE');
          console.log('2. Ask the contract owner to whitelist your address');
          console.log('3. Use the admin panel to approve yourself for this token');
        }
      } catch (error) {
        console.log(`   ❌ Error checking roles: ${error.message}`);
      }
    } else {
      console.log('\n✅ You are already whitelisted!');
      console.log('   The client should detect this. Try refreshing your browser.');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkSpecificWhitelist().catch(console.error);
