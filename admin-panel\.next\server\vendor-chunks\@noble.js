"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(ssr)/./node_modules/@noble/curves/esm/_shortw_utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(ssr)/./node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n// connects noble-curves to noble-hashes\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return Object.freeze({ ...create(defHash), create });\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vX3Nob3J0d191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQzBDO0FBQ3FCO0FBQ1A7QUFDeEQ7QUFDTztBQUNQO0FBQ0E7QUFDQSxnQ0FBZ0Msd0RBQUksWUFBWSxnRUFBVztBQUMzRCxtQkFBbUI7QUFDbkI7QUFDQTtBQUNPO0FBQ1AsNkJBQTZCLHFFQUFXLEdBQUcsK0JBQStCO0FBQzFFLDJCQUEyQiw0QkFBNEI7QUFDdkQ7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGN1cnZlc1xcZXNtXFxfc2hvcnR3X3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qISBub2JsZS1jdXJ2ZXMgLSBNSVQgTGljZW5zZSAoYykgMjAyMiBQYXVsIE1pbGxlciAocGF1bG1pbGxyLmNvbSkgKi9cbmltcG9ydCB7IGhtYWMgfSBmcm9tICdAbm9ibGUvaGFzaGVzL2htYWMnO1xuaW1wb3J0IHsgY29uY2F0Qnl0ZXMsIHJhbmRvbUJ5dGVzIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy91dGlscyc7XG5pbXBvcnQgeyB3ZWllcnN0cmFzcyB9IGZyb20gJy4vYWJzdHJhY3Qvd2VpZXJzdHJhc3MuanMnO1xuLy8gY29ubmVjdHMgbm9ibGUtY3VydmVzIHRvIG5vYmxlLWhhc2hlc1xuZXhwb3J0IGZ1bmN0aW9uIGdldEhhc2goaGFzaCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGhhc2gsXG4gICAgICAgIGhtYWM6IChrZXksIC4uLm1zZ3MpID0+IGhtYWMoaGFzaCwga2V5LCBjb25jYXRCeXRlcyguLi5tc2dzKSksXG4gICAgICAgIHJhbmRvbUJ5dGVzLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ3VydmUoY3VydmVEZWYsIGRlZkhhc2gpIHtcbiAgICBjb25zdCBjcmVhdGUgPSAoaGFzaCkgPT4gd2VpZXJzdHJhc3MoeyAuLi5jdXJ2ZURlZiwgLi4uZ2V0SGFzaChoYXNoKSB9KTtcbiAgICByZXR1cm4gT2JqZWN0LmZyZWV6ZSh7IC4uLmNyZWF0ZShkZWZIYXNoKSwgY3JlYXRlIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9X3Nob3J0d191dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/_shortw_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/curve.js":
/*!**********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/curve.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// Abelian group utilities\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n// Elliptic curve multiplication of Point by scalar. Fragile.\n// Scalars should always be less than curve order: this should be checked inside of a curve itself.\n// Creates precomputation tables for fast multiplication:\n// - private scalar is split by fixed size windows of W bits\n// - every window point is collected from window's table & added to accumulator\n// - since windows are different, same point inside tables won't be accessed more than once per calc\n// - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n// - +1 window is neccessary for wNAF\n// - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n// TODO: Research returning 2d JS array of windows, instead of a single window. This would allow\n// windows to be in different memory locations\nfunction wNAF(c, bits) {\n    const constTimeNegate = (condition, item) => {\n        const neg = item.negate();\n        return condition ? neg : item;\n    };\n    const opts = (W) => {\n        const windows = Math.ceil(bits / W) + 1; // +1, because\n        const windowSize = 2 ** (W - 1); // -1 because we skip zero\n        return { windows, windowSize };\n    };\n    return {\n        constTimeNegate,\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n) {\n            let p = c.ZERO;\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = opts(W);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = opts(W);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        wNAFCached(P, precomputesMap, n, transform) {\n            // @ts-ignore\n            const W = P._WINDOW_SIZE || 1;\n            // Calculate precomputes on a first run, reuse them after\n            let comp = precomputesMap.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1) {\n                    precomputesMap.set(P, transform(comp));\n                }\n            }\n            return this.wNAF(W, comp, n);\n        },\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_0__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_0__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/curve.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!******************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\nfunction validateDST(dst) {\n    if (dst instanceof Uint8Array)\n        return dst;\n    if (typeof dst === 'string')\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(dst);\n    throw new Error('DST must be Uint8Array or string');\n}\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    if (value < 0 || value >= 1 << (8 * length)) {\n        throw new Error(`bad I2OSP call: value=${value} length=${length}`);\n    }\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction isBytes(item) {\n    if (!(item instanceof Uint8Array))\n        throw new Error('Uint8Array expected');\n}\nfunction isNum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n// Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    isBytes(msg);\n    isBytes(DST);\n    isNum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (ell > 255)\n        throw new Error('Invalid xmd length');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n// Produces a uniformly random byte string using an extendable-output function (XOF) H.\n// 1. The collision resistance of H MUST be at least k bits.\n// 2. H MUST be an XOF that has been proved indifferentiable from\n//    a random oracle under a reasonable cryptographic assumption.\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    isBytes(msg);\n    isBytes(DST);\n    isNum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F\n * https://www.rfc-editor.org/rfc/rfc9380#section-5.2\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    isBytes(msg);\n    isNum(count);\n    const DST = validateDST(_DST);\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const COEFF = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xNum, xDen, yNum, yDen] = COEFF.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        x = field.div(xNum, xDen); // xNum / xDen\n        y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n        return { x, y };\n    };\n}\nfunction createHasher(Point, mapToCurve, def) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    return {\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...def, DST: def.DST, ...options });\n            const u0 = Point.fromAffine(mapToCurve(u[0]));\n            const u1 = Point.fromAffine(mapToCurve(u[1]));\n            const P = u0.add(u1).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...def, DST: def.encodeDST, ...options });\n            const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js":
/*!************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/modular.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// Utilities for modular arithmetics and finite fields\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _4n = BigInt(4), _5n = BigInt(5), _8n = BigInt(8);\n// prettier-ignore\nconst _9n = BigInt(9), _16n = BigInt(16);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\n// TODO: use field version && remove\nfunction pow(num, power, modulo) {\n    if (modulo <= _0n || power < _0n)\n        throw new Error('Expected power/modulo > 0');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n// Does x ^ (2 ^ power) mod p. pow2(30, 4) == 30 ^ (2 ^ 4)\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n// Inverses number over modulo\nfunction invert(number, modulo) {\n    if (number === _0n || modulo <= _0n) {\n        throw new Error(`invert: expected positive integers, got n=${number} mod=${modulo}`);\n    }\n    // Euclidean GCD https://brilliant.org/wiki/extended-euclidean-algorithm/\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++)\n        ;\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\nfunction FpSqrt(P) {\n    // NOTE: different algorithms can give different roots, it is up to user to decide which one they want.\n    // For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('Expected power > 0');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nfunction FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nfunction FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(f) {\n    const legendreConst = (f.ORDER - _1n) / _2n; // Integer arithmetic\n    return (x) => {\n        const p = f.pow(x, legendreConst);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime. **Non-primes are not supported.**\n * Do not init in loop: slow. Very fragile: always run a benchmark on a change.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error(`Expected Field ORDER > 0, got ${ORDER}`);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('Field lengths over 2048 bytes are not supported');\n    const sqrtP = FpSqrt(ORDER);\n    const f = Object.freeze({\n        ORDER,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error(`Invalid field element: expected bigint, got ${typeof num}`);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt || ((n) => sqrtP(f, n)),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error(`Fp.fromBytes: expected ${BYTES}, got ${bytes.length}`);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use mapKeyToField instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error(`hashToPrivateScalar: expected ${minLen}-1024 bytes of input, got ${hashLen}`);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error(`expected ${minLen}-1024 bytes of input, got ${len}`);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst u8a = (a) => a instanceof Uint8Array;\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? `0${hex}` : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // Big Endian\n    return BigInt(hex === '' ? '0' : `0x${hex}`);\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(`${title} must be valid hex string, got \"${hex}\". Cause: ${e}`);\n        }\n    }\n    else if (u8a(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(`${title} must be hex string or Uint8Array`);\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(`${title} expected ${expectedLength} bytes, got ${len}`);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\nfunction equalBytes(b1, b2) {\n    // We don't care about timing attacks here\n    if (b1.length !== b2.length)\n        return false;\n    for (let i = 0; i < b1.length; i++)\n        if (b1[i] !== b2[i])\n            return false;\n    return true;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nconst bitSet = (n, pos, value) => {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n};\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || val instanceof Uint8Array,\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error(`Invalid validator \"${type}\", expected function`);\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error(`Invalid param ${String(fieldName)}=${val} (${typeof val}), expected ${type}`);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!****************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./curve.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/curve.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// Short Weierstrass curve. The formula is: y² = x³ + ax + b\n\n\n\n\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_0__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_1__.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('Endomorphism can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('Expected endomorphism with beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\n// ASN.1 DER encoding utilities\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = _utils_js__WEBPACK_IMPORTED_MODULE_1__;\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: class DERErr extends Error {\n        constructor(m = '') {\n            super(m);\n        }\n    },\n    _parseInt(data) {\n        const { Err: E } = DER;\n        if (data.length < 2 || data[0] !== 0x02)\n            throw new E('Invalid signature integer tag');\n        const len = data[1];\n        const res = data.subarray(2, len + 2);\n        if (!len || res.length !== len)\n            throw new E('Invalid signature integer: wrong length');\n        // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n        // since we always use positive integers here. It must always be empty:\n        // - add zero byte if exists\n        // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n        if (res[0] & 0b10000000)\n            throw new E('Invalid signature integer: negative');\n        if (res[0] === 0x00 && !(res[1] & 0b10000000))\n            throw new E('Invalid signature integer: unnecessary leading zero');\n        return { d: b2n(res), l: data.subarray(len + 2) }; // d is data, l is left\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        if (!(data instanceof Uint8Array))\n            throw new Error('ui8a expected');\n        let l = data.length;\n        if (l < 2 || data[0] != 0x30)\n            throw new E('Invalid signature tag');\n        if (data[1] !== l - 2)\n            throw new E('Invalid signature: incorrect length');\n        const { d: r, l: sBytes } = DER._parseInt(data.subarray(2));\n        const { d: s, l: rBytesLeft } = DER._parseInt(sBytes);\n        if (rBytesLeft.length)\n            throw new E('Invalid signature: left bytes after parsing');\n        return { r, s };\n    },\n    hexFromSig(sig) {\n        // Add leading zero if first byte has negative bit enabled. More details in '_parseInt'\n        const slice = (s) => (Number.parseInt(s[0], 16) & 0b1000 ? '00' + s : s);\n        const h = (num) => {\n            const hex = num.toString(16);\n            return hex.length & 1 ? `0${hex}` : hex;\n        };\n        const s = slice(h(sig.s));\n        const r = slice(h(sig.r));\n        const shl = s.length / 2;\n        const rhl = r.length / 2;\n        const sl = h(shl);\n        const rl = h(rhl);\n        return `30${h(rhl + shl + 4)}02${rl}${r}02${sl}${s}`;\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return typeof num === 'bigint' && _0n < num && num < CURVE.n;\n    }\n    function assertGE(num) {\n        if (!isWithinCurveOrder(num))\n            throw new Error('Expected valid bigint: 0 < bigint < curve.n');\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (key instanceof Uint8Array)\n                key = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('Invalid key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error(`private key must be ${nByteLength} bytes, hex or bigint, not ${typeof key}`);\n        }\n        if (wrapPrivateKey)\n            num = _modular_js__WEBPACK_IMPORTED_MODULE_2__.mod(num, n); // disabled by default, enabled for BLS\n        assertGE(num); // num in range [1..N-1]\n        return num;\n    }\n    const pointPrecomputes = new Map();\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            this._WINDOW_SIZE = windowSize;\n            pointPrecomputes.delete(this);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            if (this.is0()) {\n                // (0, 1, 0) aka ZERO is invalid in most contexts.\n                // In BLS, ZERO can be serialized, so we allow it.\n                // (0, 0, 0) is wrong representation of ZERO and is always invalid.\n                if (CURVE.allowInfinityPoint && !Fp.is0(this.py))\n                    return;\n                throw new Error('bad point: ZERO');\n            }\n            // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n            const { x, y } = this.toAffine();\n            // Check if x, y are valid field elements\n            if (!Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('bad point: x or y not FE');\n            const left = Fp.sqr(y); // y²\n            const right = weierstrassEquation(x); // x³ + ax + b\n            if (!Fp.eql(left, right))\n                throw new Error('bad point: equation left != right');\n            if (!this.isTorsionFree())\n                throw new Error('bad point: not in prime-order subgroup');\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, pointPrecomputes, n, (comp) => {\n                const toInv = Fp.invertBatch(comp.map((p) => p.pz));\n                return comp.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n            });\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(n) {\n            const I = Point.ZERO;\n            if (n === _0n)\n                return I;\n            assertGE(n); // Will throw on 0\n            if (n === _1n)\n                return this;\n            const { endo } = CURVE;\n            if (!endo)\n                return wnaf.unsafeLadder(this, n);\n            // Apply endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            assertGE(scalar);\n            let n = scalar;\n            let point, fake; // Fake point is used to const-time mult\n            const { endo } = CURVE;\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(n);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            const { px: x, py: y, pz: z } = this;\n            const is0 = this.is0();\n            // If invZ was 0, we return zero point. However we still want to execute\n            // all operations, so we replace invZ with a random number, 1.\n            if (iz == null)\n                iz = is0 ? Fp.ONE : Fp.inv(z);\n            const ax = Fp.mul(x, iz);\n            const ay = Fp.mul(y, iz);\n            const zz = Fp.mul(z, iz);\n            if (is0)\n                return { x: Fp.ZERO, y: Fp.ZERO };\n            if (!Fp.eql(zz, Fp.ONE))\n                throw new Error('invZ was invalid');\n            return { x: ax, y: ay };\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_0__.wNAF)(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_0__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_1__.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function isValidFieldElement(num) {\n        return _0n < num && num < Fp.ORDER; // 0 is banned since it's not invertible FE\n    }\n    function modN(a) {\n        return _modular_js__WEBPACK_IMPORTED_MODULE_2__.mod(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return _modular_js__WEBPACK_IMPORTED_MODULE_2__.invert(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_1__.concatBytes;\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE(tail);\n                if (!isValidFieldElement(x))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                throw new Error(`Point of length ${len} was invalid. Expected ${compressedLen} compressed bytes or ${uncompressedLen} uncompressed bytes`);\n            }\n        },\n    });\n    const numToNByteStr = (num) => _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToHex(_utils_js__WEBPACK_IMPORTED_MODULE_1__.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            // can use assertGE here\n            if (!isWithinCurveOrder(this.r))\n                throw new Error('r must be 0 < r < CURVE.n');\n            if (!isWithinCurveOrder(this.s))\n                throw new Error('s must be 0 < s < CURVE.n');\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = _modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength(CURVE.n);\n            return _modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = item instanceof Uint8Array;\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        if (typeof num !== 'bigint')\n            throw new Error('bigint expected');\n        if (!(_0n <= num && num < ORDER_MASK))\n            throw new Error(`bigint expected < 2^${CURVE.nBitLength}`);\n        // works with order, can have different size than numToField!\n        return _utils_js__WEBPACK_IMPORTED_MODULE_1__.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order, this will be wrong at least for P521.\n    // Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('msgHash', msgHash);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = _utils_js__WEBPACK_IMPORTED_MODULE_1__.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = _utils_js__WEBPACK_IMPORTED_MODULE_1__.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)('publicKey', publicKey);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        const { lowS, prehash } = opts;\n        let _sig = undefined;\n        let P;\n        try {\n            if (typeof sg === 'string' || sg instanceof Uint8Array) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                    _sig = Signature.fromCompact(sg);\n                }\n            }\n            else if (typeof sg === 'object' && typeof sg.r === 'bigint' && typeof sg.s === 'bigint') {\n                const { r, s } = sg;\n                _sig = new Signature(r, s);\n            }\n            else {\n                throw new Error('PARSE');\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            if (error.message === 'PARSE')\n                throw new Error(`signature must be Signature instance, Uint8Array or hex string`);\n            return false;\n        }\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    _modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/secp256k1.js":
/*!*****************************************************!*\
  !*** ./node_modules/@noble/curves/esm/secp256k1.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha256 */ \"(ssr)/./node_modules/@noble/hashes/esm/sha256.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/_shortw_utils.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fp.eql(Fp.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fp = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: BigInt(0),\n    b: BigInt(7),\n    Fp,\n    n: secp256k1N,\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1),\n    lowS: true,\n    /**\n     * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n     * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n     * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n     * Explanation: https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066\n     */\n    endo: {\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\nconst fe = (x) => typeof x === 'bigint' && _0n < x && x < secp256k1P;\nconst ge = (x) => typeof x === 'bigint' && _0n < x && x < secp256k1N;\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    if (!fe(x))\n        throw new Error('bad x: need 0 < x < p'); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!fe(r))\n            return false;\n        const s = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!ge(s))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fp, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fp, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fp.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fp.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fp.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\nconst hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\nconst encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vc2VjcDI1NmsxLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDOEM7QUFDSTtBQUNPO0FBQ087QUFDaUM7QUFDMUI7QUFDdEI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDLGtDQUFrQztBQUNsQyxnQkFBZ0IsMERBQUk7QUFDcEIsZ0JBQWdCLDBEQUFJO0FBQ3BCLGlCQUFpQiwwREFBSTtBQUNyQixpQkFBaUIsMERBQUk7QUFDckIsaUJBQWlCLDBEQUFJO0FBQ3JCLGlCQUFpQiwwREFBSTtBQUNyQixrQkFBa0IsMERBQUk7QUFDdEIsa0JBQWtCLDBEQUFJO0FBQ3RCLGtCQUFrQiwwREFBSTtBQUN0QixnQkFBZ0IsMERBQUk7QUFDcEIsZ0JBQWdCLDBEQUFJO0FBQ3BCLGlCQUFpQiwwREFBSTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMkRBQUsscUNBQXFDLGVBQWU7QUFDN0Qsa0JBQWtCLDZEQUFXO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkU7QUFDN0U7QUFDQTtBQUNBLHFCQUFxQix5REFBRztBQUN4QixxQkFBcUIseURBQUc7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLFNBQVM7QUFDVCxLQUFLO0FBQ0wsQ0FBQyxFQUFFLHdEQUFNO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsNERBQU07QUFDM0IsZUFBZSwrREFBVztBQUMxQjtBQUNBO0FBQ0EsV0FBVyw0REFBTSxDQUFDLCtEQUFXO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixtRUFBZTtBQUN2QyxvQkFBb0IseURBQUc7QUFDdkIsb0JBQW9CLHlEQUFHO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJEO0FBQzNELHNDQUFzQyxhQUFhO0FBQ25EO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtEO0FBQ2xEO0FBQ0Esd0NBQXdDO0FBQ3hDLHdCQUF3QjtBQUN4QjtBQUNBLHNCQUFzQjtBQUN0QixvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUVBQWU7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGdFQUFXO0FBQy9ELGNBQWMsK0RBQVc7QUFDekIsWUFBWSx1QkFBdUIsbUNBQW1DO0FBQ3RFLGNBQWMsK0RBQVcsMEJBQTBCO0FBQ25ELDJCQUEyQixtRUFBZSxpQ0FBaUM7QUFDM0Usd0RBQXdEO0FBQ3hELG9CQUFvQixtRUFBZSxTQUFTO0FBQzVDO0FBQ0EsbURBQW1EO0FBQ25ELFlBQVksdUJBQXVCLDJCQUEyQjtBQUM5RCxvQ0FBb0M7QUFDcEMsb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwrREFBVztBQUMzQixjQUFjLCtEQUFXO0FBQ3pCLGdCQUFnQiwrREFBVztBQUMzQjtBQUNBLHlCQUF5QixtRUFBZSxRQUFRLHdCQUF3QjtBQUN4RSxrQkFBa0IsbUVBQWUsdUJBQXVCLDJCQUEyQjtBQUNuRjtBQUNBO0FBQ0Esa0JBQWtCLG1FQUFlLHdCQUF3Qiw0QkFBNEI7QUFDckY7QUFDQTtBQUNBLDhEQUE4RDtBQUM5RCwyQ0FBMkM7QUFDM0M7QUFDQSwwQkFBMEI7QUFDMUIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCLHVCQUF1QjtBQUN2QjtBQUNBLFdBQVc7QUFDWCxLQUFLO0FBQ0wsQ0FBQztBQUNELHNDQUFzQyxzRUFBVTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw2RUFBbUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELG1DQUFtQyx3RUFBWTtBQUMvQyxZQUFZLE9BQU87QUFDbkI7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx3REFBTTtBQUNoQixDQUFDO0FBQ007QUFDQTtBQUNQIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXEBub2JsZVxcY3VydmVzXFxlc21cXHNlY3AyNTZrMS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiEgbm9ibGUtY3VydmVzIC0gTUlUIExpY2Vuc2UgKGMpIDIwMjIgUGF1bCBNaWxsZXIgKHBhdWxtaWxsci5jb20pICovXG5pbXBvcnQgeyBzaGEyNTYgfSBmcm9tICdAbm9ibGUvaGFzaGVzL3NoYTI1Nic7XG5pbXBvcnQgeyByYW5kb21CeXRlcyB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvdXRpbHMnO1xuaW1wb3J0IHsgRmllbGQsIG1vZCwgcG93MiB9IGZyb20gJy4vYWJzdHJhY3QvbW9kdWxhci5qcyc7XG5pbXBvcnQgeyBtYXBUb0N1cnZlU2ltcGxlU1dVIH0gZnJvbSAnLi9hYnN0cmFjdC93ZWllcnN0cmFzcy5qcyc7XG5pbXBvcnQgeyBieXRlc1RvTnVtYmVyQkUsIGNvbmNhdEJ5dGVzLCBlbnN1cmVCeXRlcywgbnVtYmVyVG9CeXRlc0JFIH0gZnJvbSAnLi9hYnN0cmFjdC91dGlscy5qcyc7XG5pbXBvcnQgeyBjcmVhdGVIYXNoZXIsIGlzb2dlbnlNYXAgfSBmcm9tICcuL2Fic3RyYWN0L2hhc2gtdG8tY3VydmUuanMnO1xuaW1wb3J0IHsgY3JlYXRlQ3VydmUgfSBmcm9tICcuL19zaG9ydHdfdXRpbHMuanMnO1xuY29uc3Qgc2VjcDI1NmsxUCA9IEJpZ0ludCgnMHhmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZWZmZmZmYzJmJyk7XG5jb25zdCBzZWNwMjU2azFOID0gQmlnSW50KCcweGZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZlYmFhZWRjZTZhZjQ4YTAzYmJmZDI1ZThjZDAzNjQxNDEnKTtcbmNvbnN0IF8xbiA9IEJpZ0ludCgxKTtcbmNvbnN0IF8ybiA9IEJpZ0ludCgyKTtcbmNvbnN0IGRpdk5lYXJlc3QgPSAoYSwgYikgPT4gKGEgKyBiIC8gXzJuKSAvIGI7XG4vKipcbiAqIOKImm4gPSBuXigocCsxKS80KSBmb3IgZmllbGRzIHAgPSAzIG1vZCA0LiBXZSB1bndyYXAgdGhlIGxvb3AgYW5kIG11bHRpcGx5IGJpdC1ieS1iaXQuXG4gKiAoUCsxbi80bikudG9TdHJpbmcoMikgd291bGQgcHJvZHVjZSBiaXRzIFsyMjN4IDEsIDAsIDIyeCAxLCA0eCAwLCAxMSwgMDBdXG4gKi9cbmZ1bmN0aW9uIHNxcnRNb2QoeSkge1xuICAgIGNvbnN0IFAgPSBzZWNwMjU2azFQO1xuICAgIC8vIHByZXR0aWVyLWlnbm9yZVxuICAgIGNvbnN0IF8zbiA9IEJpZ0ludCgzKSwgXzZuID0gQmlnSW50KDYpLCBfMTFuID0gQmlnSW50KDExKSwgXzIybiA9IEJpZ0ludCgyMik7XG4gICAgLy8gcHJldHRpZXItaWdub3JlXG4gICAgY29uc3QgXzIzbiA9IEJpZ0ludCgyMyksIF80NG4gPSBCaWdJbnQoNDQpLCBfODhuID0gQmlnSW50KDg4KTtcbiAgICBjb25zdCBiMiA9ICh5ICogeSAqIHkpICUgUDsgLy8geF4zLCAxMVxuICAgIGNvbnN0IGIzID0gKGIyICogYjIgKiB5KSAlIFA7IC8vIHheN1xuICAgIGNvbnN0IGI2ID0gKHBvdzIoYjMsIF8zbiwgUCkgKiBiMykgJSBQO1xuICAgIGNvbnN0IGI5ID0gKHBvdzIoYjYsIF8zbiwgUCkgKiBiMykgJSBQO1xuICAgIGNvbnN0IGIxMSA9IChwb3cyKGI5LCBfMm4sIFApICogYjIpICUgUDtcbiAgICBjb25zdCBiMjIgPSAocG93MihiMTEsIF8xMW4sIFApICogYjExKSAlIFA7XG4gICAgY29uc3QgYjQ0ID0gKHBvdzIoYjIyLCBfMjJuLCBQKSAqIGIyMikgJSBQO1xuICAgIGNvbnN0IGI4OCA9IChwb3cyKGI0NCwgXzQ0biwgUCkgKiBiNDQpICUgUDtcbiAgICBjb25zdCBiMTc2ID0gKHBvdzIoYjg4LCBfODhuLCBQKSAqIGI4OCkgJSBQO1xuICAgIGNvbnN0IGIyMjAgPSAocG93MihiMTc2LCBfNDRuLCBQKSAqIGI0NCkgJSBQO1xuICAgIGNvbnN0IGIyMjMgPSAocG93MihiMjIwLCBfM24sIFApICogYjMpICUgUDtcbiAgICBjb25zdCB0MSA9IChwb3cyKGIyMjMsIF8yM24sIFApICogYjIyKSAlIFA7XG4gICAgY29uc3QgdDIgPSAocG93Mih0MSwgXzZuLCBQKSAqIGIyKSAlIFA7XG4gICAgY29uc3Qgcm9vdCA9IHBvdzIodDIsIF8ybiwgUCk7XG4gICAgaWYgKCFGcC5lcWwoRnAuc3FyKHJvb3QpLCB5KSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW5ub3QgZmluZCBzcXVhcmUgcm9vdCcpO1xuICAgIHJldHVybiByb290O1xufVxuY29uc3QgRnAgPSBGaWVsZChzZWNwMjU2azFQLCB1bmRlZmluZWQsIHVuZGVmaW5lZCwgeyBzcXJ0OiBzcXJ0TW9kIH0pO1xuZXhwb3J0IGNvbnN0IHNlY3AyNTZrMSA9IGNyZWF0ZUN1cnZlKHtcbiAgICBhOiBCaWdJbnQoMCksXG4gICAgYjogQmlnSW50KDcpLFxuICAgIEZwLFxuICAgIG46IHNlY3AyNTZrMU4sXG4gICAgLy8gQmFzZSBwb2ludCAoeCwgeSkgYWthIGdlbmVyYXRvciBwb2ludFxuICAgIEd4OiBCaWdJbnQoJzU1MDY2MjYzMDIyMjc3MzQzNjY5NTc4NzE4ODk1MTY4NTM0MzI2MjUwNjAzNDUzNzc3NTk0MTc1NTAwMTg3MzYwMzg5MTE2NzI5MjQwJyksXG4gICAgR3k6IEJpZ0ludCgnMzI2NzA1MTAwMjA3NTg4MTY5NzgwODMwODUxMzA1MDcwNDMxODQ0NzEyNzMzODA2NTkyNDMyNzU5Mzg5MDQzMzU3NTczMzc0ODI0MjQnKSxcbiAgICBoOiBCaWdJbnQoMSksXG4gICAgbG93UzogdHJ1ZSxcbiAgICAvKipcbiAgICAgKiBzZWNwMjU2azEgYmVsb25ncyB0byBLb2JsaXR6IGN1cnZlczogaXQgaGFzIGVmZmljaWVudGx5IGNvbXB1dGFibGUgZW5kb21vcnBoaXNtLlxuICAgICAqIEVuZG9tb3JwaGlzbSB1c2VzIDJ4IGxlc3MgUkFNLCBzcGVlZHMgdXAgcHJlY29tcHV0YXRpb24gYnkgMnggYW5kIEVDREggLyBrZXkgcmVjb3ZlcnkgYnkgMjAlLlxuICAgICAqIEZvciBwcmVjb21wdXRlZCB3TkFGIGl0IHRyYWRlcyBvZmYgMS8yIGluaXQgdGltZSAmIDEvMyByYW0gZm9yIDIwJSBwZXJmIGhpdC5cbiAgICAgKiBFeHBsYW5hdGlvbjogaHR0cHM6Ly9naXN0LmdpdGh1Yi5jb20vcGF1bG1pbGxyL2ViNjcwODA2NzkzZTg0ZGY2MjhhN2M0MzRhODczMDY2XG4gICAgICovXG4gICAgZW5kbzoge1xuICAgICAgICBiZXRhOiBCaWdJbnQoJzB4N2FlOTZhMmI2NTdjMDcxMDZlNjQ0NzllYWMzNDM0ZTk5Y2YwNDk3NTEyZjU4OTk1YzEzOTZjMjg3MTk1MDFlZScpLFxuICAgICAgICBzcGxpdFNjYWxhcjogKGspID0+IHtcbiAgICAgICAgICAgIGNvbnN0IG4gPSBzZWNwMjU2azFOO1xuICAgICAgICAgICAgY29uc3QgYTEgPSBCaWdJbnQoJzB4MzA4NmQyMjFhN2Q0NmJjZGU4NmM5MGU0OTI4NGViMTUnKTtcbiAgICAgICAgICAgIGNvbnN0IGIxID0gLV8xbiAqIEJpZ0ludCgnMHhlNDQzN2VkNjAxMGU4ODI4NmY1NDdmYTkwYWJmZTRjMycpO1xuICAgICAgICAgICAgY29uc3QgYTIgPSBCaWdJbnQoJzB4MTE0Y2E1MGY3YThlMmYzZjY1N2MxMTA4ZDlkNDRjZmQ4Jyk7XG4gICAgICAgICAgICBjb25zdCBiMiA9IGExO1xuICAgICAgICAgICAgY29uc3QgUE9XXzJfMTI4ID0gQmlnSW50KCcweDEwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMCcpOyAvLyAoMm4qKjEyOG4pLnRvU3RyaW5nKDE2KVxuICAgICAgICAgICAgY29uc3QgYzEgPSBkaXZOZWFyZXN0KGIyICogaywgbik7XG4gICAgICAgICAgICBjb25zdCBjMiA9IGRpdk5lYXJlc3QoLWIxICogaywgbik7XG4gICAgICAgICAgICBsZXQgazEgPSBtb2QoayAtIGMxICogYTEgLSBjMiAqIGEyLCBuKTtcbiAgICAgICAgICAgIGxldCBrMiA9IG1vZCgtYzEgKiBiMSAtIGMyICogYjIsIG4pO1xuICAgICAgICAgICAgY29uc3QgazFuZWcgPSBrMSA+IFBPV18yXzEyODtcbiAgICAgICAgICAgIGNvbnN0IGsybmVnID0gazIgPiBQT1dfMl8xMjg7XG4gICAgICAgICAgICBpZiAoazFuZWcpXG4gICAgICAgICAgICAgICAgazEgPSBuIC0gazE7XG4gICAgICAgICAgICBpZiAoazJuZWcpXG4gICAgICAgICAgICAgICAgazIgPSBuIC0gazI7XG4gICAgICAgICAgICBpZiAoazEgPiBQT1dfMl8xMjggfHwgazIgPiBQT1dfMl8xMjgpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ3NwbGl0U2NhbGFyOiBFbmRvbW9ycGhpc20gZmFpbGVkLCBrPScgKyBrKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB7IGsxbmVnLCBrMSwgazJuZWcsIGsyIH07XG4gICAgICAgIH0sXG4gICAgfSxcbn0sIHNoYTI1Nik7XG4vLyBTY2hub3JyIHNpZ25hdHVyZXMgYXJlIHN1cGVyaW9yIHRvIEVDRFNBIGZyb20gYWJvdmUuIEJlbG93IGlzIFNjaG5vcnItc3BlY2lmaWMgQklQMDM0MCBjb2RlLlxuLy8gaHR0cHM6Ly9naXRodWIuY29tL2JpdGNvaW4vYmlwcy9ibG9iL21hc3Rlci9iaXAtMDM0MC5tZWRpYXdpa2lcbmNvbnN0IF8wbiA9IEJpZ0ludCgwKTtcbmNvbnN0IGZlID0gKHgpID0+IHR5cGVvZiB4ID09PSAnYmlnaW50JyAmJiBfMG4gPCB4ICYmIHggPCBzZWNwMjU2azFQO1xuY29uc3QgZ2UgPSAoeCkgPT4gdHlwZW9mIHggPT09ICdiaWdpbnQnICYmIF8wbiA8IHggJiYgeCA8IHNlY3AyNTZrMU47XG4vKiogQW4gb2JqZWN0IG1hcHBpbmcgdGFncyB0byB0aGVpciB0YWdnZWQgaGFzaCBwcmVmaXggb2YgW1NIQTI1Nih0YWcpIHwgU0hBMjU2KHRhZyldICovXG5jb25zdCBUQUdHRURfSEFTSF9QUkVGSVhFUyA9IHt9O1xuZnVuY3Rpb24gdGFnZ2VkSGFzaCh0YWcsIC4uLm1lc3NhZ2VzKSB7XG4gICAgbGV0IHRhZ1AgPSBUQUdHRURfSEFTSF9QUkVGSVhFU1t0YWddO1xuICAgIGlmICh0YWdQID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY29uc3QgdGFnSCA9IHNoYTI1NihVaW50OEFycmF5LmZyb20odGFnLCAoYykgPT4gYy5jaGFyQ29kZUF0KDApKSk7XG4gICAgICAgIHRhZ1AgPSBjb25jYXRCeXRlcyh0YWdILCB0YWdIKTtcbiAgICAgICAgVEFHR0VEX0hBU0hfUFJFRklYRVNbdGFnXSA9IHRhZ1A7XG4gICAgfVxuICAgIHJldHVybiBzaGEyNTYoY29uY2F0Qnl0ZXModGFnUCwgLi4ubWVzc2FnZXMpKTtcbn1cbi8vIEVDRFNBIGNvbXBhY3QgcG9pbnRzIGFyZSAzMy1ieXRlLiBTY2hub3JyIGlzIDMyOiB3ZSBzdHJpcCBmaXJzdCBieXRlIDB4MDIgb3IgMHgwM1xuY29uc3QgcG9pbnRUb0J5dGVzID0gKHBvaW50KSA9PiBwb2ludC50b1Jhd0J5dGVzKHRydWUpLnNsaWNlKDEpO1xuY29uc3QgbnVtVG8zMmIgPSAobikgPT4gbnVtYmVyVG9CeXRlc0JFKG4sIDMyKTtcbmNvbnN0IG1vZFAgPSAoeCkgPT4gbW9kKHgsIHNlY3AyNTZrMVApO1xuY29uc3QgbW9kTiA9ICh4KSA9PiBtb2QoeCwgc2VjcDI1NmsxTik7XG5jb25zdCBQb2ludCA9IHNlY3AyNTZrMS5Qcm9qZWN0aXZlUG9pbnQ7XG5jb25zdCBHbXVsQWRkID0gKFEsIGEsIGIpID0+IFBvaW50LkJBU0UubXVsdGlwbHlBbmRBZGRVbnNhZmUoUSwgYSwgYik7XG4vLyBDYWxjdWxhdGUgcG9pbnQsIHNjYWxhciBhbmQgYnl0ZXNcbmZ1bmN0aW9uIHNjaG5vcnJHZXRFeHRQdWJLZXkocHJpdikge1xuICAgIGxldCBkXyA9IHNlY3AyNTZrMS51dGlscy5ub3JtUHJpdmF0ZUtleVRvU2NhbGFyKHByaXYpOyAvLyBzYW1lIG1ldGhvZCBleGVjdXRlZCBpbiBmcm9tUHJpdmF0ZUtleVxuICAgIGxldCBwID0gUG9pbnQuZnJvbVByaXZhdGVLZXkoZF8pOyAvLyBQID0gZCfii4VHOyAwIDwgZCcgPCBuIGNoZWNrIGlzIGRvbmUgaW5zaWRlXG4gICAgY29uc3Qgc2NhbGFyID0gcC5oYXNFdmVuWSgpID8gZF8gOiBtb2ROKC1kXyk7XG4gICAgcmV0dXJuIHsgc2NhbGFyOiBzY2FsYXIsIGJ5dGVzOiBwb2ludFRvQnl0ZXMocCkgfTtcbn1cbi8qKlxuICogbGlmdF94IGZyb20gQklQMzQwLiBDb252ZXJ0IDMyLWJ5dGUgeCBjb29yZGluYXRlIHRvIGVsbGlwdGljIGN1cnZlIHBvaW50LlxuICogQHJldHVybnMgdmFsaWQgcG9pbnQgY2hlY2tlZCBmb3IgYmVpbmcgb24tY3VydmVcbiAqL1xuZnVuY3Rpb24gbGlmdF94KHgpIHtcbiAgICBpZiAoIWZlKHgpKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2JhZCB4OiBuZWVkIDAgPCB4IDwgcCcpOyAvLyBGYWlsIGlmIHgg4omlIHAuXG4gICAgY29uc3QgeHggPSBtb2RQKHggKiB4KTtcbiAgICBjb25zdCBjID0gbW9kUCh4eCAqIHggKyBCaWdJbnQoNykpOyAvLyBMZXQgYyA9IHjCsyArIDcgbW9kIHAuXG4gICAgbGV0IHkgPSBzcXJ0TW9kKGMpOyAvLyBMZXQgeSA9IGNeKHArMSkvNCBtb2QgcC5cbiAgICBpZiAoeSAlIF8ybiAhPT0gXzBuKVxuICAgICAgICB5ID0gbW9kUCgteSk7IC8vIFJldHVybiB0aGUgdW5pcXVlIHBvaW50IFAgc3VjaCB0aGF0IHgoUCkgPSB4IGFuZFxuICAgIGNvbnN0IHAgPSBuZXcgUG9pbnQoeCwgeSwgXzFuKTsgLy8geShQKSA9IHkgaWYgeSBtb2QgMiA9IDAgb3IgeShQKSA9IHAteSBvdGhlcndpc2UuXG4gICAgcC5hc3NlcnRWYWxpZGl0eSgpO1xuICAgIHJldHVybiBwO1xufVxuLyoqXG4gKiBDcmVhdGUgdGFnZ2VkIGhhc2gsIGNvbnZlcnQgaXQgdG8gYmlnaW50LCByZWR1Y2UgbW9kdWxvLW4uXG4gKi9cbmZ1bmN0aW9uIGNoYWxsZW5nZSguLi5hcmdzKSB7XG4gICAgcmV0dXJuIG1vZE4oYnl0ZXNUb051bWJlckJFKHRhZ2dlZEhhc2goJ0JJUDAzNDAvY2hhbGxlbmdlJywgLi4uYXJncykpKTtcbn1cbi8qKlxuICogU2Nobm9yciBwdWJsaWMga2V5IGlzIGp1c3QgYHhgIGNvb3JkaW5hdGUgb2YgUG9pbnQgYXMgcGVyIEJJUDM0MC5cbiAqL1xuZnVuY3Rpb24gc2Nobm9yckdldFB1YmxpY0tleShwcml2YXRlS2V5KSB7XG4gICAgcmV0dXJuIHNjaG5vcnJHZXRFeHRQdWJLZXkocHJpdmF0ZUtleSkuYnl0ZXM7IC8vIGQnPWludChzaykuIEZhaWwgaWYgZCc9MCBvciBkJ+KJpW4uIFJldCBieXRlcyhkJ+KLhUcpXG59XG4vKipcbiAqIENyZWF0ZXMgU2Nobm9yciBzaWduYXR1cmUgYXMgcGVyIEJJUDM0MC4gVmVyaWZpZXMgaXRzZWxmIGJlZm9yZSByZXR1cm5pbmcgYW55dGhpbmcuXG4gKiBhdXhSYW5kIGlzIG9wdGlvbmFsIGFuZCBpcyBub3QgdGhlIHNvbGUgc291cmNlIG9mIGsgZ2VuZXJhdGlvbjogYmFkIENTUFJORyB3b24ndCBiZSBkYW5nZXJvdXMuXG4gKi9cbmZ1bmN0aW9uIHNjaG5vcnJTaWduKG1lc3NhZ2UsIHByaXZhdGVLZXksIGF1eFJhbmQgPSByYW5kb21CeXRlcygzMikpIHtcbiAgICBjb25zdCBtID0gZW5zdXJlQnl0ZXMoJ21lc3NhZ2UnLCBtZXNzYWdlKTtcbiAgICBjb25zdCB7IGJ5dGVzOiBweCwgc2NhbGFyOiBkIH0gPSBzY2hub3JyR2V0RXh0UHViS2V5KHByaXZhdGVLZXkpOyAvLyBjaGVja3MgZm9yIGlzV2l0aGluQ3VydmVPcmRlclxuICAgIGNvbnN0IGEgPSBlbnN1cmVCeXRlcygnYXV4UmFuZCcsIGF1eFJhbmQsIDMyKTsgLy8gQXV4aWxpYXJ5IHJhbmRvbSBkYXRhIGE6IGEgMzItYnl0ZSBhcnJheVxuICAgIGNvbnN0IHQgPSBudW1UbzMyYihkIF4gYnl0ZXNUb051bWJlckJFKHRhZ2dlZEhhc2goJ0JJUDAzNDAvYXV4JywgYSkpKTsgLy8gTGV0IHQgYmUgdGhlIGJ5dGUtd2lzZSB4b3Igb2YgYnl0ZXMoZCkgYW5kIGhhc2gvYXV4KGEpXG4gICAgY29uc3QgcmFuZCA9IHRhZ2dlZEhhc2goJ0JJUDAzNDAvbm9uY2UnLCB0LCBweCwgbSk7IC8vIExldCByYW5kID0gaGFzaC9ub25jZSh0IHx8IGJ5dGVzKFApIHx8IG0pXG4gICAgY29uc3Qga18gPSBtb2ROKGJ5dGVzVG9OdW1iZXJCRShyYW5kKSk7IC8vIExldCBrJyA9IGludChyYW5kKSBtb2QgblxuICAgIGlmIChrXyA9PT0gXzBuKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ3NpZ24gZmFpbGVkOiBrIGlzIHplcm8nKTsgLy8gRmFpbCBpZiBrJyA9IDAuXG4gICAgY29uc3QgeyBieXRlczogcngsIHNjYWxhcjogayB9ID0gc2Nobm9yckdldEV4dFB1YktleShrXyk7IC8vIExldCBSID0gayfii4VHLlxuICAgIGNvbnN0IGUgPSBjaGFsbGVuZ2UocngsIHB4LCBtKTsgLy8gTGV0IGUgPSBpbnQoaGFzaC9jaGFsbGVuZ2UoYnl0ZXMoUikgfHwgYnl0ZXMoUCkgfHwgbSkpIG1vZCBuLlxuICAgIGNvbnN0IHNpZyA9IG5ldyBVaW50OEFycmF5KDY0KTsgLy8gTGV0IHNpZyA9IGJ5dGVzKFIpIHx8IGJ5dGVzKChrICsgZWQpIG1vZCBuKS5cbiAgICBzaWcuc2V0KHJ4LCAwKTtcbiAgICBzaWcuc2V0KG51bVRvMzJiKG1vZE4oayArIGUgKiBkKSksIDMyKTtcbiAgICAvLyBJZiBWZXJpZnkoYnl0ZXMoUCksIG0sIHNpZykgKHNlZSBiZWxvdykgcmV0dXJucyBmYWlsdXJlLCBhYm9ydFxuICAgIGlmICghc2Nobm9yclZlcmlmeShzaWcsIG0sIHB4KSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdzaWduOiBJbnZhbGlkIHNpZ25hdHVyZSBwcm9kdWNlZCcpO1xuICAgIHJldHVybiBzaWc7XG59XG4vKipcbiAqIFZlcmlmaWVzIFNjaG5vcnIgc2lnbmF0dXJlLlxuICogV2lsbCBzd2FsbG93IGVycm9ycyAmIHJldHVybiBmYWxzZSBleGNlcHQgZm9yIGluaXRpYWwgdHlwZSB2YWxpZGF0aW9uIG9mIGFyZ3VtZW50cy5cbiAqL1xuZnVuY3Rpb24gc2Nobm9yclZlcmlmeShzaWduYXR1cmUsIG1lc3NhZ2UsIHB1YmxpY0tleSkge1xuICAgIGNvbnN0IHNpZyA9IGVuc3VyZUJ5dGVzKCdzaWduYXR1cmUnLCBzaWduYXR1cmUsIDY0KTtcbiAgICBjb25zdCBtID0gZW5zdXJlQnl0ZXMoJ21lc3NhZ2UnLCBtZXNzYWdlKTtcbiAgICBjb25zdCBwdWIgPSBlbnN1cmVCeXRlcygncHVibGljS2V5JywgcHVibGljS2V5LCAzMik7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgUCA9IGxpZnRfeChieXRlc1RvTnVtYmVyQkUocHViKSk7IC8vIFAgPSBsaWZ0X3goaW50KHBrKSk7IGZhaWwgaWYgdGhhdCBmYWlsc1xuICAgICAgICBjb25zdCByID0gYnl0ZXNUb051bWJlckJFKHNpZy5zdWJhcnJheSgwLCAzMikpOyAvLyBMZXQgciA9IGludChzaWdbMDozMl0pOyBmYWlsIGlmIHIg4omlIHAuXG4gICAgICAgIGlmICghZmUocikpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IHMgPSBieXRlc1RvTnVtYmVyQkUoc2lnLnN1YmFycmF5KDMyLCA2NCkpOyAvLyBMZXQgcyA9IGludChzaWdbMzI6NjRdKTsgZmFpbCBpZiBzIOKJpSBuLlxuICAgICAgICBpZiAoIWdlKHMpKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICBjb25zdCBlID0gY2hhbGxlbmdlKG51bVRvMzJiKHIpLCBwb2ludFRvQnl0ZXMoUCksIG0pOyAvLyBpbnQoY2hhbGxlbmdlKGJ5dGVzKHIpfHxieXRlcyhQKXx8bSkpJW5cbiAgICAgICAgY29uc3QgUiA9IEdtdWxBZGQoUCwgcywgbW9kTigtZSkpOyAvLyBSID0gc+KLhUcgLSBl4ouFUFxuICAgICAgICBpZiAoIVIgfHwgIVIuaGFzRXZlblkoKSB8fCBSLnRvQWZmaW5lKCkueCAhPT0gcilcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTsgLy8gLWVQID09IChuLWUpUFxuICAgICAgICByZXR1cm4gdHJ1ZTsgLy8gRmFpbCBpZiBpc19pbmZpbml0ZShSKSAvIG5vdCBoYXNfZXZlbl95KFIpIC8geChSKSDiiaAgci5cbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG59XG5leHBvcnQgY29uc3Qgc2Nobm9yciA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gKHtcbiAgICBnZXRQdWJsaWNLZXk6IHNjaG5vcnJHZXRQdWJsaWNLZXksXG4gICAgc2lnbjogc2Nobm9yclNpZ24sXG4gICAgdmVyaWZ5OiBzY2hub3JyVmVyaWZ5LFxuICAgIHV0aWxzOiB7XG4gICAgICAgIHJhbmRvbVByaXZhdGVLZXk6IHNlY3AyNTZrMS51dGlscy5yYW5kb21Qcml2YXRlS2V5LFxuICAgICAgICBsaWZ0X3gsXG4gICAgICAgIHBvaW50VG9CeXRlcyxcbiAgICAgICAgbnVtYmVyVG9CeXRlc0JFLFxuICAgICAgICBieXRlc1RvTnVtYmVyQkUsXG4gICAgICAgIHRhZ2dlZEhhc2gsXG4gICAgICAgIG1vZCxcbiAgICB9LFxufSkpKCk7XG5jb25zdCBpc29NYXAgPSAvKiBAX19QVVJFX18gKi8gKCgpID0+IGlzb2dlbnlNYXAoRnAsIFtcbiAgICAvLyB4TnVtXG4gICAgW1xuICAgICAgICAnMHg4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZTM4ZGFhYWFhOGM3JyxcbiAgICAgICAgJzB4N2QzZDRjODBiYzMyMWQ1YjlmMzE1Y2VhN2ZkNDRjNWQ1OTVkMmZjMGJmNjNiOTJkZmZmMTA0NGYxN2M2NTgxJyxcbiAgICAgICAgJzB4NTM0YzMyOGQyM2YyMzRlNmUyYTQxM2RlY2EyNWNhZWNlNDUwNjE0NDAzN2M0MDMxNGVjYmQwYjUzZDlkZDI2MicsXG4gICAgICAgICcweDhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhkYWFhYWE4OGMnLFxuICAgIF0sXG4gICAgLy8geERlblxuICAgIFtcbiAgICAgICAgJzB4ZDM1NzcxMTkzZDk0OTE4YTljYTM0Y2NiYjdiNjQwZGQ4NmNkNDA5NTQyZjg0ODdkOWZlNmI3NDU3ODFlYjQ5YicsXG4gICAgICAgICcweGVkYWRjNmY2NDM4M2RjMWRmN2M0YjJkNTFiNTQyMjU0MDZkMzZiNjQxZjVlNDFiYmM1MmE1NjYxMmE4YzZkMTQnLFxuICAgICAgICAnMHgwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAxJywgLy8gTEFTVCAxXG4gICAgXSxcbiAgICAvLyB5TnVtXG4gICAgW1xuICAgICAgICAnMHg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YjhlMzhlMjNjJyxcbiAgICAgICAgJzB4Yzc1ZTBjMzJkNWNiN2MwZmE5ZDBhNTRiMTJhMGE2ZDU2NDdhYjA0NmQ2ODZkYTZmZGZmYzkwZmMyMDFkNzFhMycsXG4gICAgICAgICcweDI5YTYxOTQ2OTFmOTFhNzM3MTUyMDllZjY1MTJlNTc2NzIyODMwYTIwMWJlMjAxOGE3NjVlODVhOWVjZWU5MzEnLFxuICAgICAgICAnMHgyZjY4NGJkYTEyZjY4NGJkYTEyZjY4NGJkYTEyZjY4NGJkYTEyZjY4NGJkYTEyZjY4NGJkYTEyZjM4ZTM4ZDg0JyxcbiAgICBdLFxuICAgIC8vIHlEZW5cbiAgICBbXG4gICAgICAgICcweGZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZlZmZmZmY5M2InLFxuICAgICAgICAnMHg3YTA2NTM0YmI4YmRiNDlmZDVlOWU2NjMyNzIyYzI5ODk0NjdjMWJmYzhlOGQ5NzhkZmI0MjVkMjY4NWMyNTczJyxcbiAgICAgICAgJzB4NjQ4NGFhNzE2NTQ1Y2EyY2YzYTcwYzNmYThmZTMzN2UwYTNkMjExNjJmMGQ2Mjk5YTdiZjgxOTJiZmQyYTc2ZicsXG4gICAgICAgICcweDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDEnLCAvLyBMQVNUIDFcbiAgICBdLFxuXS5tYXAoKGkpID0+IGkubWFwKChqKSA9PiBCaWdJbnQoaikpKSkpKCk7XG5jb25zdCBtYXBTV1UgPSAvKiBAX19QVVJFX18gKi8gKCgpID0+IG1hcFRvQ3VydmVTaW1wbGVTV1UoRnAsIHtcbiAgICBBOiBCaWdJbnQoJzB4M2Y4NzMxYWJkZDY2MWFkY2EwOGE1NTU4ZjBmNWQyNzJlOTUzZDM2M2NiNmYwZTVkNDA1NDQ3YzAxYTQ0NDUzMycpLFxuICAgIEI6IEJpZ0ludCgnMTc3MScpLFxuICAgIFo6IEZwLmNyZWF0ZShCaWdJbnQoJy0xMScpKSxcbn0pKSgpO1xuY29uc3QgaHRmID0gLyogQF9fUFVSRV9fICovICgoKSA9PiBjcmVhdGVIYXNoZXIoc2VjcDI1NmsxLlByb2plY3RpdmVQb2ludCwgKHNjYWxhcnMpID0+IHtcbiAgICBjb25zdCB7IHgsIHkgfSA9IG1hcFNXVShGcC5jcmVhdGUoc2NhbGFyc1swXSkpO1xuICAgIHJldHVybiBpc29NYXAoeCwgeSk7XG59LCB7XG4gICAgRFNUOiAnc2VjcDI1NmsxX1hNRDpTSEEtMjU2X1NTV1VfUk9fJyxcbiAgICBlbmNvZGVEU1Q6ICdzZWNwMjU2azFfWE1EOlNIQS0yNTZfU1NXVV9OVV8nLFxuICAgIHA6IEZwLk9SREVSLFxuICAgIG06IDEsXG4gICAgazogMTI4LFxuICAgIGV4cGFuZDogJ3htZCcsXG4gICAgaGFzaDogc2hhMjU2LFxufSkpKCk7XG5leHBvcnQgY29uc3QgaGFzaFRvQ3VydmUgPSAvKiBAX19QVVJFX18gKi8gKCgpID0+IGh0Zi5oYXNoVG9DdXJ2ZSkoKTtcbmV4cG9ydCBjb25zdCBlbmNvZGVUb0N1cnZlID0gLyogQF9fUFVSRV9fICovICgoKSA9PiBodGYuZW5jb2RlVG9DdXJ2ZSkoKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNlY3AyNTZrMS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/secp256k1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_assert.js":
/*!***************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_assert.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bool: () => (/* binding */ bool),\n/* harmony export */   bytes: () => (/* binding */ bytes),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   exists: () => (/* binding */ exists),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   output: () => (/* binding */ output)\n/* harmony export */ });\nfunction number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`Expected boolean, not ${b}`);\n}\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\n\nconst assert = { number, bool, bytes, hash, exists, output };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (assert);\n//# sourceMappingURL=_assert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vX2Fzc2VydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBLG1EQUFtRCxFQUFFO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxFQUFFO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsUUFBUSxrQkFBa0IsU0FBUztBQUM1RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUZBQWlGLElBQUk7QUFDckY7QUFDQTtBQUNxRDtBQUNyRCxpQkFBaUI7QUFDakIsaUVBQWUsTUFBTSxFQUFDO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFxlc21cXF9hc3NlcnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gbnVtYmVyKG4pIHtcbiAgICBpZiAoIU51bWJlci5pc1NhZmVJbnRlZ2VyKG4pIHx8IG4gPCAwKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFdyb25nIHBvc2l0aXZlIGludGVnZXI6ICR7bn1gKTtcbn1cbmZ1bmN0aW9uIGJvb2woYikge1xuICAgIGlmICh0eXBlb2YgYiAhPT0gJ2Jvb2xlYW4nKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEV4cGVjdGVkIGJvb2xlYW4sIG5vdCAke2J9YCk7XG59XG5mdW5jdGlvbiBieXRlcyhiLCAuLi5sZW5ndGhzKSB7XG4gICAgaWYgKCEoYiBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0V4cGVjdGVkIFVpbnQ4QXJyYXknKTtcbiAgICBpZiAobGVuZ3Rocy5sZW5ndGggPiAwICYmICFsZW5ndGhzLmluY2x1ZGVzKGIubGVuZ3RoKSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFeHBlY3RlZCBVaW50OEFycmF5IG9mIGxlbmd0aCAke2xlbmd0aHN9LCBub3Qgb2YgbGVuZ3RoPSR7Yi5sZW5ndGh9YCk7XG59XG5mdW5jdGlvbiBoYXNoKGhhc2gpIHtcbiAgICBpZiAodHlwZW9mIGhhc2ggIT09ICdmdW5jdGlvbicgfHwgdHlwZW9mIGhhc2guY3JlYXRlICE9PSAnZnVuY3Rpb24nKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0hhc2ggc2hvdWxkIGJlIHdyYXBwZWQgYnkgdXRpbHMud3JhcENvbnN0cnVjdG9yJyk7XG4gICAgbnVtYmVyKGhhc2gub3V0cHV0TGVuKTtcbiAgICBudW1iZXIoaGFzaC5ibG9ja0xlbik7XG59XG5mdW5jdGlvbiBleGlzdHMoaW5zdGFuY2UsIGNoZWNrRmluaXNoZWQgPSB0cnVlKSB7XG4gICAgaWYgKGluc3RhbmNlLmRlc3Ryb3llZClcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdIYXNoIGluc3RhbmNlIGhhcyBiZWVuIGRlc3Ryb3llZCcpO1xuICAgIGlmIChjaGVja0ZpbmlzaGVkICYmIGluc3RhbmNlLmZpbmlzaGVkKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0hhc2gjZGlnZXN0KCkgaGFzIGFscmVhZHkgYmVlbiBjYWxsZWQnKTtcbn1cbmZ1bmN0aW9uIG91dHB1dChvdXQsIGluc3RhbmNlKSB7XG4gICAgYnl0ZXMob3V0KTtcbiAgICBjb25zdCBtaW4gPSBpbnN0YW5jZS5vdXRwdXRMZW47XG4gICAgaWYgKG91dC5sZW5ndGggPCBtaW4pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBkaWdlc3RJbnRvKCkgZXhwZWN0cyBvdXRwdXQgYnVmZmVyIG9mIGxlbmd0aCBhdCBsZWFzdCAke21pbn1gKTtcbiAgICB9XG59XG5leHBvcnQgeyBudW1iZXIsIGJvb2wsIGJ5dGVzLCBoYXNoLCBleGlzdHMsIG91dHB1dCB9O1xuY29uc3QgYXNzZXJ0ID0geyBudW1iZXIsIGJvb2wsIGJ5dGVzLCBoYXNoLCBleGlzdHMsIG91dHB1dCB9O1xuZXhwb3J0IGRlZmF1bHQgYXNzZXJ0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9X2Fzc2VydC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_sha2.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_sha2.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA2: () => (/* binding */ SHA2)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nclass SHA2 extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        const { view, buffer, blockLen } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.output)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_sha2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_u64.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_u64.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js":
/*!******************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// See utils.ts for details.\n// The file will throw on node.js 14 and earlier.\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNrQztBQUMzQixlQUFlLDJNQUFFLFdBQVcsMk1BQUUsaUJBQWlCLDBOQUFpQixHQUFHLGtEQUFZO0FBQ3RGIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFxlc21cXGNyeXB0b05vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gV2UgdXNlIFdlYkNyeXB0byBha2EgZ2xvYmFsVGhpcy5jcnlwdG8sIHdoaWNoIGV4aXN0cyBpbiBicm93c2VycyBhbmQgbm9kZS5qcyAxNisuXG4vLyBTZWUgdXRpbHMudHMgZm9yIGRldGFpbHMuXG4vLyBUaGUgZmlsZSB3aWxsIHRocm93IG9uIG5vZGUuanMgMTQgYW5kIGVhcmxpZXIuXG4vLyBAdHMtaWdub3JlXG5pbXBvcnQgKiBhcyBuYyBmcm9tICdub2RlOmNyeXB0byc7XG5leHBvcnQgY29uc3QgY3J5cHRvID0gbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAnd2ViY3J5cHRvJyBpbiBuYyA/IG5jLndlYmNyeXB0byA6IHVuZGVmaW5lZDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyeXB0b05vZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/hmac.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/hmac.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n// HMAC (RFC 2104)\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.hash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/hmac.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha256.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha256.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n/* harmony import */ var _sha2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_sha2.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_sha2.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _sha2_js__WEBPACK_IMPORTED_MODULE_0__.SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA256());\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new SHA224());\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha3.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha3.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keccak: () => (/* binding */ Keccak),\n/* harmony export */   keccakP: () => (/* binding */ keccakP),\n/* harmony export */   keccak_224: () => (/* binding */ keccak_224),\n/* harmony export */   keccak_256: () => (/* binding */ keccak_256),\n/* harmony export */   keccak_384: () => (/* binding */ keccak_384),\n/* harmony export */   keccak_512: () => (/* binding */ keccak_512),\n/* harmony export */   sha3_224: () => (/* binding */ sha3_224),\n/* harmony export */   sha3_256: () => (/* binding */ sha3_256),\n/* harmony export */   sha3_384: () => (/* binding */ sha3_384),\n/* harmony export */   sha3_512: () => (/* binding */ sha3_512),\n/* harmony export */   shake128: () => (/* binding */ shake128),\n/* harmony export */   shake256: () => (/* binding */ shake256)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_assert.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n// Various per round constants calculations\nconst [SHA3_PI, SHA3_ROTL, _SHA3_IOTA] = [[], [], []];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.split)(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBH)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSH)(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBL)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSL)(h, l, s));\n// Same as keccakf1600, but allows to skip some rounds\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    B.fill(0);\n}\nclass Keccak extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        // Can be passed from user as dkLen\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.number)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        if (0 >= this.blockLen || this.blockLen >= 200)\n            throw new Error('Sha3 supports only keccak-f1600 function');\n        this.state = new Uint8Array(200);\n        this.state32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.state);\n    }\n    keccak() {\n        keccakP(this.state32, this.rounds);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.exists)(this);\n        const { blockLen, state } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.exists)(this, false);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.bytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.number)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.output)(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        this.state.fill(0);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new Keccak(blockLen, suffix, outputLen));\nconst sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */\nconst sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\nconst sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\nconst sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\nconst keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */\nconst keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\nconst keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\nconst keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\nconst genShake = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapXOFConstructorWithOpts)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\nconst shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\nconst shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n//# sourceMappingURL=sha3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\");\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\n\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to different type\nconst u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nconst u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nconst createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nconst rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nconst isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nconst nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\n// For runtime check if class implements interface\nclass Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nfunction wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function') {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/utils.js\n");

/***/ })

};
;