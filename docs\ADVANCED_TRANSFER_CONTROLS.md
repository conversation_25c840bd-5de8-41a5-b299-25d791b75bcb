# Advanced Transfer Controls for ERC-3643 Security Tokens

This document describes the advanced transfer control features implemented in our ERC-3643 compliant security tokens. These features provide additional layers of control and compliance for token transfers beyond the basic whitelist and freeze functionality.

## Overview

The advanced transfer controls include three main features:

1. **Conditional Transfers (Approval Flow)** - Require pre-approval for all transfers
2. **Transfer Whitelisting** - Additional layer to control who can initiate transfers
3. **Transfer Fees** - Collect percentage-based fees on transfers

## Features

### 1. Conditional Transfers (Approval Flow)

**Purpose**: Require explicit approval from agents before any transfer can be executed.

**How it works**:
- When enabled, all regular transfers between investors are blocked
- Transfers must be pre-approved by an agent using `approveTransfer()`
- Users execute approved transfers using `executeApprovedTransfer()`
- Each transfer has a unique nonce to prevent replay attacks

**Use Cases**:
- High-security tokens requiring manual review of all transfers
- Compliance with regulations requiring transfer approval
- Preventing unauthorized or suspicious transfers

**Functions**:
```solidity
// Enable/disable conditional transfers (Admin only)
function setConditionalTransfers(bool enabled) external;

// Approve a specific transfer (Agent only)
function approveTransfer(address from, address to, uint256 amount, uint256 nonce) external;

// Execute a pre-approved transfer (Token holder)
function executeApprovedTransfer(address to, uint256 amount, uint256 nonce) external;

// Get the next nonce for an address
function getTransferNonce(address account) external view returns (uint256);
```

**Events**:
```solidity
event ConditionalTransfersUpdated(bool enabled);
event TransferApproved(bytes32 indexed transferId, address indexed from, address indexed to, uint256 amount);
```

### 2. Transfer Whitelisting

**Purpose**: Add an additional layer of control over who can initiate transfers, separate from the identity registry whitelist.

**How it works**:
- When enabled, only addresses in the transfer whitelist can initiate transfers
- This is separate from the identity registry whitelist (both sender and recipient must still be in the identity registry)
- Agents can manage the transfer whitelist

**Use Cases**:
- Restricting transfers to specific authorized dealers or market makers
- Implementing tiered access controls
- Temporary restrictions on certain addresses

**Functions**:
```solidity
// Enable/disable transfer whitelisting (Admin only)
function setTransferWhitelist(bool enabled) external;

// Add/remove address from transfer whitelist (Agent only)
function setTransferWhitelistAddress(address account, bool whitelisted) external;

// Check if address is transfer whitelisted
function isTransferWhitelisted(address account) external view returns (bool);
```

**Events**:
```solidity
event TransferWhitelistUpdated(bool enabled);
event TransferWhitelistAddressUpdated(address indexed account, bool whitelisted);
```

### 3. Transfer Fees

**Purpose**: Collect a percentage fee on transfers to a designated collector address.

**How it works**:
- When enabled, a percentage of each transfer is sent to the fee collector
- Fee percentage is specified in basis points (100 = 1%)
- Fee collector must be a whitelisted investor
- Fees are deducted from the transfer amount

**Use Cases**:
- Transaction fees for platform revenue
- Regulatory fees or taxes
- Market maker fees

**Functions**:
```solidity
// Enable/disable transfer fees (Admin only)
function setTransferFees(bool enabled, uint256 feePercentage, address feeCollector) external;

// Get transfer fee configuration
function getTransferFeeConfig() external view returns (uint256 feePercentage, address feeCollector);
```

**Events**:
```solidity
event TransferFeesUpdated(bool enabled, uint256 feePercentage, address feeCollector);
event TransferFeeCollected(address indexed from, address indexed to, uint256 transferAmount, uint256 feeAmount);
```

## Admin Panel Integration

The admin panel provides a user-friendly interface to manage these features:

### Transfer Controls Page

Access via: `/transfer-controls` or `/transfer-controls?token=<address>`

**Features**:
- Select token from dropdown or via URL parameter
- Enable/disable each transfer control feature
- Configure transfer fees with percentage and collector address
- Real-time status display
- Transaction confirmation and explorer links

### Token Details Integration

Each token details page includes a "🔒 Transfer Controls" button that links directly to the transfer controls page for that specific token.

## Implementation Details

### Contract Architecture

The advanced transfer controls are implemented in the `SecurityToken.sol` contract with:

- State variables for each feature's configuration
- Internal flags to track transfer context (approved transfers, forced transfers)
- Enhanced `_update()` function that applies all transfer controls
- Role-based access control (Admin and Agent roles)

### Gas Optimization

- Transfer fees are processed in the same transaction as the transfer
- Conditional transfers use nonces to prevent replay attacks efficiently
- State variables are packed to minimize storage costs

### Security Considerations

- All features respect existing compliance checks (whitelist, freeze status)
- Forced transfers bypass advanced controls for emergency situations
- Nonce system prevents replay attacks in conditional transfers
- Fee collector must be whitelisted to receive fees

## Usage Examples

### Example 1: High-Security Token with Approval Flow

```javascript
// Enable conditional transfers
await token.setConditionalTransfers(true);

// Agent approves a transfer
const nonce = await token.getTransferNonce(investor1.address);
await token.connect(agent).approveTransfer(investor1.address, investor2.address, amount, nonce);

// Investor executes the approved transfer
await token.connect(investor1).executeApprovedTransfer(investor2.address, amount, nonce);
```

### Example 2: Market Maker with Transfer Fees

```javascript
// Enable transfer whitelisting and fees
await token.setTransferWhitelist(true);
await token.setTransferFees(true, 100, feeCollector.address); // 1% fee

// Whitelist the market maker
await token.connect(agent).setTransferWhitelistAddress(marketMaker.address, true);

// Market maker can now transfer with fees collected
await token.connect(marketMaker).transfer(investor.address, amount);
```

## Testing

Comprehensive tests are available in `test/AdvancedTransferControls.test.js` covering:

- Individual feature functionality
- Combined feature scenarios
- Error conditions and edge cases
- Role-based access control
- Event emissions

Run tests with:
```bash
npx hardhat test test/AdvancedTransferControls.test.js
```

## Deployment

The advanced transfer controls are included in the standard SecurityToken deployment. No additional deployment steps are required.

## Compatibility

- Compatible with existing ERC-3643 compliance features
- Works with all existing whitelist and freeze functionality
- Maintains backward compatibility with standard ERC-20 interfaces
- Supports proxy upgrades for future enhancements
