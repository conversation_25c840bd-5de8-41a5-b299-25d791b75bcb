require("dotenv").config();
const { ethers } = require("hardhat");
const factoryABI = require("../admin-panel/src/contracts/SecurityTokenFactory.json").abi;

async function main() {
  try {
    // Get values from environment variables or use defaults
    const tokenName = process.env.TOKEN_NAME || "Example KYC Security Token";
    const tokenSymbol = process.env.TOKEN_SYMBOL || "KYCST";
    const maxSupply = process.env.MAX_SUPPLY || "1000000";
    const adminAddress = process.env.ADMIN_ADDRESS || "";
    const tokenPrice = process.env.TOKEN_PRICE || "10 USD";
    const bonusTiers = process.env.BONUS_TIERS || "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%";
    
    // Validate input
    if (!adminAddress) {
      console.error("Please set the ADMIN_ADDRESS environment variable");
      process.exit(1);
    }
    
    // Get the network configuration
    const network = process.env.NETWORK || "amoy";
    
    // Get network specific factory address
    let factoryAddress;
    if (network === "amoy") {
      factoryAddress = "******************************************"; // Example factory address for Amoy
    } else if (network === "polygon") {
      factoryAddress = "******************************************"; // Example factory address for Polygon
    } else {
      console.error(`Unsupported network: ${network}`);
      process.exit(1);
    }
    
    // Connect to the provider
    const [deployer] = await ethers.getSigners();
    
    console.log("Deploying token with parameters:");
    console.log(`  Network: ${network}`);
    console.log(`  Token Name: ${tokenName}`);
    console.log(`  Symbol: ${tokenSymbol}`);
    console.log(`  Max Supply: ${maxSupply}`);
    console.log(`  Admin Address: ${adminAddress}`);
    console.log(`  Token Price: ${tokenPrice}`);
    console.log(`  Bonus Tiers: ${bonusTiers}`);
    console.log(`  KYC Support: Enabled`);
    console.log(`  Factory Address: ${factoryAddress}`);
    console.log(`  Deployer Address: ${deployer.address}`);
    console.log("---");
    
    // Convert maxSupply to wei
    const maxSupplyWei = ethers.parseUnits(maxSupply, 18);
    
    // Connect to the factory
    const factory = new ethers.Contract(
      factoryAddress,
      factoryABI,
      deployer
    );
    
    console.log("Connected to factory, checking permissions...");
    
    // Check if the deployer has the DEPLOYER_ROLE
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    const hasRole = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
    
    if (!hasRole) {
      console.error(`Deployer address ${deployer.address} does not have the DEPLOYER_ROLE`);
      process.exit(1);
    }
    
    console.log("Deployer has the required permissions.");
    
    // Check if KYC is supported
    console.log("Checking KYC support...");
    let supportsKYC = true;
    
    try {
      // Check if whitelistWithKYCImplementation exists
      const kycImplementation = await factory.whitelistWithKYCImplementation();
      console.log("KYC implementation address:", kycImplementation);
      
      // Check function exists
      const hasKYCFunction = factory.interface.fragments.some(
        fragment => fragment.type === "function" && 
                    fragment.name === "deploySecurityTokenWithOptions"
      );
      
      if (!hasKYCFunction) {
        console.error("Error: Factory contract does not support deploySecurityTokenWithOptions function");
        supportsKYC = false;
      }
      
      if (kycImplementation === ethers.ZeroAddress) {
        console.error("Error: KYC implementation address is not set in the factory contract");
        supportsKYC = false;
      }
    } catch (err) {
      console.error("Error checking KYC support:", err.message);
      supportsKYC = false;
    }
    
    if (!supportsKYC) {
      console.error("This factory contract does not support KYC functionality.");
      console.error("Please deploy an updated factory contract or use the standard token deployment script.");
      process.exit(1);
    }
    
    console.log("KYC functionality is supported.");
    
    // Deploy the token with KYC support
    console.log("Deploying token with KYC support...");
    
    // Prepare gas parameters based on the network
    let gasLimit;
    if (network === "amoy") {
      gasLimit = ethers.parseUnits("5000000", 0); // 5 million gas
    } else {
      gasLimit = ethers.parseUnits("2000000", 0); // 2 million gas
    }
    
    // Deploy the token
    const tx = await factory.deploySecurityTokenWithOptions(
      tokenName,
      tokenSymbol,
      maxSupplyWei,
      adminAddress,
      tokenPrice,
      bonusTiers,
      true, // Enable KYC
      { gasLimit }
    );
    
    console.log(`Transaction sent: ${tx.hash}`);
    console.log("Waiting for confirmation...");
    
    // Wait for the transaction to be mined
    const receipt = await tx.wait();
    
    console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
    
    // Get the token address using getTokenAddressBySymbol
    const tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
    
    console.log("Deployment successful!");
    console.log(`Token Address: ${tokenAddress}`);
    
    // Look for the TokenDeployed event to get the whitelist address
    const deployEvent = receipt.logs
      .filter(log => log.topics[0] === factory.interface.getEventTopic('TokenDeployed'))
      .map(log => {
        try {
          return factory.interface.parseLog({
            topics: log.topics,
            data: log.data
          });
        } catch (e) {
          return null;
        }
      })
      .find(event => event !== null);
    
    if (deployEvent) {
      console.log(`Whitelist Address: ${deployEvent.args.whitelistAddress}`);
    }
    
    console.log("---");
    console.log("All done! The token has been deployed with KYC support.");
    
  } catch (error) {
    console.error("Error deploying token:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  }); 