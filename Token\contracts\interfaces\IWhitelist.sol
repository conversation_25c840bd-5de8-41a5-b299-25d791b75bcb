// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

/**
 * @title IWhitelist
 * @dev Interface for whitelist/identity registry management for ERC-3643 compliant tokens
 */
interface IWhitelist {
    /**
     * @dev Emitted when an address is added to the whitelist
     */
    event AddedToWhitelist(address indexed account);
    
    /**
     * @dev Emitted when an address is removed from the whitelist
     */
    event RemovedFromWhitelist(address indexed account);
    
    /**
     * @dev Emitted when an address is frozen
     */
    event AddressFrozen(address indexed account);
    
    /**
     * @dev Emitted when an address is unfrozen
     */
    event AddressUnfrozen(address indexed account);
    
    /**
     * @dev Emitted when KYC approval is granted to an address
     */
    event KycApproved(address indexed account);
    
    /**
     * @dev Emitted when KYC approval is revoked from an address
     */
    event KycRevoked(address indexed account);

    /**
     * @dev Check if an address is whitelisted
     * @param _address The address to check
     * @return bool True if the address is whitelisted, false otherwise
     */
    function isWhitelisted(address _address) external view returns (bool);

    /**
     * @dev Add an address to the whitelist
     * @param _address The address to add to the whitelist
     */
    function addToWhitelist(address _address) external;

    /**
     * @dev Remove an address from the whitelist
     * @param _address The address to remove from the whitelist
     */
    function removeFromWhitelist(address _address) external;

    /**
     * @dev Check if an address is frozen
     * @param _address The address to check
     * @return bool True if the address is frozen, false otherwise
     */
    function isFrozen(address _address) external view returns (bool);

    /**
     * @dev Freeze an address
     * @param _address The address to freeze
     */
    function freezeAddress(address _address) external;

    /**
     * @dev Unfreeze an address
     * @param _address The address to unfreeze
     */
    function unfreezeAddress(address _address) external;
    
    /**
     * @dev Batch add addresses to whitelist
     * @param _addresses The addresses to add to the whitelist
     */
    function batchAddToWhitelist(address[] calldata _addresses) external;
    
    /**
     * @dev Batch remove addresses from whitelist
     * @param _addresses The addresses to remove from the whitelist
     */
    function batchRemoveFromWhitelist(address[] calldata _addresses) external;
    
    /**
     * @dev Batch freeze addresses
     * @param _addresses The addresses to freeze
     */
    function batchFreezeAddresses(address[] calldata _addresses) external;
    
    /**
     * @dev Batch unfreeze addresses
     * @param _addresses The addresses to unfreeze
     */
    function batchUnfreezeAddresses(address[] calldata _addresses) external;

    /**
     * @dev Check if an address is KYC approved
     * @param _address The address to check
     * @return bool True if the address is KYC approved, false otherwise
     */
    function isKycApproved(address _address) external view returns (bool);

    /**
     * @dev Approve KYC for an address
     * @param _address The address to approve KYC for
     */
    function approveKyc(address _address) external;

    /**
     * @dev Revoke KYC approval for an address
     * @param _address The address to revoke KYC approval from
     */
    function revokeKyc(address _address) external;

    /**
     * @dev Batch approve KYC for addresses
     * @param _addresses The addresses to approve KYC for
     */
    function batchApproveKyc(address[] calldata _addresses) external;

    /**
     * @dev Batch revoke KYC approval for addresses
     * @param _addresses The addresses to revoke KYC approval from
     */
    function batchRevokeKyc(address[] calldata _addresses) external;
}