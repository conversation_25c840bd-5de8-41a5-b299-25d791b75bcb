@echo off
echo Deploying Security Token Factory...

:: Set the private key from .env.local if not already in environment
if not defined PRIVATE_KEY (
    for /f "tokens=2 delims==" %%a in ('findstr /C:"CONTRACT_ADMIN_PRIVATE_KEY" .env.local') do (
        set PRIVATE_KEY=%%a
        echo Using private key from .env.local
    )
)

:: Check if network is provided as argument
if "%1"=="" (
    echo No network specified, using 'hardhat' network
    set NETWORK=hardhat
) else (
    set NETWORK=%1
    echo Using network: %NETWORK%
)

:: Run the deployment script
echo Running deployment script...
npx hardhat run scripts/01-deploy-factory.js --network %NETWORK%

:: Check if deployment was successful
if %ERRORLEVEL% NEQ 0 (
    echo Deployment failed
    exit /b %ERRORLEVEL%
)

echo Deployment completed successfully!