const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Simple Agreement Features Test...");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  // Factory address
  const factoryAddress = "******************************************";
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  // Check if the new implementation has agreement functions
  const newImplAddress = await factory.securityTokenImplementation();
  console.log("Current SecurityToken implementation:", newImplAddress);

  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  const impl = SecurityToken.attach(newImplAddress);

  console.log("\n🔍 Checking if agreement functions exist in implementation...");

  // Check function signatures
  const functions = [
    'getAgreementUrl',
    'hasAcceptedAgreement',
    'acceptAgreement',
    'getAgreementAcceptanceTimestamp',
    'setAgreementUrl'
  ];

  for (const funcName of functions) {
    try {
      // Try to call the function (will fail because not initialized, but we just want to check it exists)
      await impl[funcName].staticCall();
      console.log(`✅ ${funcName} function exists`);
    } catch (error) {
      if (error.message.includes("function selector was not recognized")) {
        console.log(`❌ ${funcName} function missing`);
      } else {
        console.log(`✅ ${funcName} function exists (expected revert: ${error.message.split('.')[0]})`);
      }
    }
  }

  // Try deploying a simple token without agreement URL first
  console.log("\n🚀 Testing simple deployment without agreement...");
  
  const testSymbol = "SMP" + Date.now().toString().slice(-4);
  
  try {
    const deployTx = await factory.deploySecurityToken(
      "Simple Test Token",
      testSymbol,
      0,
      ethers.parseUnits("1000", 0),
      deployer.address,
      "10 USD",
      "Tier 1: 5%",
      "Simple test token",
      ""
    );

    console.log("Simple deployment transaction:", deployTx.hash);
    const receipt = await deployTx.wait();
    console.log("✅ Simple deployment successful");

    // Find the token address
    const tokenDeployedEvent = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (tokenDeployedEvent) {
      const parsed = factory.interface.parseLog(tokenDeployedEvent);
      const tokenAddress = parsed.args.tokenAddress;
      console.log("Token deployed at:", tokenAddress);

      // Test agreement functions on deployed token
      const token = SecurityToken.attach(tokenAddress);
      
      try {
        const agreementUrl = await token.getAgreementUrl();
        console.log("✅ Agreement URL (should be empty):", agreementUrl);
      } catch (error) {
        console.log("❌ getAgreementUrl failed:", error.message);
      }

      try {
        const hasAccepted = await token.hasAcceptedAgreement(deployer.address);
        console.log("✅ hasAcceptedAgreement:", hasAccepted);
      } catch (error) {
        console.log("❌ hasAcceptedAgreement failed:", error.message);
      }
    }

  } catch (error) {
    console.log("❌ Simple deployment failed:", error.message);
  }

  console.log("\n📊 TEST SUMMARY");
  console.log("================");
  console.log("Implementation Address:", newImplAddress);
  console.log("Agreement functions should be available in new tokens");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
