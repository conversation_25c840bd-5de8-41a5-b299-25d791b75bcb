'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { CheckCircleIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { WalletConnection } from './WalletConnection'
import { QualificationForm } from './QualificationForm'
import { AutomaticKYC } from './AutomaticKYC'
import { AgreementAcceptance } from './AgreementAcceptance'

interface QualificationStep {
  id: string
  title: string
  description: string
  status: 'completed' | 'current' | 'pending' | 'error'
}

export function QualificationModule() {
  const [activeStep, setActiveStep] = useState<string | null>(null) // Which step is currently being worked on
  const [kycStatus, setKycStatus] = useState<string>('idle')
  const [kycError, setKycError] = useState<string | null>(null)
  const [agreementAccepted, setAgreementAccepted] = useState(false)

  // Fetch user's qualification status
  const { data: profile, isLoading, error } = useQuery({
    queryKey: ['client-profile'],
    queryFn: async () => {
      console.log('Fetching client profile...')
      const response = await fetch('/api/client/profile')
      console.log('Profile response status:', response.status)
      if (response.ok) {
        const data = await response.json()
        console.log('Profile data received:', data)
        return data
      }
      if (response.status === 404) {
        console.log('No profile found (404)')
        return null
      }
      throw new Error(`Failed to fetch profile: ${response.status}`)
    },
  })

  const { data: walletStatus } = useQuery({
    queryKey: ['wallet-status'],
    queryFn: async () => {
      const response = await fetch('/api/client/wallet')
      if (response.ok) {
        return response.json()
      }
      return null
    },
  })

  // Check agreement status from API
  const { data: agreementStatus } = useQuery({
    queryKey: ['agreement-status'],
    queryFn: async () => {
      const response = await fetch('/api/client/agreement')
      if (response.ok) {
        return response.json()
      }
      return null
    },
  })

  // Update agreement status when data is loaded
  useEffect(() => {
    if (agreementStatus?.accepted) {
      setAgreementAccepted(true)
    }
  }, [agreementStatus])

  // Helper functions to determine step status (independent steps)
  const getAgreementStatus = (): QualificationStep['status'] => {
    if (agreementAccepted) return 'completed'
    if (activeStep === 'agreement') return 'current'
    return 'pending'
  }

  const getQualificationStatus = (): QualificationStep['status'] => {
    if (profile) return 'completed'
    if (activeStep === 'qualification') return 'current'
    return 'pending'
  }

  const getWalletStatus = (): QualificationStep['status'] => {
    if (walletStatus?.verified) return 'completed'
    if (activeStep === 'wallet') return 'current'
    return 'pending'
  }

  const getKYCStatus = (): QualificationStep['status'] => {
    // Check automatic KYC status
    if (kycStatus === 'completed') return 'completed'
    if (kycStatus === 'failed') return 'error'
    if (kycStatus === 'pending') return 'current'
    if (activeStep === 'kyc') return 'current'
    return 'pending'
  }

  // Handle step completion callbacks
  const handleAgreementComplete = () => {
    setAgreementAccepted(true)
    setActiveStep(null) // Close the active step
  }

  const handleQualificationComplete = () => {
    setActiveStep(null) // Close the active step
  }

  const handleWalletComplete = () => {
    setActiveStep(null) // Close the active step
  }

  // Handle step click to make it active
  const handleStepClick = (stepId: string) => {
    if (activeStep === stepId) {
      setActiveStep(null) // Close if already active
    } else {
      setActiveStep(stepId) // Open the clicked step
    }
  }

  const handleKYCStatusChange = (status: string, error?: string) => {
    setKycStatus(status)
    if (error) {
      setKycError(error)
    } else {
      setKycError(null)
    }
  }

  const steps: QualificationStep[] = [
    {
      id: 'agreement',
      title: 'Review & Accept Agreement',
      description: 'Review and accept the investment agreement document.',
      status: getAgreementStatus(),
    },
    {
      id: 'qualification',
      title: 'Complete Qualification Form',
      description: 'Provide your personal and financial information for qualification.',
      status: getQualificationStatus(),
    },
    {
      id: 'wallet',
      title: 'Connect & Verify Wallet',
      description: 'Connect your cryptocurrency wallet and verify ownership.',
      status: getWalletStatus(),
    },
    {
      id: 'kyc',
      title: 'Identity Verification',
      description: 'Automatic identity verification using advanced document scanning.',
      status: getKYCStatus(),
    },
  ]

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />
      case 'current':
        return <ClockIcon className="h-6 w-6 text-blue-500" />
      case 'error':
        return <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
      default:
        return <div className="h-6 w-6 rounded-full border-2 border-gray-300" />
    }
  }

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200'
      case 'current': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'error': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const completedSteps = steps.filter(step => step.status === 'completed').length
  const totalSteps = steps.length
  const progressPercentage = (completedSteps / totalSteps) * 100

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Security Token Qualification</h1>
        <p className="text-lg text-gray-600 mb-6">
          Complete the following steps to qualify for security token investment
        </p>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-3 mb-8">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>

        <p className="text-sm text-gray-500">
          {completedSteps} of {totalSteps} steps completed
        </p>
      </div>

      {/* Steps */}
      <div className="space-y-6">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`border rounded-lg transition-all duration-200 ${getStepColor(step.status)} ${
              step.status !== 'completed' ? 'cursor-pointer hover:shadow-md' : ''
            }`}
          >
            <div
              className="p-6"
              onClick={() => step.status !== 'completed' && handleStepClick(step.id)}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 mt-1">
                  {getStepIcon(step.status)}
                </div>

                <div className="flex-grow">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold">
                      {index + 1}. {step.title}
                    </h3>

                    <div className="flex items-center space-x-2">
                      {step.status === 'completed' && (
                        <span className="text-sm font-medium text-green-600">Completed</span>
                      )}
                      {step.status === 'current' && (
                        <span className="text-sm font-medium text-blue-600">In Progress</span>
                      )}
                      {step.status === 'error' && (
                        <span className="text-sm font-medium text-red-600">Action Required</span>
                      )}
                      {step.status !== 'completed' && (
                        <span className="text-xs text-gray-500">Click to {activeStep === step.id ? 'close' : 'open'}</span>
                      )}
                    </div>
                  </div>

                  <p className="text-gray-700 mb-2">{step.description}</p>
                </div>
              </div>
            </div>

            {/* Step content - only show when step is active or completed */}
            {(activeStep === step.id || step.status === 'completed') && (
              <div className="px-6 pb-6 border-t border-gray-200 pt-4">

                {/* Step-specific content */}
                {step.id === 'agreement' && (
                  <div className="space-y-4">
                    {step.status === 'completed' ? (
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-green-800 font-medium">✅ Agreement Accepted</p>
                        <p className="text-green-600 text-sm mt-1">
                          You have successfully accepted the investment agreement.
                        </p>
                        {agreementStatus?.acceptedAt && (
                          <p className="text-green-600 text-xs mt-1">
                            Accepted on: {new Date(agreementStatus.acceptedAt).toLocaleString()}
                          </p>
                        )}
                      </div>
                    ) : (
                      <AgreementAcceptance
                        onComplete={handleAgreementComplete}
                        existingProfile={profile}
                      />
                    )}
                  </div>
                )}

                {step.id === 'qualification' && (
                  <div className="space-y-4">
                    {step.status === 'completed' ? (
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-green-800 font-medium">✅ Qualification Complete</p>
                        <p className="text-green-600 text-sm mt-1">
                          Your qualification form has been submitted successfully.
                        </p>
                        <button
                          onClick={() => setActiveStep('qualification')}
                          className="mt-2 text-sm text-blue-600 hover:text-blue-800 underline"
                        >
                          View/Edit Details
                        </button>
                      </div>
                    ) : (
                      <QualificationForm
                        onComplete={handleQualificationComplete}
                        existingProfile={profile}
                      />
                    )}
                  </div>
                )}

                {step.id === 'wallet' && (
                  <div className="space-y-4">
                    {step.status === 'completed' ? (
                      <div className="space-y-4">
                        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                          <p className="text-green-800 font-medium">✅ Wallet Connected</p>
                          <p className="text-green-600 text-sm mt-1">
                            Your wallet has been connected and verified successfully.
                          </p>
                          {walletStatus?.address && (
                            <p className="text-green-600 text-xs mt-1 font-mono">
                              {walletStatus.address}
                            </p>
                          )}
                        </div>
                        {/* Show wallet details */}
                        <div className="bg-white rounded-lg border border-gray-200 p-6">
                          <h4 className="text-md font-medium text-gray-900 mb-4">Connected Wallet Details</h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Wallet Address
                              </label>
                              <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 font-mono text-sm">
                                {walletStatus?.address || 'Not available'}
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Verification Status
                              </label>
                              <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  walletStatus?.verified
                                    ? 'text-green-600 bg-green-100'
                                    : 'text-yellow-600 bg-yellow-100'
                                }`}>
                                  {walletStatus?.verified ? 'Verified' : 'Pending Verification'}
                                </span>
                              </div>
                            </div>
                            {walletStatus?.signature && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Verification Signature
                                </label>
                                <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 font-mono text-xs break-all">
                                  {walletStatus.signature}
                                </div>
                              </div>
                            )}
                            {walletStatus?.verifiedAt && (
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Verified At
                                </label>
                                <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                  {new Date(walletStatus.verifiedAt).toLocaleString()}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <WalletConnection onWalletConnected={handleWalletComplete} />
                    )}
                  </div>
                )}

                {step.id === 'kyc' && (
                  <div className="space-y-4">
                    {step.status === 'completed' ? (
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-green-800 font-medium">✅ Identity Verified</p>
                        <p className="text-green-600 text-sm mt-1">
                          Your identity has been successfully verified through automated document scanning.
                        </p>
                      </div>
                    ) : step.status === 'error' ? (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-800 font-medium">❌ Verification Failed</p>
                        <p className="text-red-600 text-sm mt-1">
                          Identity verification could not be completed. Please try again.
                        </p>
                        {kycError && (
                          <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-xs text-red-700">
                            <strong>Error Details:</strong> {kycError}
                          </div>
                        )}
                        <button
                          onClick={() => {
                            setKycStatus('idle')
                            setKycError(null)
                          }}
                          className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                        >
                          Retry Verification
                        </button>
                      </div>
                    ) : (
                      <AutomaticKYC
                        onStatusChange={handleKYCStatusChange}
                        existingProfile={profile}
                      />
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Debug Information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-900 mb-2">Debug Information</h4>
          <div className="text-xs text-yellow-800 space-y-1">
            <div>Active Step: {activeStep || 'none'}</div>
            <div>Agreement Accepted: {agreementAccepted ? 'true' : 'false'}</div>
            <div>Profile: {profile ? 'exists' : 'null'}</div>
            <div>Profile Loading: {isLoading ? 'true' : 'false'}</div>
            <div>Profile Error: {error ? error.message : 'none'}</div>
            <div>Wallet Status: {walletStatus ? 'exists' : 'null'}</div>
            <div>KYC Status: {kycStatus}</div>
            <div>Completed Steps: {completedSteps}/{totalSteps}</div>
            {profile && (
              <div className="mt-2 p-2 bg-yellow-100 rounded">
                <div>Profile Email: {profile.email}</div>
                <div>Profile KYC Status: {profile.kycStatus}</div>
                <div>Profile First Name: {profile.firstName}</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
