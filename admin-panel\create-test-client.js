// Create test client with wallet and whitelist approvals
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestClient() {
  console.log('=== Creating Test Client with Wallet ===');
  
  try {
    // Create a test client
    const testClient = await prisma.client.create({
      data: {
        firstName: '<PERSON>',
        lastName: 'Doe',
        gender: 'MA<PERSON>',
        nationality: 'US',
        birthday: new Date('1990-01-01'),
        birthPlace: 'New York',
        identificationType: 'PASSPORT',
        passportNumber: 'US123456789',
        documentExpiration: new Date('2030-01-01'),
        phoneNumber: '+**********',
        email: '<EMAIL>',
        occupation: 'Software Engineer',
        sectorOfActivity: 'Technology',
        pepStatus: 'NOT_PEP',
        street: '123 Main St',
        buildingNumber: '123',
        city: 'New York',
        state: 'NY',
        country: 'United States',
        zipCode: '10001',
        sourceOfWealth: 'Employment',
        bankAccountNumber: 'US**********12',
        sourceOfFunds: 'Salary',
        taxIdentificationNumber: 'US123456789',
        kycStatus: 'APPROVED',
        kycCompletedAt: new Date(),
        walletAddress: '******************************************', // Test wallet address
        walletSignature: '0x**********abcdef...',
        walletVerifiedAt: new Date(),
        isWhitelisted: true,
        whitelistedAt: new Date(),
        agreementAccepted: true,
        agreementAcceptedAt: new Date()
      }
    });

    console.log('✅ Test client created:', testClient.id);
    console.log(`   Email: ${testClient.email}`);
    console.log(`   Wallet: ${testClient.walletAddress}`);
    console.log(`   KYC Status: ${testClient.kycStatus}`);
    console.log(`   Whitelisted: ${testClient.isWhitelisted}`);

    return testClient;
  } catch (error) {
    console.error('Error creating test client:', error);
    throw error;
  }
}

async function createTokenApprovals(clientId) {
  console.log('\n=== Creating Token Approvals ===');
  
  try {
    // Get some tokens from the database
    const tokens = await prisma.token.findMany({
      take: 5,
      select: { id: true, name: true, symbol: true, address: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens found in database');
      return;
    }

    console.log(`Found ${tokens.length} tokens, creating approvals...`);

    // Create approvals for some tokens (whitelist the first 3, leave others not whitelisted)
    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];
      const isWhitelisted = i < 3; // Whitelist first 3 tokens

      const approval = await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: clientId,
          approvalStatus: isWhitelisted ? 'APPROVED' : 'PENDING',
          kycApproved: true,
          whitelistApproved: isWhitelisted,
          approvedBy: isWhitelisted ? '<EMAIL>' : null,
          approvedAt: isWhitelisted ? new Date() : null,
          notes: isWhitelisted ? 'Test approval for demo' : 'Pending approval'
        }
      });

      console.log(`${isWhitelisted ? '✅' : '⏳'} ${token.symbol}: ${isWhitelisted ? 'WHITELISTED' : 'PENDING'}`);
    }

    console.log('\n✅ Token approvals created successfully!');
  } catch (error) {
    console.error('Error creating token approvals:', error);
    throw error;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    // Test the whitelist check API
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens to test');
      return;
    }

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Whitelist API Response:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken-specific whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        console.log(`  ${symbol}: ${token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED'}`);
      });
    } else {
      console.log('❌ Whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing whitelist API:', error);
  }
}

async function main() {
  try {
    // Create test client
    const client = await createTestClient();
    
    // Create token approvals
    await createTokenApprovals(client.id);
    
    // Test the whitelist API
    await testWhitelistAPI(client.walletAddress);
    
    console.log('\n🎉 Test data created successfully!');
    console.log('\nTo test the whitelist feature:');
    console.log('1. Login to the client app with email: <EMAIL>');
    console.log('2. Connect wallet: ******************************************');
    console.log('3. Visit the offers page to see WHITELISTED tags');
    
  } catch (error) {
    console.error('Error in main:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
