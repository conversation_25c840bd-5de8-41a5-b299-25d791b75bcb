const fetch = require('node-fetch');

async function testConnection() {
  console.log('🔍 Testing Admin Panel <-> Client Portal Connection...');
  
  const adminApiUrl = 'http://localhost:3000/api';
  const clientApiUrl = 'http://localhost:7788/api';
  
  console.log('\n1. Testing Admin Panel API directly...');
  try {
    const response = await fetch(`${adminApiUrl}/clients?limit=1`);
    console.log(`✅ Admin Panel API Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Admin Panel API Response: ${JSON.stringify(data).substring(0, 100)}...`);
    } else {
      console.log(`❌ Admin Panel API Error: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Admin Panel API Connection Failed: ${error.message}`);
    console.log('   Make sure admin panel is running on port 3000');
  }
  
  console.log('\n2. Testing Client Portal API...');
  try {
    const response = await fetch(`${clientApiUrl}/client/profile`);
    console.log(`✅ Client Portal API Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('✅ Client Portal API is working (401 = needs authentication)');
    } else if (response.ok) {
      console.log('✅ Client Portal API is working');
    } else {
      console.log(`❌ Client Portal API Error: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Client Portal API Connection Failed: ${error.message}`);
    console.log('   Make sure client portal is running on port 3001');
  }
  
  console.log('\n3. Testing CORS preflight request...');
  try {
    const response = await fetch(`${adminApiUrl}/clients`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:7788',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type',
      },
    });
    
    console.log(`✅ CORS Preflight Status: ${response.status}`);
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
      'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
    };
    
    console.log('✅ CORS Headers:', corsHeaders);
    
    if (corsHeaders['Access-Control-Allow-Origin'] === 'http://localhost:7788') {
      console.log('✅ CORS is properly configured');
    } else {
      console.log('❌ CORS configuration issue');
    }
  } catch (error) {
    console.log(`❌ CORS Test Failed: ${error.message}`);
  }
  
  console.log('\n4. Testing cross-origin request simulation...');
  try {
    const response = await fetch(`${adminApiUrl}/clients?limit=1`, {
      headers: {
        'Origin': 'http://localhost:7788',
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`✅ Cross-origin request Status: ${response.status}`);
    
    if (response.ok) {
      console.log('✅ Cross-origin requests are working');
    } else {
      console.log(`❌ Cross-origin request failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Cross-origin request failed: ${error.message}`);
  }
  
  console.log('\n📋 SUMMARY:');
  console.log('===========');
  console.log('If all tests pass, the connection should work.');
  console.log('If tests fail:');
  console.log('1. Make sure admin panel is running: npm run dev (in admin-panel folder)');
  console.log('2. Make sure client portal is running: npm run dev (in client folder)');
  console.log('3. Check that ports 3000 and 3001 are not blocked');
  console.log('4. Restart both applications after CORS changes');
}

testConnection().catch(console.error);
