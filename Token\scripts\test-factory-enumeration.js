const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Testing SecurityTokenFactory enumeration functionality...");
  
  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  // You need to replace this with your actual factory address
  const FACTORY_ADDRESS = process.env.FACTORY_ADDRESS || "0x..."; // Replace with your factory address
  
  if (FACTORY_ADDRESS === "0x...") {
    console.error("❌ Please set FACTORY_ADDRESS environment variable or update the script");
    console.log("Usage: FACTORY_ADDRESS=0x... npx hardhat run scripts/test-factory-enumeration.js --network amoy");
    process.exit(1);
  }

  try {
    // Connect to the factory
    const factory = await ethers.getContractAt("SecurityTokenFactory", FACTORY_ADDRESS);
    console.log("✅ Connected to factory at:", FACTORY_ADDRESS);

    // Test enumeration functions
    console.log("\n📊 Testing enumeration functions...");
    
    // Get token count
    const tokenCount = await factory.getTokenCount();
    console.log(`📈 Total tokens deployed: ${tokenCount}`);

    if (tokenCount > 0) {
      // Get all tokens at once
      console.log("\n📋 Getting all deployed tokens...");
      const allTokens = await factory.getAllDeployedTokens();
      console.log(`✅ Retrieved ${allTokens.length} token addresses`);

      // Test individual token access
      console.log("\n🔍 Testing individual token access...");
      for (let i = 0; i < Math.min(tokenCount, 5); i++) { // Test first 5 tokens
        try {
          const tokenAddress = await factory.getDeployedToken(i);
          console.log(`   Token ${i}: ${tokenAddress}`);
          
          // Verify it matches the all tokens array
          if (tokenAddress === allTokens[i]) {
            console.log(`   ✅ Matches getAllDeployedTokens()[${i}]`);
          } else {
            console.log(`   ❌ Mismatch with getAllDeployedTokens()[${i}]`);
          }

          // Try to get token details
          try {
            const token = await ethers.getContractAt("SecurityToken", tokenAddress);
            const name = await token.name();
            const symbol = await token.symbol();
            const decimals = await token.decimals();
            console.log(`   📄 ${name} (${symbol}) - ${decimals} decimals`);
          } catch (error) {
            console.log(`   ⚠️ Could not read token details: ${error.message}`);
          }
        } catch (error) {
          console.log(`   ❌ Error accessing token ${i}: ${error.message}`);
        }
      }

      // Test boundary conditions
      console.log("\n🔬 Testing boundary conditions...");
      try {
        await factory.getDeployedToken(tokenCount);
        console.log("❌ Should have failed for out-of-bounds index");
      } catch (error) {
        console.log("✅ Correctly rejected out-of-bounds index");
      }

    } else {
      console.log("📝 No tokens deployed yet. Deploy a token to test enumeration.");
      
      // Test that getAllDeployedTokens returns empty array
      const allTokens = await factory.getAllDeployedTokens();
      if (allTokens.length === 0) {
        console.log("✅ getAllDeployedTokens() correctly returns empty array");
      } else {
        console.log("❌ getAllDeployedTokens() should return empty array");
      }
    }

    // Test other factory functions
    console.log("\n🔧 Testing other factory functions...");
    
    try {
      const tokenImpl = await factory.securityTokenImplementation();
      const whitelistImpl = await factory.whitelistImplementation();
      const whitelistKYCImpl = await factory.whitelistWithKYCImplementation();
      
      console.log("✅ Implementation addresses:");
      console.log(`   SecurityToken: ${tokenImpl}`);
      console.log(`   Whitelist: ${whitelistImpl}`);
      console.log(`   WhitelistWithKYC: ${whitelistKYCImpl}`);
    } catch (error) {
      console.log("❌ Error reading implementations:", error.message);
    }

    // Test role checking
    try {
      const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
      const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
      
      const isAdmin = await factory.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
      const isDeployer = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
      
      console.log("✅ Role status:");
      console.log(`   Is Admin: ${isAdmin}`);
      console.log(`   Is Deployer: ${isDeployer}`);
    } catch (error) {
      console.log("❌ Error checking roles:", error.message);
    }

    console.log("\n🎉 Factory enumeration test completed!");

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
