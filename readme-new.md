# ERC3643 / ERC20 Token Implementation

This is a comprehensive implementation of the ERC3643 standard for tokenized securities and regulated assets on the Ethereum blockchain, following the T-REX (Token for Regulated EXchanges) standard. The implementation provides a robust, secure, and compliant framework for issuing and managing security tokens.

## Table of Contents

- [Introduction](#introduction)
- [Project Structure](#project-structure)
- [Core Components](#core-components)
- [Installation and Setup](#installation-and-setup)
- [Deployment](#deployment)
- [Token Management](#token-management)
- [Whitelist Management](#whitelist-management)
- [Upgrade Process](#upgrade-process)
- [Security Considerations](#security-considerations)
- [Coding Standards](#coding-standards)

## Introduction

The ERC3643 standard extends the functionality of ERC20 tokens to include compliance requirements for security tokens. This implementation provides a complete framework for:

- Issuing regulated tokens
- Managing token holder identities
- Enforcing transfer restrictions
- Implementing compliance rules
- Supporting KYC/AML requirements
- Providing administrative controls

## Project Structure

The project is organized as follows:

```
├── contracts/                # Smart contracts
│   ├── interfaces/           # Contract interfaces
│   ├── base/                 # Base contracts and reusable components
│   ├── SecurityToken.sol     # Main token contract
│   ├── SecurityTokenFactory.sol # Factory for deploying tokens
│   ├── Whitelist.sol         # Basic whitelist implementation
│   └── WhitelistWithKYC.sol  # Extended whitelist with KYC
├── scripts/                  # Deployment and management scripts
├── test/                     # Test suite
└── admin-panel/              # Administrative web interface
```

## Core Components

### Contracts

#### SecurityToken.sol
The main token contract that implements the ERC3643 standard, extending ERC20 with compliance enforcement. Key features:
- Whitelist/identity registry integration
- Transfer restrictions
- Compliance enforcement
- Forced transfers for regulatory actions
- Freezing capabilities
- Metadata management
- Agent-based management

#### SecurityTokenFactory.sol
Factory contract for deploying new security tokens with standardized configurations:
- Proxy-based deployment
- Configurable token parameters
- Identity registry integration
- Implements UUPS (Universal Upgradeable Proxy Standard)

#### Whitelist.sol
Basic whitelist implementation that serves as an identity registry:
- Whitelisting functionality
- Address freezing
- Minimal KYC support for interface compatibility

#### WhitelistWithKYC.sol
Extended whitelist with full KYC capabilities:
- Complete KYC approval workflow
- Enhanced identity verification
- Extends basic whitelist functionality

#### Base Contracts

- **BaseIdentityRegistry.sol**: Core implementation of identity management functionality
- **BaseKYCRegistry.sol**: Base implementation for KYC functionality

#### Interfaces

- **ISecurityToken.sol**: Interface for ERC3643 compliant tokens
- **IIdentityRegistry.sol**: Interface for identity registry functions
- **IKYCRegistry.sol**: Interface for KYC management
- **ICompleteWhitelist.sol**: Combined interface for full whitelist functionality

### Scripts

- **01-deploy-factory.js**: Deploys the SecurityTokenFactory
- **02-deploy-token.js**: Deploys a new security token using the factory
- **03-manage-whitelist.js**: Manages whitelist entries, freezing, and KYC
- **04-upgrade-contracts.js**: Handles contract upgrades
- **05-manage-token.js**: Token management operations
- **direct-deploy-token.js**: Direct deployment without factory
- **test-whitelist.js**: Testing whitelist functionality

## Installation and Setup

### Prerequisites

- Node.js 16+ and npm
- Hardhat development environment

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Compile contracts:
   ```
   npx hardhat compile
   ```

## Deployment

### Deploy Factory

```
node scripts/01-deploy-factory.js
```

This deploys the SecurityTokenFactory and creates implementation contracts for SecurityToken, Whitelist, and WhitelistWithKYC.

### Deploy Token

```
node scripts/02-deploy-token.js
```

Environment variables for token deployment:
- `TOKEN_NAME`: Name of the token
- `TOKEN_SYMBOL`: Symbol of the token
- `MAX_SUPPLY`: Maximum supply (in ether units)
- `ADMIN_ADDRESS`: Admin address (defaults to deployer)
- `TOKEN_PRICE`: Token price metadata
- `BONUS_TIERS`: Bonus tiers metadata
- `TOKEN_DETAILS`: Additional token details
- `WITH_KYC`: Whether to use KYC functionality (true/false)

Alternatively, use direct deployment:
```
node scripts/direct-deploy-token.js
```

## Token Management

### Manage Whitelist

```
node scripts/03-manage-whitelist.js <operation> <token-symbol> [addresses...]
```

Operations:
- `add`: Add addresses to whitelist
- `remove`: Remove addresses from whitelist
- `freeze`: Freeze addresses
- `unfreeze`: Unfreeze addresses
- `approveKyc`: Approve KYC for addresses
- `revokeKyc`: Revoke KYC for addresses
- `list`: List available tokens
- `status`: Check status of addresses

### Manage Token

```
node scripts/05-manage-token.js <operation> <token-symbol> [parameters...]
```

Operations:
- `mint`: Mint new tokens
- `forcedTransfer`: Force transfer tokens
- `updateMetadata`: Update token metadata
- `updateMaxSupply`: Update maximum supply
- `pause`: Pause token transfers
- `unpause`: Unpause token transfers

## Whitelist Management

The whitelist (identity registry) manages the addresses that are allowed to hold and transfer tokens. Features:

1. **Whitelisting**: Add or remove addresses from the whitelist
2. **Freezing**: Freeze or unfreeze addresses (prevent transfers)
3. **KYC Management**: Approve or revoke KYC status for addresses
4. **Batch Operations**: Perform operations on multiple addresses at once

## Upgrade Process

The contracts use the UUPS (Universal Upgradeable Proxy Standard) pattern for upgrades:

1. Deploy new implementation contracts
2. Update the implementation addresses in the factory
3. For existing tokens, use the `04-upgrade-contracts.js` script

## Security Considerations

The implementation includes several security features:

- Role-based access control
- Reentrancy protection
- Freezing capabilities
- Forced transfers for regulatory compliance
- Comprehensive test coverage
- Proxy-based upgrades

## Coding Standards

This codebase adheres to the following coding standards:

- Professional coding practices throughout
- No code duplication
- Full separation of concerns
- Streamlined, flat architecture with minimal complexity
- Secure, reliable, robust, extensible, maintainable, and modular design
- No coupling between application layers or components
- Safe fallback states for all components
- Comprehensive documentation and comments
- Minimal, simple, reusable functions
- Implementation according to ERC3643 documentation (https://docs.erc3643.org/erc-3643)
- Follows T-REX best practices as outlined in the whitepaper (https://github.com/TokenySolutions/T-REX/blob/main/docs/TREX-WhitePaper.pdf)
- Complies with ERC20 security best practices (https://medium.com/thesis-defense/erc20-token-security-what-you-need-to-consider-46ab8231a050)

These standards ensure the codebase is maintainable, secure, and follows best practices for smart contract development. The implementation prioritizes security, compliance, and robustness while maintaining a clean, modular design that can be extended and upgraded as needed.

## Development Instructions

The following instructions were followed during the development and refactoring of this codebase:

```
Coding standards:

	Use proven, professional coding practices everywhere
	No code duplication
	Maintain full separation of concerns
	Reduce the codebase to the simplest, flattest, most streamlined version possible
	Ensure the app is safe, secure, reliable, robust, extensible, maintainable, modular
	Do not couple app layers or components
	Use safe fallback states for every component
	Comment and document your code
	Produce a readme-new.md file in the root that documents the entire codebase, including all files, folders, and database tables.
	Check for existing functionality before building new functionality
	Extend and improve functionality instead of recreating it
	Refactor functions into the smallest, simplest, most reusable form
	Refer to and implement ERC3643 functions according to ERC3643 documentation which can be found here https://docs.erc3643.org/erc-3643
	Refer to and implement ERC3643 best practices according to https://github.com/TokenySolutions/T-REX/blob/main/docs/TREX-WhitePaper.pdf
	Refer to and implement ERC20 functions according to ERC20 documentation, also refer to this https://medium.com/thesis-defense/erc20-token-security-what-you-need-to-consider-46ab8231a050
	Do not guess or assume. Always check documents and code. Ask the user for information if you need clarification.
```