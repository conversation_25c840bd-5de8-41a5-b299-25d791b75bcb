const { ethers } = require("hardhat");

async function main() {
  const proxyAddress = "******************************************";
  
  // Get the signer
  const [signer] = await ethers.getSigners();
  console.log("Using signer address:", signer.address);
  
  // Test address - using the signer's own address for simplicity
  const testAddress = signer.address;
  
  // Create a contract instance without ABI validation
  const provider = ethers.provider;
  
  // Function signatures
  // isKycApproved(address) => bytes4(keccak256("isKycApproved(address)"))
  const isKycApprovedSig = "0x7b9417c8";
  // approveKyc(address) => bytes4(keccak256("approveKyc(address)"))
  const approveKycSig = "0x784cb314";
  
  // Encode the function call data
  const isKycApprovedData = isKycApprovedSig + testAddress.substring(2).padStart(64, "0");
  
  try {
    console.log("Trying low-level call to isKycApproved...");
    
    // Call the function directly using eth_call
    const result = await provider.call({
      to: proxyAddress,
      data: isKycApprovedData
    });
    
    console.log("Raw result:", result);
    
    // Decode the result (boolean)
    const isApproved = parseInt(result, 16) === 1;
    console.log(`Address ${testAddress} KYC approved: ${isApproved}`);
    
    if (!isApproved) {
      console.log("Attempting to approve KYC for address using low-level call...");
      
      // Encode the function call data for approveKyc
      const approveKycData = approveKycSig + testAddress.substring(2).padStart(64, "0");
      
      // Send the transaction
      const tx = await signer.sendTransaction({
        to: proxyAddress,
        data: approveKycData
      });
      
      console.log("Transaction sent:", tx.hash);
      
      // Wait for confirmation
      console.log("Waiting for transaction confirmation...");
      await tx.wait();
      console.log("Transaction confirmed!");
      
      // Check again
      const resultAfter = await provider.call({
        to: proxyAddress,
        data: isKycApprovedData
      });
      
      const isApprovedAfter = parseInt(resultAfter, 16) === 1;
      console.log(`Address ${testAddress} KYC approved after transaction: ${isApprovedAfter}`);
    }
  } catch (error) {
    console.error("Error with low-level call:", error);
    console.error("Error details:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 