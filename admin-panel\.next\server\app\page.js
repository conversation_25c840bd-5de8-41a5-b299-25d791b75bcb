/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(rsc)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNhZG1pbi1wYW5lbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBaUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcYWRtaW4tcGFuZWxcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"962e3d2093e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NjJlM2QyMDkzZTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n\n\n\n\n// UI components\n\n\nconst metadata = {\n    title: \"Security Token Admin Panel\",\n    description: \"Admin panel for managing ERC-3643 security tokens\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased bg-gray-50 min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-grow container mx-auto px-4 py-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"bg-gray-800 text-white py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" Security Token Admin Panel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\admin-panel\\src\\components\\Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\admin-panel\\src\\components\\Providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNhZG1pbi1wYW5lbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBaUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcYWRtaW4tcGFuZWxcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config */ \"(ssr)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts/SecurityTokenFactory.json */ \"(ssr)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contracts/SecurityToken.json */ \"(ssr)/./src/contracts/SecurityToken.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [network, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('amoy');\n    const [factoryAddress, setFactoryAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tokenImplementation, setTokenImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistImplementation, setWhitelistImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationAddress, setVerificationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadMethod, setLoadMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('logs');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Get contract addresses for current network\n            const addresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            setFactoryAddress(addresses.factory || '');\n            setTokenImplementation(addresses.tokenImplementation || '');\n            setWhitelistImplementation(addresses.whitelistImplementation || '');\n            checkWalletConnection();\n        }\n    }[\"Home.useEffect\"], [\n        network\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (walletConnected) {\n                fetchTokens();\n            } else {\n                // Load known tokens even without wallet connection for display\n                loadKnownTokensOnly();\n            }\n        }\n    }[\"Home.useEffect\"], [\n        walletConnected,\n        network\n    ]);\n    // Function to load only known tokens (for display without wallet)\n    const loadKnownTokensOnly = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n            if (knownTokens.length > 0) {\n                console.log(`Loading ${knownTokens.length} known tokens for display...`);\n                // Create basic token objects from known tokens\n                const basicTokens = knownTokens.map((knownToken)=>({\n                        address: knownToken.address,\n                        name: knownToken.name,\n                        symbol: knownToken.symbol,\n                        maxSupply: \"Connect wallet to view\",\n                        totalSupply: \"Connect wallet to view\",\n                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                        tokenImageUrl: \"\"\n                    }));\n                setTokens(basicTokens);\n                setError(\"Connect your wallet to view live token data and access management features.\");\n            } else {\n                setTokens([]);\n                setError(\"No known tokens configured for this network. Connect your wallet to discover tokens from the factory.\");\n            }\n        } catch (error) {\n            console.error(\"Error loading known tokens:\", error);\n            setError(\"Error loading token information. Please try again.\");\n        }\n        setIsLoading(false);\n    };\n    const checkWalletConnection = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                const accounts = await provider.listAccounts();\n                if (accounts.length > 0) {\n                    setWalletConnected(true);\n                } else {\n                    setWalletConnected(false);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setWalletConnected(false);\n        }\n    };\n    const connectWallet = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                await provider.send(\"eth_requestAccounts\", []);\n                setWalletConnected(true);\n            } else {\n                setError(\"Please install MetaMask to use this feature!\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n        }\n    };\n    // Add your token address to verify\n    const addTokenManually = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address');\n            return;\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_8__.isAddress(verificationAddress)) {\n            alert('Please enter a valid address');\n            return;\n        }\n        loadTokenByAddress(verificationAddress);\n    };\n    const loadTokenByAddress = async (address)=>{\n        try {\n            setIsLoading(true);\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            // Use the new loadTokenDetails function\n            const tokenDetails = await loadTokenDetails(address);\n            if (tokenDetails) {\n                // Check if the token is already in the list\n                const exists = tokens.some((token)=>token.address.toLowerCase() === address.toLowerCase());\n                if (!exists) {\n                    setTokens((prevTokens)=>[\n                            ...prevTokens,\n                            tokenDetails\n                        ]);\n                }\n                setVerificationAddress('');\n            } else {\n                alert(\"Could not load token details. Is this a valid Security Token address?\");\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error loading token:', err);\n            setError(err.message || 'Error loading token. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const loadTokenDetails = async (address)=>{\n        try {\n            if (!window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            // Get the token contract\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__.abi, provider);\n            // Read token details\n            const name = await tokenContract.name();\n            const symbol = await tokenContract.symbol();\n            // Get decimals to format amounts correctly\n            const decimalsRaw = await tokenContract.decimals();\n            const decimals = Number(decimalsRaw);\n            let maxSupply = \"0\";\n            let totalSupply = \"0\";\n            let whitelistAddress = \"******************************************\";\n            try {\n                const maxSupplyRaw = await tokenContract.maxSupply();\n                maxSupply = decimals === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(maxSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read maxSupply:\", err);\n            }\n            try {\n                const totalSupplyRaw = await tokenContract.totalSupply();\n                totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(totalSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read totalSupply:\", err);\n            }\n            try {\n                whitelistAddress = await tokenContract.identityRegistry();\n            } catch (err) {\n                try {\n                    whitelistAddress = await tokenContract.whitelistAddress();\n                } catch (err2) {\n                    console.warn(\"Could not read whitelist address:\", err2);\n                }\n            }\n            // Try to get token image URL if supported\n            let tokenImageUrl = \"\";\n            try {\n                tokenImageUrl = await tokenContract.tokenImageUrl();\n            } catch (err) {\n                console.log(\"Token doesn't support image URL or image URL is empty\");\n            }\n            return {\n                address,\n                name,\n                symbol,\n                maxSupply,\n                totalSupply,\n                whitelistAddress,\n                tokenImageUrl\n            };\n        } catch (error) {\n            console.error(`Error loading token details for ${address}:`, error);\n            return null;\n        }\n    };\n    const fetchTokens = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setTokens([]);\n        try {\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getNetworkConfig)(network);\n            const contractAddresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            if (!contractAddresses.factory) {\n                throw new Error(`No factory address configured for network: ${network}`);\n            }\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(contractAddresses.factory, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__.abi, provider);\n            console.log(\"Loading tokens from factory:\", contractAddresses.factory);\n            console.log(\"Network:\", network);\n            console.log(\"Provider:\", provider);\n            try {\n                // Try to get tokens using the new enumeration methods\n                console.log(\"Attempting to fetch tokens using factory enumeration...\");\n                try {\n                    // First, try to get the token count (new factory enumeration)\n                    const tokenCount = await factory.getTokenCount();\n                    console.log(`Factory reports ${tokenCount} deployed tokens`);\n                    if (tokenCount > 0) {\n                        // Get all token addresses at once for better performance\n                        const tokenAddresses = await factory.getAllDeployedTokens();\n                        console.log(\"Retrieved token addresses:\", tokenAddresses);\n                        // Load details for each token\n                        const tokenPromises = tokenAddresses.map(async (address)=>{\n                            try {\n                                return await loadTokenDetails(address);\n                            } catch (error) {\n                                console.warn(`Failed to load token details for ${address}:`, error);\n                                return null;\n                            }\n                        });\n                        const tokenResults = await Promise.all(tokenPromises);\n                        const validTokens = tokenResults.filter((token)=>token !== null);\n                        if (validTokens.length > 0) {\n                            setTokens(validTokens);\n                            console.log(`Successfully loaded ${validTokens.length} tokens from factory`);\n                        } else {\n                            setError(\"Factory has tokens but could not load their details. Please check network connection.\");\n                        }\n                    } else {\n                        setError(\"No tokens found in factory. Create your first token to see it here.\");\n                    }\n                } catch (enumerationError) {\n                    console.warn(\"Factory enumeration failed, trying event-based discovery:\", enumerationError);\n                    // Fallback: Use event-based token discovery for older factories\n                    try {\n                        console.log(\"Searching for TokenDeployed events...\");\n                        // Get TokenDeployed events from the factory\n                        const filter = factory.filters.TokenDeployed();\n                        let events = [];\n                        // Try different block ranges to find events\n                        const ranges = [\n                            -10000,\n                            -50000\n                        ]; // Last 10k, then 50k blocks\n                        for (const range of ranges){\n                            try {\n                                events = await factory.queryFilter(filter, range);\n                                if (events.length > 0) break; // Found events, stop searching\n                            } catch (error) {\n                                console.warn(`Failed to query ${Math.abs(range)} blocks:`, error);\n                            }\n                        }\n                        console.log(`Found ${events.length} TokenDeployed events`);\n                        if (events.length > 0) {\n                            // Extract unique token addresses from events\n                            const tokenAddresses = [\n                                ...new Set(events.map((event)=>event.args?.tokenAddress).filter(Boolean))\n                            ];\n                            console.log(\"Token addresses from events:\", tokenAddresses);\n                            // Load details for each token\n                            const tokenPromises = tokenAddresses.map(async (address)=>{\n                                try {\n                                    return await loadTokenDetails(address);\n                                } catch (error) {\n                                    console.warn(`Failed to load token details for ${address}:`, error);\n                                    return null;\n                                }\n                            });\n                            const tokenResults = await Promise.all(tokenPromises);\n                            const validTokens = tokenResults.filter((token)=>token !== null);\n                            if (validTokens.length > 0) {\n                                setTokens(validTokens);\n                                console.log(`Successfully loaded ${validTokens.length} tokens from events`);\n                            } else {\n                                setError(\"Found token events but could not load token details. Please check network connection.\");\n                            }\n                        } else {\n                            setError(\"No tokens found in factory events. Create your first token to see it here.\");\n                        }\n                    } catch (eventError) {\n                        console.warn(\"Event-based discovery failed:\", eventError);\n                        // Final fallback: Load known tokens from configuration\n                        console.log(\"Falling back to known tokens from configuration...\");\n                        const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                        if (knownTokens.length > 0) {\n                            console.log(`Loading ${knownTokens.length} known tokens...`);\n                            const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                                try {\n                                    const tokenDetails = await loadTokenDetails(knownToken.address);\n                                    return tokenDetails;\n                                } catch (error) {\n                                    console.warn(`Failed to load known token ${knownToken.address}:`, error);\n                                    // Return basic info even if contract call fails\n                                    return {\n                                        address: knownToken.address,\n                                        name: knownToken.name,\n                                        symbol: knownToken.symbol,\n                                        maxSupply: \"Unknown\",\n                                        totalSupply: \"Unknown\",\n                                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                        tokenImageUrl: \"\"\n                                    };\n                                }\n                            });\n                            const knownTokenResults = await Promise.all(knownTokenPromises);\n                            const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                            if (validKnownTokens.length > 0) {\n                                setTokens(validKnownTokens);\n                                console.log(`Successfully loaded ${validKnownTokens.length} known tokens`);\n                                setError(\"Factory enumeration failed, but loaded known tokens. You can add more tokens manually below.\");\n                            } else {\n                                setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the tool below.\");\n                            }\n                        } else {\n                            setError(\"Could not load tokens from factory. Please add token addresses manually using the tool below.\");\n                        }\n                    }\n                }\n            } catch (err) {\n                console.error(\"Error loading tokens:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n                // Try to load known tokens as fallback even on factory connection error\n                console.log(\"Factory connection failed, trying known tokens...\");\n                const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                if (knownTokens.length > 0) {\n                    try {\n                        const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                            try {\n                                const tokenDetails = await loadTokenDetails(knownToken.address);\n                                return tokenDetails;\n                            } catch (error) {\n                                console.warn(`Failed to load known token ${knownToken.address}:`, error);\n                                return {\n                                    address: knownToken.address,\n                                    name: knownToken.name,\n                                    symbol: knownToken.symbol,\n                                    maxSupply: \"Unknown\",\n                                    totalSupply: \"Unknown\",\n                                    whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                    tokenImageUrl: \"\"\n                                };\n                            }\n                        });\n                        const knownTokenResults = await Promise.all(knownTokenPromises);\n                        const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                        if (validKnownTokens.length > 0) {\n                            setTokens(validKnownTokens);\n                            console.log(`Loaded ${validKnownTokens.length} known tokens as fallback`);\n                            setError(\"Factory connection failed, but loaded known tokens. Please check your network connection.\");\n                        } else {\n                            setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                        }\n                    } catch (fallbackError) {\n                        console.error(\"Fallback token loading failed:\", fallbackError);\n                        setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                    }\n                } else {\n                    setError(\"Could not load tokens from factory. Please add token addresses manually using the verify tool below.\");\n                }\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error fetching tokens:', err);\n            setError(err.message || 'Error fetching tokens. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const handleNetworkChange = (event)=>{\n        const newNetwork = event.target.value;\n        console.log(`Switching to network: ${newNetwork}`);\n        setNetwork(newNetwork);\n    };\n    const verifyToken = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address to verify');\n            return;\n        }\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = `https://www.oklink.com/amoy/address/${verificationAddress}`;\n        } else if (network === 'polygon') {\n            explorerUrl = `https://polygonscan.com/address/${verificationAddress}`;\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const getBlockExplorerUrl = (network)=>{\n        if (network === 'amoy') {\n            return 'https://www.oklink.com/amoy';\n        } else if (network === 'polygon') {\n            return 'https://polygonscan.com';\n        }\n        return '#';\n    };\n    const viewFactoryTokens = ()=>{\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = `https://www.oklink.com/amoy/address/${factoryAddress}#eventlog`;\n        } else if (network === 'polygon') {\n            explorerUrl = `https://polygonscan.com/address/${factoryAddress}#events`;\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const formatNumber = (value)=>{\n        // Format the number with commas\n        const number = parseFloat(value);\n        return number.toLocaleString(undefined, {\n            maximumFractionDigits: 0\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Security Token Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"View and manage your security tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"network\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"network\",\n                                className: \"p-2 border border-gray-300 rounded-md\",\n                                value: network,\n                                onChange: handleNetworkChange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"amoy\",\n                                        children: \"Amoy Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"polygon\",\n                                        children: \"Polygon Mainnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: `/create-token?network=${network}`,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Create New Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold mb-2\",\n                        children: \"Connected to Your Deployed Factory Contract\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Factory Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: factoryAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            tokenImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Token Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this),\n                            whitelistImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Whitelist Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: whitelistImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: `${getBlockExplorerUrl(network)}/address/${factoryAddress}`,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-green-700 underline hover:text-green-900\",\n                                children: [\n                                    \"View Factory on \",\n                                    network === 'amoy' ? 'OKLink Explorer' : 'PolygonScan'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: viewFactoryTokens,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm\",\n                                children: \"View All Factory Token Deployments\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/create-token\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDE80 Create Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/transfer-controls\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD12 Transfer Controls\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/clients\",\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDC65 Client Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open('https://amoy.polygonscan.com/', '_blank'),\n                                className: \"bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD0D Block Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"Add Token to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-600\",\n                        children: \"Manually add a token by entering its address below.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: verificationAddress,\n                                onChange: (e)=>setVerificationAddress(e.target.value),\n                                placeholder: \"Enter token address (0x...)\",\n                                className: \"flex-grow p-2 border border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: addTokenManually,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                                children: \"Add Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: verifyToken,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded\",\n                                children: \"Verify on Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-xs text-gray-500\",\n                        children: \"Note: After creating a token, you can add it here to display it on the dashboard.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg overflow-hidden mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Security Token Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 15\n                                            }, this),\n                                            walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Live data from \",\n                                                    network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet',\n                                                    \" factory\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"Showing known tokens - connect wallet for live data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-3 h-3 rounded-full ${walletConnected ? 'bg-green-500' : 'bg-yellow-500'}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: walletConnected ? 'Connected' : 'Offline Mode'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: walletConnected ? fetchTokens : loadKnownTokensOnly,\n                                        className: \"text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded\",\n                                        disabled: isLoading,\n                                        children: isLoading ? 'Loading...' : 'Refresh Token List'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded\",\n                                        children: \"Connect for Live Data\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center my-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this) : tokens.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Total Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Max Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: tokens.map((token, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: [\n                                                                token.tokenImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    className: \"h-10 w-10 rounded-full object-cover border-2 border-gray-200\",\n                                                                    src: token.tokenImageUrl,\n                                                                    alt: `${token.name} logo`,\n                                                                    onError: (e)=>{\n                                                                        // Fallback to default icon if image fails to load\n                                                                        const target = e.target;\n                                                                        target.style.display = 'none';\n                                                                        target.nextElementSibling?.classList.remove('hidden');\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 27\n                                                                }, this) : null,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center ${token.tokenImageUrl ? 'hidden' : ''}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: token.symbol.substring(0, 2).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 truncate\",\n                                                                    title: token.address,\n                                                                    children: [\n                                                                        token.address.substring(0, 10),\n                                                                        \"...\",\n                                                                        token.address.substring(token.address.length - 8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.symbol\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.totalSupply ? formatNumber(token.totalSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.maxSupply ? formatNumber(token.maxSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                                                    children: walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'Known + Live' : 'Factory' : 'Known Token'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: `/tokens/${token.address}`,\n                                                        className: \"text-blue-600 hover:text-blue-900 mr-4\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: getBlockExplorerUrl(network) + '/address/' + token.address,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-gray-600 hover:text-gray-900\",\n                                                        children: \"Explorer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: [\n                                        \"No tokens found on \",\n                                        network,\n                                        \" network.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'To add a token you\\'ve created, use the \"Add Token to Dashboard\" section above.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/create-token?network=${network}`,\n                                    className: \"text-blue-600 hover:text-blue-900 font-medium\",\n                                    children: \"Create your first token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: connectWallet,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                            children: \"Connect Wallet to View Tokens\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: `/create-token?network=${network}`,\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded inline-block\",\n                    children: [\n                        \"Create New Token on \",\n                        network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet'\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 844,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectWallet.tsx":
/*!******************************************!*\
  !*** ./src/components/ConnectWallet.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ConnectWallet = ()=>{\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [networkName, setNetworkName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectWallet.useEffect\": ()=>{\n            // Check if already connected\n            checkIfWalletIsConnected();\n        }\n    }[\"ConnectWallet.useEffect\"], []);\n    // Listen for account changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectWallet.useEffect\": ()=>{\n            if (window.ethereum) {\n                window.ethereum.on('accountsChanged', {\n                    \"ConnectWallet.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setAccount(accounts[0]);\n                        } else {\n                            setAccount(null);\n                        }\n                    }\n                }[\"ConnectWallet.useEffect\"]);\n                window.ethereum.on('chainChanged', {\n                    \"ConnectWallet.useEffect\": (_chainId)=>{\n                        window.location.reload();\n                    }\n                }[\"ConnectWallet.useEffect\"]);\n            }\n            return ({\n                \"ConnectWallet.useEffect\": ()=>{\n                    if (window.ethereum) {\n                        window.ethereum.removeAllListeners();\n                    }\n                }\n            })[\"ConnectWallet.useEffect\"];\n        }\n    }[\"ConnectWallet.useEffect\"], []);\n    const checkIfWalletIsConnected = async ()=>{\n        try {\n            if (!window.ethereum) {\n                console.log('Make sure you have MetaMask installed!');\n                return;\n            }\n            // Get the provider\n            const web3Provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(window.ethereum);\n            setProvider(web3Provider);\n            // Get the network\n            const network = await web3Provider.getNetwork();\n            setNetworkName(network.name === 'unknown' ? `Chain ID ${network.chainId}` : network.name);\n            // Get accounts\n            const accounts = await web3Provider.listAccounts();\n            if (accounts.length > 0) {\n                setAccount(accounts[0].address);\n            }\n        } catch (error) {\n            console.error('Error checking if wallet is connected:', error);\n        }\n    };\n    const connectWallet = async ()=>{\n        try {\n            setIsConnecting(true);\n            if (!window.ethereum) {\n                alert('Please install MetaMask to use this feature!');\n                setIsConnecting(false);\n                return;\n            }\n            // Request accounts\n            const web3Provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(window.ethereum);\n            await web3Provider.send('eth_requestAccounts', []);\n            // Get the connected account\n            const signer = await web3Provider.getSigner();\n            const connectedAccount = await signer.getAddress();\n            // Get the network\n            const network = await web3Provider.getNetwork();\n            setProvider(web3Provider);\n            setAccount(connectedAccount);\n            setNetworkName(network.name === 'unknown' ? `Chain ID ${network.chainId.toString()}` : network.name);\n            setIsConnecting(false);\n        } catch (error) {\n            console.error('Error connecting wallet:', error);\n            setIsConnecting(false);\n        }\n    };\n    const disconnectWallet = ()=>{\n        setAccount(null);\n        setProvider(null);\n        setNetworkName('');\n    };\n    const shortenAddress = (address)=>{\n        return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs md:text-sm bg-blue-900 px-2 py-1 rounded\",\n                    children: networkName\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded flex items-center text-sm\",\n                            children: shortenAddress(account)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute z-10 hidden group-hover:block right-0 mt-2 w-48 bg-white text-gray-800 rounded shadow-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: disconnectWallet,\n                                className: \"w-full text-left px-4 py-2 hover:bg-gray-100 rounded text-sm\",\n                                children: \"Disconnect\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n            lineNumber: 108,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: connectWallet,\n            disabled: isConnecting,\n            className: \"bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm flex items-center\",\n            children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 15\n                    }, undefined),\n                    \"Connecting...\"\n                ]\n            }, void 0, true) : 'Connect Wallet'\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n            lineNumber: 127,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectWallet);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectWallet.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ConnectWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ConnectWallet */ \"(ssr)/./src/components/ConnectWallet.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navItems = [\n        {\n            title: 'Dashboard',\n            path: '/'\n        },\n        {\n            title: 'Tokens',\n            path: '/tokens'\n        },\n        {\n            title: 'Create Token',\n            path: '/create-token'\n        },\n        {\n            title: 'Claims',\n            path: '/claims-management'\n        },\n        {\n            title: 'Clients',\n            path: '/clients'\n        },\n        {\n            title: 'Identity',\n            path: '/identity'\n        },\n        {\n            title: 'Orders',\n            path: '/orders'\n        }\n    ];\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-gray-800 text-white shadow-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"font-bold text-xl\",\n                                children: \"Security Token Admin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-6\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.path,\n                                    className: `hover:text-blue-300 transition ${pathname === item.path ? 'text-blue-300 font-semibold' : ''}`,\n                                    children: item.title\n                                }, item.path, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectWallet__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-white\",\n                            onClick: toggleMobileMenu,\n                            \"aria-label\": \"Toggle menu\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 pb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.path,\n                                    className: `hover:text-blue-300 transition ${pathname === item.path ? 'text-blue-300 font-semibold' : ''}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: item.title\n                                }, item.path, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectWallet__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _config_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/wagmi */ \"(ssr)/./src/config/wagmi.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Providers.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1\n                    }\n                }\n            })\n    }[\"Providers.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_4__.WagmiProvider, {\n        config: _config_wagmi__WEBPACK_IMPORTED_MODULE_2__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Providers.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDNkI7QUFDbkM7QUFDRztBQUVsQyxTQUFTSyxVQUFVLEVBQUVDLFFBQVEsRUFBMkI7SUFDN0QsTUFBTSxDQUFDQyxZQUFZLEdBQUdQLCtDQUFRQTs4QkFBQyxJQUFNLElBQUlDLDhEQUFXQSxDQUFDO2dCQUNuRE8sZ0JBQWdCO29CQUNkQyxTQUFTO3dCQUNQQyxXQUFXLEtBQUs7d0JBQ2hCQyxPQUFPO29CQUNUO2dCQUNGO1lBQ0Y7O0lBRUEscUJBQ0UsOERBQUNSLGdEQUFhQTtRQUFDQyxRQUFRQSxpREFBTUE7a0JBQzNCLDRFQUFDRixzRUFBbUJBO1lBQUNVLFFBQVFMO3NCQUMxQkQ7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcY29tcG9uZW50c1xcUHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBSZWFjdE5vZGUsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XHJcbmltcG9ydCB7IFdhZ21pUHJvdmlkZXIgfSBmcm9tICd3YWdtaSc7XHJcbmltcG9ydCB7IGNvbmZpZyB9IGZyb20gJy4uL2NvbmZpZy93YWdtaSc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcclxuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoKCkgPT4gbmV3IFF1ZXJ5Q2xpZW50KHtcclxuICAgIGRlZmF1bHRPcHRpb25zOiB7XHJcbiAgICAgIHF1ZXJpZXM6IHtcclxuICAgICAgICBzdGFsZVRpbWU6IDYwICogMTAwMCwgLy8gMSBtaW51dGVcclxuICAgICAgICByZXRyeTogMSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgfSkpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFdhZ21pUHJvdmlkZXIgY29uZmlnPXtjb25maWd9PlxyXG4gICAgICA8UXVlcnlDbGllbnRQcm92aWRlciBjbGllbnQ9e3F1ZXJ5Q2xpZW50fT5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cclxuICAgIDwvV2FnbWlQcm92aWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwiV2FnbWlQcm92aWRlciIsImNvbmZpZyIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJyZXRyeSIsImNsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contractAddresses: () => (/* binding */ contractAddresses),\n/* harmony export */   defaultNetwork: () => (/* binding */ defaultNetwork),\n/* harmony export */   getContractAddresses: () => (/* binding */ getContractAddresses),\n/* harmony export */   getKnownTokens: () => (/* binding */ getKnownTokens),\n/* harmony export */   getNetworkConfig: () => (/* binding */ getNetworkConfig),\n/* harmony export */   isKnownToken: () => (/* binding */ isKnownToken),\n/* harmony export */   knownTokens: () => (/* binding */ knownTokens),\n/* harmony export */   networkConfig: () => (/* binding */ networkConfig),\n/* harmony export */   tokenTypes: () => (/* binding */ tokenTypes),\n/* harmony export */   verifyTokenExists: () => (/* binding */ verifyTokenExists)\n/* harmony export */ });\n// Network configuration\nconst networkConfig = {\n    // Amoy testnet\n    amoy: {\n        chainId: 80002,\n        name: \"Amoy\",\n        rpcUrl: \"https://rpc-amoy.polygon.technology\",\n        blockExplorer: \"https://www.oklink.com/amoy\"\n    },\n    // Polygon mainnet\n    polygon: {\n        chainId: 137,\n        name: \"Polygon\",\n        rpcUrl: \"https://polygon-rpc.com\",\n        blockExplorer: \"https://polygonscan.com\"\n    }\n};\n// Default network\nconst defaultNetwork = \"amoy\";\n// Contract addresses - using the newly deployed factory address\nconst contractAddresses = {\n    // Updated factory contract address from latest deployment with decimals support\n    amoy: {\n        factory: \"0x69a6536629369F8948f47b897045929a57c630Fd\",\n        tokenImplementation: \"0xae2aA28708120CAA177e4c98CCCa0e152E30E506\",\n        whitelistImplementation: \"0x63eeE78ccc281413272bE68d9553Ae82680a0B09\",\n        whitelistWithKYCImplementation: \"0xf7c9C30Ad2E5b72F86489f26ab66cc1E79F44A7D\"\n    },\n    polygon: {\n        factory: process.env.NEXT_PUBLIC_FACTORY_ADDRESS_POLYGON || \"0x6543210987654321098765432109876543210987\"\n    }\n};\n// Known deployed tokens for fallback display (from memory)\nconst knownTokens = {\n    amoy: [\n        {\n            address: \"0x7544A3072FAA793e3f89048C31b794f171779544\",\n            name: \"Advanced Control Token\",\n            symbol: \"ACT\",\n            description: \"Security token with advanced transfer controls (conditional transfers, whitelisting, fees)\"\n        },\n        {\n            address: \"0xfccB88D208f5Ec7166ce2291138aaD5274C671dE\",\n            name: \"Augment_019\",\n            symbol: \"AUG019\",\n            description: \"Commodity token with 0 decimals, 1M max supply\"\n        },\n        {\n            address: \"0xe5F81d7dCeB8a8F97274C749773659B7288EcF90\",\n            name: \"Augment_01z\",\n            symbol: \"AUG01Z\",\n            description: \"Test token with custom configuration\"\n        },\n        {\n            address: \"0x391a0FA1498B869d0b9445596ed49b03aA8bf46e\",\n            name: \"Test Image Token\",\n            symbol: \"TIT2789\",\n            description: \"Test token with image URL support - deployed from upgraded factory\"\n        }\n    ]\n};\n// Token types for creating new tokens\nconst tokenTypes = [\n    {\n        id: \"equity\",\n        name: \"Equity\"\n    },\n    {\n        id: \"bond\",\n        name: \"Bond\"\n    },\n    {\n        id: \"debenture\",\n        name: \"Debenture\"\n    },\n    {\n        id: \"warrant\",\n        name: \"Warrant\"\n    },\n    {\n        id: \"realestate\",\n        name: \"Real Estate\"\n    },\n    {\n        id: \"carbon\",\n        name: \"Carbon Credit\"\n    },\n    {\n        id: \"commodity\",\n        name: \"Commodity\"\n    }\n];\n// Helper function to get contract addresses for the current network\nconst getContractAddresses = (network)=>{\n    return contractAddresses[network] || contractAddresses[defaultNetwork];\n};\n// Helper function to get network configuration for the current network\nconst getNetworkConfig = (network)=>{\n    return networkConfig[network] || networkConfig[defaultNetwork];\n};\n// Helper function to get known tokens for a network\nconst getKnownTokens = (network)=>{\n    return knownTokens[network] || [];\n};\n// Helper to check if a token exists in factory (in a real implementation, this would query the blockchain)\nconst verifyTokenExists = async (network, tokenAddress)=>{\n    // In a real implementation, this would:\n    // 1. Connect to the factory contract\n    // 2. Call a method or check events to verify the token's existence\n    // For demo purposes, we'll just return true\n    return true;\n};\n// Helper function to validate if an address is a known token\nconst isKnownToken = (network, tokenAddress)=>{\n    const tokens = getKnownTokens(network);\n    return tokens.some((token)=>token.address.toLowerCase() === tokenAddress.toLowerCase());\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/config/wagmi.ts":
/*!*****************************!*\
  !*** ./src/config/wagmi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chains: () => (/* binding */ chains),\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/polygonAmoy.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/polygon.js\");\n/* harmony import */ var wagmi_connectors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi/connectors */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var wagmi_connectors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi/connectors */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\");\n\n\n\n// Define the chains we support\nconst chains = [\n    wagmi_chains__WEBPACK_IMPORTED_MODULE_0__.polygonAmoy,\n    wagmi_chains__WEBPACK_IMPORTED_MODULE_1__.polygon\n];\n// Create wagmi config\nconst config = (0,wagmi__WEBPACK_IMPORTED_MODULE_2__.createConfig)({\n    chains,\n    connectors: [\n        (0,wagmi_connectors__WEBPACK_IMPORTED_MODULE_3__.injected)(),\n        (0,wagmi_connectors__WEBPACK_IMPORTED_MODULE_4__.metaMask)()\n    ],\n    transports: {\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_0__.polygonAmoy.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.http)('https://rpc-amoy.polygon.technology'),\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_1__.polygon.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.http)('https://polygon-rpc.com')\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uZmlnL3dhZ21pLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ1M7QUFDRTtBQUVyRCwrQkFBK0I7QUFDeEIsTUFBTU0sU0FBUztJQUFDSCxxREFBV0E7SUFBRUQsaURBQU9BO0NBQUMsQ0FBUztBQUVyRCxzQkFBc0I7QUFDZixNQUFNSyxTQUFTUCxtREFBWUEsQ0FBQztJQUNqQ007SUFDQUUsWUFBWTtRQUNWSiwwREFBUUE7UUFDUkMsMERBQVFBO0tBQ1Q7SUFDREksWUFBWTtRQUNWLENBQUNOLHFEQUFXQSxDQUFDTyxFQUFFLENBQUMsRUFBRVQsMkNBQUlBLENBQUM7UUFDdkIsQ0FBQ0MsaURBQU9BLENBQUNRLEVBQUUsQ0FBQyxFQUFFVCwyQ0FBSUEsQ0FBQztJQUNyQjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcY29uZmlnXFx3YWdtaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb25maWcsIGh0dHAgfSBmcm9tICd3YWdtaSdcbmltcG9ydCB7IHBvbHlnb24sIHBvbHlnb25BbW95IH0gZnJvbSAnd2FnbWkvY2hhaW5zJ1xuaW1wb3J0IHsgaW5qZWN0ZWQsIG1ldGFNYXNrIH0gZnJvbSAnd2FnbWkvY29ubmVjdG9ycydcblxuLy8gRGVmaW5lIHRoZSBjaGFpbnMgd2Ugc3VwcG9ydFxuZXhwb3J0IGNvbnN0IGNoYWlucyA9IFtwb2x5Z29uQW1veSwgcG9seWdvbl0gYXMgY29uc3RcblxuLy8gQ3JlYXRlIHdhZ21pIGNvbmZpZ1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGNyZWF0ZUNvbmZpZyh7XG4gIGNoYWlucyxcbiAgY29ubmVjdG9yczogW1xuICAgIGluamVjdGVkKCksXG4gICAgbWV0YU1hc2soKSxcbiAgXSxcbiAgdHJhbnNwb3J0czoge1xuICAgIFtwb2x5Z29uQW1veS5pZF06IGh0dHAoJ2h0dHBzOi8vcnBjLWFtb3kucG9seWdvbi50ZWNobm9sb2d5JyksXG4gICAgW3BvbHlnb24uaWRdOiBodHRwKCdodHRwczovL3BvbHlnb24tcnBjLmNvbScpLFxuICB9LFxufSlcblxuZGVjbGFyZSBtb2R1bGUgJ3dhZ21pJyB7XG4gIGludGVyZmFjZSBSZWdpc3RlciB7XG4gICAgY29uZmlnOiB0eXBlb2YgY29uZmlnXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb25maWciLCJodHRwIiwicG9seWdvbiIsInBvbHlnb25BbW95IiwiaW5qZWN0ZWQiLCJtZXRhTWFzayIsImNoYWlucyIsImNvbmZpZyIsImNvbm5lY3RvcnMiLCJ0cmFuc3BvcnRzIiwiaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/config/wagmi.ts\n");

/***/ }),

/***/ "(ssr)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"_format":"hh-sol-artifact-1","contractName":"SecurityToken","sourceName":"contracts/SecurityToken.sol","abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}],"bytecode":"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","deployedBytecode":"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","linkReferences":{},"deployedLinkReferences":{}}');

/***/ }),

/***/ "(ssr)/./src/contracts/SecurityTokenFactory.json":
/*!*************************************************!*\
  !*** ./src/contracts/SecurityTokenFactory.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldTokenImplementation","type":"address"},{"indexed":true,"internalType":"address","name":"newTokenImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"}],"name":"ImplementationsUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"identityRegistryAddress","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"},{"indexed":false,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"bool","name":"hasKYC","type":"bool"},{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenDeployed","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"addDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"deploySecurityToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"},{"internalType":"bool","name":"withKYC","type":"bool"}],"name":"deploySecurityTokenWithOptions","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllDeployedTokens","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getDeployedToken","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"symbol","type":"string"}],"name":"getTokenAddressBySymbol","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTokenCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"removeDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newTokenImplementation","type":"address"},{"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"}],"name":"updateImplementations","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"whitelistImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"whitelistWithKYCImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]}');

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/viem","vendor-chunks/@wagmi","vendor-chunks/@tanstack","vendor-chunks/zustand","vendor-chunks/eventemitter3","vendor-chunks/mipd","vendor-chunks/@swc","vendor-chunks/wagmi"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();