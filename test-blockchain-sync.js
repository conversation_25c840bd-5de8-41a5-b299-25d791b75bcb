// Test script to verify blockchain sync functionality
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3002/api';

async function testBlockchainSync() {
  console.log('=== Testing Blockchain Sync for TotalSupply ===');
  
  // Create a token with a real address that exists on blockchain
  const realToken = {
    address: '0xfccB88D208f5Ec7166ce2291138aaD5274C671dE', // Real Augment_019 token
    name: 'Augment_019_Real',
    symbol: 'AUG019R',
    decimals: 0, // This token has 0 decimals
    maxSupply: '1000000',
    totalSupply: '0', // Will be updated from blockchain
    tokenType: 'commodity',
    tokenPrice: '10 USD',
    currency: 'USD',
    bonusTiers: 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%',
    whitelistAddress: '******************************************',
    adminAddress: '******************************************',
    hasKYC: true,
    network: 'amoy',
    deploymentNotes: 'Real token for blockchain sync testing'
  };
  
  try {
    console.log('Creating token with real blockchain address...');
    
    const response = await fetch(`${ADMIN_API_URL}/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(realToken),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Token created successfully!');
      console.log(`Token ID: ${data.id}`);
      console.log(`Initial Total Supply: ${data.totalSupply}`);
      
      // Now test the blockchain sync by fetching from database source
      console.log('\nTesting blockchain sync...');
      
      const syncResponse = await fetch(`${ADMIN_API_URL}/tokens?source=database`);
      const tokens = await syncResponse.json();
      
      const syncedToken = tokens.find(t => t.symbol === 'AUG019R');
      
      if (syncedToken) {
        console.log(`✅ Token found after sync`);
        console.log(`Total Supply after sync: ${syncedToken.totalSupply}`);
        console.log(`Decimals: ${syncedToken.decimals}`);
        
        // The sync should have attempted to fetch from blockchain
        // If totalSupply is still 0, it means either:
        // 1. The token really has 0 total supply on blockchain
        // 2. The blockchain call failed (which is expected in test environment)
        
        if (syncedToken.totalSupply === '0') {
          console.log('ℹ️  Total supply is 0 - this is expected for a newly deployed token or if blockchain sync failed');
        } else {
          console.log('✅ Total supply was updated from blockchain!');
        }
      } else {
        console.log('❌ Token not found after sync');
      }
      
    } else {
      console.log(`❌ Failed to create token: ${data.error}`);
    }
  } catch (error) {
    console.error('Error testing blockchain sync:', error.message);
  }
}

async function testClientAPIWithRealData() {
  console.log('\n=== Testing Client API with Real Data ===');
  
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    const tokens = await response.json();
    
    console.log(`Number of tokens available to client: ${tokens.length}`);
    
    // Find tokens with 0 decimals to verify they're handled correctly
    const zeroDecimalTokens = tokens.filter(t => t.decimals === 0);
    console.log(`Tokens with 0 decimals: ${zeroDecimalTokens.length}`);
    
    if (zeroDecimalTokens.length > 0) {
      console.log('\nZero decimal tokens:');
      zeroDecimalTokens.forEach((token, index) => {
        console.log(`${index + 1}. ${token.name} (${token.symbol})`);
        console.log(`   - Decimals: ${token.decimals}`);
        console.log(`   - Total Supply: ${token.totalSupply}`);
        console.log(`   - Max Supply: ${token.maxSupply}`);
        console.log(`   - Price: ${token.price}`);
      });
    }
    
    // Find tokens with non-zero decimals
    const nonZeroDecimalTokens = tokens.filter(t => t.decimals > 0);
    console.log(`\nTokens with non-zero decimals: ${nonZeroDecimalTokens.length}`);
    
    if (nonZeroDecimalTokens.length > 0) {
      console.log('\nNon-zero decimal tokens:');
      nonZeroDecimalTokens.slice(0, 3).forEach((token, index) => {
        console.log(`${index + 1}. ${token.name} (${token.symbol})`);
        console.log(`   - Decimals: ${token.decimals}`);
        console.log(`   - Total Supply: ${token.totalSupply}`);
        console.log(`   - Max Supply: ${token.maxSupply}`);
      });
    }
    
  } catch (error) {
    console.error('Error testing client API:', error.message);
  }
}

async function main() {
  await testBlockchainSync();
  await testClientAPIWithRealData();
}

main().catch(console.error);
