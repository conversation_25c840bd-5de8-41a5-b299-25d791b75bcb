const { ethers } = require("ethers");

async function debugWhitelistIssue() {
  console.log("🔍 Debugging Whitelist Transaction Failure");
  console.log("==========================================");

  // Configuration
  const WHITELIST_ADDRESS = "******************************************";
  const CLIENT_ADDRESS = "******************************************"; // The address being whitelisted
  const ADMIN_ADDRESS = "******************************************"; // Your admin address
  const RPC_URL = process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology/";

  try {
    // Connect to the network
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    console.log(`📡 Connected to network: ${RPC_URL}`);

    // Load the Whitelist ABI
    const WhitelistABI = require("./src/contracts/Whitelist.json");
    
    // Connect to the whitelist contract (read-only)
    const whitelistContract = new ethers.Contract(
      WHITELIST_ADDRESS,
      WhitelistABI.abi,
      provider
    );

    console.log(`📋 Whitelist Contract: ${WHITELIST_ADDRESS}`);
    console.log(`👤 Client Address: ${CLIENT_ADDRESS}`);
    console.log(`🔑 Admin Address: ${ADMIN_ADDRESS}`);
    console.log("");

    // Check 1: Is the address already whitelisted?
    console.log("1️⃣ Checking if address is already whitelisted...");
    try {
      const isWhitelisted = await whitelistContract.isWhitelisted(CLIENT_ADDRESS);
      console.log(`   Result: ${isWhitelisted ? "✅ ALREADY WHITELISTED" : "❌ NOT WHITELISTED"}`);
      
      if (isWhitelisted) {
        console.log("   🚨 ERROR FOUND: Address is already whitelisted!");
        console.log("   💡 Solution: The address is already on the whitelist. No action needed.");
        return;
      }
    } catch (error) {
      console.log(`   ❌ Error checking whitelist status: ${error.message}`);
    }

    // Check 2: Does the admin have the AGENT_ROLE?
    console.log("\n2️⃣ Checking admin role permissions...");
    try {
      // Get the AGENT_ROLE hash
      const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
      console.log(`   AGENT_ROLE hash: ${AGENT_ROLE}`);

      // Check if admin has the AGENT_ROLE
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, ADMIN_ADDRESS);
      console.log(`   Admin has AGENT_ROLE: ${hasAgentRole ? "✅ YES" : "❌ NO"}`);

      if (!hasAgentRole) {
        console.log("   🚨 ERROR FOUND: Admin doesn't have AGENT_ROLE!");
        console.log("   💡 Solution: Grant AGENT_ROLE to the admin address.");
        
        // Check if admin has DEFAULT_ADMIN_ROLE to grant roles
        const DEFAULT_ADMIN_ROLE = await whitelistContract.DEFAULT_ADMIN_ROLE();
        const hasDefaultAdminRole = await whitelistContract.hasRole(DEFAULT_ADMIN_ROLE, ADMIN_ADDRESS);
        console.log(`   Admin has DEFAULT_ADMIN_ROLE: ${hasDefaultAdminRole ? "✅ YES" : "❌ NO"}`);
        
        if (hasDefaultAdminRole) {
          console.log("   🔧 You can grant AGENT_ROLE using: grantRole(AGENT_ROLE, adminAddress)");
        } else {
          console.log("   ⚠️  Admin needs DEFAULT_ADMIN_ROLE to grant other roles");
        }
        return;
      }
    } catch (error) {
      console.log(`   ❌ Error checking roles: ${error.message}`);
    }

    // Check 3: Contract state and other potential issues
    console.log("\n3️⃣ Checking contract state...");
    try {
      // Check if contract is paused (if it has a paused function)
      try {
        const isPaused = await whitelistContract.paused();
        console.log(`   Contract paused: ${isPaused ? "⚠️ YES" : "✅ NO"}`);
        if (isPaused) {
          console.log("   🚨 ERROR FOUND: Contract is paused!");
          console.log("   💡 Solution: Unpause the contract first.");
          return;
        }
      } catch (error) {
        console.log("   ℹ️  Contract doesn't have pause functionality");
      }

      // Check contract owner
      try {
        const owner = await whitelistContract.owner();
        console.log(`   Contract owner: ${owner}`);
        console.log(`   Is admin the owner: ${owner.toLowerCase() === ADMIN_ADDRESS.toLowerCase() ? "✅ YES" : "❌ NO"}`);
      } catch (error) {
        console.log("   ℹ️  Contract doesn't have owner function");
      }

    } catch (error) {
      console.log(`   ❌ Error checking contract state: ${error.message}`);
    }

    // Check 4: Gas estimation
    console.log("\n4️⃣ Estimating gas for addToWhitelist...");
    try {
      // Create a wallet instance for gas estimation
      const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
      if (!privateKey) {
        console.log("   ⚠️  No private key found for gas estimation");
      } else {
        const wallet = new ethers.Wallet(privateKey, provider);
        const whitelistWithSigner = new ethers.Contract(
          WHITELIST_ADDRESS,
          WhitelistABI.abi,
          wallet
        );

        const gasEstimate = await whitelistWithSigner.addToWhitelist.estimateGas(CLIENT_ADDRESS);
        console.log(`   Estimated gas: ${gasEstimate.toString()}`);
        console.log(`   Gas limit used: 200,000 (${gasEstimate > 200000 ? "❌ TOO LOW" : "✅ SUFFICIENT"})`);
      }
    } catch (error) {
      console.log(`   ❌ Gas estimation failed: ${error.message}`);
      console.log("   🚨 This might be the cause of the transaction failure!");
      
      // Try to decode the error
      if (error.data) {
        console.log(`   Error data: ${error.data}`);
      }
      if (error.reason) {
        console.log(`   Error reason: ${error.reason}`);
      }
    }

    console.log("\n📋 Summary:");
    console.log("===========");
    console.log("If no specific errors were found above, the issue might be:");
    console.log("1. Network congestion or RPC issues");
    console.log("2. Insufficient gas price (currently using 50 gwei)");
    console.log("3. Contract-specific validation that's not visible in the ABI");
    console.log("4. The address format or checksum issues");
    console.log("\n💡 Recommended actions:");
    console.log("1. Check if the address is already whitelisted in the UI");
    console.log("2. Verify the admin has proper roles");
    console.log("3. Try with higher gas limit (300,000) and gas price (100 gwei)");
    console.log("4. Check the transaction on Polygonscan for more details");

  } catch (error) {
    console.error("❌ Debug script failed:", error);
  }
}

// Run the debug script
debugWhitelistIssue().catch(console.error);
