import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenABI from '../../../../../contracts/SecurityToken.json';
import WhitelistABI from '../../../../../contracts/Whitelist.json';

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002, // Default to Amoy
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tokenAddress,
      address,
      network = 'amoy'
    } = body;

    if (!tokenAddress || !address) {
      return NextResponse.json(
        { error: 'Token address and address to unfreeze are required' },
        { status: 400 }
      );
    }

    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials.'
        },
        { status: 422 }
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;

    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);

    // Connect to the network with explicit chainId
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });

    // Ensure the network is connected and recognized
    const network_details = await provider.getNetwork();
    console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);

    // Create the wallet with the correctly configured provider
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    console.log(`Wallet address: ${wallet.address}`);

    // Step 1: Connect to the token contract
    const tokenContract = new ethers.Contract(
      tokenAddress,
      SecurityTokenABI.abi,
      wallet
    );

    // Step 2: Get the whitelist contract address from the token
    console.log("Getting whitelist address from token contract...");
    const whitelistAddress = await tokenContract.identityRegistry();
    console.log(`Whitelist contract address: ${whitelistAddress}`);

    if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      return NextResponse.json(
        { error: 'Whitelist contract address not set on the token' },
        { status: 400 }
      );
    }

    // Step 3: Connect to the whitelist contract
    const whitelistContract = new ethers.Contract(
      whitelistAddress,
      WhitelistABI.abi,
      wallet
    );

    // Execute unfreeze address
    try {
      console.log(`Unfreezing address ${address}...`);
      const tx = await whitelistContract.unfreezeAddress(address);
      console.log(`Transaction hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      return NextResponse.json({
        success: true,
        action: 'unfreezeAddress',
        txHash: tx.hash,
        blockNumber: receipt.blockNumber,
        address: address
      });

    } catch (txError: any) {
      console.error('Transaction error:', txError);

      // Check if the issue might be a permissions problem
      try {
        const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
        const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, wallet.address);

        if (!hasAgentRole) {
          return NextResponse.json({
            success: false,
            error: "The connected wallet doesn't have the AGENT_ROLE required for unfreezing addresses",
            details: `Please grant AGENT_ROLE to ${wallet.address} on the whitelist contract`
          }, { status: 403 });
        }
      } catch (roleCheckError) {
        console.error('Role check error:', roleCheckError);
      }

      throw txError; // Re-throw to be caught by the outer catch
    }

  } catch (error: any) {
    console.error('Error unfreezing address:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}