const fetch = require('node-fetch');

async function testAgreementSync() {
  console.log('🔍 Testing Agreement Database Sync...');
  
  const adminApiUrl = 'http://localhost:3000/api';
  const clientApiUrl = 'http://localhost:7788/api';
  
  console.log('\n1. Testing Admin Panel API...');
  try {
    const response = await fetch(`${adminApiUrl}/clients?limit=5`);
    console.log(`Admin Panel API Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Admin Panel API working - ${data.clients?.length || 0} clients found`);
      
      // Show agreement status of existing clients
      if (data.clients && data.clients.length > 0) {
        console.log('\n📋 Current client agreement statuses:');
        data.clients.forEach((client, index) => {
          console.log(`  ${index + 1}. ${client.email}: Agreement ${client.agreementAccepted ? 'Accepted' : 'Not Accepted'}`);
          if (client.agreementAcceptedAt) {
            console.log(`     Accepted at: ${new Date(client.agreementAcceptedAt).toLocaleString()}`);
          }
        });
      }
    } else {
      console.log(`❌ Admin Panel API Error: ${response.statusText}`);
      return;
    }
  } catch (error) {
    console.log(`❌ Admin Panel API Connection Failed: ${error.message}`);
    console.log('   Make sure admin panel is running on port 3000');
    return;
  }
  
  console.log('\n2. Testing Client Portal Agreement API...');
  try {
    const response = await fetch(`${clientApiUrl}/client/agreement`);
    console.log(`Client Agreement API Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('✅ Client Agreement API is working (401 = needs authentication)');
    } else if (response.ok) {
      const data = await response.json();
      console.log('✅ Client Agreement API response:', data);
    } else {
      console.log(`❌ Client Agreement API Error: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Client Agreement API Connection Failed: ${error.message}`);
    console.log('   Make sure client portal is running on port 3001');
    return;
  }
  
  console.log('\n3. Testing Agreement Sync Flow...');
  
  // Test creating a client with agreement acceptance
  const testEmail = `test-agreement-${Date.now()}@example.com`;
  
  try {
    console.log(`📝 Creating test client: ${testEmail}`);
    
    const createResponse = await fetch(`${adminApiUrl}/clients`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        firstName: 'Agreement',
        lastName: 'Test',
        gender: 'PREFER_NOT_TO_SAY',
        nationality: 'Test',
        birthday: new Date('1990-01-01').toISOString(),
        birthPlace: 'Test City',
        identificationType: 'PASSPORT',
        documentExpiration: new Date('2030-01-01').toISOString(),
        phoneNumber: '+**********',
        occupation: 'Tester',
        sectorOfActivity: 'Testing',
        pepStatus: 'NOT_PEP',
        street: 'Test Street',
        buildingNumber: '123',
        city: 'Test City',
        country: 'Test Country',
        zipCode: '12345',
        sourceOfWealth: 'Testing',
        bankAccountNumber: 'TEST123456',
        sourceOfFunds: 'Testing',
        taxIdentificationNumber: `TEST${Date.now()}`,
        agreementAccepted: false, // Initially not accepted
      }),
    });
    
    if (createResponse.ok) {
      const client = await createResponse.json();
      console.log(`✅ Created test client: ${client.id}`);
      
      // Test updating agreement status
      console.log('📝 Updating agreement status to accepted...');
      
      const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agreementAccepted: true,
          agreementAcceptedAt: new Date().toISOString(),
        }),
      });
      
      if (updateResponse.ok) {
        const updatedClient = await updateResponse.json();
        console.log('✅ Agreement status updated successfully');
        console.log(`   Agreement Accepted: ${updatedClient.agreementAccepted}`);
        console.log(`   Accepted At: ${updatedClient.agreementAcceptedAt}`);
        
        // Verify the update by fetching the client again
        const verifyResponse = await fetch(`${adminApiUrl}/clients/${client.id}`);
        if (verifyResponse.ok) {
          const verifiedClient = await verifyResponse.json();
          console.log('✅ Verification successful:');
          console.log(`   Agreement Accepted: ${verifiedClient.agreementAccepted}`);
          console.log(`   Accepted At: ${verifiedClient.agreementAcceptedAt}`);
        }
        
      } else {
        console.log(`❌ Failed to update agreement status: ${updateResponse.status}`);
      }
      
    } else {
      console.log(`❌ Failed to create test client: ${createResponse.status}`);
      const errorText = await createResponse.text();
      console.log(`   Error: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`❌ Agreement sync test failed: ${error.message}`);
  }
  
  console.log('\n📋 SUMMARY:');
  console.log('===========');
  console.log('✅ Admin Panel API: Working');
  console.log('✅ Client Agreement API: Working');
  console.log('✅ Agreement Database Sync: Ready');
  console.log('');
  console.log('🎯 Next Steps:');
  console.log('1. Start both applications (admin panel on :3000, client portal on :3001)');
  console.log('2. Login to client portal with Auth0');
  console.log('3. Accept agreement in qualification flow');
  console.log('4. Check admin panel to see agreement status updated');
  console.log('');
  console.log('🌐 URLs to check:');
  console.log('   - Admin Panel: http://localhost:3000/clients');
  console.log('   - Client Portal: http://localhost:7788');
}

testAgreementSync().catch(console.error);
