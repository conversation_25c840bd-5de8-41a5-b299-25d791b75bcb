'use client';

import { useState } from 'react';
import { CheckCircleIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface TokenAgreementProps {
  onComplete: () => Promise<void> | void;
  tokenName?: string;
  tokenSymbol?: string;
  isCompleted?: boolean;
  agreementText?: string;
}

export function TokenAgreement({ 
  onComplete, 
  tokenName = 'Security Token', 
  tokenSymbol = 'TOKEN',
  isCompleted,
  agreementText 
}: TokenAgreementProps) {
  const [checkboxChecked, setCheckboxChecked] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAcceptAgreement = async () => {
    if (!checkboxChecked) {
      alert('Please check the agreement checkbox before confirming.');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('📄 Accepting token agreement for:', tokenName, tokenSymbol);

      // Call the completion handler (which will save the agreement)
      await onComplete();

      console.log('✅ Token agreement accepted successfully');
    } catch (error) {
      console.error('❌ Error accepting agreement:', error);
      alert('Failed to accept agreement. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If already completed, show completion status
  if (isCompleted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-green-800">Agreement Accepted</h3>
            <p className="text-green-700">
              You have successfully accepted the {tokenName} ({tokenSymbol}) investment agreement.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <DocumentTextIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {tokenName} Investment Agreement
        </h2>
        <p className="text-gray-600">
          Please review and accept the specific terms for investing in {tokenName} ({tokenSymbol}).
        </p>
      </div>

      {/* Agreement Document Section */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-start space-x-4">
          <DocumentTextIcon className="h-8 w-8 text-blue-600 mt-1 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {tokenName} ({tokenSymbol}) Investment Terms
            </h3>
            
            {/* Agreement Content */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
              {agreementText ? (
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {agreementText}
                </div>
              ) : (
                <div className="text-sm text-gray-700 space-y-4">
                  <p><strong>INVESTMENT AGREEMENT FOR {tokenName.toUpperCase()} ({tokenSymbol})</strong></p>
                  
                  <p>This agreement governs your investment in {tokenName} security tokens. By proceeding, you acknowledge and agree to the following terms:</p>
                  
                  <div className="space-y-2">
                    <p><strong>1. Token Details:</strong></p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                      <li>Token Name: {tokenName}</li>
                      <li>Token Symbol: {tokenSymbol}</li>
                      <li>Token Type: Security Token</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <p><strong>2. Investment Terms:</strong></p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                      <li>This is a security token offering subject to applicable securities laws</li>
                      <li>Tokens are restricted securities and may not be freely transferable</li>
                      <li>Investment involves significant risk and may result in total loss</li>
                      <li>No guarantee of returns or liquidity</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <p><strong>3. Compliance Requirements:</strong></p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                      <li>You must be an accredited investor or qualified purchaser</li>
                      <li>You must complete KYC/AML verification</li>
                      <li>You must comply with transfer restrictions</li>
                    </ul>
                  </div>

                  <p className="text-xs text-gray-500 mt-4">
                    This is a simplified version. The complete agreement will be provided upon investment confirmation.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Agreement Acceptance Section */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Agreement Acceptance
        </h3>

        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-800">
              <strong>Important:</strong> This agreement is specific to {tokenName} ({tokenSymbol}). 
              By accepting, you agree to the terms and conditions for this particular token investment.
            </p>
          </div>

          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="token-agreement-checkbox"
              checked={checkboxChecked}
              onChange={(e) => setCheckboxChecked(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
              disabled={isSubmitting}
            />
            <label htmlFor="token-agreement-checkbox" className="text-sm text-gray-700 leading-relaxed">
              I have read, understood, and agree to the {tokenName} ({tokenSymbol}) investment agreement 
              terms and conditions. I acknowledge that this agreement is legally binding and governs 
              my investment in this specific security token.
            </label>
          </div>

          <div className="pt-2">
            <button
              onClick={handleAcceptAgreement}
              disabled={!checkboxChecked || isSubmitting}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
                checkboxChecked && !isSubmitting
                  ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing Agreement...
                </>
              ) : (
                `Accept ${tokenName} Agreement`
              )}
            </button>

            {!checkboxChecked && (
              <p className="mt-2 text-xs text-gray-500 text-center">
                Please check the agreement checkbox above to enable confirmation
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
