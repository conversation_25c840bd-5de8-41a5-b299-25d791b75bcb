import React from 'react';
import Link from 'next/link';
import { DeployedToken } from '../types';

interface TokenDeploymentProps {
  deployedToken: DeployedToken;
  transactionHash: string | null;
  network: string;
  getBlockExplorerUrl: (network: string, address: string) => string;
  getTransactionExplorerUrl: (network: string, txHash: string) => string;
}

/**
 * TokenDeployment Component
 *
 * Displays the details of a successfully deployed token
 */
const TokenDeployment: React.FC<TokenDeploymentProps> = ({
  deployedToken,
  transactionHash,
  network,
  getBlockExplorerUrl,
  getTransactionExplorerUrl
}) => {
  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-6">
      <h2 className="text-xl font-bold mb-4 text-green-700 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
        Token Successfully Deployed!
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 mb-4">
        <div>
          <p className="text-sm font-medium text-gray-500">Token Name:</p>
          <p className="font-semibold">{deployedToken.name}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Token Symbol:</p>
          <p className="font-semibold">{deployedToken.symbol}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Decimals:</p>
          <p className="font-semibold">{deployedToken.decimals}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Token Address:</p>
          <p className="font-mono text-sm break-all">{deployedToken.address}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Whitelist Address:</p>
          <p className="font-mono text-sm break-all">{deployedToken.whitelistAddress}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Max Supply:</p>
          <p>{deployedToken.maxSupply} tokens</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Admin:</p>
          <p className="font-mono text-sm break-all">{deployedToken.admin}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Token Price:</p>
          <p>{deployedToken.tokenPrice}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Bonus Tiers:</p>
          <p>{deployedToken.bonusTiers}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">KYC Support:</p>
          <p>{deployedToken.hasKYC ? 'Enabled' : 'Disabled'}</p>
        </div>
      </div>

      {transactionHash && (
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-500">Transaction Hash:</p>
          <a
            href={getTransactionExplorerUrl(network, transactionHash)}
            target="_blank"
            rel="noopener noreferrer"
            className="font-mono text-sm break-all text-blue-600 hover:text-blue-800"
          >
            {transactionHash}
          </a>
        </div>
      )}

      <div className="flex flex-wrap gap-2 mt-4">
        <a
          href={getBlockExplorerUrl(network, deployedToken.address)}
          target="_blank"
          rel="noopener noreferrer"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded inline-flex items-center"
        >
          View Token on Explorer
        </a>

        <Link
          href={`/tokens/${deployedToken.address}`}
          className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded inline-flex items-center"
        >
          View Token Details
        </Link>

        <Link
          href="/"
          className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded inline-flex items-center"
        >
          Return to Dashboard
        </Link>
      </div>
    </div>
  );
};

export default TokenDeployment;