const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Testing Direct Deployment...");

  const [deployer] = await ethers.getSigners();
  console.log("Account:", deployer.address);

  // Deploy contracts directly (not through factory)
  console.log("\n🚀 Deploying contracts directly...");

  // Deploy Whitelist implementation
  const Whitelist = await ethers.getContractFactory("Whitelist");
  const whitelistImpl = await Whitelist.deploy();
  await whitelistImpl.waitForDeployment();
  const whitelistImplAddress = await whitelistImpl.getAddress();
  console.log("Whitelist implementation:", whitelistImplAddress);

  // Deploy Whitelist proxy
  const ERC1967Proxy = await ethers.getContractFactory("ERC1967Proxy");
  const whitelistData = Whitelist.interface.encodeFunctionData("initializeWithAgent", [deployer.address]);
  const whitelistProxy = await ERC1967Proxy.deploy(whitelistImplAddress, whitelistData);
  await whitelistProxy.waitForDeployment();
  const whitelistProxyAddress = await whitelistProxy.getAddress();
  console.log("Whitelist proxy:", whitelistProxyAddress);

  // Deploy SecurityToken implementation
  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  const tokenImpl = await SecurityToken.deploy();
  await tokenImpl.waitForDeployment();
  const tokenImplAddress = await tokenImpl.getAddress();
  console.log("SecurityToken implementation:", tokenImplAddress);

  // Deploy SecurityToken proxy with agreement
  const agreementUrl = "https://example.com/investment-agreement.pdf";
  const tokenData = SecurityToken.interface.encodeFunctionData("initializeWithAgreement", [
    "Direct Agreement Token",
    "DAT001",
    0,
    ethers.parseUnits("1000000", 0),
    whitelistProxyAddress,
    deployer.address,
    "100 USD",
    "Tier 1: 5%",
    "Direct deployment test token with agreement",
    "",
    agreementUrl
  ]);

  console.log("\n📄 Deploying SecurityToken proxy...");
  const tokenProxy = await ERC1967Proxy.deploy(tokenImplAddress, tokenData);
  await tokenProxy.waitForDeployment();
  const tokenProxyAddress = await tokenProxy.getAddress();
  console.log("✅ SecurityToken proxy deployed:", tokenProxyAddress);

  // Test the deployed token
  const token = SecurityToken.attach(tokenProxyAddress);

  console.log("\n🔍 Testing agreement functions...");

  try {
    const retrievedUrl = await token.getAgreementUrl();
    console.log("✅ Agreement URL:", retrievedUrl);

    const hasAccepted = await token.hasAcceptedAgreement(deployer.address);
    console.log("✅ Has accepted agreement (initial):", hasAccepted);

    // Accept agreement
    console.log("\n📝 Accepting agreement...");
    const acceptTx = await token.acceptAgreement();
    await acceptTx.wait();
    console.log("✅ Agreement accepted");

    const hasAcceptedAfter = await token.hasAcceptedAgreement(deployer.address);
    console.log("✅ Has accepted agreement (after):", hasAcceptedAfter);

    const timestamp = await token.getAgreementAcceptanceTimestamp(deployer.address);
    console.log("✅ Acceptance timestamp:", timestamp.toString());

    console.log("\n🎉 DIRECT DEPLOYMENT SUCCESS!");
    console.log("Token Address:", tokenProxyAddress);
    console.log("Agreement URL:", agreementUrl);
    console.log("✅ All agreement functions working correctly!");

  } catch (error) {
    console.log("❌ Agreement function test failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Direct deployment failed:", error);
    process.exit(1);
  });
