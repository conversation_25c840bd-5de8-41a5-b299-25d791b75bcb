"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction XCircleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = XCircleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(XCircleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"XCircleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [qualificationStatus, setQualificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PENDING');\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                // Try to get more recent data from localStorage first\n                const storageKey = \"qualification_progress_\".concat(tokenAddress);\n                let localProgress = null;\n                try {\n                    const stored = localStorage.getItem(storageKey);\n                    if (stored) {\n                        localProgress = JSON.parse(stored);\n                        console.log('📱 Found localStorage progress:', localProgress);\n                    }\n                } catch (error) {\n                    console.error('Error reading localStorage:', error);\n                }\n                // Use localStorage data if it's more recent, otherwise use API data\n                const progressToUse = localProgress || qualificationProgress;\n                const newStepData = {\n                    country: progressToUse.country || '',\n                    agreementAccepted: progressToUse.agreementAccepted || false,\n                    profileCompleted: progressToUse.profileCompleted || !!profile,\n                    walletConnected: progressToUse.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: progressToUse.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set qualification status\n                if (progressToUse.qualificationStatus) {\n                    setQualificationStatus(progressToUse.qualificationStatus);\n                }\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = progressToUse.currentStep || 0;\n                // Allow users to progress through all steps without blocking\n                // Only auto-advance to next incomplete step if current step is completed\n                if (calculatedStep === 0 && newStepData.country) {\n                    calculatedStep = 1; // Move to agreement if country is selected\n                } else if (calculatedStep === 1 && newStepData.agreementAccepted) {\n                    calculatedStep = 2; // Move to profile if agreement is accepted\n                } else if (calculatedStep === 2 && newStepData.profileCompleted) {\n                    calculatedStep = 3; // Move to wallet if profile is completed\n                } else if (calculatedStep === 3 && newStepData.walletConnected) {\n                    calculatedStep = 4; // Move to KYC if wallet is connected\n                } else if (calculatedStep === 4 && newStepData.kycCompleted) {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: progressToUse,\n                    source: localProgress ? 'localStorage' : 'API'\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus,\n        tokenAddress\n    ]);\n    // Function to manually fix qualification progress flags\n    const fixQualificationFlags = async ()=>{\n        if (!profile || !tokenAddress) {\n            console.error('❌ Missing profile or token address');\n            return;\n        }\n        try {\n            console.log('🔧 Attempting to fix qualification progress flags...');\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userEmail: profile.email,\n                    tokenAddress: tokenAddress\n                })\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log('✅ Qualification progress flags fixed successfully:', result);\n                // Reload the page to see the updated data\n                window.location.reload();\n            } else {\n                console.error('❌ Failed to fix qualification progress flags:', response.status);\n            }\n        } catch (error) {\n            console.error('❌ Error fixing qualification progress flags:', error);\n        }\n    };\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            // Ensure we have the correct completion flags based on current state\n            // For country: check if it was explicitly completed OR if there's a country value\n            const countryCompleted = updatedStepData.countryCompleted === true || updatedStepData.country && updatedStepData.country !== '' || stepData.countryCompleted === true || stepData.country && stepData.country !== '';\n            const agreementAccepted = updatedStepData.agreementAccepted === true || stepData.agreementAccepted === true;\n            const profileCompleted = updatedStepData.profileCompleted === true || stepData.profileCompleted === true || !!profile;\n            const walletConnected = updatedStepData.walletConnected === true || stepData.walletConnected === true || !!(profile === null || profile === void 0 ? void 0 : profile.walletAddress);\n            const kycCompleted = updatedStepData.kycCompleted === true || stepData.kycCompleted === true || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED';\n            // Calculate completed steps based on actual step completion flags\n            const stepCompletionFlags = [\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted\n            ];\n            const actualCompletedSteps = stepCompletionFlags.filter(Boolean).length;\n            const progressData = {\n                ...updatedStepData,\n                // Ensure all completion flags are explicitly set\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                // Preserve country value if it exists\n                country: updatedStepData.country || stepData.country || '',\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: actualCompletedSteps\n            };\n            console.log('💾 Saving progress to database:', progressData);\n            console.log('🔍 Step completion flags:', {\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                actualCompletedSteps\n            });\n            // Save to backend database via admin panel API\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully to database:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    // Step navigation functions\n    const canNavigateToStep = (stepIndex)=>{\n        // Users can always navigate to completed steps or the next incomplete step\n        if (stepIndex === 0) return true; // Country selection always available\n        if (stepIndex === 1) return stepData.country !== ''; // Agreement if country selected\n        if (stepIndex === 2) return stepData.agreementAccepted; // Profile if agreement accepted\n        if (stepIndex === 3) return stepData.profileCompleted; // Wallet if profile completed\n        if (stepIndex === 4) return stepData.walletConnected; // KYC if wallet connected\n        if (stepIndex === 5) return stepData.kycCompleted; // Completion if KYC done\n        return false;\n    };\n    const handleStepClick = (stepIndex)=>{\n        if (canNavigateToStep(stepIndex)) {\n            setCurrentStep(stepIndex);\n            // Force save with correct completion flags based on current state\n            const updatedData = {\n                ...stepData,\n                // Ensure all completion flags are set based on current state\n                countryCompleted: stepData.countryCompleted || stepData.country && stepData.country !== '' || completedSteps >= 1,\n                agreementAccepted: stepData.agreementAccepted || completedSteps >= 2,\n                profileCompleted: stepData.profileCompleted || !!profile || completedSteps >= 3,\n                walletConnected: stepData.walletConnected || !!(profile === null || profile === void 0 ? void 0 : profile.walletAddress) || completedSteps >= 4,\n                kycCompleted: stepData.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED' || completedSteps >= 5\n            };\n            console.log('🔧 Forcing save with correct completion flags:', updatedData);\n            saveProgress(updatedData, stepIndex);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 407,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 406,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleStepClick(index),\n                        className: \"p-4 rounded-lg border text-center transition-all duration-200 \".concat(getStepColor(step.status), \" \").concat(canNavigateToStep(index) ? 'cursor-pointer hover:shadow-md hover:scale-105' : 'cursor-not-allowed opacity-60'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            canNavigateToStep(index) && index !== currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 font-medium\",\n                                children: \"Click to navigate\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            qualificationStatus === 'APPROVED' || qualificationStatus === 'FORCE_APPROVED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Qualification Approved!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: [\n                                            \"Congratulations! Your qualification for \",\n                                            tokenName || 'this token',\n                                            \" has been approved. You are now eligible to invest in this token.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-green-800\",\n                                                            children: \"Approved for Investment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-green-700\",\n                                                            children: \"You can now proceed to invest in this token through the platform.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : qualificationStatus === 'REJECTED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-16 w-16 text-red-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Qualification Rejected\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: [\n                                            \"Unfortunately, your qualification for \",\n                                            tokenName || 'this token',\n                                            \" has been rejected. Please contact support for more information.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-red-800\",\n                                                            children: \"Application Rejected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-700\",\n                                                            children: \"Please review your information and contact support if needed.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Qualification Submitted!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: [\n                                            \"You have successfully completed all qualification steps for \",\n                                            tokenName || 'this token',\n                                            \". Your application is now pending admin review and approval.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-yellow-800\",\n                                                            children: \"Pending Admin Approval\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-yellow-700\",\n                                                            children: \"An administrator will review your qualification and approve you for token investment.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = '/',\n                                        className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"Return to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: fixQualificationFlags,\n                                        className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: \"Fix Progress Flags\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 465,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 417,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"iWBKLqK5TA8lhoQ0oRF1sD9fiNc=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3F1YWxpZmljYXRpb24vUXVhbGlmaWNhdGlvbkZsb3cudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDSztBQUM4RDtBQUN6RDtBQUNKO0FBQ087QUFDRjtBQUNSO0FBZXhDLFNBQVNZLGtCQUFrQixLQUFnRTtRQUFoRSxFQUFFQyxZQUFZLEVBQUVDLFNBQVMsRUFBRUMsV0FBVyxFQUEwQixHQUFoRTs7SUFDaEMsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FBQztRQUN2Q29CLFNBQVM7UUFDVEMsbUJBQW1CO1FBQ25CQyxrQkFBa0I7UUFDbEJDLGlCQUFpQjtRQUNqQkMsY0FBYztJQUNoQjtJQUNBLE1BQU0sQ0FBQ0MscUJBQXFCQyx1QkFBdUIsR0FBRzFCLCtDQUFRQSxDQUFTO0lBQ3ZFLE1BQU0sQ0FBQzJCLFdBQVdDLGFBQWEsR0FBRzVCLCtDQUFRQSxDQUFTO0lBQ25ELE1BQU0sQ0FBQzZCLFVBQVVDLFlBQVksR0FBRzlCLCtDQUFRQSxDQUFnQjtJQUV4RCx3Q0FBd0M7SUFDeEMsTUFBTSxFQUFFK0IsTUFBTUMscUJBQXFCLEVBQUVDLFNBQVMsRUFBRSxHQUFHL0IsK0RBQVFBLENBQUM7UUFDMURnQyxVQUFVO1lBQUM7WUFBMEJyQjtTQUFhO1FBQ2xEc0IsT0FBTzswQ0FBRTtnQkFDUCxNQUFNQyxTQUFTLElBQUlDO2dCQUNuQixJQUFJeEIsY0FBY3VCLE9BQU9FLE1BQU0sQ0FBQyxnQkFBZ0J6QjtnQkFFaEQsTUFBTTBCLFdBQVcsTUFBTUMsTUFBTSxzQ0FBd0QsT0FBbEJKLE9BQU9LLFFBQVE7Z0JBQ2xGLElBQUlGLFNBQVNHLEVBQUUsRUFBRTtvQkFDZixPQUFPSCxTQUFTSSxJQUFJO2dCQUN0QjtnQkFDQSxPQUFPO1lBQ1Q7O0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTSxFQUFFWixNQUFNYSxPQUFPLEVBQUUsR0FBRzFDLCtEQUFRQSxDQUFDO1FBQ2pDZ0MsVUFBVTtZQUFDO1NBQWlCO1FBQzVCQyxPQUFPOzBDQUFFO2dCQUNQLE1BQU1JLFdBQVcsTUFBTUMsTUFBTTtnQkFDN0IsSUFBSUQsU0FBU0csRUFBRSxFQUFFO29CQUNmLE9BQU9ILFNBQVNJLElBQUk7Z0JBQ3RCO2dCQUNBLE9BQU87WUFDVDs7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QixNQUFNLEVBQUVaLE1BQU1jLFlBQVksRUFBRSxHQUFHM0MsK0RBQVFBLENBQUM7UUFDdENnQyxVQUFVO1lBQUM7U0FBZ0I7UUFDM0JDLE9BQU87MENBQUU7Z0JBQ1AsTUFBTUksV0FBVyxNQUFNQyxNQUFNO2dCQUM3QixJQUFJRCxTQUFTRyxFQUFFLEVBQUU7b0JBQ2YsT0FBT0gsU0FBU0ksSUFBSTtnQkFDdEI7Z0JBQ0EsT0FBTztZQUNUOztJQUNGO0lBRUEsNkNBQTZDO0lBQzdDMUMsZ0RBQVNBO3VDQUFDO1lBQ1IsSUFBSStCLHVCQUF1QjtnQkFDekIsc0RBQXNEO2dCQUN0RCxNQUFNYyxhQUFhLDBCQUF1QyxPQUFiakM7Z0JBQzdDLElBQUlrQyxnQkFBZ0I7Z0JBQ3BCLElBQUk7b0JBQ0YsTUFBTUMsU0FBU0MsYUFBYUMsT0FBTyxDQUFDSjtvQkFDcEMsSUFBSUUsUUFBUTt3QkFDVkQsZ0JBQWdCSSxLQUFLQyxLQUFLLENBQUNKO3dCQUMzQkssUUFBUUMsR0FBRyxDQUFDLG1DQUFtQ1A7b0JBQ2pEO2dCQUNGLEVBQUUsT0FBT1EsT0FBTztvQkFDZEYsUUFBUUUsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQy9DO2dCQUVBLG9FQUFvRTtnQkFDcEUsTUFBTUMsZ0JBQWdCVCxpQkFBaUJmO2dCQUV2QyxNQUFNeUIsY0FBYztvQkFDbEJyQyxTQUFTb0MsY0FBY3BDLE9BQU8sSUFBSTtvQkFDbENDLG1CQUFtQm1DLGNBQWNuQyxpQkFBaUIsSUFBSTtvQkFDdERDLGtCQUFrQmtDLGNBQWNsQyxnQkFBZ0IsSUFBSSxDQUFDLENBQUNzQjtvQkFDdERyQixpQkFBaUJpQyxjQUFjakMsZUFBZSxJQUFJLENBQUMsRUFBQ3NCLHlCQUFBQSxtQ0FBQUEsYUFBY2EsUUFBUTtvQkFDMUVsQyxjQUFjZ0MsY0FBY2hDLFlBQVksSUFBS29CLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU2pCLFNBQVMsTUFBSztnQkFDdEU7Z0JBRUFSLFlBQVlzQztnQkFFWiwyQkFBMkI7Z0JBQzNCLElBQUlELGNBQWMvQixtQkFBbUIsRUFBRTtvQkFDckNDLHVCQUF1QjhCLGNBQWMvQixtQkFBbUI7Z0JBQzFEO2dCQUVBLCtFQUErRTtnQkFDL0UsSUFBSWtDLGlCQUFpQkgsY0FBY3hDLFdBQVcsSUFBSTtnQkFFbEQsNkRBQTZEO2dCQUM3RCx5RUFBeUU7Z0JBQ3pFLElBQUkyQyxtQkFBbUIsS0FBS0YsWUFBWXJDLE9BQU8sRUFBRTtvQkFDL0N1QyxpQkFBaUIsR0FBRywyQ0FBMkM7Z0JBQ2pFLE9BQU8sSUFBSUEsbUJBQW1CLEtBQUtGLFlBQVlwQyxpQkFBaUIsRUFBRTtvQkFDaEVzQyxpQkFBaUIsR0FBRywyQ0FBMkM7Z0JBQ2pFLE9BQU8sSUFBSUEsbUJBQW1CLEtBQUtGLFlBQVluQyxnQkFBZ0IsRUFBRTtvQkFDL0RxQyxpQkFBaUIsR0FBRyx5Q0FBeUM7Z0JBQy9ELE9BQU8sSUFBSUEsbUJBQW1CLEtBQUtGLFlBQVlsQyxlQUFlLEVBQUU7b0JBQzlEb0MsaUJBQWlCLEdBQUcscUNBQXFDO2dCQUMzRCxPQUFPLElBQUlBLG1CQUFtQixLQUFLRixZQUFZakMsWUFBWSxFQUFFO29CQUMzRG1DLGlCQUFpQixHQUFHLGdCQUFnQjtnQkFDdEM7Z0JBRUExQyxlQUFlMEM7Z0JBRWZOLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0M7b0JBQzlDcEMsVUFBVXVDO29CQUNWekMsYUFBYTJDO29CQUNiQyxlQUFlSjtvQkFDZkssUUFBUWQsZ0JBQWdCLGlCQUFpQjtnQkFDM0M7WUFDRjtRQUNGO3NDQUFHO1FBQUNmO1FBQXVCWTtRQUFTQztRQUFjaEM7S0FBYTtJQUUvRCx3REFBd0Q7SUFDeEQsTUFBTWlELHdCQUF3QjtRQUM1QixJQUFJLENBQUNsQixXQUFXLENBQUMvQixjQUFjO1lBQzdCd0MsUUFBUUUsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLElBQUk7WUFDRkYsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTWYsV0FBVyxNQUFNQyxNQUFNLHNDQUFzQztnQkFDakV1QixRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNZCxLQUFLZSxTQUFTLENBQUM7b0JBQ25CQyxXQUFXdkIsUUFBUXdCLEtBQUs7b0JBQ3hCdkQsY0FBY0E7Z0JBQ2hCO1lBQ0Y7WUFFQSxJQUFJMEIsU0FBU0csRUFBRSxFQUFFO2dCQUNmLE1BQU0yQixTQUFTLE1BQU05QixTQUFTSSxJQUFJO2dCQUNsQ1UsUUFBUUMsR0FBRyxDQUFDLHNEQUFzRGU7Z0JBQ2xFLDBDQUEwQztnQkFDMUNDLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTtZQUN4QixPQUFPO2dCQUNMbkIsUUFBUUUsS0FBSyxDQUFDLGlEQUFpRGhCLFNBQVNrQyxNQUFNO1lBQ2hGO1FBQ0YsRUFBRSxPQUFPbEIsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsZ0RBQWdEQTtRQUNoRTtJQUNGO0lBRUEsTUFBTW1CLFFBQTZCO1FBQ2pDO1lBQ0VDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JKLFFBQVF2RCxTQUFTRSxPQUFPLEdBQUcsY0FBY0osZ0JBQWdCLElBQUksWUFBWTtRQUMzRTtRQUNBO1lBQ0UyRCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYSxjQUFtQyxPQUFyQi9ELGFBQWEsU0FBUTtZQUNoRDJELFFBQVF2RCxTQUFTRyxpQkFBaUIsR0FBRyxjQUFjTCxnQkFBZ0IsSUFBSSxZQUFZO1FBQ3JGO1FBQ0E7WUFDRTJELElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JKLFFBQVF2RCxTQUFTSSxnQkFBZ0IsR0FBRyxjQUFjTixnQkFBZ0IsSUFBSSxZQUFZO1FBQ3BGO1FBQ0E7WUFDRTJELElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JKLFFBQVF2RCxTQUFTSyxlQUFlLEdBQUcsY0FBY1AsZ0JBQWdCLElBQUksWUFBWTtRQUNuRjtRQUNBO1lBQ0UyRCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYTtZQUNiSixRQUFRdkQsU0FBU00sWUFBWSxHQUFHLGNBQ3hCRyxjQUFjLFdBQVcsVUFDekJYLGdCQUFnQixJQUFJLFlBQVk7UUFDMUM7S0FDRDtJQUVELE1BQU04RCxjQUFjLENBQUNMO1FBQ25CLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQ3RFLHVKQUFlQTtvQkFBQzRFLFdBQVU7Ozs7OztZQUNwQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDM0UsdUpBQVNBO29CQUFDMkUsV0FBVTs7Ozs7O1lBQzlCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMxRSx3SkFBdUJBO29CQUFDMEUsV0FBVTs7Ozs7O1lBQzVDO2dCQUNFLHFCQUFPLDhEQUFDQztvQkFBSUQsV0FBVTs7Ozs7O1FBQzFCO0lBQ0Y7SUFFQSxNQUFNRSxlQUFlLENBQUNSO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFTLE9BQU87WUFDckI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsMENBQTBDO0lBQzFDLE1BQU1TLGVBQWUsT0FBT0MsaUJBQXNCQztRQUNoRCxJQUFJO1lBQ0YscUVBQXFFO1lBQ3JFLGtGQUFrRjtZQUNsRixNQUFNQyxtQkFBbUJGLGdCQUFnQkUsZ0JBQWdCLEtBQUssUUFDckNGLGdCQUFnQi9ELE9BQU8sSUFBSStELGdCQUFnQi9ELE9BQU8sS0FBSyxNQUN2REYsU0FBU21FLGdCQUFnQixLQUFLLFFBQzlCbkUsU0FBU0UsT0FBTyxJQUFJRixTQUFTRSxPQUFPLEtBQUs7WUFFbEUsTUFBTUMsb0JBQW9COEQsZ0JBQWdCOUQsaUJBQWlCLEtBQUssUUFBUUgsU0FBU0csaUJBQWlCLEtBQUs7WUFDdkcsTUFBTUMsbUJBQW1CNkQsZ0JBQWdCN0QsZ0JBQWdCLEtBQUssUUFBUUosU0FBU0ksZ0JBQWdCLEtBQUssUUFBUSxDQUFDLENBQUNzQjtZQUM5RyxNQUFNckIsa0JBQWtCNEQsZ0JBQWdCNUQsZUFBZSxLQUFLLFFBQVFMLFNBQVNLLGVBQWUsS0FBSyxRQUFRLENBQUMsRUFBQ3FCLG9CQUFBQSw4QkFBQUEsUUFBUzBDLGFBQWE7WUFDakksTUFBTTlELGVBQWUyRCxnQkFBZ0IzRCxZQUFZLEtBQUssUUFBUU4sU0FBU00sWUFBWSxLQUFLLFFBQVFvQixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNqQixTQUFTLE1BQUs7WUFFdkgsa0VBQWtFO1lBQ2xFLE1BQU00RCxzQkFBc0I7Z0JBQzFCRjtnQkFDQWhFO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2FBQ0Q7WUFDRCxNQUFNZ0UsdUJBQXVCRCxvQkFBb0JFLE1BQU0sQ0FBQ0MsU0FBU0MsTUFBTTtZQUV2RSxNQUFNQyxlQUFlO2dCQUNuQixHQUFHVCxlQUFlO2dCQUNsQixpREFBaUQ7Z0JBQ2pERTtnQkFDQWhFO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBLHNDQUFzQztnQkFDdENKLFNBQVMrRCxnQkFBZ0IvRCxPQUFPLElBQUlGLFNBQVNFLE9BQU8sSUFBSTtnQkFDeERQO2dCQUNBRyxhQUFhb0U7Z0JBQ2JTLGdCQUFnQkw7WUFDbEI7WUFFQW5DLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNzQztZQUMvQ3ZDLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkI7Z0JBQ3ZDK0I7Z0JBQ0FoRTtnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQWdFO1lBQ0Y7WUFFQSwrQ0FBK0M7WUFDL0MsTUFBTWpELFdBQVcsTUFBTUMsTUFBTSxzQ0FBc0M7Z0JBQ2pFdUIsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTWQsS0FBS2UsU0FBUyxDQUFDMEI7WUFDdkI7WUFFQSxJQUFJLENBQUNyRCxTQUFTRyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSW9ELE1BQU07WUFDbEI7WUFFQSxNQUFNekIsU0FBUyxNQUFNOUIsU0FBU0ksSUFBSTtZQUNsQ1UsUUFBUUMsR0FBRyxDQUFDLDhDQUE4Q2U7UUFDNUQsRUFBRSxPQUFPZCxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLDRDQUE0QztRQUM5QztJQUNGO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU13Qyx3QkFBd0IsT0FBTzNFO1FBQ25DLE1BQU0rRCxrQkFBa0I7WUFBRSxHQUFHakUsUUFBUTtZQUFFRTtZQUFTaUUsa0JBQWtCO1FBQUs7UUFDdkVsRSxZQUFZZ0U7UUFDWmxFLGVBQWU7UUFFZixnQkFBZ0I7UUFDaEIsTUFBTWlFLGFBQWFDLGlCQUFpQjtJQUN0QztJQUVBLE1BQU1hLDBCQUEwQjtRQUM5QixpQ0FBaUM7UUFDakMsSUFBSTtZQUNGLE1BQU16RCxXQUFXLE1BQU1DLE1BQU0sK0JBQStCO2dCQUMxRHVCLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1kLEtBQUtlLFNBQVMsQ0FBQztvQkFDbkJyRDtvQkFDQUU7b0JBQ0FrRixVQUFVO2dCQUNaO1lBQ0Y7WUFFQSxJQUFJLENBQUMxRCxTQUFTRyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSW9ELE1BQU07WUFDbEI7WUFFQXpDLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT0MsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsbUNBQW1DQTtRQUNuRDtRQUVBLGdDQUFnQztRQUNoQyxNQUFNNEIsa0JBQWtCO1lBQUUsR0FBR2pFLFFBQVE7WUFBRUcsbUJBQW1CO1FBQUs7UUFDL0RGLFlBQVlnRTtRQUNabEUsZUFBZTtRQUVmLGdCQUFnQjtRQUNoQixNQUFNaUUsYUFBYUMsaUJBQWlCO0lBQ3RDO0lBRUEsTUFBTWUsd0JBQXdCO1FBQzVCLE1BQU1mLGtCQUFrQjtZQUFFLEdBQUdqRSxRQUFRO1lBQUVJLGtCQUFrQjtRQUFLO1FBQzlESCxZQUFZZ0U7UUFDWmxFLGVBQWU7UUFFZixnQkFBZ0I7UUFDaEIsTUFBTWlFLGFBQWFDLGlCQUFpQjtJQUN0QztJQUVBLE1BQU1nQix1QkFBdUI7UUFDM0IsTUFBTWhCLGtCQUFrQjtZQUFFLEdBQUdqRSxRQUFRO1lBQUVLLGlCQUFpQjtRQUFLO1FBQzdESixZQUFZZ0U7UUFDWmxFLGVBQWU7UUFFZixnQkFBZ0I7UUFDaEIsTUFBTWlFLGFBQWFDLGlCQUFpQjtJQUN0QztJQUVBLE1BQU1pQix3QkFBd0IsT0FBTzNCLFFBQWdCbEI7UUFDbkQzQixhQUFhNkM7UUFDYixJQUFJbEIsT0FBTztZQUNUekIsWUFBWXlCO1FBQ2QsT0FBTztZQUNMekIsWUFBWTtRQUNkO1FBRUEsSUFBSTJDLFdBQVcsYUFBYTtZQUMxQixNQUFNVSxrQkFBa0I7Z0JBQUUsR0FBR2pFLFFBQVE7Z0JBQUVNLGNBQWM7WUFBSztZQUMxREwsWUFBWWdFO1lBQ1psRSxlQUFlO1lBRWYsZ0JBQWdCO1lBQ2hCLE1BQU1pRSxhQUFhQyxpQkFBaUI7UUFDdEM7SUFDRjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNa0Isb0JBQW9CLENBQUNDO1FBQ3pCLDJFQUEyRTtRQUMzRSxJQUFJQSxjQUFjLEdBQUcsT0FBTyxNQUFNLHFDQUFxQztRQUN2RSxJQUFJQSxjQUFjLEdBQUcsT0FBT3BGLFNBQVNFLE9BQU8sS0FBSyxJQUFJLGdDQUFnQztRQUNyRixJQUFJa0YsY0FBYyxHQUFHLE9BQU9wRixTQUFTRyxpQkFBaUIsRUFBRSxnQ0FBZ0M7UUFDeEYsSUFBSWlGLGNBQWMsR0FBRyxPQUFPcEYsU0FBU0ksZ0JBQWdCLEVBQUUsOEJBQThCO1FBQ3JGLElBQUlnRixjQUFjLEdBQUcsT0FBT3BGLFNBQVNLLGVBQWUsRUFBRSwwQkFBMEI7UUFDaEYsSUFBSStFLGNBQWMsR0FBRyxPQUFPcEYsU0FBU00sWUFBWSxFQUFFLHlCQUF5QjtRQUM1RSxPQUFPO0lBQ1Q7SUFFQSxNQUFNK0Usa0JBQWtCLENBQUNEO1FBQ3ZCLElBQUlELGtCQUFrQkMsWUFBWTtZQUNoQ3JGLGVBQWVxRjtZQUNmLGtFQUFrRTtZQUNsRSxNQUFNRSxjQUFjO2dCQUNsQixHQUFHdEYsUUFBUTtnQkFDWCw2REFBNkQ7Z0JBQzdEbUUsa0JBQWtCbkUsU0FBU21FLGdCQUFnQixJQUFLbkUsU0FBU0UsT0FBTyxJQUFJRixTQUFTRSxPQUFPLEtBQUssTUFBT3lFLGtCQUFrQjtnQkFDbEh4RSxtQkFBbUJILFNBQVNHLGlCQUFpQixJQUFJd0Usa0JBQWtCO2dCQUNuRXZFLGtCQUFrQkosU0FBU0ksZ0JBQWdCLElBQUksQ0FBQyxDQUFDc0IsV0FBV2lELGtCQUFrQjtnQkFDOUV0RSxpQkFBaUJMLFNBQVNLLGVBQWUsSUFBSSxDQUFDLEVBQUNxQixvQkFBQUEsOEJBQUFBLFFBQVMwQyxhQUFhLEtBQUlPLGtCQUFrQjtnQkFDM0ZyRSxjQUFjTixTQUFTTSxZQUFZLElBQUlvQixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNqQixTQUFTLE1BQUssY0FBY2tFLGtCQUFrQjtZQUNoRztZQUNBeEMsUUFBUUMsR0FBRyxDQUFDLGtEQUFrRGtEO1lBQzlEdEIsYUFBYXNCLGFBQWFGO1FBQzVCO0lBQ0Y7SUFFQSxJQUFJckUsV0FBVztRQUNiLHFCQUNFLDhEQUFDK0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEsTUFBTWMsaUJBQWlCbkIsTUFBTWUsTUFBTSxDQUFDZ0IsQ0FBQUEsT0FBUUEsS0FBS2hDLE1BQU0sS0FBSyxhQUFha0IsTUFBTTtJQUMvRSxNQUFNZSxhQUFhaEMsTUFBTWlCLE1BQU07SUFDL0IsTUFBTWdCLHFCQUFxQixpQkFBa0JELGFBQWM7SUFFM0QscUJBQ0UsOERBQUMxQjtRQUFJRCxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDNkI7d0JBQUc3QixXQUFVO2tDQUNYakUsWUFBWSxHQUFhLE9BQVZBLFdBQVUsb0JBQWtCOzs7Ozs7a0NBRTlDLDhEQUFDK0Y7d0JBQUU5QixXQUFVOzs0QkFBNkI7NEJBQ0tqRSxhQUFhOzRCQUFROzs7Ozs7O2tDQUlwRSw4REFBQ2tFO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFDQ0QsV0FBVTs0QkFDVitCLE9BQU87Z0NBQUVDLE9BQU8sR0FBc0IsT0FBbkJKLG9CQUFtQjs0QkFBRzs7Ozs7Ozs7Ozs7a0NBSTdDLDhEQUFDRTt3QkFBRTlCLFdBQVU7OzRCQUNWYzs0QkFBZTs0QkFBS2E7NEJBQVc7Ozs7Ozs7Ozs7Ozs7MEJBS3BDLDhEQUFDMUI7Z0JBQUlELFdBQVU7MEJBQ1pMLE1BQU1zQyxHQUFHLENBQUMsQ0FBQ1AsTUFBTVEsc0JBQ2hCLDhEQUFDakM7d0JBRUNrQyxTQUFTLElBQU1YLGdCQUFnQlU7d0JBQy9CbEMsV0FBVyxpRUFDVHNCLE9BRDBFcEIsYUFBYXdCLEtBQUtoQyxNQUFNLEdBQUUsS0FJckcsT0FIQzRCLGtCQUFrQlksU0FDZCxtREFDQTs7MENBR04sOERBQUNqQztnQ0FBSUQsV0FBVTswQ0FDWkQsWUFBWTJCLEtBQUtoQyxNQUFNOzs7Ozs7MENBRTFCLDhEQUFDMEM7Z0NBQUdwQyxXQUFVOzBDQUE4QjBCLEtBQUs3QixLQUFLOzs7Ozs7MENBQ3RELDhEQUFDaUM7Z0NBQUU5QixXQUFVOzBDQUFXMEIsS0FBSzVCLFdBQVc7Ozs7Ozs0QkFDdkN3QixrQkFBa0JZLFVBQVVBLFVBQVVqRyw2QkFDckMsOERBQUM2RjtnQ0FBRTlCLFdBQVU7MENBQTJCOzs7Ozs7O3VCQWRyQzBCLEtBQUs5QixFQUFFOzs7Ozs7Ozs7OzBCQXFCbEIsOERBQUNLO2dCQUFJRCxXQUFVOztvQkFDWi9ELGdCQUFnQixtQkFDZiw4REFBQ1QsK0RBQWdCQTt3QkFDZjZHLFlBQVlyQjt3QkFDWnNCLGlCQUFpQm5HLFNBQVNFLE9BQU87d0JBQ2pDa0csYUFBYXBHLFNBQVNFLE9BQU8sS0FBSzs7Ozs7O29CQUlyQ0osZ0JBQWdCLG1CQUNmLDhEQUFDUiwyREFBY0E7d0JBQ2I0RyxZQUFZcEI7d0JBQ1psRixXQUFXQTt3QkFDWEMsYUFBYUE7d0JBQ2J1RyxhQUFhcEcsU0FBU0csaUJBQWlCOzs7Ozs7b0JBSTFDTCxnQkFBZ0IsbUJBQ2YsOERBQUNnRTt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ3dDO3dDQUFHeEMsV0FBVTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUM4Qjt3Q0FBRTlCLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBSS9CLDhEQUFDdEUsaUVBQWlCQTtnQ0FDaEIyRyxZQUFZbEI7Z0NBQ1pzQixpQkFBaUI1RTs7Ozs7Ozs7Ozs7O29CQUt0QjVCLGdCQUFnQixtQkFDZiw4REFBQ2dFO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDd0M7d0NBQUd4QyxXQUFVO2tEQUF3Qzs7Ozs7O2tEQUN0RCw4REFBQzhCO3dDQUFFOUIsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FJL0IsOERBQUNyRSwrREFBZ0JBO2dDQUFDK0csbUJBQW1CdEI7Ozs7Ozs7Ozs7OztvQkFJeENuRixnQkFBZ0IsbUJBQ2YsOERBQUNnRTt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ3dDO3dDQUFHeEMsV0FBVTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUM4Qjt3Q0FBRTlCLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBSS9CLDhEQUFDcEUsdURBQVlBO2dDQUNYK0csZ0JBQWdCdEI7Z0NBQ2hCb0IsaUJBQWlCNUU7Ozs7Ozs7Ozs7OztvQkFLdEI1QixnQkFBZ0IsbUJBQ2YsOERBQUNnRTt3QkFBSUQsV0FBVTs7NEJBQ1h0RCx3QkFBd0IsY0FBY0Esd0JBQXdCLGlDQUM5RDs7a0RBQ0UsOERBQUN0Qix1SkFBZUE7d0NBQUM0RSxXQUFVOzs7Ozs7a0RBQzNCLDhEQUFDd0M7d0NBQUd4QyxXQUFVO2tEQUF3Qzs7Ozs7O2tEQUd0RCw4REFBQzhCO3dDQUFFOUIsV0FBVTs7NENBQXFCOzRDQUNTakUsYUFBYTs0Q0FBYTs7Ozs7OztrREFHckUsOERBQUNrRTt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDNUUsdUpBQWVBO29EQUFDNEUsV0FBVTs7Ozs7OzhEQUMzQiw4REFBQ0M7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDOEI7NERBQUU5QixXQUFVO3NFQUFxQzs7Ozs7O3NFQUNsRCw4REFBQzhCOzREQUFFOUIsV0FBVTtzRUFBeUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQ0FPNUN0RCx3QkFBd0IsMkJBQzFCOztrREFDRSw4REFBQ25CLHdKQUFXQTt3Q0FBQ3lFLFdBQVU7Ozs7OztrREFDdkIsOERBQUN3Qzt3Q0FBR3hDLFdBQVU7a0RBQXdDOzs7Ozs7a0RBR3RELDhEQUFDOEI7d0NBQUU5QixXQUFVOzs0Q0FBcUI7NENBQ09qRSxhQUFhOzRDQUFhOzs7Ozs7O2tEQUduRSw4REFBQ2tFO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUN6RSx3SkFBV0E7b0RBQUN5RSxXQUFVOzs7Ozs7OERBQ3ZCLDhEQUFDQztvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUM4Qjs0REFBRTlCLFdBQVU7c0VBQW1DOzs7Ozs7c0VBQ2hELDhEQUFDOEI7NERBQUU5QixXQUFVO3NFQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQVE1Qzs7a0RBQ0UsOERBQUM1RSx1SkFBZUE7d0NBQUM0RSxXQUFVOzs7Ozs7a0RBQzNCLDhEQUFDd0M7d0NBQUd4QyxXQUFVO2tEQUF3Qzs7Ozs7O2tEQUd0RCw4REFBQzhCO3dDQUFFOUIsV0FBVTs7NENBQXFCOzRDQUM2QmpFLGFBQWE7NENBQWE7Ozs7Ozs7a0RBR3pGLDhEQUFDa0U7d0NBQUlELFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQzNFLHVKQUFTQTtvREFBQzJFLFdBQVU7Ozs7Ozs4REFDckIsOERBQUNDO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQzhCOzREQUFFOUIsV0FBVTtzRUFBc0M7Ozs7OztzRUFDbkQsOERBQUM4Qjs0REFBRTlCLFdBQVU7c0VBQTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFqRCw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDNEM7d0NBQ0NULFNBQVMsSUFBTTVDLE9BQU9DLFFBQVEsQ0FBQ3FELElBQUksR0FBRzt3Q0FDdEM3QyxXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUM0Qzt3Q0FDQ1QsU0FBU3BEO3dDQUNUaUIsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2Y7R0E1a0JnQm5FOztRQWNxQ1YsMkRBQVFBO1FBZWpDQSwyREFBUUE7UUFZSEEsMkRBQVFBOzs7S0F6Q3pCVSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xccXVhbGlmaWNhdGlvblxcUXVhbGlmaWNhdGlvbkZsb3cudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVF1ZXJ5IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IENoZWNrQ2lyY2xlSWNvbiwgQ2xvY2tJY29uLCBFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiwgWENpcmNsZUljb24gfSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgQ291bnRyeVNlbGVjdGlvbiB9IGZyb20gJy4vQ291bnRyeVNlbGVjdGlvbic7XG5pbXBvcnQgeyBUb2tlbkFncmVlbWVudCB9IGZyb20gJy4vVG9rZW5BZ3JlZW1lbnQnO1xuaW1wb3J0IHsgUXVhbGlmaWNhdGlvbkZvcm0gfSBmcm9tICcuLi9RdWFsaWZpY2F0aW9uRm9ybSc7XG5pbXBvcnQgeyBXYWxsZXRDb25uZWN0aW9uIH0gZnJvbSAnLi4vV2FsbGV0Q29ubmVjdGlvbic7XG5pbXBvcnQgeyBBdXRvbWF0aWNLWUMgfSBmcm9tICcuLi9BdXRvbWF0aWNLWUMnO1xuXG5pbnRlcmZhY2UgUXVhbGlmaWNhdGlvbkZsb3dQcm9wcyB7XG4gIHRva2VuQWRkcmVzcz86IHN0cmluZztcbiAgdG9rZW5OYW1lPzogc3RyaW5nO1xuICB0b2tlblN5bWJvbD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFF1YWxpZmljYXRpb25TdGVwIHtcbiAgaWQ6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgc3RhdHVzOiAnY29tcGxldGVkJyB8ICdjdXJyZW50JyB8ICdwZW5kaW5nJyB8ICdlcnJvcic7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBRdWFsaWZpY2F0aW9uRmxvdyh7IHRva2VuQWRkcmVzcywgdG9rZW5OYW1lLCB0b2tlblN5bWJvbCB9OiBRdWFsaWZpY2F0aW9uRmxvd1Byb3BzKSB7XG4gIGNvbnN0IFtjdXJyZW50U3RlcCwgc2V0Q3VycmVudFN0ZXBdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtzdGVwRGF0YSwgc2V0U3RlcERhdGFdID0gdXNlU3RhdGUoe1xuICAgIGNvdW50cnk6ICcnLFxuICAgIGFncmVlbWVudEFjY2VwdGVkOiBmYWxzZSxcbiAgICBwcm9maWxlQ29tcGxldGVkOiBmYWxzZSxcbiAgICB3YWxsZXRDb25uZWN0ZWQ6IGZhbHNlLFxuICAgIGt5Y0NvbXBsZXRlZDogZmFsc2UsXG4gIH0pO1xuICBjb25zdCBbcXVhbGlmaWNhdGlvblN0YXR1cywgc2V0UXVhbGlmaWNhdGlvblN0YXR1c10gPSB1c2VTdGF0ZTxzdHJpbmc+KCdQRU5ESU5HJyk7XG4gIGNvbnN0IFtreWNTdGF0dXMsIHNldEt5Y1N0YXR1c10gPSB1c2VTdGF0ZTxzdHJpbmc+KCdpZGxlJyk7XG4gIGNvbnN0IFtreWNFcnJvciwgc2V0S3ljRXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gRmV0Y2ggZXhpc3RpbmcgcXVhbGlmaWNhdGlvbiBwcm9ncmVzc1xuICBjb25zdCB7IGRhdGE6IHF1YWxpZmljYXRpb25Qcm9ncmVzcywgaXNMb2FkaW5nIH0gPSB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsncXVhbGlmaWNhdGlvbi1wcm9ncmVzcycsIHRva2VuQWRkcmVzc10sXG4gICAgcXVlcnlGbjogYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgICAgaWYgKHRva2VuQWRkcmVzcykgcGFyYW1zLmFwcGVuZCgndG9rZW5BZGRyZXNzJywgdG9rZW5BZGRyZXNzKTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jbGllbnQvcXVhbGlmaWNhdGlvbi1wcm9ncmVzcz8ke3BhcmFtcy50b1N0cmluZygpfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5qc29uKCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9LFxuICB9KTtcblxuICAvLyBGZXRjaCBjbGllbnQgcHJvZmlsZVxuICBjb25zdCB7IGRhdGE6IHByb2ZpbGUgfSA9IHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydjbGllbnQtcHJvZmlsZSddLFxuICAgIHF1ZXJ5Rm46IGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2xpZW50L3Byb2ZpbGUnKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfSxcbiAgfSk7XG5cbiAgLy8gRmV0Y2ggd2FsbGV0IHN0YXR1c1xuICBjb25zdCB7IGRhdGE6IHdhbGxldFN0YXR1cyB9ID0gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ3dhbGxldC1zdGF0dXMnXSxcbiAgICBxdWVyeUZuOiBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudC93YWxsZXQnKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfSxcbiAgfSk7XG5cbiAgLy8gVXBkYXRlIHN0ZXAgZGF0YSBiYXNlZCBvbiBmZXRjaGVkIHByb2dyZXNzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHF1YWxpZmljYXRpb25Qcm9ncmVzcykge1xuICAgICAgLy8gVHJ5IHRvIGdldCBtb3JlIHJlY2VudCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlIGZpcnN0XG4gICAgICBjb25zdCBzdG9yYWdlS2V5ID0gYHF1YWxpZmljYXRpb25fcHJvZ3Jlc3NfJHt0b2tlbkFkZHJlc3N9YDtcbiAgICAgIGxldCBsb2NhbFByb2dyZXNzID0gbnVsbDtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpO1xuICAgICAgICBpZiAoc3RvcmVkKSB7XG4gICAgICAgICAgbG9jYWxQcm9ncmVzcyA9IEpTT04ucGFyc2Uoc3RvcmVkKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TsSBGb3VuZCBsb2NhbFN0b3JhZ2UgcHJvZ3Jlc3M6JywgbG9jYWxQcm9ncmVzcyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlYWRpbmcgbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcbiAgICAgIH1cblxuICAgICAgLy8gVXNlIGxvY2FsU3RvcmFnZSBkYXRhIGlmIGl0J3MgbW9yZSByZWNlbnQsIG90aGVyd2lzZSB1c2UgQVBJIGRhdGFcbiAgICAgIGNvbnN0IHByb2dyZXNzVG9Vc2UgPSBsb2NhbFByb2dyZXNzIHx8IHF1YWxpZmljYXRpb25Qcm9ncmVzcztcblxuICAgICAgY29uc3QgbmV3U3RlcERhdGEgPSB7XG4gICAgICAgIGNvdW50cnk6IHByb2dyZXNzVG9Vc2UuY291bnRyeSB8fCAnJyxcbiAgICAgICAgYWdyZWVtZW50QWNjZXB0ZWQ6IHByb2dyZXNzVG9Vc2UuYWdyZWVtZW50QWNjZXB0ZWQgfHwgZmFsc2UsXG4gICAgICAgIHByb2ZpbGVDb21wbGV0ZWQ6IHByb2dyZXNzVG9Vc2UucHJvZmlsZUNvbXBsZXRlZCB8fCAhIXByb2ZpbGUsXG4gICAgICAgIHdhbGxldENvbm5lY3RlZDogcHJvZ3Jlc3NUb1VzZS53YWxsZXRDb25uZWN0ZWQgfHwgISF3YWxsZXRTdGF0dXM/LnZlcmlmaWVkLFxuICAgICAgICBreWNDb21wbGV0ZWQ6IHByb2dyZXNzVG9Vc2Uua3ljQ29tcGxldGVkIHx8IChwcm9maWxlPy5reWNTdGF0dXMgPT09ICdBUFBST1ZFRCcpLFxuICAgICAgfTtcblxuICAgICAgc2V0U3RlcERhdGEobmV3U3RlcERhdGEpO1xuXG4gICAgICAvLyBTZXQgcXVhbGlmaWNhdGlvbiBzdGF0dXNcbiAgICAgIGlmIChwcm9ncmVzc1RvVXNlLnF1YWxpZmljYXRpb25TdGF0dXMpIHtcbiAgICAgICAgc2V0UXVhbGlmaWNhdGlvblN0YXR1cyhwcm9ncmVzc1RvVXNlLnF1YWxpZmljYXRpb25TdGF0dXMpO1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgY3VycmVudCBzdGVwIGJhc2VkIG9uIHNhdmVkIHByb2dyZXNzIG9yIGNhbGN1bGF0ZSBmcm9tIGNvbXBsZXRpb24gc3RhdHVzXG4gICAgICBsZXQgY2FsY3VsYXRlZFN0ZXAgPSBwcm9ncmVzc1RvVXNlLmN1cnJlbnRTdGVwIHx8IDA7XG5cbiAgICAgIC8vIEFsbG93IHVzZXJzIHRvIHByb2dyZXNzIHRocm91Z2ggYWxsIHN0ZXBzIHdpdGhvdXQgYmxvY2tpbmdcbiAgICAgIC8vIE9ubHkgYXV0by1hZHZhbmNlIHRvIG5leHQgaW5jb21wbGV0ZSBzdGVwIGlmIGN1cnJlbnQgc3RlcCBpcyBjb21wbGV0ZWRcbiAgICAgIGlmIChjYWxjdWxhdGVkU3RlcCA9PT0gMCAmJiBuZXdTdGVwRGF0YS5jb3VudHJ5KSB7XG4gICAgICAgIGNhbGN1bGF0ZWRTdGVwID0gMTsgLy8gTW92ZSB0byBhZ3JlZW1lbnQgaWYgY291bnRyeSBpcyBzZWxlY3RlZFxuICAgICAgfSBlbHNlIGlmIChjYWxjdWxhdGVkU3RlcCA9PT0gMSAmJiBuZXdTdGVwRGF0YS5hZ3JlZW1lbnRBY2NlcHRlZCkge1xuICAgICAgICBjYWxjdWxhdGVkU3RlcCA9IDI7IC8vIE1vdmUgdG8gcHJvZmlsZSBpZiBhZ3JlZW1lbnQgaXMgYWNjZXB0ZWRcbiAgICAgIH0gZWxzZSBpZiAoY2FsY3VsYXRlZFN0ZXAgPT09IDIgJiYgbmV3U3RlcERhdGEucHJvZmlsZUNvbXBsZXRlZCkge1xuICAgICAgICBjYWxjdWxhdGVkU3RlcCA9IDM7IC8vIE1vdmUgdG8gd2FsbGV0IGlmIHByb2ZpbGUgaXMgY29tcGxldGVkXG4gICAgICB9IGVsc2UgaWYgKGNhbGN1bGF0ZWRTdGVwID09PSAzICYmIG5ld1N0ZXBEYXRhLndhbGxldENvbm5lY3RlZCkge1xuICAgICAgICBjYWxjdWxhdGVkU3RlcCA9IDQ7IC8vIE1vdmUgdG8gS1lDIGlmIHdhbGxldCBpcyBjb25uZWN0ZWRcbiAgICAgIH0gZWxzZSBpZiAoY2FsY3VsYXRlZFN0ZXAgPT09IDQgJiYgbmV3U3RlcERhdGEua3ljQ29tcGxldGVkKSB7XG4gICAgICAgIGNhbGN1bGF0ZWRTdGVwID0gNTsgLy8gQWxsIGNvbXBsZXRlZFxuICAgICAgfVxuXG4gICAgICBzZXRDdXJyZW50U3RlcChjYWxjdWxhdGVkU3RlcCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFJlc3RvcmVkIHF1YWxpZmljYXRpb24gc3RhdGU6Jywge1xuICAgICAgICBzdGVwRGF0YTogbmV3U3RlcERhdGEsXG4gICAgICAgIGN1cnJlbnRTdGVwOiBjYWxjdWxhdGVkU3RlcCxcbiAgICAgICAgc2F2ZWRQcm9ncmVzczogcHJvZ3Jlc3NUb1VzZSxcbiAgICAgICAgc291cmNlOiBsb2NhbFByb2dyZXNzID8gJ2xvY2FsU3RvcmFnZScgOiAnQVBJJ1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbcXVhbGlmaWNhdGlvblByb2dyZXNzLCBwcm9maWxlLCB3YWxsZXRTdGF0dXMsIHRva2VuQWRkcmVzc10pO1xuXG4gIC8vIEZ1bmN0aW9uIHRvIG1hbnVhbGx5IGZpeCBxdWFsaWZpY2F0aW9uIHByb2dyZXNzIGZsYWdzXG4gIGNvbnN0IGZpeFF1YWxpZmljYXRpb25GbGFncyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXByb2ZpbGUgfHwgIXRva2VuQWRkcmVzcykge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIE1pc3NpbmcgcHJvZmlsZSBvciB0b2tlbiBhZGRyZXNzJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SnIEF0dGVtcHRpbmcgdG8gZml4IHF1YWxpZmljYXRpb24gcHJvZ3Jlc3MgZmxhZ3MuLi4nKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jbGllbnQvcXVhbGlmaWNhdGlvbi1wcm9ncmVzcycsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICB1c2VyRW1haWw6IHByb2ZpbGUuZW1haWwsXG4gICAgICAgICAgdG9rZW5BZGRyZXNzOiB0b2tlbkFkZHJlc3MsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgUXVhbGlmaWNhdGlvbiBwcm9ncmVzcyBmbGFncyBmaXhlZCBzdWNjZXNzZnVsbHk6JywgcmVzdWx0KTtcbiAgICAgICAgLy8gUmVsb2FkIHRoZSBwYWdlIHRvIHNlZSB0aGUgdXBkYXRlZCBkYXRhXG4gICAgICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gZml4IHF1YWxpZmljYXRpb24gcHJvZ3Jlc3MgZmxhZ3M6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGZpeGluZyBxdWFsaWZpY2F0aW9uIHByb2dyZXNzIGZsYWdzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc3RlcHM6IFF1YWxpZmljYXRpb25TdGVwW10gPSBbXG4gICAge1xuICAgICAgaWQ6ICdjb3VudHJ5JyxcbiAgICAgIHRpdGxlOiAnQ291bnRyeSBTZWxlY3Rpb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdTZWxlY3QgeW91ciBjb3VudHJ5IG9mIHJlc2lkZW5jZSBmb3IgY29tcGxpYW5jZScsXG4gICAgICBzdGF0dXM6IHN0ZXBEYXRhLmNvdW50cnkgPyAnY29tcGxldGVkJyA6IGN1cnJlbnRTdGVwID09PSAwID8gJ2N1cnJlbnQnIDogJ3BlbmRpbmcnLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdhZ3JlZW1lbnQnLFxuICAgICAgdGl0bGU6ICdUb2tlbiBBZ3JlZW1lbnQnLFxuICAgICAgZGVzY3JpcHRpb246IGBBY2NlcHQgdGhlICR7dG9rZW5OYW1lIHx8ICd0b2tlbid9IHNwZWNpZmljIGludmVzdG1lbnQgYWdyZWVtZW50YCxcbiAgICAgIHN0YXR1czogc3RlcERhdGEuYWdyZWVtZW50QWNjZXB0ZWQgPyAnY29tcGxldGVkJyA6IGN1cnJlbnRTdGVwID09PSAxID8gJ2N1cnJlbnQnIDogJ3BlbmRpbmcnLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdwcm9maWxlJyxcbiAgICAgIHRpdGxlOiAnTWFpbiBJbmZvcm1hdGlvbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ0NvbXBsZXRlIHlvdXIgcGVyc29uYWwgYW5kIGZpbmFuY2lhbCBpbmZvcm1hdGlvbicsXG4gICAgICBzdGF0dXM6IHN0ZXBEYXRhLnByb2ZpbGVDb21wbGV0ZWQgPyAnY29tcGxldGVkJyA6IGN1cnJlbnRTdGVwID09PSAyID8gJ2N1cnJlbnQnIDogJ3BlbmRpbmcnLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICd3YWxsZXQnLFxuICAgICAgdGl0bGU6ICdXYWxsZXQgQ29ubmVjdGlvbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ0Nvbm5lY3QgYW5kIHZlcmlmeSB5b3VyIGNyeXB0b2N1cnJlbmN5IHdhbGxldCcsXG4gICAgICBzdGF0dXM6IHN0ZXBEYXRhLndhbGxldENvbm5lY3RlZCA/ICdjb21wbGV0ZWQnIDogY3VycmVudFN0ZXAgPT09IDMgPyAnY3VycmVudCcgOiAncGVuZGluZycsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2t5YycsXG4gICAgICB0aXRsZTogJ0tZQyBWZXJpZmljYXRpb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdDb21wbGV0ZSBpZGVudGl0eSB2ZXJpZmljYXRpb24gdXNpbmcgU3Vtc3ViJyxcbiAgICAgIHN0YXR1czogc3RlcERhdGEua3ljQ29tcGxldGVkID8gJ2NvbXBsZXRlZCcgOiBcbiAgICAgICAgICAgICAga3ljU3RhdHVzID09PSAnZmFpbGVkJyA/ICdlcnJvcicgOiBcbiAgICAgICAgICAgICAgY3VycmVudFN0ZXAgPT09IDQgPyAnY3VycmVudCcgOiAncGVuZGluZycsXG4gICAgfSxcbiAgXTtcblxuICBjb25zdCBnZXRTdGVwSWNvbiA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdjb21wbGV0ZWQnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JlZW4tNTAwXCIgLz47XG4gICAgICBjYXNlICdjdXJyZW50JzpcbiAgICAgICAgcmV0dXJuIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNTAwXCIgLz47XG4gICAgICBjYXNlICdlcnJvcic6XG4gICAgICAgIHJldHVybiA8RXhjbGFtYXRpb25UcmlhbmdsZUljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXJlZC01MDBcIiAvPjtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cImgtNiB3LTYgcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMFwiIC8+O1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTdGVwQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnY29tcGxldGVkJzogcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJztcbiAgICAgIGNhc2UgJ2N1cnJlbnQnOiByZXR1cm4gJ3RleHQtYmx1ZS02MDAgYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDAnO1xuICAgICAgY2FzZSAnZXJyb3InOiByZXR1cm4gJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICd0ZXh0LWdyYXktNjAwIGJnLWdyYXktNTAgYm9yZGVyLWdyYXktMjAwJztcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuY3Rpb24gdG8gc2F2ZSBxdWFsaWZpY2F0aW9uIHByb2dyZXNzXG4gIGNvbnN0IHNhdmVQcm9ncmVzcyA9IGFzeW5jICh1cGRhdGVkU3RlcERhdGE6IGFueSwgbmV3Q3VycmVudFN0ZXA6IG51bWJlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBFbnN1cmUgd2UgaGF2ZSB0aGUgY29ycmVjdCBjb21wbGV0aW9uIGZsYWdzIGJhc2VkIG9uIGN1cnJlbnQgc3RhdGVcbiAgICAgIC8vIEZvciBjb3VudHJ5OiBjaGVjayBpZiBpdCB3YXMgZXhwbGljaXRseSBjb21wbGV0ZWQgT1IgaWYgdGhlcmUncyBhIGNvdW50cnkgdmFsdWVcbiAgICAgIGNvbnN0IGNvdW50cnlDb21wbGV0ZWQgPSB1cGRhdGVkU3RlcERhdGEuY291bnRyeUNvbXBsZXRlZCA9PT0gdHJ1ZSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHVwZGF0ZWRTdGVwRGF0YS5jb3VudHJ5ICYmIHVwZGF0ZWRTdGVwRGF0YS5jb3VudHJ5ICE9PSAnJykgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChzdGVwRGF0YS5jb3VudHJ5Q29tcGxldGVkID09PSB0cnVlKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHN0ZXBEYXRhLmNvdW50cnkgJiYgc3RlcERhdGEuY291bnRyeSAhPT0gJycpO1xuXG4gICAgICBjb25zdCBhZ3JlZW1lbnRBY2NlcHRlZCA9IHVwZGF0ZWRTdGVwRGF0YS5hZ3JlZW1lbnRBY2NlcHRlZCA9PT0gdHJ1ZSB8fCBzdGVwRGF0YS5hZ3JlZW1lbnRBY2NlcHRlZCA9PT0gdHJ1ZTtcbiAgICAgIGNvbnN0IHByb2ZpbGVDb21wbGV0ZWQgPSB1cGRhdGVkU3RlcERhdGEucHJvZmlsZUNvbXBsZXRlZCA9PT0gdHJ1ZSB8fCBzdGVwRGF0YS5wcm9maWxlQ29tcGxldGVkID09PSB0cnVlIHx8ICEhcHJvZmlsZTtcbiAgICAgIGNvbnN0IHdhbGxldENvbm5lY3RlZCA9IHVwZGF0ZWRTdGVwRGF0YS53YWxsZXRDb25uZWN0ZWQgPT09IHRydWUgfHwgc3RlcERhdGEud2FsbGV0Q29ubmVjdGVkID09PSB0cnVlIHx8ICEhcHJvZmlsZT8ud2FsbGV0QWRkcmVzcztcbiAgICAgIGNvbnN0IGt5Y0NvbXBsZXRlZCA9IHVwZGF0ZWRTdGVwRGF0YS5reWNDb21wbGV0ZWQgPT09IHRydWUgfHwgc3RlcERhdGEua3ljQ29tcGxldGVkID09PSB0cnVlIHx8IHByb2ZpbGU/Lmt5Y1N0YXR1cyA9PT0gJ0FQUFJPVkVEJztcblxuICAgICAgLy8gQ2FsY3VsYXRlIGNvbXBsZXRlZCBzdGVwcyBiYXNlZCBvbiBhY3R1YWwgc3RlcCBjb21wbGV0aW9uIGZsYWdzXG4gICAgICBjb25zdCBzdGVwQ29tcGxldGlvbkZsYWdzID0gW1xuICAgICAgICBjb3VudHJ5Q29tcGxldGVkLFxuICAgICAgICBhZ3JlZW1lbnRBY2NlcHRlZCxcbiAgICAgICAgcHJvZmlsZUNvbXBsZXRlZCxcbiAgICAgICAgd2FsbGV0Q29ubmVjdGVkLFxuICAgICAgICBreWNDb21wbGV0ZWQsXG4gICAgICBdO1xuICAgICAgY29uc3QgYWN0dWFsQ29tcGxldGVkU3RlcHMgPSBzdGVwQ29tcGxldGlvbkZsYWdzLmZpbHRlcihCb29sZWFuKS5sZW5ndGg7XG5cbiAgICAgIGNvbnN0IHByb2dyZXNzRGF0YSA9IHtcbiAgICAgICAgLi4udXBkYXRlZFN0ZXBEYXRhLFxuICAgICAgICAvLyBFbnN1cmUgYWxsIGNvbXBsZXRpb24gZmxhZ3MgYXJlIGV4cGxpY2l0bHkgc2V0XG4gICAgICAgIGNvdW50cnlDb21wbGV0ZWQsXG4gICAgICAgIGFncmVlbWVudEFjY2VwdGVkLFxuICAgICAgICBwcm9maWxlQ29tcGxldGVkLFxuICAgICAgICB3YWxsZXRDb25uZWN0ZWQsXG4gICAgICAgIGt5Y0NvbXBsZXRlZCxcbiAgICAgICAgLy8gUHJlc2VydmUgY291bnRyeSB2YWx1ZSBpZiBpdCBleGlzdHNcbiAgICAgICAgY291bnRyeTogdXBkYXRlZFN0ZXBEYXRhLmNvdW50cnkgfHwgc3RlcERhdGEuY291bnRyeSB8fCAnJyxcbiAgICAgICAgdG9rZW5BZGRyZXNzLFxuICAgICAgICBjdXJyZW50U3RlcDogbmV3Q3VycmVudFN0ZXAsXG4gICAgICAgIGNvbXBsZXRlZFN0ZXBzOiBhY3R1YWxDb21wbGV0ZWRTdGVwcyxcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFNhdmluZyBwcm9ncmVzcyB0byBkYXRhYmFzZTonLCBwcm9ncmVzc0RhdGEpO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gU3RlcCBjb21wbGV0aW9uIGZsYWdzOicsIHtcbiAgICAgICAgY291bnRyeUNvbXBsZXRlZCxcbiAgICAgICAgYWdyZWVtZW50QWNjZXB0ZWQsXG4gICAgICAgIHByb2ZpbGVDb21wbGV0ZWQsXG4gICAgICAgIHdhbGxldENvbm5lY3RlZCxcbiAgICAgICAga3ljQ29tcGxldGVkLFxuICAgICAgICBhY3R1YWxDb21wbGV0ZWRTdGVwc1xuICAgICAgfSk7XG5cbiAgICAgIC8vIFNhdmUgdG8gYmFja2VuZCBkYXRhYmFzZSB2aWEgYWRtaW4gcGFuZWwgQVBJXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudC9xdWFsaWZpY2F0aW9uLXByb2dyZXNzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHByb2dyZXNzRGF0YSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBzYXZlIHByb2dyZXNzJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgUHJvZ3Jlc3Mgc2F2ZWQgc3VjY2Vzc2Z1bGx5IHRvIGRhdGFiYXNlOicsIHJlc3VsdCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBzYXZpbmcgcHJvZ3Jlc3M6JywgZXJyb3IpO1xuICAgICAgLy8gRG9uJ3QgYmxvY2sgdGhlIHVzZXIgZmxvdyBpZiBzYXZpbmcgZmFpbHNcbiAgICB9XG4gIH07XG5cbiAgLy8gU3RlcCBjb21wbGV0aW9uIGhhbmRsZXJzXG4gIGNvbnN0IGhhbmRsZUNvdW50cnlDb21wbGV0ZSA9IGFzeW5jIChjb3VudHJ5OiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCB1cGRhdGVkU3RlcERhdGEgPSB7IC4uLnN0ZXBEYXRhLCBjb3VudHJ5LCBjb3VudHJ5Q29tcGxldGVkOiB0cnVlIH07XG4gICAgc2V0U3RlcERhdGEodXBkYXRlZFN0ZXBEYXRhKTtcbiAgICBzZXRDdXJyZW50U3RlcCgxKTtcblxuICAgIC8vIFNhdmUgcHJvZ3Jlc3NcbiAgICBhd2FpdCBzYXZlUHJvZ3Jlc3ModXBkYXRlZFN0ZXBEYXRhLCAxKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVBZ3JlZW1lbnRDb21wbGV0ZSA9IGFzeW5jICgpID0+IHtcbiAgICAvLyBGaXJzdCBzYXZlIHRoZSB0b2tlbiBhZ3JlZW1lbnRcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jbGllbnQvdG9rZW4tYWdyZWVtZW50Jywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICB0b2tlbkFkZHJlc3MsXG4gICAgICAgICAgdG9rZW5TeW1ib2wsXG4gICAgICAgICAgYWNjZXB0ZWQ6IHRydWUsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gc2F2ZSBhZ3JlZW1lbnQnKTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+KchSBUb2tlbiBhZ3JlZW1lbnQgc2F2ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBzYXZpbmcgdG9rZW4gYWdyZWVtZW50OicsIGVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBVcGRhdGUgc3RlcCBkYXRhIGFuZCBwcm9ncmVzc1xuICAgIGNvbnN0IHVwZGF0ZWRTdGVwRGF0YSA9IHsgLi4uc3RlcERhdGEsIGFncmVlbWVudEFjY2VwdGVkOiB0cnVlIH07XG4gICAgc2V0U3RlcERhdGEodXBkYXRlZFN0ZXBEYXRhKTtcbiAgICBzZXRDdXJyZW50U3RlcCgyKTtcblxuICAgIC8vIFNhdmUgcHJvZ3Jlc3NcbiAgICBhd2FpdCBzYXZlUHJvZ3Jlc3ModXBkYXRlZFN0ZXBEYXRhLCAyKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVQcm9maWxlQ29tcGxldGUgPSBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZFN0ZXBEYXRhID0geyAuLi5zdGVwRGF0YSwgcHJvZmlsZUNvbXBsZXRlZDogdHJ1ZSB9O1xuICAgIHNldFN0ZXBEYXRhKHVwZGF0ZWRTdGVwRGF0YSk7XG4gICAgc2V0Q3VycmVudFN0ZXAoMyk7XG5cbiAgICAvLyBTYXZlIHByb2dyZXNzXG4gICAgYXdhaXQgc2F2ZVByb2dyZXNzKHVwZGF0ZWRTdGVwRGF0YSwgMyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlV2FsbGV0Q29tcGxldGUgPSBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZFN0ZXBEYXRhID0geyAuLi5zdGVwRGF0YSwgd2FsbGV0Q29ubmVjdGVkOiB0cnVlIH07XG4gICAgc2V0U3RlcERhdGEodXBkYXRlZFN0ZXBEYXRhKTtcbiAgICBzZXRDdXJyZW50U3RlcCg0KTtcblxuICAgIC8vIFNhdmUgcHJvZ3Jlc3NcbiAgICBhd2FpdCBzYXZlUHJvZ3Jlc3ModXBkYXRlZFN0ZXBEYXRhLCA0KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVLWUNTdGF0dXNDaGFuZ2UgPSBhc3luYyAoc3RhdHVzOiBzdHJpbmcsIGVycm9yPzogc3RyaW5nKSA9PiB7XG4gICAgc2V0S3ljU3RhdHVzKHN0YXR1cyk7XG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBzZXRLeWNFcnJvcihlcnJvcik7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldEt5Y0Vycm9yKG51bGwpO1xuICAgIH1cblxuICAgIGlmIChzdGF0dXMgPT09ICdjb21wbGV0ZWQnKSB7XG4gICAgICBjb25zdCB1cGRhdGVkU3RlcERhdGEgPSB7IC4uLnN0ZXBEYXRhLCBreWNDb21wbGV0ZWQ6IHRydWUgfTtcbiAgICAgIHNldFN0ZXBEYXRhKHVwZGF0ZWRTdGVwRGF0YSk7XG4gICAgICBzZXRDdXJyZW50U3RlcCg1KTtcblxuICAgICAgLy8gU2F2ZSBwcm9ncmVzc1xuICAgICAgYXdhaXQgc2F2ZVByb2dyZXNzKHVwZGF0ZWRTdGVwRGF0YSwgNSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIFN0ZXAgbmF2aWdhdGlvbiBmdW5jdGlvbnNcbiAgY29uc3QgY2FuTmF2aWdhdGVUb1N0ZXAgPSAoc3RlcEluZGV4OiBudW1iZXIpID0+IHtcbiAgICAvLyBVc2VycyBjYW4gYWx3YXlzIG5hdmlnYXRlIHRvIGNvbXBsZXRlZCBzdGVwcyBvciB0aGUgbmV4dCBpbmNvbXBsZXRlIHN0ZXBcbiAgICBpZiAoc3RlcEluZGV4ID09PSAwKSByZXR1cm4gdHJ1ZTsgLy8gQ291bnRyeSBzZWxlY3Rpb24gYWx3YXlzIGF2YWlsYWJsZVxuICAgIGlmIChzdGVwSW5kZXggPT09IDEpIHJldHVybiBzdGVwRGF0YS5jb3VudHJ5ICE9PSAnJzsgLy8gQWdyZWVtZW50IGlmIGNvdW50cnkgc2VsZWN0ZWRcbiAgICBpZiAoc3RlcEluZGV4ID09PSAyKSByZXR1cm4gc3RlcERhdGEuYWdyZWVtZW50QWNjZXB0ZWQ7IC8vIFByb2ZpbGUgaWYgYWdyZWVtZW50IGFjY2VwdGVkXG4gICAgaWYgKHN0ZXBJbmRleCA9PT0gMykgcmV0dXJuIHN0ZXBEYXRhLnByb2ZpbGVDb21wbGV0ZWQ7IC8vIFdhbGxldCBpZiBwcm9maWxlIGNvbXBsZXRlZFxuICAgIGlmIChzdGVwSW5kZXggPT09IDQpIHJldHVybiBzdGVwRGF0YS53YWxsZXRDb25uZWN0ZWQ7IC8vIEtZQyBpZiB3YWxsZXQgY29ubmVjdGVkXG4gICAgaWYgKHN0ZXBJbmRleCA9PT0gNSkgcmV0dXJuIHN0ZXBEYXRhLmt5Y0NvbXBsZXRlZDsgLy8gQ29tcGxldGlvbiBpZiBLWUMgZG9uZVxuICAgIHJldHVybiBmYWxzZTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdGVwQ2xpY2sgPSAoc3RlcEluZGV4OiBudW1iZXIpID0+IHtcbiAgICBpZiAoY2FuTmF2aWdhdGVUb1N0ZXAoc3RlcEluZGV4KSkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoc3RlcEluZGV4KTtcbiAgICAgIC8vIEZvcmNlIHNhdmUgd2l0aCBjb3JyZWN0IGNvbXBsZXRpb24gZmxhZ3MgYmFzZWQgb24gY3VycmVudCBzdGF0ZVxuICAgICAgY29uc3QgdXBkYXRlZERhdGEgPSB7XG4gICAgICAgIC4uLnN0ZXBEYXRhLFxuICAgICAgICAvLyBFbnN1cmUgYWxsIGNvbXBsZXRpb24gZmxhZ3MgYXJlIHNldCBiYXNlZCBvbiBjdXJyZW50IHN0YXRlXG4gICAgICAgIGNvdW50cnlDb21wbGV0ZWQ6IHN0ZXBEYXRhLmNvdW50cnlDb21wbGV0ZWQgfHwgKHN0ZXBEYXRhLmNvdW50cnkgJiYgc3RlcERhdGEuY291bnRyeSAhPT0gJycpIHx8IGNvbXBsZXRlZFN0ZXBzID49IDEsXG4gICAgICAgIGFncmVlbWVudEFjY2VwdGVkOiBzdGVwRGF0YS5hZ3JlZW1lbnRBY2NlcHRlZCB8fCBjb21wbGV0ZWRTdGVwcyA+PSAyLFxuICAgICAgICBwcm9maWxlQ29tcGxldGVkOiBzdGVwRGF0YS5wcm9maWxlQ29tcGxldGVkIHx8ICEhcHJvZmlsZSB8fCBjb21wbGV0ZWRTdGVwcyA+PSAzLFxuICAgICAgICB3YWxsZXRDb25uZWN0ZWQ6IHN0ZXBEYXRhLndhbGxldENvbm5lY3RlZCB8fCAhIXByb2ZpbGU/LndhbGxldEFkZHJlc3MgfHwgY29tcGxldGVkU3RlcHMgPj0gNCxcbiAgICAgICAga3ljQ29tcGxldGVkOiBzdGVwRGF0YS5reWNDb21wbGV0ZWQgfHwgcHJvZmlsZT8ua3ljU3RhdHVzID09PSAnQVBQUk9WRUQnIHx8IGNvbXBsZXRlZFN0ZXBzID49IDUsXG4gICAgICB9O1xuICAgICAgY29uc29sZS5sb2coJ/CflKcgRm9yY2luZyBzYXZlIHdpdGggY29ycmVjdCBjb21wbGV0aW9uIGZsYWdzOicsIHVwZGF0ZWREYXRhKTtcbiAgICAgIHNhdmVQcm9ncmVzcyh1cGRhdGVkRGF0YSwgc3RlcEluZGV4KTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IGNvbXBsZXRlZFN0ZXBzID0gc3RlcHMuZmlsdGVyKHN0ZXAgPT4gc3RlcC5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKS5sZW5ndGg7XG4gIGNvbnN0IHRvdGFsU3RlcHMgPSBzdGVwcy5sZW5ndGg7XG4gIGNvbnN0IHByb2dyZXNzUGVyY2VudGFnZSA9IChjb21wbGV0ZWRTdGVwcyAvIHRvdGFsU3RlcHMpICogMTAwO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBzcGFjZS15LThcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAge3Rva2VuTmFtZSA/IGAke3Rva2VuTmFtZX0gUXVhbGlmaWNhdGlvbmAgOiAnVG9rZW4gUXVhbGlmaWNhdGlvbid9XG4gICAgICAgIDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgQ29tcGxldGUgdGhlIGZvbGxvd2luZyBzdGVwcyB0byBxdWFsaWZ5IGZvciB7dG9rZW5OYW1lIHx8ICd0b2tlbid9IGludmVzdG1lbnRcbiAgICAgICAgPC9wPlxuXG4gICAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTMgbWItOFwiPlxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGgtMyByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtwcm9ncmVzc1BlcmNlbnRhZ2V9JWAgfX1cbiAgICAgICAgICA+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgIHtjb21wbGV0ZWRTdGVwc30gb2Yge3RvdGFsU3RlcHN9IHN0ZXBzIGNvbXBsZXRlZFxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN0ZXBzIE92ZXJ2aWV3ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy01IGdhcC00IG1iLThcIj5cbiAgICAgICAge3N0ZXBzLm1hcCgoc3RlcCwgaW5kZXgpID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e3N0ZXAuaWR9XG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTdGVwQ2xpY2soaW5kZXgpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgYm9yZGVyIHRleHQtY2VudGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke2dldFN0ZXBDb2xvcihzdGVwLnN0YXR1cyl9ICR7XG4gICAgICAgICAgICAgIGNhbk5hdmlnYXRlVG9TdGVwKGluZGV4KVxuICAgICAgICAgICAgICAgID8gJ2N1cnNvci1wb2ludGVyIGhvdmVyOnNoYWRvdy1tZCBob3ZlcjpzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgOiAnY3Vyc29yLW5vdC1hbGxvd2VkIG9wYWNpdHktNjAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItMlwiPlxuICAgICAgICAgICAgICB7Z2V0U3RlcEljb24oc3RlcC5zdGF0dXMpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIG1iLTFcIj57c3RlcC50aXRsZX08L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14c1wiPntzdGVwLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgIHtjYW5OYXZpZ2F0ZVRvU3RlcChpbmRleCkgJiYgaW5kZXggIT09IGN1cnJlbnRTdGVwICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBtdC0xIGZvbnQtbWVkaXVtXCI+Q2xpY2sgdG8gbmF2aWdhdGU8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ3VycmVudCBTdGVwIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMCAmJiAoXG4gICAgICAgICAgPENvdW50cnlTZWxlY3Rpb25cbiAgICAgICAgICAgIG9uQ29tcGxldGU9e2hhbmRsZUNvdW50cnlDb21wbGV0ZX1cbiAgICAgICAgICAgIHNlbGVjdGVkQ291bnRyeT17c3RlcERhdGEuY291bnRyeX1cbiAgICAgICAgICAgIGlzQ29tcGxldGVkPXtzdGVwRGF0YS5jb3VudHJ5ICE9PSAnJ31cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMSAmJiAoXG4gICAgICAgICAgPFRva2VuQWdyZWVtZW50XG4gICAgICAgICAgICBvbkNvbXBsZXRlPXtoYW5kbGVBZ3JlZW1lbnRDb21wbGV0ZX1cbiAgICAgICAgICAgIHRva2VuTmFtZT17dG9rZW5OYW1lfVxuICAgICAgICAgICAgdG9rZW5TeW1ib2w9e3Rva2VuU3ltYm9sfVxuICAgICAgICAgICAgaXNDb21wbGV0ZWQ9e3N0ZXBEYXRhLmFncmVlbWVudEFjY2VwdGVkfVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2N1cnJlbnRTdGVwID09PSAyICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+TWFpbiBJbmZvcm1hdGlvbjwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICBQbGVhc2UgcHJvdmlkZSB5b3VyIGNvbXBsZXRlIHBlcnNvbmFsIGFuZCBmaW5hbmNpYWwgaW5mb3JtYXRpb24uXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPFF1YWxpZmljYXRpb25Gb3JtXG4gICAgICAgICAgICAgIG9uQ29tcGxldGU9e2hhbmRsZVByb2ZpbGVDb21wbGV0ZX1cbiAgICAgICAgICAgICAgZXhpc3RpbmdQcm9maWxlPXtwcm9maWxlfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDMgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5XYWxsZXQgQ29ubmVjdGlvbjwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICBDb25uZWN0IHlvdXIgY3J5cHRvY3VycmVuY3kgd2FsbGV0IHVzaW5nIFJlb3duIChXYWxsZXRDb25uZWN0KS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8V2FsbGV0Q29ubmVjdGlvbiBvbldhbGxldENvbm5lY3RlZD17aGFuZGxlV2FsbGV0Q29tcGxldGV9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2N1cnJlbnRTdGVwID09PSA0ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+S1lDIFZlcmlmaWNhdGlvbjwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICBDb21wbGV0ZSB5b3VyIGlkZW50aXR5IHZlcmlmaWNhdGlvbiB1c2luZyBTdW1zdWIuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPEF1dG9tYXRpY0tZQ1xuICAgICAgICAgICAgICBvblN0YXR1c0NoYW5nZT17aGFuZGxlS1lDU3RhdHVzQ2hhbmdlfVxuICAgICAgICAgICAgICBleGlzdGluZ1Byb2ZpbGU9e3Byb2ZpbGV9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHtjdXJyZW50U3RlcCA9PT0gNSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgeyhxdWFsaWZpY2F0aW9uU3RhdHVzID09PSAnQVBQUk9WRUQnIHx8IHF1YWxpZmljYXRpb25TdGF0dXMgPT09ICdGT1JDRV9BUFBST1ZFRCcpID8gKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JlZW4tNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIFF1YWxpZmljYXRpb24gQXBwcm92ZWQhXG4gICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIENvbmdyYXR1bGF0aW9ucyEgWW91ciBxdWFsaWZpY2F0aW9uIGZvciB7dG9rZW5OYW1lIHx8ICd0aGlzIHRva2VuJ30gaGFzIGJlZW4gYXBwcm92ZWQuXG4gICAgICAgICAgICAgICAgICBZb3UgYXJlIG5vdyBlbGlnaWJsZSB0byBpbnZlc3QgaW4gdGhpcyB0b2tlbi5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNCBtYi02IG1heC13LW1kIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi04MDBcIj5BcHByb3ZlZCBmb3IgSW52ZXN0bWVudDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JlZW4tNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBZb3UgY2FuIG5vdyBwcm9jZWVkIHRvIGludmVzdCBpbiB0aGlzIHRva2VuIHRocm91Z2ggdGhlIHBsYXRmb3JtLlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApIDogcXVhbGlmaWNhdGlvblN0YXR1cyA9PT0gJ1JFSkVDVEVEJyA/IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8WENpcmNsZUljb24gY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtcmVkLTUwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBRdWFsaWZpY2F0aW9uIFJlamVjdGVkXG4gICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIFVuZm9ydHVuYXRlbHksIHlvdXIgcXVhbGlmaWNhdGlvbiBmb3Ige3Rva2VuTmFtZSB8fCAndGhpcyB0b2tlbid9IGhhcyBiZWVuIHJlamVjdGVkLlxuICAgICAgICAgICAgICAgICAgUGxlYXNlIGNvbnRhY3Qgc3VwcG9ydCBmb3IgbW9yZSBpbmZvcm1hdGlvbi5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC00IG1iLTYgbWF4LXctbWQgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8WENpcmNsZUljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXJlZC02MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXJlZC04MDBcIj5BcHBsaWNhdGlvbiBSZWplY3RlZDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcmVkLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUGxlYXNlIHJldmlldyB5b3VyIGluZm9ybWF0aW9uIGFuZCBjb250YWN0IHN1cHBvcnQgaWYgbmVlZGVkLlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JlZW4tNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIFF1YWxpZmljYXRpb24gU3VibWl0dGVkIVxuICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgICAgICAgICBZb3UgaGF2ZSBzdWNjZXNzZnVsbHkgY29tcGxldGVkIGFsbCBxdWFsaWZpY2F0aW9uIHN0ZXBzIGZvciB7dG9rZW5OYW1lIHx8ICd0aGlzIHRva2VuJ30uXG4gICAgICAgICAgICAgICAgICBZb3VyIGFwcGxpY2F0aW9uIGlzIG5vdyBwZW5kaW5nIGFkbWluIHJldmlldyBhbmQgYXBwcm92YWwuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTUwIGJvcmRlciBib3JkZXIteWVsbG93LTIwMCByb3VuZGVkLWxnIHAtNCBtYi02IG1heC13LW1kIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPENsb2NrSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQteWVsbG93LTYwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTgwMFwiPlBlbmRpbmcgQWRtaW4gQXBwcm92YWw8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXllbGxvdy03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFuIGFkbWluaXN0cmF0b3Igd2lsbCByZXZpZXcgeW91ciBxdWFsaWZpY2F0aW9uIGFuZCBhcHByb3ZlIHlvdSBmb3IgdG9rZW4gaW52ZXN0bWVudC5cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnLyd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBSZXR1cm4gdG8gRGFzaGJvYXJkXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17Zml4UXVhbGlmaWNhdGlvbkZsYWdzfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGhvdmVyOmJnLWdyZWVuLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBGaXggUHJvZ3Jlc3MgRmxhZ3NcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUXVlcnkiLCJDaGVja0NpcmNsZUljb24iLCJDbG9ja0ljb24iLCJFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiIsIlhDaXJjbGVJY29uIiwiQ291bnRyeVNlbGVjdGlvbiIsIlRva2VuQWdyZWVtZW50IiwiUXVhbGlmaWNhdGlvbkZvcm0iLCJXYWxsZXRDb25uZWN0aW9uIiwiQXV0b21hdGljS1lDIiwiUXVhbGlmaWNhdGlvbkZsb3ciLCJ0b2tlbkFkZHJlc3MiLCJ0b2tlbk5hbWUiLCJ0b2tlblN5bWJvbCIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJzdGVwRGF0YSIsInNldFN0ZXBEYXRhIiwiY291bnRyeSIsImFncmVlbWVudEFjY2VwdGVkIiwicHJvZmlsZUNvbXBsZXRlZCIsIndhbGxldENvbm5lY3RlZCIsImt5Y0NvbXBsZXRlZCIsInF1YWxpZmljYXRpb25TdGF0dXMiLCJzZXRRdWFsaWZpY2F0aW9uU3RhdHVzIiwia3ljU3RhdHVzIiwic2V0S3ljU3RhdHVzIiwia3ljRXJyb3IiLCJzZXRLeWNFcnJvciIsImRhdGEiLCJxdWFsaWZpY2F0aW9uUHJvZ3Jlc3MiLCJpc0xvYWRpbmciLCJxdWVyeUtleSIsInF1ZXJ5Rm4iLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJhcHBlbmQiLCJyZXNwb25zZSIsImZldGNoIiwidG9TdHJpbmciLCJvayIsImpzb24iLCJwcm9maWxlIiwid2FsbGV0U3RhdHVzIiwic3RvcmFnZUtleSIsImxvY2FsUHJvZ3Jlc3MiLCJzdG9yZWQiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwiY29uc29sZSIsImxvZyIsImVycm9yIiwicHJvZ3Jlc3NUb1VzZSIsIm5ld1N0ZXBEYXRhIiwidmVyaWZpZWQiLCJjYWxjdWxhdGVkU3RlcCIsInNhdmVkUHJvZ3Jlc3MiLCJzb3VyY2UiLCJmaXhRdWFsaWZpY2F0aW9uRmxhZ3MiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInN0cmluZ2lmeSIsInVzZXJFbWFpbCIsImVtYWlsIiwicmVzdWx0Iiwid2luZG93IiwibG9jYXRpb24iLCJyZWxvYWQiLCJzdGF0dXMiLCJzdGVwcyIsImlkIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImdldFN0ZXBJY29uIiwiY2xhc3NOYW1lIiwiZGl2IiwiZ2V0U3RlcENvbG9yIiwic2F2ZVByb2dyZXNzIiwidXBkYXRlZFN0ZXBEYXRhIiwibmV3Q3VycmVudFN0ZXAiLCJjb3VudHJ5Q29tcGxldGVkIiwid2FsbGV0QWRkcmVzcyIsInN0ZXBDb21wbGV0aW9uRmxhZ3MiLCJhY3R1YWxDb21wbGV0ZWRTdGVwcyIsImZpbHRlciIsIkJvb2xlYW4iLCJsZW5ndGgiLCJwcm9ncmVzc0RhdGEiLCJjb21wbGV0ZWRTdGVwcyIsIkVycm9yIiwiaGFuZGxlQ291bnRyeUNvbXBsZXRlIiwiaGFuZGxlQWdyZWVtZW50Q29tcGxldGUiLCJhY2NlcHRlZCIsImhhbmRsZVByb2ZpbGVDb21wbGV0ZSIsImhhbmRsZVdhbGxldENvbXBsZXRlIiwiaGFuZGxlS1lDU3RhdHVzQ2hhbmdlIiwiY2FuTmF2aWdhdGVUb1N0ZXAiLCJzdGVwSW5kZXgiLCJoYW5kbGVTdGVwQ2xpY2siLCJ1cGRhdGVkRGF0YSIsInN0ZXAiLCJ0b3RhbFN0ZXBzIiwicHJvZ3Jlc3NQZXJjZW50YWdlIiwiaDEiLCJwIiwic3R5bGUiLCJ3aWR0aCIsIm1hcCIsImluZGV4Iiwib25DbGljayIsImgzIiwib25Db21wbGV0ZSIsInNlbGVjdGVkQ291bnRyeSIsImlzQ29tcGxldGVkIiwiaDIiLCJleGlzdGluZ1Byb2ZpbGUiLCJvbldhbGxldENvbm5lY3RlZCIsIm9uU3RhdHVzQ2hhbmdlIiwiYnV0dG9uIiwiaHJlZiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});