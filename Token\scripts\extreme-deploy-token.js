// EXTREME Token Deployment Script
// This script is designed to work even with very unstable RPC connections
// It includes multiple fallback mechanisms and extreme retry logic

const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

// List of RPC endpoints to try
const RPC_ENDPOINTS = [
  "https://polygon-amoy.blockpi.network/v1/rpc/public",
  "https://polygon-amoy-rpc.publicnode.com",
  "https://polygon-amoy.drpc.org",
  "https://rpc-amoy.polygon.technology"
];

// Maximum gas parameters - adjusted to optimal values
const MAX_GAS_LIMIT = 5000000; // 5M gas (reduced from 10M)
const MAX_GAS_PRICE = 50; // 50 gwei (reduced from 500)

// Retries
const MAX_RETRIES = 5;
const RETRY_DELAY = 5000; // 5 seconds

// Helper function to sleep
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to configure hardhat network dynamically
async function configureNetwork() {
  // Get network config from hardhat
  const network = await ethers.provider.getNetwork();
  console.log(`Connected to network: ${network.name} (chainId: ${network.chainId})`);
  
  return network;
}

// Helper function to try multiple RPC endpoints
async function withFallbackRpc(operation) {
  let lastError;
  
  // Try each RPC endpoint
  for (const rpcUrl of RPC_ENDPOINTS) {
    try {
      console.log(`Trying RPC endpoint: ${rpcUrl}`);
      
      // Update the RPC URL environment variable
      process.env.AMOY_RPC_URL = rpcUrl;
      
      // Reconfigure the network if possible
      await configureNetwork().catch(e => console.log(`Network config error: ${e.message}`));
      
      // Try the operation
      return await operation();
    } catch (error) {
      console.log(`RPC ${rpcUrl} failed: ${error.message}`);
      lastError = error;
      // Wait before trying next endpoint
      await sleep(1000);
    }
  }
  
  // If all endpoints fail, throw the last error
  throw new Error(`All RPC endpoints failed. Last error: ${lastError.message}`);
}

// Helper function for retries
async function withRetry(operation, name, maxRetries = MAX_RETRIES) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`${name} - Attempt ${attempt}/${maxRetries}`);
      return await operation();
    } catch (error) {
      console.log(`${name} failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
      lastError = error;
      
      if (attempt < maxRetries) {
        const delay = RETRY_DELAY * attempt;
        console.log(`Retrying in ${delay/1000} seconds...`);
        await sleep(delay);
      }
    }
  }
  
  throw new Error(`${name} failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

// Main deployment function
async function main() {
  console.log("\n=== EXTREME TOKEN DEPLOYMENT FOR AMOY ===");
  console.log("Using maximum resilience approach for unreliable networks");

  try {
    // Get arguments from command line or set defaults
    const factoryAddress = process.env.FACTORY_ADDRESS;
    if (!factoryAddress) {
      throw new Error("FACTORY_ADDRESS environment variable not set");
    }

    // Token parameters - these can be customized or passed as env variables
    const tokenName = process.env.TOKEN_NAME || "Example Security Token";
    const tokenSymbol = process.env.TOKEN_SYMBOL || "EXST";
    const maxSupply = process.env.MAX_SUPPLY ? ethers.parseEther(process.env.MAX_SUPPLY) : ethers.parseEther("1000000");
    const tokenPrice = process.env.TOKEN_PRICE || "5 USD";
    const bonusTiers = process.env.BONUS_TIERS || "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%";
    
    // Gas parameters - use extreme values by default
    const gasLimit = process.env.GAS_LIMIT ? BigInt(process.env.GAS_LIMIT) : BigInt(MAX_GAS_LIMIT);
    const gasPrice = process.env.GAS_PRICE 
      ? ethers.parseUnits(process.env.GAS_PRICE, "gwei") 
      : ethers.parseUnits(MAX_GAS_PRICE.toString(), "gwei");

    console.log("Deploying a new SecurityToken with the following parameters:");
    console.log(`Factory Address: ${factoryAddress}`);
    console.log(`Name: ${tokenName}`);
    console.log(`Symbol: ${tokenSymbol}`);
    console.log(`Max Supply: ${ethers.formatEther(maxSupply)} tokens`);
    console.log(`Token Price: ${tokenPrice}`);
    console.log(`Bonus Tiers: ${bonusTiers}`);
    console.log(`Gas Limit: ${gasLimit.toString()}`);
    console.log(`Gas Price: ${ethers.formatUnits(gasPrice, "gwei")} gwei`);

    // Try to get signer with fallback
    let deployer;
    await withFallbackRpc(async () => {
      [deployer] = await ethers.getSigners();
      console.log(`Deployer: ${deployer.address}`);
      return deployer;
    });

    // Get admin address
    const adminAddress = process.env.ADMIN_ADDRESS || deployer.address;
    console.log(`Admin: ${adminAddress}`);
    
    // Log deployment start time for reference
    const startTime = new Date();
    console.log(`Deployment started at: ${startTime.toISOString()}`);
    
    // Save initial transaction info to a file in case everything fails
    const deploymentInfo = {
      status: 'pending',
      factoryAddress,
      tokenName,
      tokenSymbol,
      maxSupply: ethers.formatEther(maxSupply),
      tokenPrice,
      bonusTiers,
      deployer: deployer.address,
      admin: adminAddress,
      startTime: startTime.toISOString(),
      gasLimit: gasLimit.toString(),
      gasPrice: ethers.formatUnits(gasPrice, "gwei"),
    };
    
    const deploymentPath = path.join(__dirname, '../pending-deployments.json');
    fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
    
    // Connect to the factory with retry and fallback
    let factory;
    await withRetry(async () => {
      await withFallbackRpc(async () => {
        factory = await ethers.getContractAt("SecurityTokenFactory", factoryAddress);
        return factory;
      });
    }, "Factory connection");
    
    // Create raw transaction
    console.log("Creating transaction data...");
    
    // Get ABI for encoding directly if needed
    const factoryAbi = [
      "function deploySecurityToken(string name, string symbol, uint256 maxSupply, address admin, string tokenPrice, string bonusTiers) returns (address, address)"
    ];
    const iface = new ethers.Interface(factoryAbi);
    const data = iface.encodeFunctionData("deploySecurityToken", [
      tokenName,
      tokenSymbol,
      maxSupply,
      adminAddress,
      tokenPrice,
      bonusTiers
    ]);
    
    // Get nonce with retry and fallback
    let nonce;
    await withRetry(async () => {
      await withFallbackRpc(async () => {
        nonce = await deployer.getNonce();
        console.log(`Using nonce: ${nonce}`);
        return nonce;
      });
    }, "Nonce retrieval");
    
    // Create transaction object
    const tx = {
      to: factoryAddress,
      data: data,
      gasLimit: gasLimit,
      gasPrice: gasPrice,
      nonce: nonce,
      value: ethers.parseEther("0")
    };
    
    // Get chain ID if possible, but make it optional
    try {
      const network = await ethers.provider.getNetwork();
      tx.chainId = network.chainId;
      console.log(`Setting chainId: ${tx.chainId}`);
    } catch (error) {
      console.log(`Could not get chainId: ${error.message}`);
      console.log("Continuing without explicit chainId");
    }
    
    console.log("Transaction prepared:", JSON.stringify(tx, (_, v) => 
      typeof v === 'bigint' ? v.toString() : v, 2));
      
    // Deploy with retry and fallback
    console.log("\nSending transaction...");
    let txResponse;
    
    try {
      await withRetry(async () => {
        await withFallbackRpc(async () => {
          txResponse = await deployer.sendTransaction(tx);
          console.log(`Transaction sent! Hash: ${txResponse.hash}`);
          
          // Update deployment info with tx hash
          deploymentInfo.transactionHash = txResponse.hash;
          deploymentInfo.sentTime = new Date().toISOString();
          fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
          
          return txResponse;
        });
      }, "Transaction sending", 3);
    } catch (error) {
      console.error(`Failed to send transaction after multiple attempts: ${error.message}`);
      
      // Create a final record with the error
      deploymentInfo.status = 'failed';
      deploymentInfo.error = error.message;
      deploymentInfo.endTime = new Date().toISOString();
      
      const failedPath = path.join(__dirname, '../failed-deployments.json');
      fs.writeFileSync(failedPath, JSON.stringify(deploymentInfo, null, 2));
      
      throw error;
    }
    
    // Wait for receipt with extreme patience and polling
    console.log("\nWaiting for transaction confirmation...");
    console.log("This could take a long time due to RPC instability");
    console.log(`You can check status manually at: https://amoy.polygonscan.com/tx/${txResponse.hash}`);
    
    // Polling approach with fallback RPCs
    let receipt = null;
    let confirmed = false;
    let timeout = 600000; // 10 minutes
    let startWaitTime = Date.now();
    
    while (!confirmed && (Date.now() - startWaitTime < timeout)) {
      // Try all RPC endpoints for confirmation
      for (const rpcUrl of RPC_ENDPOINTS) {
        try {
          console.log(`Checking tx status using RPC: ${rpcUrl} (${Math.round((Date.now() - startWaitTime)/1000)}s elapsed)`);
          
          // Update RPC URL
          process.env.AMOY_RPC_URL = rpcUrl;
          
          // Reconfigure network
          await configureNetwork().catch(() => {});
          
          // Check receipt
          receipt = await ethers.provider.getTransactionReceipt(txResponse.hash);
          
          if (receipt) {
            confirmed = true;
            console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
            break;
          } else {
            console.log("Transaction not yet confirmed");
          }
        } catch (error) {
          console.log(`Error checking receipt with ${rpcUrl}: ${error.message}`);
        }
        
        // Don't hammer the RPC endpoints
        await sleep(3000);
      }
      
      if (!confirmed) {
        // Wait between polling cycles
        await sleep(10000);
      }
    }
    
    // Extract token info from receipt or query factory
    console.log("\nExtracting token information...");
    let tokenAddress, whitelistAddress;
    
    // Try to extract from receipt first if we have it
    if (receipt) {
      try {
        // Parse logs if possible
        console.log("Looking for TokenDeployed event in logs...");
        
        const factoryInterface = new ethers.Interface([
          "event TokenDeployed(address indexed tokenAddress, address indexed whitelistAddress, string name, string symbol)"
        ]);
        
        let found = false;
        
        for (const log of receipt.logs) {
          try {
            const parsedLog = factoryInterface.parseLog(log);
            if (parsedLog && parsedLog.name === 'TokenDeployed') {
              tokenAddress = parsedLog.args.tokenAddress;
              whitelistAddress = parsedLog.args.whitelistAddress;
              console.log("TokenDeployed event found!");
              found = true;
              break;
            }
          } catch (e) {
            // Not this log
          }
        }
        
        if (!found) {
          console.log("Could not find TokenDeployed event in logs");
        }
      } catch (error) {
        console.log(`Error parsing logs: ${error.message}`);
      }
    }
    
    // If we couldn't extract from logs, query the factory
    if (!tokenAddress) {
      console.log("Trying to get token address from factory using symbol...");
      
      await withRetry(async () => {
        await withFallbackRpc(async () => {
          tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
          console.log(`Token address from factory: ${tokenAddress}`);
          
          if (tokenAddress && tokenAddress !== ethers.ZeroAddress) {
            const token = await ethers.getContractAt("SecurityToken", tokenAddress);
            whitelistAddress = await token.whitelistAddress();
            console.log(`Whitelist address from token: ${whitelistAddress}`);
          } else {
            throw new Error("Token address not found or is zero address");
          }
          
          return { tokenAddress, whitelistAddress };
        });
      }, "Token address retrieval");
    }
    
    // If we have the token address but not the whitelist, get it from the token
    if (tokenAddress && !whitelistAddress) {
      await withRetry(async () => {
        await withFallbackRpc(async () => {
          const token = await ethers.getContractAt("SecurityToken", tokenAddress);
          whitelistAddress = await token.whitelistAddress();
          console.log(`Whitelist address from token: ${whitelistAddress}`);
          return whitelistAddress;
        });
      }, "Whitelist retrieval");
    }
    
    // If we still don't have the addresses, consider it a failure
    if (!tokenAddress || !whitelistAddress) {
      throw new Error("Could not determine token and whitelist addresses");
    }
    
    // Log all information
    console.log("\n=== DEPLOYMENT SUCCESSFUL ===");
    console.log(`SecurityToken deployed to: ${tokenAddress}`);
    console.log(`Whitelist deployed to: ${whitelistAddress}`);
    
    // Prepare result object
    const result = {
      tokenAddress,
      whitelistAddress,
      transactionHash: txResponse.hash,
      blockNumber: receipt ? receipt.blockNumber : "Unknown",
      deployer: deployer.address,
      admin: adminAddress,
      tokenName,
      tokenSymbol,
      maxSupply: ethers.formatEther(maxSupply),
      startTime: startTime.toISOString(),
      endTime: new Date().toISOString(),
      status: 'success'
    };
    
    // Save result to file
    const successPath = path.join(__dirname, '../successful-deployments.json');
    
    if (fs.existsSync(successPath)) {
      // Append to existing data
      let existing = [];
      try {
        existing = JSON.parse(fs.readFileSync(successPath, 'utf8'));
        if (!Array.isArray(existing)) existing = [existing];
      } catch (e) {
        existing = [];
      }
      existing.push(result);
      fs.writeFileSync(successPath, JSON.stringify(existing, null, 2));
    } else {
      // Create new file
      fs.writeFileSync(successPath, JSON.stringify([result], null, 2));
    }
    
    // Clean up pending deployment file
    try {
      fs.unlinkSync(deploymentPath);
    } catch (e) {
      // Ignore error
    }
    
    // Try to get token details if possible
    try {
      await withFallbackRpc(async () => {
        const token = await ethers.getContractAt("SecurityToken", tokenAddress);
        console.log(`\nToken name: ${await token.name()}`);
        console.log(`Token symbol: ${await token.symbol()}`);
        console.log(`Token max supply: ${ethers.formatEther(await token.maxSupply())} tokens`);
        console.log(`Token totalSupply: ${ethers.formatEther(await token.totalSupply())} tokens`);
      });
    } catch (error) {
      console.log(`Could not retrieve token details: ${error.message}`);
    }
    
    return result;
  } catch (error) {
    console.error("\n❌ ERROR during token deployment:");
    if (error.reason) console.error(`Reason: ${error.reason}`);
    console.error(error);
    
    console.log("\n=== TROUBLESHOOTING SUGGESTIONS ===");
    console.log("1. The Amoy testnet is extremely unstable right now");
    console.log("2. Check if your transaction went through at https://amoy.polygonscan.com/");
    console.log("3. Check pending-deployments.json for details about your attempt");
    console.log("4. Try again later when the network is more stable");
    
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then((result) => {
      console.log("\nDeployment completed successfully!");
      console.log(`Token Address: ${result.tokenAddress}`);
      console.log(`Whitelist Address: ${result.whitelistAddress}`);
      console.log(`Transaction Hash: ${result.transactionHash}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error("\nDeployment failed!");
      process.exit(1);
    });
} else {
  // Export for use in other scripts
  module.exports = main;
} 