"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                // Try to get more recent data from localStorage first\n                const storageKey = \"qualification_progress_\".concat(tokenAddress);\n                let localProgress = null;\n                try {\n                    const stored = localStorage.getItem(storageKey);\n                    if (stored) {\n                        localProgress = JSON.parse(stored);\n                        console.log('📱 Found localStorage progress:', localProgress);\n                    }\n                } catch (error) {\n                    console.error('Error reading localStorage:', error);\n                }\n                // Use localStorage data if it's more recent, otherwise use API data\n                const progressToUse = localProgress || qualificationProgress;\n                const newStepData = {\n                    country: progressToUse.country || '',\n                    agreementAccepted: progressToUse.agreementAccepted || false,\n                    profileCompleted: progressToUse.profileCompleted || !!profile,\n                    walletConnected: progressToUse.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: progressToUse.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = progressToUse.currentStep || 0;\n                // Validate and adjust step based on actual completion status\n                if (!newStepData.country) {\n                    calculatedStep = 0; // Country selection\n                } else if (!newStepData.agreementAccepted) {\n                    calculatedStep = 1; // Agreement acceptance\n                } else if (!newStepData.profileCompleted) {\n                    calculatedStep = 2; // Profile completion\n                } else if (!newStepData.walletConnected) {\n                    calculatedStep = 3; // Wallet connection\n                } else if (!newStepData.kycCompleted) {\n                    calculatedStep = 4; // KYC verification\n                } else {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: progressToUse,\n                    source: localProgress ? 'localStorage' : 'API'\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus,\n        tokenAddress\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            const progressData = {\n                ...updatedStepData,\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: Object.values(updatedStepData).filter(Boolean).length\n            };\n            console.log('💾 Saving progress to database:', progressData);\n            // Save to backend database via admin panel API\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully to database:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border text-center \".concat(getStepColor(step.status)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Complete!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". You can now proceed with your investment.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});