// Update <NAME_EMAIL> with your wallet
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateExistingUser() {
  console.log('=== Updating Existing User with Your Wallet ===');
  
  const yourWallet = '******************************************';
  const existingEmail = '<EMAIL>';
  
  try {
    // Find the existing user
    const existingUser = await prisma.client.findFirst({
      where: { email: existingEmail }
    });
    
    if (!existingUser) {
      console.log(`❌ User with email ${existingEmail} not found`);
      return;
    }
    
    console.log('✅ Found existing user:');
    console.log(`   ID: ${existingUser.id}`);
    console.log(`   Email: ${existingUser.email}`);
    console.log(`   Current Wallet: ${existingUser.walletAddress || 'None'}`);
    console.log(`   KYC Status: ${existingUser.kycStatus}`);
    
    // Update the user with your wallet address
    const updatedUser = await prisma.client.update({
      where: { id: existingUser.id },
      data: {
        walletAddress: yourWallet,
        walletSignature: '0xabcdef1234567890...',
        walletVerifiedAt: new Date(),
        isWhitelisted: true,
        whitelistedAt: new Date(),
        kycStatus: 'APPROVED',
        kycCompletedAt: new Date()
      }
    });
    
    console.log('\n✅ Updated user with your wallet:');
    console.log(`   Email: ${updatedUser.email}`);
    console.log(`   Wallet: ${updatedUser.walletAddress}`);
    console.log(`   KYC Status: ${updatedUser.kycStatus}`);
    console.log(`   Global Whitelisted: ${updatedUser.isWhitelisted}`);
    
    return updatedUser;
    
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
}

async function addTokenApprovals(clientId) {
  console.log('\n=== Adding Token Approvals ===');
  
  try {
    // Delete existing approvals for this client
    const deletedApprovals = await prisma.tokenClientApproval.deleteMany({
      where: { clientId: clientId }
    });
    console.log(`🗑️  Deleted ${deletedApprovals.count} existing token approvals`);
    
    // Get all tokens from the database
    const tokens = await prisma.token.findMany({
      select: { id: true, name: true, symbol: true, address: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens found in database');
      return;
    }

    console.log(`📋 Found ${tokens.length} tokens, creating approvals...`);

    // Whitelist the main tokens you mentioned you're already whitelisted for
    const tokensToWhitelist = ['AUG019', 'AUG01Z', 'TZD', 'EURT', 'ETHF'];

    for (const token of tokens) {
      const shouldWhitelist = tokensToWhitelist.includes(token.symbol);

      const approval = await prisma.tokenClientApproval.create({
        data: {
          tokenId: token.id,
          clientId: clientId,
          approvalStatus: shouldWhitelist ? 'APPROVED' : 'PENDING',
          kycApproved: true,
          whitelistApproved: shouldWhitelist,
          approvedBy: shouldWhitelist ? '<EMAIL>' : null,
          approvedAt: shouldWhitelist ? new Date() : null,
          notes: shouldWhitelist ? 'Whitelisted for your wallet' : 'Pending approval'
        }
      });

      const status = shouldWhitelist ? '✅ WHITELISTED' : '⏳ PENDING';
      console.log(`   ${token.symbol.padEnd(10)} | ${status}`);
    }

    const whitelistedCount = tokensToWhitelist.length;
    console.log(`\n✅ Token approvals created: ${whitelistedCount} whitelisted, ${tokens.length - whitelistedCount} pending`);
    
  } catch (error) {
    console.error('Error creating token approvals:', error);
    throw error;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Whitelist API Response:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${symbol.padEnd(10)} | ${status}`);
      });
      
      const whitelistedCount = data.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nSummary: ${whitelistedCount}/${data.tokens.length} tokens whitelisted for your wallet`);
      
    } else {
      console.log('❌ Whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing whitelist API:', error);
  }
}

async function main() {
  const yourWallet = '******************************************';
  const existingEmail = '<EMAIL>';
  
  try {
    // Update existing user with your wallet
    const user = await updateExistingUser();
    
    if (!user) {
      console.log('❌ Could not find or update user');
      return;
    }
    
    // Add token approvals
    await addTokenApprovals(user.id);
    
    // Test the whitelist API
    await testWhitelistAPI(yourWallet);
    
    console.log('\n🎉 Setup Complete!');
    console.log('\n🎯 READY TO TEST WHITELIST TAGS:');
    console.log('1. Open client app: http://localhost:3003/offers');
    console.log(`2. Login with email: ${existingEmail}`);
    console.log('3. Connect wallet: ******************************************');
    console.log('4. You should see green WHITELISTED tags on approved tokens');
    
    console.log('\n📋 WHITELISTED TOKENS FOR YOUR WALLET:');
    console.log('   - AUG019 (Augment_019)');
    console.log('   - AUG01Z (Augment_01z)');
    console.log('   - TZD (Test Zero Decimals Token)');
    console.log('   - EURT (European Real Estate Token)');
    console.log('   - ETHF (Ethereum DeFi Fund)');
    
    console.log('\n🌐 TESTING URLS:');
    console.log(`   - With your wallet: http://localhost:3003/offers?testWallet=${encodeURIComponent(yourWallet)}`);
    console.log('   - Normal login: http://localhost:3003/offers');
    
  } catch (error) {
    console.error('Error in main:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error);
