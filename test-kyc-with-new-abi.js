const { ethers } = require("hardhat");

async function main() {
  const proxyAddress = "******************************************";
  
  // Connect to the contract using the new ABI
  const whitelist = await ethers.getContractAt("WhitelistWithKYC", proxyAddress);
  
  // Get the signer
  const [signer] = await ethers.getSigners();
  console.log("Using signer address:", signer.address);
  
  // Test address - using the signer's own address for simplicity
  const testAddress = signer.address;
  
  // Check if address is already KYC approved
  try {
    console.log("Checking if address is already KYC approved...");
    const isApproved = await whitelist.isKycApproved(testAddress);
    console.log(`Address ${testAddress} KYC approved: ${isApproved}`);
    
    if (!isApproved) {
      console.log("Attempting to approve KYC for address...");
      
      // Check if we have AGENT_ROLE
      const AGENT_ROLE = await whitelist.AGENT_ROLE();
      const hasAgentRole = await whitelist.hasRole(AGENT_ROLE, signer.address);
      console.log(`Signer has AGENT_ROLE: ${hasAgentRole}`);
      
      if (hasAgentRole) {
        // Approve KYC for the address
        const tx = await whitelist.approveKyc(testAddress);
        console.log("Transaction sent:", tx.hash);
        
        // Wait for confirmation
        console.log("Waiting for transaction confirmation...");
        await tx.wait();
        console.log("Transaction confirmed!");
        
        // Check again
        const isApprovedNow = await whitelist.isKycApproved(testAddress);
        console.log(`Address ${testAddress} KYC approved after transaction: ${isApprovedNow}`);
      } else {
        console.log("Cannot approve KYC: signer does not have AGENT_ROLE");
      }
    }
  } catch (error) {
    console.error("Error testing KYC functions:", error);
    console.error("Error details:", error.message);
    
    // If there's a transaction in the error, log it
    if (error.transaction) {
      console.log("Transaction that caused the error:", error.transaction);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 