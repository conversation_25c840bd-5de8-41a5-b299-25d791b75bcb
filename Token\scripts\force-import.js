// <PERSON>ript to force import a previously deployed proxy contract
const { ethers, upgrades } = require("hardhat");

async function main() {
  // Set the specific addresses from the error message with correct checksum
  const proxyAddress = "******************************************"; // The token contract we want to upgrade

  console.log(`Importing proxy at ${proxyAddress}...`);

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log(`Executor: ${deployer.address}`);

  // Get the contract factory for the token
  const contractFactory = await ethers.getContractFactory("SecurityToken");
  
  // Force import the proxy
  console.log("Importing as SecurityToken...");
  const importedContract = await upgrades.forceImport(proxyAddress, contractFactory, { kind: "uups" });
  
  console.log(`Successfully imported proxy at ${await importedContract.getAddress()}`);
  console.log("Now you can run the upgrade script again.");
  
  // Log the current implementation address for reference
  console.log("\nFor debugging purposes:");
  const implAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
  console.log(`Current implementation address: ${implAddress}`);
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 