const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("Deploying new WhitelistWithKYC contract...");
  
  // Get the contract factory
  const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
  
  // Get the signer
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  
  // Deploy the contract as a proxy
  console.log("Deploying proxy...");
  const proxy = await upgrades.deployProxy(WhitelistWithKYC, [deployer.address], {
    initializer: "initialize",
  });
  
  // Wait for deployment to complete
  console.log("Waiting for deployment transaction confirmation...");
  try {
    // For ethers v6
    if (proxy.deploymentTransaction) {
      await proxy.deploymentTransaction().wait();
    } 
    // For ethers v5
    else if (proxy.deployTransaction) {
      await proxy.deployTransaction.wait();
    }
  } catch (error) {
    console.warn("Warning: Could not wait for transaction confirmation:", error.message);
  }
  
  console.log("WhitelistWithKYC deployed successfully!");
  console.log("Proxy address:", proxy.address);
  
  // Get the implementation address
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxy.address);
  console.log("Implementation address:", implementationAddress);
  console.log(`View on OKLink: https://www.oklink.com/amoy/address/${proxy.address}`);
  
  // Grant AGENT_ROLE to the deployer
  console.log("Granting AGENT_ROLE to deployer...");
  const AGENT_ROLE = await proxy.AGENT_ROLE();
  const tx = await proxy.grantRole(AGENT_ROLE, deployer.address);
  await tx.wait();
  console.log("AGENT_ROLE granted to deployer!");
  
  // Verify that the deployer has AGENT_ROLE
  const hasAgentRole = await proxy.hasRole(AGENT_ROLE, deployer.address);
  console.log(`Deployer has AGENT_ROLE: ${hasAgentRole}`);
  
  // Test KYC functionality
  console.log("\nTesting KYC functionality...");
  
  // Check if address is already KYC approved
  const isApproved = await proxy.isKycApproved(deployer.address);
  console.log(`Deployer KYC approved: ${isApproved}`);
  
  if (!isApproved) {
    console.log("Approving KYC for deployer...");
    const approveTx = await proxy.approveKyc(deployer.address);
    await approveTx.wait();
    console.log("KYC approved for deployer!");
    
    // Check again
    const isApprovedNow = await proxy.isKycApproved(deployer.address);
    console.log(`Deployer KYC approved after transaction: ${isApprovedNow}`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 