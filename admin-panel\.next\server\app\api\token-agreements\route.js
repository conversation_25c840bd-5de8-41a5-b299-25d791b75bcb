/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/token-agreements/route";
exports.ids = ["app/api/token-agreements/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftoken-agreements%2Froute&page=%2Fapi%2Ftoken-agreements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftoken-agreements%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftoken-agreements%2Froute&page=%2Fapi%2Ftoken-agreements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftoken-agreements%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_token_agreements_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/token-agreements/route.ts */ \"(rsc)/./src/app/api/token-agreements/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/token-agreements/route\",\n        pathname: \"/api/token-agreements\",\n        filename: \"route\",\n        bundlePath: \"app/api/token-agreements/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\token-agreements\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_token_agreements_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftoken-agreements%2Froute&page=%2Fapi%2Ftoken-agreements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftoken-agreements%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/token-agreements/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/token-agreements/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const clientEmail = searchParams.get('clientEmail');\n        const clientId = searchParams.get('clientId');\n        const tokenAddress = searchParams.get('tokenAddress');\n        const tokenSymbol = searchParams.get('tokenSymbol');\n        if (!clientEmail && !clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client email or ID required'\n            }, {\n                status: 400\n            });\n        }\n        if (!tokenAddress && !tokenSymbol) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address or symbol required'\n            }, {\n                status: 400\n            });\n        }\n        // Find client\n        let client;\n        if (clientId) {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n                where: {\n                    id: clientId\n                }\n            });\n        } else {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n                where: {\n                    email: clientEmail\n                }\n            });\n        }\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find token\n        let token = null;\n        if (tokenAddress) {\n            token = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findFirst({\n                where: {\n                    address: tokenAddress\n                }\n            });\n        }\n        // Find token agreement\n        const whereClause = {\n            clientId: client.id\n        };\n        if (token) {\n            whereClause.tokenId = token.id;\n        } else if (tokenSymbol) {\n            whereClause.tokenSymbol = tokenSymbol;\n        } else if (tokenAddress) {\n            whereClause.tokenAddress = tokenAddress;\n        }\n        const agreement = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.tokenAgreement.findFirst({\n            where: whereClause,\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        if (!agreement) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                accepted: false,\n                acceptedAt: null,\n                tokenAddress: tokenAddress,\n                tokenSymbol: tokenSymbol,\n                agreementVersion: '1.0'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            accepted: agreement.accepted,\n            acceptedAt: agreement.acceptedAt?.toISOString() || null,\n            tokenAddress: agreement.token?.address || agreement.tokenAddress,\n            tokenSymbol: agreement.token?.symbol || agreement.tokenSymbol,\n            agreementVersion: agreement.agreementVersion\n        });\n    } catch (error) {\n        console.error('Error fetching token agreement:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch token agreement'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { clientEmail, clientId, tokenAddress, tokenSymbol, accepted, agreementVersion = '1.0' } = body;\n        if (!clientEmail && !clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client email or ID required'\n            }, {\n                status: 400\n            });\n        }\n        if (!tokenAddress && !tokenSymbol) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address or symbol required'\n            }, {\n                status: 400\n            });\n        }\n        if (!accepted) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Agreement must be accepted'\n            }, {\n                status: 400\n            });\n        }\n        // Find client\n        let client;\n        if (clientId) {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n                where: {\n                    id: clientId\n                }\n            });\n        } else {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n                where: {\n                    email: clientEmail\n                }\n            });\n        }\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find token\n        let token = null;\n        if (tokenAddress) {\n            token = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findFirst({\n                where: {\n                    address: tokenAddress\n                }\n            });\n        }\n        const acceptedAt = new Date();\n        // Create or update token agreement\n        const agreementData = {\n            clientId: client.id,\n            tokenId: token?.id,\n            tokenSymbol: tokenSymbol,\n            tokenAddress: tokenAddress,\n            accepted: true,\n            acceptedAt: acceptedAt,\n            agreementVersion: agreementVersion\n        };\n        const whereClause = {\n            clientId: client.id\n        };\n        if (token) {\n            whereClause.tokenId = token.id;\n        } else if (tokenSymbol) {\n            whereClause.tokenSymbol = tokenSymbol;\n        }\n        const agreement = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.tokenAgreement.upsert({\n            where: {\n                clientId_tokenId: whereClause\n            },\n            update: agreementData,\n            create: agreementData,\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        console.log('💾 Saved token agreement to database:', {\n            clientEmail: client.email,\n            tokenAddress: token?.address || tokenAddress,\n            tokenSymbol: tokenSymbol,\n            accepted: true,\n            acceptedAt: acceptedAt.toISOString()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Token agreement saved successfully',\n            data: {\n                accepted: true,\n                acceptedAt: acceptedAt.toISOString(),\n                tokenAddress: token?.address || tokenAddress,\n                tokenSymbol: tokenSymbol,\n                agreementVersion: agreementVersion\n            }\n        });\n    } catch (error) {\n        console.error('Error saving token agreement:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save token agreement'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/token-agreements/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftoken-agreements%2Froute&page=%2Fapi%2Ftoken-agreements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftoken-agreements%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();