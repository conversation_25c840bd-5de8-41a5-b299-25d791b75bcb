// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

/**
 * @title IIdentityRegistry
 * @dev Interface for identity registry functionality for ERC-3643 compliant tokens
 */
interface IIdentityRegistry {
    /**
     * @dev Emitted when an address is added to the whitelist
     */
    event AddedToWhitelist(address indexed account);
    
    /**
     * @dev Emitted when an address is removed from the whitelist
     */
    event RemovedFromWhitelist(address indexed account);
    
    /**
     * @dev Emitted when an address is frozen
     */
    event AddressFrozen(address indexed account);
    
    /**
     * @dev Emitted when an address is unfrozen
     */
    event AddressUnfrozen(address indexed account);
    
    /**
     * @dev Check if an address is whitelisted
     * @param account The address to check
     * @return bool True if the address is whitelisted, false otherwise
     */
    function isWhitelisted(address account) external view returns (bool);
    
    /**
     * @dev Add an address to the whitelist
     * @param account The address to add to the whitelist
     */
    function addToWhitelist(address account) external;
    
    /**
     * @dev Remove an address from the whitelist
     * @param account The address to remove from the whitelist
     */
    function removeFromWhitelist(address account) external;
    
    /**
     * @dev Check if an address is frozen
     * @param account The address to check
     * @return bool True if the address is frozen, false otherwise
     */
    function isFrozen(address account) external view returns (bool);
    
    /**
     * @dev Freeze an address
     * @param account The address to freeze
     */
    function freezeAddress(address account) external;
    
    /**
     * @dev Unfreeze an address
     * @param account The address to unfreeze
     */
    function unfreezeAddress(address account) external;
    
    /**
     * @dev Batch add addresses to the whitelist
     * @param accounts The addresses to add to the whitelist
     */
    function batchAddToWhitelist(address[] calldata accounts) external;
    
    /**
     * @dev Batch remove addresses from the whitelist
     * @param accounts The addresses to remove from the whitelist
     */
    function batchRemoveFromWhitelist(address[] calldata accounts) external;
    
    /**
     * @dev Batch freeze addresses
     * @param accounts The addresses to freeze
     */
    function batchFreezeAddresses(address[] calldata accounts) external;
    
    /**
     * @dev Batch unfreeze addresses
     * @param accounts The addresses to unfreeze
     */
    function batchUnfreezeAddresses(address[] calldata accounts) external;
}