// Test whitelist tags functionality
const fetch = require('node-fetch');

async function testWithTestWallet() {
  console.log('=== Testing Client Tokens API with Test Wallet ===');
  
  const testWallet = '******************************************';
  
  try {
    const response = await fetch(`http://localhost:3003/api/tokens?testWallet=${encodeURIComponent(testWallet)}`);
    
    if (!response.ok) {
      console.log(`❌ Client tokens API failed: ${response.status}`);
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }
    
    const tokens = await response.json();
    
    console.log(`✅ Client tokens API returned ${tokens.length} tokens`);
    
    // Check whitelist status
    console.log('\nToken whitelist status:');
    tokens.forEach(token => {
      const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
      console.log(`  ${token.symbol.padEnd(10)} | ${status} | ${token.price} ${token.currency}`);
    });
    
    const whitelistedCount = tokens.filter(t => t.isWhitelisted).length;
    console.log(`\nSummary: ${whitelistedCount}/${tokens.length} tokens are whitelisted for wallet ${testWallet}`);
    
    if (whitelistedCount > 0) {
      console.log('\n🎉 SUCCESS! Whitelist tags should now appear on the offers page!');
    } else {
      console.log('\n⚠️  No tokens are whitelisted. Check the token approvals in the database.');
    }
    
  } catch (error) {
    console.error('Error testing with test wallet:', error);
  }
}

async function testWithoutWallet() {
  console.log('\n=== Testing Client Tokens API without Wallet ===');
  
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    
    if (!response.ok) {
      console.log(`❌ Client tokens API failed: ${response.status}`);
      return;
    }
    
    const tokens = await response.json();
    
    console.log(`✅ Client tokens API returned ${tokens.length} tokens`);
    
    const whitelistedCount = tokens.filter(t => t.isWhitelisted).length;
    console.log(`Summary: ${whitelistedCount}/${tokens.length} tokens are whitelisted (should be 0 without wallet)`);
    
  } catch (error) {
    console.error('Error testing without wallet:', error);
  }
}

async function checkServerLogs() {
  console.log('\n=== Check Server Logs ===');
  console.log('Look at the client server logs (terminal) to see:');
  console.log('- "User wallet address for whitelist check: ******************************************"');
  console.log('- Any whitelist API call logs');
  console.log('- Any error messages');
}

async function main() {
  await testWithoutWallet();
  await testWithTestWallet();
  await checkServerLogs();
  
  console.log('\n🎯 NEXT STEPS TO SEE WHITELISTED TAGS:');
  console.log('1. Open browser: http://localhost:3003/offers?testWallet=******************************************');
  console.log('2. Look for green "WHITELISTED" tags on token tiles');
  console.log('3. Should see 3 tokens with WHITELISTED tags (AUG019, AUG01Z, TZD)');
}

main().catch(console.error);
