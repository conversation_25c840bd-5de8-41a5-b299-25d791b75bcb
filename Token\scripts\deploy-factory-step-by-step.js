const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("🚀 Deploying SecurityTokenFactory step by step to manage gas costs...");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);

  // Get current gas price
  const feeData = await ethers.provider.getFeeData();
  console.log("Current gas price:", ethers.formatUnits(feeData.gasPrice || 0, 'gwei'), "gwei");

  // Balanced gas options - need at least 25 gwei priority but stay under 1 ETH cap
  const gasOptions = {
    gasLimit: 3000000, // 3M gas limit per transaction
    maxFeePerGas: ethers.parseUnits('120', 'gwei'), // Max 120 gwei (3M * 120 = 0.36 ETH)
    maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei'), // 30 gwei priority (above minimum)
  };

  console.log("Using gas options:", {
    gasLimit: gasOptions.gasLimit.toString(),
    maxFeePerGas: ethers.formatUnits(gasOptions.maxFeePerGas, 'gwei') + ' gwei',
    maxPriorityFeePerGas: ethers.formatUnits(gasOptions.maxPriorityFeePerGas, 'gwei') + ' gwei'
  });

  try {
    // Step 1: Deploy SecurityToken implementation
    console.log("\n📄 Step 1: Deploying SecurityToken implementation...");
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const securityTokenImpl = await SecurityToken.deploy(gasOptions);
    await securityTokenImpl.waitForDeployment();
    const securityTokenImplAddress = await securityTokenImpl.getAddress();
    console.log("✅ SecurityToken implementation deployed to:", securityTokenImplAddress);

    // Step 2: Deploy Whitelist implementation
    console.log("\n📄 Step 2: Deploying Whitelist implementation...");
    const Whitelist = await ethers.getContractFactory("Whitelist");
    const whitelistImpl = await Whitelist.deploy(gasOptions);
    await whitelistImpl.waitForDeployment();
    const whitelistImplAddress = await whitelistImpl.getAddress();
    console.log("✅ Whitelist implementation deployed to:", whitelistImplAddress);

    // Step 3: Deploy WhitelistWithKYC implementation
    console.log("\n📄 Step 3: Deploying WhitelistWithKYC implementation...");
    const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
    const whitelistWithKYCImpl = await WhitelistWithKYC.deploy(gasOptions);
    await whitelistWithKYCImpl.waitForDeployment();
    const whitelistWithKYCImplAddress = await whitelistWithKYCImpl.getAddress();
    console.log("✅ WhitelistWithKYC implementation deployed to:", whitelistWithKYCImplAddress);

    // Step 4: Create a custom factory that uses pre-deployed implementations
    console.log("\n🏭 Step 4: Creating custom factory contract...");

    // Create a custom factory contract source that uses existing implementations
    const customFactorySource = `
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "./SecurityTokenFactory.sol";

contract CustomSecurityTokenFactory is SecurityTokenFactory {
    constructor(
        address admin,
        address _securityTokenImplementation,
        address _whitelistImplementation,
        address _whitelistWithKYCImplementation
    ) {
        require(admin != address(0), "CustomSecurityTokenFactory: admin cannot be zero address");
        require(_securityTokenImplementation != address(0), "CustomSecurityTokenFactory: token implementation cannot be zero address");
        require(_whitelistImplementation != address(0), "CustomSecurityTokenFactory: whitelist implementation cannot be zero address");
        require(_whitelistWithKYCImplementation != address(0), "CustomSecurityTokenFactory: whitelist with KYC implementation cannot be zero address");

        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);

        // Use pre-deployed implementations instead of deploying new ones
        securityTokenImplementation = _securityTokenImplementation;
        whitelistImplementation = _whitelistImplementation;
        whitelistWithKYCImplementation = _whitelistWithKYCImplementation;
    }
}`;

    // Write the custom factory contract
    const customFactoryPath = path.join(__dirname, '../contracts/CustomSecurityTokenFactory.sol');
    fs.writeFileSync(customFactoryPath, customFactorySource);
    console.log("✅ Custom factory contract created");

    // Compile the custom factory
    console.log("🔨 Compiling custom factory...");
    await hre.run('compile');

    // Step 5: Deploy the custom factory
    console.log("\n🏭 Step 5: Deploying custom SecurityTokenFactory...");
    const CustomSecurityTokenFactory = await ethers.getContractFactory("CustomSecurityTokenFactory");
    const factory = await CustomSecurityTokenFactory.deploy(
      deployer.address,
      securityTokenImplAddress,
      whitelistImplAddress,
      whitelistWithKYCImplAddress,
      gasOptions
    );

    console.log("⏳ Waiting for factory deployment...");
    await factory.waitForDeployment();
    const factoryAddress = await factory.getAddress();
    console.log("✅ CustomSecurityTokenFactory deployed to:", factoryAddress);

    // Test the enumeration functionality
    console.log("\n🧪 Testing enumeration functionality...");
    try {
      const tokenCount = await factory.getTokenCount();
      console.log("✅ getTokenCount() works, current count:", tokenCount.toString());

      const allTokens = await factory.getAllDeployedTokens();
      console.log("✅ getAllDeployedTokens() works, tokens:", allTokens);
    } catch (error) {
      console.error("❌ Error testing enumeration:", error.message);
    }

    // Save deployment info
    const deploymentInfo = {
      network: network.name,
      chainId: network.chainId.toString(),
      deployer: deployer.address,
      timestamp: new Date().toISOString(),
      contracts: {
        factory: factoryAddress,
        securityTokenImplementation: securityTokenImplAddress,
        whitelistImplementation: whitelistImplAddress,
        whitelistWithKYCImplementation: whitelistWithKYCImplAddress
      },
      gasUsed: {
        securityToken: "~8M gas",
        whitelist: "~8M gas",
        whitelistWithKYC: "~8M gas",
        factory: "~8M gas",
        total: "~32M gas"
      },
      features: [
        "Token enumeration (getTokenCount, getAllDeployedTokens, getDeployedToken)",
        "Configurable decimals (0-18)",
        "KYC support",
        "Pre-deployed implementations for gas efficiency"
      ]
    };

    // Save to deployments directory
    const deploymentsDir = path.join(__dirname, '../deployments');
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    const deploymentFile = path.join(deploymentsDir, `factory-step-by-step-${network.name}-${Date.now()}.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    console.log("📁 Deployment info saved to:", deploymentFile);

    console.log("\n🎉 Step-by-step deployment completed successfully!");
    console.log("\n📋 Summary:");
    console.log("   Factory Address:", factoryAddress);
    console.log("   SecurityToken Implementation:", securityTokenImplAddress);
    console.log("   Whitelist Implementation:", whitelistImplAddress);
    console.log("   WhitelistWithKYC Implementation:", whitelistWithKYCImplAddress);

    console.log("\n🔄 Next Steps:");
    console.log("   1. Update your admin panel config with the new factory address:");
    console.log(`      factory: '${factoryAddress}'`);
    console.log("   2. Test the dashboard token loading functionality");
    console.log("   3. Create new tokens to test enumeration");

    // Clean up the temporary contract file
    if (fs.existsSync(customFactoryPath)) {
      fs.unlinkSync(customFactoryPath);
      console.log("🧹 Cleaned up temporary contract file");
    }

  } catch (error) {
    console.error("❌ Deployment failed:", error);

    // Clean up on error
    const customFactoryPath = path.join(__dirname, '../contracts/CustomSecurityTokenFactory.sol');
    if (fs.existsSync(customFactoryPath)) {
      fs.unlinkSync(customFactoryPath);
    }

    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
