const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestOrders() {
  try {
    console.log('🧪 Creating Test Orders');
    console.log('======================');

    // Find the Augment_019 token
    const token = await prisma.token.findUnique({
      where: { address: '0xfccB88D208f5Ec7166ce2291138aaD5274C671dE' }
    });

    if (!token) {
      console.log('❌ Token not found');
      return;
    }

    console.log(`✅ Found token: ${token.name} (${token.symbol})`);

    // Find a client to create orders for
    const client = await prisma.client.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!client) {
      console.log('❌ Client not found');
      return;
    }

    console.log(`✅ Found client: ${client.firstName} ${client.lastName}`);

    // Generate payment reference
    function generatePaymentReference() {
      const prefix = 'ORD';
      const timestamp = Date.now().toString(36).toUpperCase();
      const random = Math.random().toString(36).substring(2, 8).toUpperCase();
      return `${prefix}-${timestamp}-${random}`;
    }

    // Create test orders
    const orders = [
      {
        tokenId: token.id,
        clientId: client.id,
        tokensOrdered: '100',
        tokenPrice: '10 USD',
        amountToPay: '1000',
        paymentReference: generatePaymentReference(),
        status: 'CONFIRMED'
      },
      {
        tokenId: token.id,
        clientId: client.id,
        tokensOrdered: '50',
        tokenPrice: '10 USD',
        amountToPay: '500',
        tokensConfirmed: '50',
        confirmedPayment: '500',
        paymentReference: generatePaymentReference(),
        status: 'MINTED'
      },
      {
        tokenId: token.id,
        clientId: client.id,
        tokensOrdered: '25',
        tokenPrice: '10 USD',
        amountToPay: '250',
        paymentReference: generatePaymentReference(),
        status: 'CANCELLED'
      }
    ];

    console.log('\n📝 Creating orders...');
    
    for (const orderData of orders) {
      const order = await prisma.order.create({
        data: orderData,
        include: {
          token: {
            select: {
              name: true,
              symbol: true
            }
          },
          client: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      });

      console.log(`✅ Created order: ${order.tokensOrdered} ${order.token.symbol} tokens for ${order.client.firstName} ${order.client.lastName} (${order.status})`);
    }

    console.log('\n🎉 Test orders created successfully!');
    console.log('\n🌐 Test the Orders functionality:');
    console.log('1. Main Orders page: http://localhost:3000/orders');
    console.log('2. Token Orders tab: http://localhost:3000/tokens/0xfccB88D208f5Ec7166ce2291138aaD5274C671dE (click Orders tab)');

  } catch (error) {
    console.error('❌ Error creating test orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function clearTestOrders() {
  try {
    console.log('🧹 Clearing Test Orders');
    console.log('=======================');

    const deletedOrders = await prisma.order.deleteMany({});
    console.log(`✅ Deleted ${deletedOrders.count} orders`);

  } catch (error) {
    console.error('❌ Error clearing orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Check command line arguments
const args = process.argv.slice(2);

if (args.includes('--clear')) {
  clearTestOrders();
} else {
  createTestOrders();
}
