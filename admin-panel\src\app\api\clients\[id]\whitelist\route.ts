import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateWhitelistSchema } from '@/lib/validations/client';

// PUT /api/clients/[id]/whitelist - Update whitelist status
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const validatedData = updateWhitelistSchema.parse({ ...body, clientId: id });

    const updateData: any = {
      walletAddress: validatedData.walletAddress,
      isWhitelisted: validatedData.isWhitelisted,
      updatedAt: new Date(),
    };

    // Set whitelisted date if being whitelisted
    if (validatedData.isWhitelisted) {
      updateData.whitelistedAt = new Date();
    } else {
      updateData.whitelistedAt = null;
    }

    const client = await prisma.client.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        walletAddress: true,
        isWhitelisted: true,
        whitelistedAt: true,
        updatedAt: true,
      },
    });

    // TODO: Integrate with blockchain whitelist contract
    // This would call the actual smart contract to add/remove from whitelist
    // For now, we're just updating the database

    return NextResponse.json(client);
  } catch (error) {
    console.error('Error updating whitelist status:', error);

    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Wallet address already exists for another client' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update whitelist status' },
      { status: 500 }
    );
  }
}

// GET /api/clients/[id]/whitelist - Get whitelist status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const client = await prisma.client.findUnique({
      where: { id },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        walletAddress: true,
        isWhitelisted: true,
        whitelistedAt: true,
        kycStatus: true,
      },
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(client);
  } catch (error) {
    console.error('Error fetching whitelist status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch whitelist status' },
      { status: 500 }
    );
  }
}
