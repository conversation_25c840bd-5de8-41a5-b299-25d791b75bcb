'use client';

import { useState, useEffect } from 'react';
import IdentityManagement from '@/components/IdentityManagement';
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  DocumentCheckIcon,
  CogIcon,
  ChartBarIcon,
  CircleStackIcon
} from '@heroicons/react/24/outline';

interface ContractInfo {
  address: string;
  name: string;
  status: 'connected' | 'error' | 'unknown';
}

export default function IdentityPage() {
  const [contracts, setContracts] = useState<ContractInfo[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [batchAddresses, setBatchAddresses] = useState('');
  const [batchLoading, setBatchLoading] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [activeTab, setActiveTab] = useState('individual');

  useEffect(() => {
    fetchContractInfo();
    fetchStats();
  }, []);

  const fetchContractInfo = async () => {
    const contractAddresses = [
      { 
        address: process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS || process.env.CLAIM_REGISTRY_ADDRESS || '', 
        name: 'ClaimRegistry' 
      },
      { 
        address: process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS || process.env.IDENTITY_REGISTRY_ADDRESS || '', 
        name: 'IdentityRegistry' 
      },
      { 
        address: process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS || process.env.COMPLIANCE_ADDRESS || '', 
        name: 'Compliance' 
      }
    ];

    const contractInfo = contractAddresses.map(contract => ({
      ...contract,
      status: contract.address ? 'connected' as const : 'error' as const
    }));

    setContracts(contractInfo);
  };

  const fetchStats = async () => {
    try {
      // This would typically fetch from your API
      // For now, we'll use placeholder data
      setStats({
        totalVerified: 0,
        totalWhitelisted: 0,
        totalKycApproved: 0,
        totalClaims: 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const performBatchAction = async (action: string) => {
    const addresses = batchAddresses
      .split('\n')
      .map(addr => addr.trim())
      .filter(addr => addr.length > 0);

    if (addresses.length === 0) {
      setMessage({ type: 'error', text: 'Please enter at least one address' });
      return;
    }

    if (addresses.length > 50) {
      setMessage({ type: 'error', text: 'Maximum 50 addresses allowed per batch' });
      return;
    }

    setBatchLoading(action);
    setMessage(null);

    try {
      const response = await fetch('/api/identity/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          addresses
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: `Batch ${action} completed: ${data.summary.successful}/${data.summary.total} successful` 
        });
        setBatchAddresses('');
        fetchStats(); // Refresh stats
      } else {
        throw new Error(data.error || 'Batch action failed');
      }
    } catch (error) {
      console.error(`Error performing batch ${action}:`, error);
      setMessage({ type: 'error', text: `Failed to perform batch ${action}: ${error.message}` });
    } finally {
      setBatchLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return (
          <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
            <CheckCircleIcon className="w-3 h-3 mr-1" />Connected
          </span>
        );
      case 'error':
        return (
          <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
            <XCircleIcon className="w-3 h-3 mr-1" />Not Configured
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
            <ExclamationTriangleIcon className="w-3 h-3 mr-1" />Unknown
          </span>
        );
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">ERC-3643 Identity Management</h1>
        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full border border-gray-300 text-gray-700">
          Compliance System v3.0
        </span>
      </div>

      {message && (
        <div className={`p-4 rounded-md ${
          message.type === 'error'
            ? 'bg-red-100 border border-red-400 text-red-700'
            : 'bg-green-100 border border-green-400 text-green-700'
        }`}>
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
            <span>{message.text}</span>
          </div>
        </div>
      )}

      {/* Custom Tabs */}
      <div className="space-y-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'individual', label: 'Individual Management' },
              { id: 'batch', label: 'Batch Operations' },
              { id: 'stats', label: 'Statistics' },
              { id: 'system', label: 'System Status' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'individual' && (
          <div className="space-y-6">
            <IdentityManagement onStatusUpdate={fetchStats} />
          </div>
        )}

        {activeTab === 'batch' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center mb-4">
                <UserGroupIcon className="w-5 h-5 mr-2 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Batch Operations</h3>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Wallet Addresses (one per line, max 50)
                  </label>
                  <textarea
                    placeholder="0x1234...&#10;0x5678...&#10;0x9abc..."
                    value={batchAddresses}
                    onChange={(e) => setBatchAddresses(e.target.value)}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {batchAddresses.split('\n').filter(addr => addr.trim().length > 0).length} addresses
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <button
                    onClick={() => performBatchAction('batch_whitelist')}
                    disabled={!!batchLoading}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {batchLoading === 'batch_whitelist' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                    ) : (
                      'Batch Whitelist'
                    )}
                  </button>

                  <button
                    onClick={() => performBatchAction('batch_approve_kyc')}
                    disabled={!!batchLoading}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {batchLoading === 'batch_approve_kyc' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                    ) : (
                      'Batch Approve KYC'
                    )}
                  </button>

                  <button
                    onClick={() => performBatchAction('batch_unwhitelist')}
                    disabled={!!batchLoading}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {batchLoading === 'batch_unwhitelist' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                    ) : (
                      'Batch Remove Whitelist'
                    )}
                  </button>

                  <button
                    onClick={() => performBatchAction('batch_issue_qualification_claims')}
                    disabled={!!batchLoading}
                    className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {batchLoading === 'batch_issue_qualification_claims' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                    ) : (
                      'Issue Qualification Claims'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'stats' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">Total Verified</h4>
                  <ShieldCheckIcon className="h-4 w-4 text-gray-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{loading ? '...' : stats?.totalVerified || 0}</div>
                <p className="text-xs text-gray-500">Registered identities</p>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">Total Whitelisted</h4>
                  <UserGroupIcon className="h-4 w-4 text-gray-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{loading ? '...' : stats?.totalWhitelisted || 0}</div>
                <p className="text-xs text-gray-500">Approved for trading</p>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">KYC Approved</h4>
                  <DocumentCheckIcon className="h-4 w-4 text-gray-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{loading ? '...' : stats?.totalKycApproved || 0}</div>
                <p className="text-xs text-gray-500">Passed verification</p>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">Total Claims</h4>
                  <ChartBarIcon className="h-4 w-4 text-gray-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{loading ? '...' : stats?.totalClaims || 0}</div>
                <p className="text-xs text-gray-500">Issued credentials</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'system' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center mb-4">
                <CogIcon className="w-5 h-5 mr-2 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Contract Status</h3>
              </div>

              <div className="space-y-4">
                {contracts.map((contract, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{contract.name}</h4>
                      <p className="text-sm text-gray-500 font-mono">{contract.address || 'Not configured'}</p>
                    </div>
                    {getStatusBadge(contract.status)}
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center mb-4">
                <CircleStackIcon className="w-5 h-5 mr-2 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Environment Configuration</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Network:</span> Polygon Amoy Testnet
                </div>
                <div>
                  <span className="font-medium text-gray-700">RPC URL:</span> {process.env.NEXT_PUBLIC_AMOY_RPC_URL || 'Not configured'}
                </div>
                <div>
                  <span className="font-medium text-gray-700">Admin Address:</span> {process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADMIN || 'Not configured'}
                </div>
                <div>
                  <span className="font-medium text-gray-700">System Version:</span> ERC-3643 v3.0.0
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
