import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'
import { polygon, polygonAmoy } from '@reown/appkit/networks'
import { QueryClient } from '@tanstack/react-query'

// 1. Get projectId from https://cloud.reown.com
export const projectId = process.env.NEXT_PUBLIC_REOWN_PROJECT_ID || 'your-project-id-here'

if (!projectId) {
  throw new Error('NEXT_PUBLIC_REOWN_PROJECT_ID is not set')
}

// 2. Configure custom networks with RPC URLs
const amoyNetwork = {
  ...polygonAmoy,
  rpcUrls: {
    default: {
      http: [process.env.NEXT_PUBLIC_AMOY_RPC_URL!]
    },
    public: {
      http: [process.env.NEXT_PUBLIC_AMOY_RPC_URL!]
    }
  }
}

const polygonNetwork = {
  ...polygon,
  rpcUrls: {
    default: {
      http: [process.env.NEXT_PUBLIC_POLYGON_RPC_URL!]
    },
    public: {
      http: [process.env.NEXT_PUBLIC_POLYGON_RPC_URL!]
    }
  }
}

// 3. Set up Wagmi adapter with AMOY as primary network
export const wagmiAdapter = new WagmiAdapter({
  networks: [amoyNetwork, polygonNetwork],
  projectId,
  ssr: true
})

// 3. Configure the metadata
const metadata = {
  name: 'Security Token Client Portal',
  description: 'Client portal for ERC-3643 security token qualification',
  url: process.env.AUTH0_BASE_URL!, // origin must match your domain & subdomain
  icons: ['https://avatars.githubusercontent.com/u/179229932']
}

// 4. Create the modal with AMOY as default network
export const modal = createAppKit({
  adapters: [wagmiAdapter],
  networks: [amoyNetwork, polygonNetwork],
  defaultNetwork: amoyNetwork,
  metadata,
  projectId,
  features: {
    analytics: true, // Optional - defaults to your Cloud configuration
    email: false, // Optional - defaults to your Cloud configuration
    socials: ['google', 'x', 'github', 'discord', 'apple'], // Optional - defaults to your Cloud configuration
    emailShowWallets: true // Optional - defaults to your Cloud configuration
  }
})

export const config = wagmiAdapter.wagmiConfig
