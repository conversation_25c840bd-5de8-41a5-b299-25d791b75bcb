const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Testing Agreement Deployment...");

  const [deployer] = await ethers.getSigners();
  console.log("Account:", deployer.address);

  // Factory address
  const factoryAddress = "******************************************";
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  console.log("Factory address:", factoryAddress);

  // Test deployment with agreement URL using deploySecurityTokenWithOptions
  console.log("\n🚀 Testing deployment with agreement URL...");
  
  const agreementUrl = "https://example.com/investment-agreement.pdf";
  const testSymbol = "AGR" + Date.now().toString().slice(-4);
  
  try {
    console.log("Deploying token with symbol:", testSymbol);
    console.log("Agreement URL:", agreementUrl);

    const deployTx = await factory.deploySecurityTokenWithOptions(
      "Agreement Test Token",
      testSymbol,
      0, // decimals
      ethers.parseUnits("1000000", 0), // maxSupply
      deployer.address, // admin
      "100 USD", // price
      "Tier 1: 5%", // bonus tiers
      "Test token with agreement functionality", // details
      "", // image URL
      agreementUrl, // agreement URL
      true // with KYC
    );

    console.log("Deployment transaction:", deployTx.hash);
    const receipt = await deployTx.wait();
    console.log("✅ Token deployed successfully!");

    // Find the TokenDeployed event
    const tokenDeployedEvent = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (tokenDeployedEvent) {
      const parsed = factory.interface.parseLog(tokenDeployedEvent);
      const tokenAddress = parsed.args.tokenAddress;
      console.log("Token deployed at:", tokenAddress);

      // Test agreement functions
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token = SecurityToken.attach(tokenAddress);

      console.log("\n🔍 Testing agreement functions...");

      // Test getAgreementUrl
      try {
        const retrievedUrl = await token.getAgreementUrl();
        console.log("✅ Agreement URL retrieved:", retrievedUrl);
        
        if (retrievedUrl === agreementUrl) {
          console.log("✅ Agreement URL matches expected value");
        } else {
          console.log("❌ Agreement URL mismatch. Expected:", agreementUrl, "Got:", retrievedUrl);
        }
      } catch (error) {
        console.log("❌ getAgreementUrl failed:", error.message);
      }

      // Test hasAcceptedAgreement (should be false initially)
      try {
        const hasAccepted = await token.hasAcceptedAgreement(deployer.address);
        console.log("✅ hasAcceptedAgreement (initial):", hasAccepted);
      } catch (error) {
        console.log("❌ hasAcceptedAgreement failed:", error.message);
      }

      // Test acceptAgreement
      try {
        console.log("\n📝 Accepting agreement...");
        const acceptTx = await token.acceptAgreement();
        console.log("Accept transaction:", acceptTx.hash);
        await acceptTx.wait();
        console.log("✅ Agreement accepted successfully");

        // Verify acceptance
        const hasAcceptedAfter = await token.hasAcceptedAgreement(deployer.address);
        console.log("✅ Agreement status after acceptance:", hasAcceptedAfter);

        // Get acceptance timestamp
        const timestamp = await token.getAgreementAcceptanceTimestamp(deployer.address);
        console.log("✅ Agreement acceptance timestamp:", timestamp.toString());

      } catch (error) {
        console.log("❌ acceptAgreement failed:", error.message);
      }

      console.log("\n🎉 AGREEMENT FEATURES TEST SUMMARY");
      console.log("==================================");
      console.log("Token Address:", tokenAddress);
      console.log("Agreement URL:", agreementUrl);
      console.log("✅ Agreement functionality is working!");

      // Save token info
      const tokenInfo = {
        name: "Agreement Test Token",
        symbol: testSymbol,
        address: tokenAddress,
        agreementUrl: agreementUrl,
        deploymentTx: deployTx.hash,
        timestamp: new Date().toISOString()
      };

      console.log("\n📄 Token Information:");
      console.log(JSON.stringify(tokenInfo, null, 2));

    } else {
      console.log("❌ Could not find TokenDeployed event");
    }

  } catch (error) {
    console.log("❌ Deployment failed:", error.message);
    
    // Try to get more details about the error
    if (error.reason) {
      console.log("Error reason:", error.reason);
    }
    if (error.data) {
      console.log("Error data:", error.data);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
