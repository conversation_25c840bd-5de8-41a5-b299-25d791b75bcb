import { ethers } from 'ethers';
import WhitelistABI from '../contracts/Whitelist.json';

/**
 * Whitelist utility functions for interacting with the Whitelist contract
 */

// Map of network names to their RPC URLs
const RPC_URLS: Record<string, string> = {
  amoy: 'https://rpc-amoy.polygon.technology/',
  polygon: 'https://polygon-rpc.com',
  unknown: 'https://rpc-amoy.polygon.technology/' // Default unknown networks to Amoy
};

/**
 * Get the actual network to use, defaults to Amoy for 'unknown' or invalid networks
 */
function getActualNetwork(network: string): string {
  return network === 'unknown' || !RPC_URLS[network] ? 'amoy' : network;
}

/**
 * Add an address to the whitelist
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToWhitelist The address to be whitelisted
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function addToWhitelist(whitelistAddress: string, addressToWhitelist: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'addToWhitelist',
    address: addressToWhitelist,
    network: actualNetwork
  });
}

/**
 * Add multiple addresses to the whitelist in a batch
 * @param whitelistAddress The address of the whitelist contract
 * @param addressesToWhitelist Array of addresses to be whitelisted
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function batchAddToWhitelist(whitelistAddress: string, addressesToWhitelist: string[], network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'batchAddToWhitelist',
    addresses: addressesToWhitelist,
    network: actualNetwork
  });
}

/**
 * Remove an address from the whitelist
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToRemove The address to be removed from whitelist
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function removeFromWhitelist(whitelistAddress: string, addressToRemove: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'removeFromWhitelist',
    address: addressToRemove,
    network: actualNetwork
  });
}

/**
 * Remove multiple addresses from the whitelist in a batch
 * @param whitelistAddress The address of the whitelist contract
 * @param addressesToRemove Array of addresses to be removed
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function batchRemoveFromWhitelist(whitelistAddress: string, addressesToRemove: string[], network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'batchRemoveFromWhitelist',
    addresses: addressesToRemove,
    network: actualNetwork
  });
}

/**
 * Freeze an address
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToFreeze The address to be frozen
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function freezeAddress(whitelistAddress: string, addressToFreeze: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'freezeAddress',
    address: addressToFreeze,
    network: actualNetwork
  });
}

/**
 * Unfreeze an address
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToUnfreeze The address to be unfrozen
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function unfreezeAddress(whitelistAddress: string, addressToUnfreeze: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'unfreezeAddress',
    address: addressToUnfreeze,
    network: actualNetwork
  });
}

/**
 * Freeze multiple addresses in a batch
 * @param whitelistAddress The address of the whitelist contract
 * @param addressesToFreeze Array of addresses to be frozen
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function batchFreezeAddresses(whitelistAddress: string, addressesToFreeze: string[], network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'batchFreezeAddresses',
    addresses: addressesToFreeze,
    network: actualNetwork
  });
}

/**
 * Unfreeze multiple addresses in a batch
 * @param whitelistAddress The address of the whitelist contract
 * @param addressesToUnfreeze Array of addresses to be unfrozen
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function batchUnfreezeAddresses(whitelistAddress: string, addressesToUnfreeze: string[], network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'batchUnfreezeAddresses',
    addresses: addressesToUnfreeze,
    network: actualNetwork
  });
}

/**
 * Check if an address is whitelisted (view function)
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToCheck The address to check whitelist status
 * @param network The network to use (amoy, polygon)
 * @returns Promise resolving to boolean indicating whitelist status
 */
export async function isWhitelisted(whitelistAddress: string, addressToCheck: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  try {
    const result = await callWhitelistAPI({
      whitelistAddress,
      action: 'isWhitelisted',
      address: addressToCheck,
      network: actualNetwork
    });
    return result.isWhitelisted;
  } catch (error) {
    console.error('Error checking whitelist status:', error);
    // Fallback to direct contract call if API fails
    return isWhitelistedDirectCall(whitelistAddress, addressToCheck, actualNetwork);
  }
}

/**
 * Check if an address is frozen (view function)
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToCheck The address to check frozen status
 * @param network The network to use (amoy, polygon)
 * @returns Promise resolving to boolean indicating frozen status
 */
export async function isFrozen(whitelistAddress: string, addressToCheck: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  try {
    const result = await callWhitelistAPI({
      whitelistAddress,
      action: 'isFrozen',
      address: addressToCheck,
      network: actualNetwork
    });
    return result.isFrozen;
  } catch (error) {
    console.error('Error checking frozen status:', error);
    // Fallback to direct contract call if API fails
    return isFrozenDirectCall(whitelistAddress, addressToCheck, actualNetwork);
  }
}

/**
 * Check if an address is KYC approved (view function)
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToCheck The address to check KYC approval status
 * @param network The network to use (amoy, polygon)
 * @returns Promise resolving to boolean indicating KYC approval status
 */
export async function isKycApproved(whitelistAddress: string, addressToCheck: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  try {
    const result = await callWhitelistAPI({
      whitelistAddress,
      action: 'isKycApproved',
      address: addressToCheck,
      network: actualNetwork
    });
    return result.isKycApproved;
  } catch (error) {
    console.error('Error checking KYC approval status:', error);
    // Fallback to direct contract call if API fails
    return isKycApprovedDirectCall(whitelistAddress, addressToCheck, actualNetwork);
  }
}

/**
 * Approve KYC for an address
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToApprove The address to approve KYC for
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function approveKyc(whitelistAddress: string, addressToApprove: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'approveKyc',
    address: addressToApprove,
    network: actualNetwork
  });
}

/**
 * Revoke KYC approval for an address
 * @param whitelistAddress The address of the whitelist contract
 * @param addressToRevoke The address to revoke KYC approval from
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function revokeKyc(whitelistAddress: string, addressToRevoke: string, network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'revokeKyc',
    address: addressToRevoke,
    network: actualNetwork
  });
}

/**
 * Batch approve KYC for multiple addresses
 * @param whitelistAddress The address of the whitelist contract
 * @param addressesToApprove Array of addresses to approve KYC for
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function batchApproveKyc(whitelistAddress: string, addressesToApprove: string[], network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'batchApproveKyc',
    addresses: addressesToApprove,
    network: actualNetwork
  });
}

/**
 * Batch revoke KYC approval for multiple addresses
 * @param whitelistAddress The address of the whitelist contract
 * @param addressesToRevoke Array of addresses to revoke KYC approval from
 * @param network The network to use (amoy, polygon)
 * @returns Transaction result object
 */
export async function batchRevokeKyc(whitelistAddress: string, addressesToRevoke: string[], network: string = 'amoy') {
  const actualNetwork = getActualNetwork(network);
  return callWhitelistAPI({
    whitelistAddress,
    action: 'batchRevokeKyc',
    addresses: addressesToRevoke,
    network: actualNetwork
  });
}

// Private helper functions

/**
 * Call the whitelist API
 * @param params Object containing API parameters
 * @returns Promise resolving to API response
 */
async function callWhitelistAPI(params: {
  whitelistAddress: string;
  action: string;
  address?: string;
  addresses?: string[];
  network?: string;
}) {
  const response = await fetch('/api/contracts/whitelist/direct', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'API call failed');
  }
  
  return data;
}

/**
 * Direct contract call fallback for isWhitelisted
 */
async function isWhitelistedDirectCall(whitelistAddress: string, addressToCheck: string, network: string = 'amoy') {
  try {
    // Get RPC URL for the specified network
    const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;
    
    // Set up provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    
    // Connect to contract
    const whitelistContract = new ethers.Contract(
      whitelistAddress,
      WhitelistABI.abi,
      provider
    );
    
    // Call view function
    return await whitelistContract.isWhitelisted(addressToCheck);
  } catch (error) {
    console.error('Error in direct contract call for isWhitelisted:', error);
    return false; // Default to false on error
  }
}

/**
 * Direct contract call fallback for isFrozen
 */
async function isFrozenDirectCall(whitelistAddress: string, addressToCheck: string, network: string = 'amoy') {
  try {
    // Get RPC URL for the specified network
    const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;
    
    // Set up provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    
    // Connect to contract
    const whitelistContract = new ethers.Contract(
      whitelistAddress,
      WhitelistABI.abi,
      provider
    );
    
    // Call view function
    return await whitelistContract.isFrozen(addressToCheck);
  } catch (error) {
    console.error('Error in direct contract call for isFrozen:', error);
    return false; // Default to false on error
  }
}

/**
 * Direct contract call fallback for isKycApproved
 */
async function isKycApprovedDirectCall(whitelistAddress: string, addressToCheck: string, network: string = 'amoy') {
  try {
    // Get RPC URL for the specified network
    const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;
    
    // Set up provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    
    // Connect to contract
    const whitelistContract = new ethers.Contract(
      whitelistAddress,
      WhitelistABI.abi,
      provider
    );
    
    // Call isKycApproved function
    const result = await whitelistContract.isKycApproved(addressToCheck);
    return result;
  } catch (error) {
    console.error('Error in direct contract call for isKycApproved:', error);
    return false;
  }
} 