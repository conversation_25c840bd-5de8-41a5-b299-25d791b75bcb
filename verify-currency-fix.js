// Verify currency fix is working correctly
const fetch = require('node-fetch');

// Replicate the formatPrice function from the client
function formatPrice(price, currency) {
  const numPrice = parseFloat(price);

  // Handle crypto currencies that don't have standard currency codes
  const cryptoCurrencies = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI'];
  if (cryptoCurrencies.includes(currency.toUpperCase())) {
    return `${numPrice} ${currency.toUpperCase()}`;
  }

  // Handle standard fiat currencies
  const supportedCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
  const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(numPrice);
}

async function verifyCurrencyFix() {
  console.log('=== Verifying Currency Fix ===');
  
  try {
    const response = await fetch('http://localhost:3003/api/tokens');
    const tokens = await response.json();
    
    console.log(`Found ${tokens.length} tokens\n`);
    
    let currencyTestResults = {
      USD: { count: 0, working: true },
      EUR: { count: 0, working: true },
      BTC: { count: 0, working: true },
      ETH: { count: 0, working: true },
      OTHER: { count: 0, working: true }
    };
    
    console.log('Currency formatting verification:');
    
    tokens.forEach(token => {
      const formatted = formatPrice(token.price, token.currency);
      const currency = token.currency.toUpperCase();
      
      console.log(`${token.symbol.padEnd(8)} | ${token.price.padEnd(8)} ${currency.padEnd(3)} → ${formatted}`);
      
      // Track currency types
      if (currency === 'USD') {
        currencyTestResults.USD.count++;
        if (!formatted.includes('$')) currencyTestResults.USD.working = false;
      } else if (currency === 'EUR') {
        currencyTestResults.EUR.count++;
        if (!formatted.includes('€')) currencyTestResults.EUR.working = false;
      } else if (currency === 'BTC') {
        currencyTestResults.BTC.count++;
        if (!formatted.includes('BTC')) currencyTestResults.BTC.working = false;
      } else if (currency === 'ETH') {
        currencyTestResults.ETH.count++;
        if (!formatted.includes('ETH')) currencyTestResults.ETH.working = false;
      } else {
        currencyTestResults.OTHER.count++;
      }
    });
    
    console.log('\n=== Currency Test Results ===');
    
    Object.entries(currencyTestResults).forEach(([currency, result]) => {
      if (result.count > 0) {
        const status = result.working ? '✅ WORKING' : '❌ FAILED';
        console.log(`${currency}: ${result.count} tokens - ${status}`);
      }
    });
    
    // Check if we have variety
    const uniqueCurrencies = [...new Set(tokens.map(t => t.currency))];
    console.log(`\nUnique currencies: ${uniqueCurrencies.join(', ')}`);
    
    if (uniqueCurrencies.length >= 3) {
      console.log('✅ Good currency variety for testing');
    } else {
      console.log('ℹ️  Limited currency variety');
    }
    
    // Overall assessment
    const allWorking = Object.values(currencyTestResults).every(r => r.count === 0 || r.working);
    console.log(`\n🎯 OVERALL CURRENCY FIX STATUS: ${allWorking ? '✅ WORKING CORRECTLY' : '❌ ISSUES DETECTED'}`);
    
    if (allWorking) {
      console.log('\n🎉 SUCCESS! Currency display is now working correctly:');
      console.log('   - USD tokens show with $ symbol');
      console.log('   - EUR tokens show with € symbol');
      console.log('   - BTC tokens show as "X BTC"');
      console.log('   - ETH tokens show as "X ETH"');
      console.log('   - No more incorrect $ symbols for non-USD currencies');
    }
    
  } catch (error) {
    console.error('❌ Error verifying currency fix:', error.message);
  }
}

verifyCurrencyFix();
