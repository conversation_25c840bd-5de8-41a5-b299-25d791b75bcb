{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220fb8720b26db07c01717f156a729c9fcef0507559f2ff0ee85811c23aca84d99264736f6c63430008160033", "deployedBytecode": "0x600080fdfea2646970667358221220fb8720b26db07c01717f156a729c9fcef0507559f2ff0ee85811c23aca84d99264736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}