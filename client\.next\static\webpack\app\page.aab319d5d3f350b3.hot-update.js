"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard.tsx":
/*!**************************************!*\
  !*** ./src/components/Dashboard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _KYCModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./KYCModal */ \"(app-pages-browser)/./src/components/KYCModal.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/MockAuthProvider */ \"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const mockAuth = useMockAuth ? (0,_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showKYCModal, setShowKYCModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOrderModal, setShowOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTokenForOrder, setSelectedTokenForOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderAmount, setOrderAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmittingOrder, setIsSubmittingOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderError, setOrderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"Dashboard.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"Dashboard.useQuery\"],\n        enabled: !!user\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (user) {\n                fetchTokens();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress,\n        user\n    ]); // Refetch when wallet address changes\n    const fetchTokens = async ()=>{\n        try {\n            setLoading(true);\n            // Construct URL with proper query parameters\n            const params = new URLSearchParams();\n            if (clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress) {\n                params.append('testWallet', clientProfile.walletAddress);\n            }\n            params.append('_t', Date.now().toString());\n            const url = \"/api/tokens?\".concat(params.toString());\n            console.log('Fetching tokens from:', url);\n            const response = await fetch(url);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Token fetch error:', response.status, errorText);\n                throw new Error(\"Failed to fetch tokens: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log('Fetched tokens:', data);\n            setTokens(data);\n        } catch (err) {\n            console.error('Error in fetchTokens:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        const numPrice = parseFloat(price);\n        // Handle crypto currencies that don't have standard currency codes\n        const cryptoCurrencies = [\n            'ETH',\n            'BTC',\n            'USDC',\n            'USDT',\n            'DAI'\n        ];\n        if (cryptoCurrencies.includes(currency.toUpperCase())) {\n            return \"\".concat(numPrice, \" \").concat(currency.toUpperCase());\n        }\n        // Handle standard fiat currencies\n        const supportedCurrencies = [\n            'USD',\n            'EUR',\n            'GBP',\n            'JPY',\n            'CAD',\n            'AUD'\n        ];\n        const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(numPrice);\n    };\n    const formatSupply = function(supply) {\n        let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const numSupply = parseFloat(supply);\n        // Handle very large numbers (like 1000000000000000000000000)\n        if (decimals > 0 && numSupply > 1000000000000) {\n            // This is likely already in wei/smallest unit, convert to human readable\n            const humanReadable = numSupply / Math.pow(10, decimals);\n            return new Intl.NumberFormat('en-US', {\n                maximumFractionDigits: 0\n            }).format(humanReadable);\n        }\n        // For normal numbers or 0 decimals, display as-is\n        return new Intl.NumberFormat('en-US', {\n            maximumFractionDigits: 0\n        }).format(numSupply);\n    };\n    const getCategoryColor = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return 'bg-amber-100 text-amber-800';\n            case 'real estate':\n            case 'realestate':\n                return 'bg-green-100 text-green-800';\n            case 'equity':\n            case 'equities':\n                return 'bg-blue-100 text-blue-800';\n            case 'debt':\n            case 'bonds':\n                return 'bg-purple-100 text-purple-800';\n            case 'fund':\n            case 'funds':\n                return 'bg-indigo-100 text-indigo-800';\n            case 'security':\n            case 'securities':\n                return 'bg-teal-100 text-teal-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getDefaultImage = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return '🏗️';\n            case 'real estate':\n            case 'realestate':\n                return '🏢';\n            case 'equity':\n            case 'equities':\n                return '📈';\n            case 'debt':\n            case 'bonds':\n                return '💰';\n            case 'fund':\n            case 'funds':\n                return '🏦';\n            case 'security':\n            case 'securities':\n                return '🛡️';\n            default:\n                return '🪙';\n        }\n    };\n    if (!user) {\n        return null; // AppLayout handles authentication\n    }\n    const handleOrderSubmit = async ()=>{\n        if (!selectedTokenForOrder || !orderAmount || !(clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.id)) {\n            setOrderError('Missing token, amount, or client information.');\n            return;\n        }\n        // Validate orderAmount is a positive number\n        const amountNumber = parseFloat(orderAmount);\n        if (isNaN(amountNumber) || amountNumber <= 0) {\n            setOrderError('Please enter a valid positive amount.');\n            return;\n        }\n        // Check if amount exceeds max supply\n        if (amountNumber > Number(selectedTokenForOrder.maxSupply)) {\n            setOrderError(\"Cannot order more than \".concat(selectedTokenForOrder.maxSupply, \" tokens\"));\n            return;\n        }\n        // Check if user is whitelisted for this token\n        if (!selectedTokenForOrder.isWhitelisted) {\n            setOrderError(\"You must be whitelisted for this token before placing an order. Please contact support to get whitelisted for \".concat(selectedTokenForOrder.name, \".\"));\n            return;\n        }\n        setIsSubmittingOrder(true);\n        setOrderError(null);\n        try {\n            const response = await fetch('/api/client-orders', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: selectedTokenForOrder.id,\n                    clientId: clientProfile.id,\n                    tokensOrdered: orderAmount\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to submit order');\n            }\n            // Order submitted successfully\n            setShowOrderModal(false);\n            setSelectedTokenForOrder(null);\n            setOrderAmount('');\n            // Show success message with order details\n            const totalAmount = formatPrice((amountNumber * Number(selectedTokenForOrder.price)).toString(), selectedTokenForOrder.currency);\n            alert(\"Order submitted successfully!\\n\\nToken: \".concat(selectedTokenForOrder.name, \"\\nAmount: \").concat(orderAmount, \" tokens\\nTotal: \").concat(totalAmount, \"\\n\\nYou will be notified once it is approved.\"));\n        } catch (err) {\n            console.error('Error submitting order:', err);\n            setOrderError(err.message || 'Failed to submit order. Please try again.');\n        } finally{\n            setIsSubmittingOrder(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Discover and invest in available security tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 mb-1\",\n                                            children: \"Wallet Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    user && clientProfile && tokens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-blue-900 mb-1\",\n                                            children: \"Available Investment Opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-700 text-sm\",\n                                            children: [\n                                                \"You have access to \",\n                                                tokens.length,\n                                                \" investment \",\n                                                tokens.length === 1 ? 'opportunity' : 'opportunities'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-900\",\n                                            children: tokens.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Available Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-red-800\",\n                                    children: \"Error loading tokens\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: tokens.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83E\\uDE99\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No tokens available\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Check back later for new investment opportunities.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: tokens.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n                                    children: token.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: token.imageUrl,\n                                        alt: token.name,\n                                        className: \"w-24 h-24 object-cover rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 23\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl\",\n                                        children: getDefaultImage(token.category)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                            children: token.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 font-mono\",\n                                                            children: token.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-1 items-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getCategoryColor(token.category)),\n                                                            children: token.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        token.needsQualification ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                            children: \"\\uD83D\\uDD12 NEEDS QUALIFICATION\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 27\n                                                        }, this) : token.hasRequiredClaims ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                                                            children: \"✅ QUALIFIED\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                            children: \"⏳ CHECKING...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: formatPrice(token.price, token.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"per token\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Total Supply\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: formatSupply(token.totalSupply, token.decimals)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Max Supply\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: formatSupply(token.maxSupply, token.decimals)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 21\n                                        }, this),\n                                        token.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4 line-clamp-2\",\n                                            children: token.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 23\n                                        }, this),\n                                        user && clientProfile ? // User is logged in and has profile\n                                        token.needsQualification ? // Token requires qualification\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                // Redirect to qualification page for this specific token\n                                                const params = new URLSearchParams({\n                                                    token: token.address,\n                                                    tokenName: token.name,\n                                                    tokenSymbol: token.symbol\n                                                });\n                                                window.location.href = \"/qualification?\".concat(params.toString());\n                                            },\n                                            className: \"w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium\",\n                                            children: \"\\uD83D\\uDD12 Complete Qualification\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 25\n                                        }, this) : token.hasRequiredClaims ? // User is qualified for this token\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setSelectedTokenForOrder(token);\n                                                setShowOrderModal(true);\n                                            },\n                                            className: \"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                            children: \"\\uD83D\\uDE80 Invest Now\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 25\n                                        }, this) : // Checking qualification status\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            disabled: true,\n                                            className: \"w-full bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium\",\n                                            children: \"⏳ Checking Qualification...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 25\n                                        }, this) : // User not logged in or no profile\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (!user) {\n                                                    // Redirect to login\n                                                    window.location.href = '/api/auth/login';\n                                                } else {\n                                                    // Show KYC modal\n                                                    setShowKYCModal(true);\n                                                }\n                                            },\n                                            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                            children: !user ? 'Sign In to Invest' : 'Complete Initial Qualification'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-3 bg-gray-50 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Network: \",\n                                                    token.network\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Decimals: \",\n                                                    token.decimals\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, token.id, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 17\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false),\n            showKYCModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KYCModal__WEBPACK_IMPORTED_MODULE_3__.KYCModal, {\n                onClose: ()=>setShowKYCModal(false),\n                existingProfile: clientProfile\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 497,\n                columnNumber: 9\n            }, this),\n            showOrderModal && selectedTokenForOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900\",\n                                        children: [\n                                            \"Order \",\n                                            selectedTokenForOrder.name,\n                                            \" (\",\n                                            selectedTokenForOrder.symbol,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Token:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.name,\n                                                            \" (\",\n                                                            selectedTokenForOrder.symbol,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formatPrice(selectedTokenForOrder.price, selectedTokenForOrder.currency),\n                                                            \" per token\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Available:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.maxSupply,\n                                                            \" tokens\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"order-amount\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Number of Tokens to Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"order-amount\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                step: \"1\",\n                                                value: orderAmount,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    setOrderAmount(value);\n                                                    setOrderError(null);\n                                                },\n                                                placeholder: \"Enter amount of tokens\",\n                                                className: \"block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, this),\n                                    orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" \",\n                                                    (()=>{\n                                                        const tokenPrice = Number(selectedTokenForOrder.price);\n                                                        const orderQty = Number(orderAmount);\n                                                        if (isNaN(tokenPrice) || isNaN(orderQty)) {\n                                                            return \"Error: Price=\".concat(selectedTokenForOrder.price, \", Qty=\").concat(orderAmount);\n                                                        }\n                                                        const total = orderQty * tokenPrice;\n                                                        return formatPrice(total.toString(), selectedTokenForOrder.currency);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: \"This order will be submitted for admin approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 19\n                                    }, this),\n                                    orderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: orderError\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOrderSubmit,\n                                        disabled: isSubmittingOrder || !orderAmount,\n                                        className: \"w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmittingOrder ? 'Submitting...' : 'Submit Order'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 505,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {}, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                    lineNumber: 627,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 626,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"ApMYDulHuTrKf3ifJxh2CXa6xLY=\", false, function() {\n    return [\n        _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser,\n        _providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_5__.useMockUser,\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_4__.useApiClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard.tsx\n"));

/***/ })

});