@echo off
setlocal

REM Direct Token Minting Script for Amoy Testnet
echo ===== DIRECT TOKEN MINTING FOR AMOY =====
echo This script uses optimized gas parameters and multiple fallbacks
echo.

REM Required params - token address
if "%~1"=="" (
  set /p TOKEN_ADDRESS=Enter token address: 
) else (
  set TOKEN_ADDRESS=%~1
)

REM Required params - recipient address
if "%~2"=="" (
  set /p TO_ADDRESS=Enter recipient address: 
) else (
  set TO_ADDRESS=%~2
)

REM Required params - amount
if "%~3"=="" (
  set /p AMOUNT=Enter amount to mint: 
) else (
  set AMOUNT=%~3
)

REM Optional params - gas settings
if not "%~4"=="" (
  set GAS_LIMIT=%~4
) else (
  set GAS_LIMIT=5000000
)

if not "%~5"=="" (
  set GAS_PRICE=%~5
) else (
  set GAS_PRICE=50
)

REM Display configuration
echo.
echo Token Address: %TOKEN_ADDRESS%
echo Recipient: %TO_ADDRESS%
echo Amount: %AMOUNT%
echo Gas Limit: %GAS_LIMIT%
echo Gas Price: %GAS_PRICE% gwei

REM Set environment variables for script
set "TOKEN_ADDRESS=%TOKEN_ADDRESS%"
set "TO_ADDRESS=%TO_ADDRESS%"
set "AMOUNT=%AMOUNT%"
set "GAS_LIMIT=%GAS_LIMIT%"
set "GAS_PRICE=%GAS_PRICE%"

echo.
echo Press any key to execute token minting (Ctrl+C to cancel)...
pause > nul

echo.
echo Executing direct token minting...
npx hardhat run scripts/direct-mint.js --network amoy

echo.
echo Minting attempt completed.
echo See above for results.

endlocal 