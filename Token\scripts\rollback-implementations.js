const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 Rolling back to previous working implementations...");

  const [deployer] = await ethers.getSigners();
  console.log("Account:", deployer.address);

  // Factory address
  const factoryAddress = "******************************************";
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  console.log("Factory address:", factoryAddress);

  // Previous working implementations
  const previousTokenImpl = "******************************************";
  const previousWhitelistImpl = "******************************************";
  const previousWhitelistKYCImpl = "******************************************";

  console.log("\n🔄 Rolling back to previous implementations...");
  console.log("Previous SecurityToken:", previousTokenImpl);
  console.log("Previous Whitelist:", previousWhitelistImpl);
  console.log("Previous WhitelistWithKYC:", previousWhitelistKYCImpl);

  try {
    const rollbackTx = await factory.updateImplementations(
      previousTokenImpl,
      previousWhitelistImpl,
      previousWhitelistKYCImpl
    );

    console.log("Rollback transaction:", rollbackTx.hash);
    await rollbackTx.wait();
    console.log("✅ Rollback completed");

    // Verify rollback
    const currentTokenImpl = await factory.securityTokenImplementation();
    const currentWhitelistImpl = await factory.whitelistImplementation();
    const currentWhitelistKYCImpl = await factory.whitelistWithKYCImplementation();

    console.log("\n✅ Current implementations after rollback:");
    console.log("SecurityToken:", currentTokenImpl);
    console.log("Whitelist:", currentWhitelistImpl);
    console.log("WhitelistWithKYC:", currentWhitelistKYCImpl);

    // Test deployment with rolled back implementations
    console.log("\n🧪 Testing deployment with rolled back implementations...");
    
    const testSymbol = "RBK" + Date.now().toString().slice(-4);
    
    try {
      const deployTx = await factory.deploySecurityToken(
        "Rollback Test Token",
        testSymbol,
        0,
        ethers.parseUnits("1000", 0),
        deployer.address,
        "10 USD",
        "Tier 1: 5%",
        "Test token after rollback",
        ""
      );

      console.log("Test deployment transaction:", deployTx.hash);
      const receipt = await deployTx.wait();
      console.log("✅ Test deployment successful with rolled back implementations");

      // Find the token address
      const tokenDeployedEvent = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === 'TokenDeployed';
        } catch {
          return false;
        }
      });

      if (tokenDeployedEvent) {
        const parsed = factory.interface.parseLog(tokenDeployedEvent);
        const tokenAddress = parsed.args.tokenAddress;
        console.log("Token deployed at:", tokenAddress);
      }

    } catch (deployError) {
      console.log("❌ Test deployment still failed:", deployError.message);
    }

  } catch (error) {
    console.log("❌ Rollback failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Rollback failed:", error);
    process.exit(1);
  });
