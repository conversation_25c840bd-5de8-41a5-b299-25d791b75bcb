const fs = require('fs');
const path = require('path');

// Read the SecurityTokenFactory artifact
const factoryArtifact = JSON.parse(fs.readFileSync('artifacts/contracts/SecurityTokenFactory.sol/SecurityTokenFactory.json', 'utf8'));

// Extract just the ABI
const factoryAbiOnly = {
  abi: factoryArtifact.abi
};

// Write to admin panel contracts directory (go up one level from Token folder)
fs.writeFileSync('../admin-panel/src/contracts/SecurityTokenFactory.json', JSON.stringify(factoryAbiOnly, null, 2));

console.log('✅ SecurityTokenFactory ABI extracted and saved to admin panel');
console.log('📊 Functions:', factoryArtifact.abi.filter(item => item.type === 'function').length);
console.log('📅 Events:', factoryArtifact.abi.filter(item => item.type === 'event').length);

// Also extract SecurityToken ABI
const tokenArtifact = JSON.parse(fs.readFileSync('artifacts/contracts/SecurityToken.sol/SecurityToken.json', 'utf8'));

const tokenAbiOnly = {
  abi: tokenArtifact.abi
};

fs.writeFileSync('../admin-panel/src/contracts/SecurityToken.json', JSON.stringify(tokenAbiOnly, null, 2));

console.log('✅ SecurityToken ABI also extracted and saved');
