const fetch = require('node-fetch');

async function testAdminPanel() {
  console.log('🔍 Testing Admin Panel API...');
  
  const baseUrl = 'http://localhost:3000/api';
  
  console.log('\n1. Testing status endpoint...');
  try {
    const response = await fetch(`${baseUrl}/status`);
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Status response:', JSON.stringify(data, null, 2));
    } else {
      console.log(`❌ Status endpoint failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Status endpoint error: ${error.message}`);
    console.log('   Make sure admin panel is running: cd admin-panel && npm run dev');
    return;
  }
  
  console.log('\n2. Testing clients endpoint...');
  try {
    const response = await fetch(`${baseUrl}/clients?limit=5`);
    console.log(`Clients API Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Clients response:');
      console.log(`   Total clients: ${data.pagination?.total || 0}`);
      console.log(`   Clients returned: ${data.clients?.length || 0}`);
      
      if (data.clients && data.clients.length > 0) {
        console.log('   Sample client:', {
          email: data.clients[0].email,
          name: `${data.clients[0].firstName} ${data.clients[0].lastName}`,
          agreementAccepted: data.clients[0].agreementAccepted,
        });
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ Clients endpoint failed: ${response.status} - ${errorText}`);
    }
  } catch (error) {
    console.log(`❌ Clients endpoint error: ${error.message}`);
  }
  
  console.log('\n3. Testing search functionality...');
  try {
    const response = await fetch(`${baseUrl}/clients?search=<EMAIL>&limit=1`);
    console.log(`Search API Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Search response:');
      console.log(`   Found clients: ${data.clients?.length || 0}`);
      
      if (data.clients && data.clients.length > 0) {
        console.log('   Found client:', {
          email: data.clients[0].email,
          agreementAccepted: data.clients[0].agreementAccepted,
          agreementAcceptedAt: data.clients[0].agreementAcceptedAt,
        });
      }
    } else {
      console.log(`❌ Search failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Search error: ${error.message}`);
  }
  
  console.log('\n4. Testing CORS headers...');
  try {
    const response = await fetch(`${baseUrl}/clients`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:7788',
        'Access-Control-Request-Method': 'GET',
      },
    });
    
    console.log(`CORS preflight Status: ${response.status}`);
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
    };
    
    console.log('✅ CORS headers:', corsHeaders);
    
    if (corsHeaders['Access-Control-Allow-Origin'] === 'http://localhost:7788') {
      console.log('✅ CORS properly configured for client portal');
    } else {
      console.log('❌ CORS not properly configured');
    }
  } catch (error) {
    console.log(`❌ CORS test error: ${error.message}`);
  }
  
  console.log('\n📋 SUMMARY:');
  console.log('===========');
  console.log('If all tests pass, the admin panel API is working correctly.');
  console.log('The client portal should now be able to fetch data from the admin panel.');
  console.log('');
  console.log('🌐 Admin Panel URLs:');
  console.log('   - Status: http://localhost:3000/api/status');
  console.log('   - Clients: http://localhost:3000/clients');
  console.log('   - API: http://localhost:3000/api/clients');
  console.log('');
  console.log('🌐 Client Portal:');
  console.log('   - Main: http://localhost:7788');
  console.log('   - Status: http://localhost:7788/api/status');
}

testAdminPanel().catch(console.error);
