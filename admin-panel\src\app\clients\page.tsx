'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Link from 'next/link';
import ClientDashboard from '@/components/ClientDashboard';
import ClientManagement from '@/components/ClientManagement';
import CreateClientForm from '@/components/CreateClientForm';

const queryClient = new QueryClient();

export default function ClientsPage() {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'management'>('dashboard');
  const [showCreateForm, setShowCreateForm] = useState(false);

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Navigation Tabs */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'dashboard'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Dashboard
              </button>
              <button
                onClick={() => setActiveTab('management')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'management'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Client Management
              </button>
              <Link
                href="/claims"
                className="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm"
              >
                Blockchain Claims
              </Link>
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'dashboard' && <ClientDashboard />}
          {activeTab === 'management' && <ClientManagement />}

          {/* Create Client Form Modal */}
          {showCreateForm && (
            <CreateClientForm
              onClose={() => setShowCreateForm(false)}
              onSuccess={() => {
                setShowCreateForm(false);
                // Optionally switch to management tab to see the new client
                setActiveTab('management');
              }}
            />
          )}

          {/* Floating Action Button */}
          <button
            onClick={() => setShowCreateForm(true)}
            className="fixed bottom-8 right-8 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
            title="Add New Client"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
      </div>
    </QueryClientProvider>
  );
}
