"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                const newStepData = {\n                    country: qualificationProgress.country || '',\n                    agreementAccepted: qualificationProgress.agreementAccepted || false,\n                    profileCompleted: qualificationProgress.profileCompleted || !!profile,\n                    walletConnected: qualificationProgress.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: qualificationProgress.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = qualificationProgress.currentStep || 0;\n                // Validate and adjust step based on actual completion status\n                if (!newStepData.country) {\n                    calculatedStep = 0; // Country selection\n                } else if (!newStepData.agreementAccepted) {\n                    calculatedStep = 1; // Agreement acceptance\n                } else if (!newStepData.profileCompleted) {\n                    calculatedStep = 2; // Profile completion\n                } else if (!newStepData.walletConnected) {\n                    calculatedStep = 3; // Wallet connection\n                } else if (!newStepData.kycCompleted) {\n                    calculatedStep = 4; // KYC verification\n                } else {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: qualificationProgress\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            const progressData = {\n                ...updatedStepData,\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: Object.values(updatedStepData).filter(Boolean).length\n            };\n            console.log('💾 Saving progress:', progressData);\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = (country)=>{\n        setStepData((prev)=>({\n                ...prev,\n                country\n            }));\n        setCurrentStep(1);\n    };\n    const handleAgreementComplete = ()=>{\n        setStepData((prev)=>({\n                ...prev,\n                agreementAccepted: true\n            }));\n        setCurrentStep(2);\n    };\n    const handleProfileComplete = ()=>{\n        setStepData((prev)=>({\n                ...prev,\n                profileCompleted: true\n            }));\n        setCurrentStep(3);\n    };\n    const handleWalletComplete = ()=>{\n        setStepData((prev)=>({\n                ...prev,\n                walletConnected: true\n            }));\n        setCurrentStep(4);\n    };\n    const handleKYCStatusChange = (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            setStepData((prev)=>({\n                    ...prev,\n                    kycCompleted: true\n                }));\n            setCurrentStep(5);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border text-center \".concat(getStepColor(step.status)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Complete!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". You can now proceed with your investment.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});