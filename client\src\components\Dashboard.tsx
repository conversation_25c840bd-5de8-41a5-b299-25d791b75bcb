'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useQuery } from '@tanstack/react-query';
import { KYCModal } from './KYCModal';
import { useApiClient } from '@/lib/api-client';
import { useMockUser } from './providers/MockAuthProvider';

interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  totalSupply: string;
  maxSupply: string;
  price: string;
  currency: string;
  category: string;
  description?: string;
  imageUrl?: string;
  network: string;
  decimals: number;
  version: string;
  bonusTiers?: string;
  whitelistAddress: string;
  createdAt: string;
  isWhitelisted: boolean;
  // New qualification fields
  hasRequiredClaims?: boolean;
  needsQualification?: boolean;
  requiredClaims?: string[];
  userClaims?: { [claimType: string]: boolean };
}

export function Dashboard() {
  const useMockAuth = process.env.NEXT_PUBLIC_USE_MOCK_AUTH === 'true';

  // Use mock auth or real Auth0 based on environment
  const auth0User = useUser();
  const mockAuth = useMockAuth ? useMockUser() : { user: undefined, isLoading: false };

  const user = useMockAuth ? mockAuth.user : auth0User.user;
  const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;

  const [tokens, setTokens] = useState<Token[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showKYCModal, setShowKYCModal] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [selectedTokenForOrder, setSelectedTokenForOrder] = useState<Token | null>(null);
  const [orderAmount, setOrderAmount] = useState('');
  const [isSubmittingOrder, setIsSubmittingOrder] = useState(false);
  const [orderError, setOrderError] = useState<string | null>(null);
  const apiClient = useApiClient();

  // Fetch client profile
  const { data: clientProfile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: () => apiClient.getClientProfile(),
    enabled: !!user,
  });

  useEffect(() => {
    if (user) {
      fetchTokens();
    }
  }, [clientProfile?.walletAddress, user]); // Refetch when wallet address changes

  const fetchTokens = async () => {
    try {
      setLoading(true);

      // Construct URL with proper query parameters
      const params = new URLSearchParams();
      if (clientProfile?.walletAddress) {
        params.append('testWallet', clientProfile.walletAddress);
      }
      params.append('_t', Date.now().toString());

      const url = `/api/tokens?${params.toString()}`;
      console.log('Fetching tokens from:', url);

      const response = await fetch(url);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Token fetch error:', response.status, errorText);
        throw new Error(`Failed to fetch tokens: ${response.status}`);
      }

      const data = await response.json();
      console.log('Fetched tokens:', data);
      setTokens(data);
    } catch (err) {
      console.error('Error in fetchTokens:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: string, currency: string) => {
    const numPrice = parseFloat(price);

    // Handle crypto currencies that don't have standard currency codes
    const cryptoCurrencies = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI'];
    if (cryptoCurrencies.includes(currency.toUpperCase())) {
      return `${numPrice} ${currency.toUpperCase()}`;
    }

    // Handle standard fiat currencies
    const supportedCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
    const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(numPrice);
  };

  const formatSupply = (supply: string, decimals: number = 0) => {
    const numSupply = parseFloat(supply);

    // Handle very large numbers (like 1000000000000000000000000)
    if (decimals > 0 && numSupply > 1000000000000) {
      // This is likely already in wei/smallest unit, convert to human readable
      const humanReadable = numSupply / Math.pow(10, decimals);
      return new Intl.NumberFormat('en-US', {
        maximumFractionDigits: 0
      }).format(humanReadable);
    }

    // For normal numbers or 0 decimals, display as-is
    return new Intl.NumberFormat('en-US', {
      maximumFractionDigits: 0
    }).format(numSupply);
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'commodity':
      case 'commodities':
        return 'bg-amber-100 text-amber-800';
      case 'real estate':
      case 'realestate':
        return 'bg-green-100 text-green-800';
      case 'equity':
      case 'equities':
        return 'bg-blue-100 text-blue-800';
      case 'debt':
      case 'bonds':
        return 'bg-purple-100 text-purple-800';
      case 'fund':
      case 'funds':
        return 'bg-indigo-100 text-indigo-800';
      case 'security':
      case 'securities':
        return 'bg-teal-100 text-teal-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDefaultImage = (category: string) => {
    switch (category.toLowerCase()) {
      case 'commodity':
      case 'commodities':
        return '🏗️';
      case 'real estate':
      case 'realestate':
        return '🏢';
      case 'equity':
      case 'equities':
        return '📈';
      case 'debt':
      case 'bonds':
        return '💰';
      case 'fund':
      case 'funds':
        return '🏦';
      case 'security':
      case 'securities':
        return '🛡️';
      default:
        return '🪙';
    }
  };

  if (!user) {
    return null; // AppLayout handles authentication
  }

  const handleOrderSubmit = async () => {
    if (!selectedTokenForOrder || !orderAmount || !clientProfile?.id) {
      setOrderError('Missing token, amount, or client information.');
      return;
    }

    // Validate orderAmount is a positive number
    const amountNumber = parseFloat(orderAmount);
    if (isNaN(amountNumber) || amountNumber <= 0) {
      setOrderError('Please enter a valid positive amount.');
      return;
    }

    // Check if amount exceeds max supply
    if (amountNumber > Number(selectedTokenForOrder.maxSupply)) {
      setOrderError(`Cannot order more than ${selectedTokenForOrder.maxSupply} tokens`);
      return;
    }

    // Check if user is whitelisted for this token
    if (!selectedTokenForOrder.isWhitelisted) {
      setOrderError(`You must be whitelisted for this token before placing an order. Please contact support to get whitelisted for ${selectedTokenForOrder.name}.`);
      return;
    }

    setIsSubmittingOrder(true);
    setOrderError(null);

    try {
      const response = await fetch('/api/client-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenId: selectedTokenForOrder.id,
          clientId: clientProfile.id,
          tokensOrdered: orderAmount,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit order');
      }

      // Order submitted successfully
      setShowOrderModal(false);
      setSelectedTokenForOrder(null);
      setOrderAmount('');

      // Show success message with order details
      const totalAmount = formatPrice((amountNumber * Number(selectedTokenForOrder.price)).toString(), selectedTokenForOrder.currency);
      alert(`Order submitted successfully!\n\nToken: ${selectedTokenForOrder.name}\nAmount: ${orderAmount} tokens\nTotal: ${totalAmount}\n\nYou will be notified once it is approved.`);

    } catch (err: any) {
      console.error('Error submitting order:', err);
      setOrderError(err.message || 'Failed to submit order. Please try again.');
    } finally {
      setIsSubmittingOrder(false);
    }
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
            <p className="text-gray-600">
              Discover and invest in available security tokens
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Wallet Connection Status */}
            <div className="text-right">
              <div className="text-sm text-gray-500 mb-1">Wallet Status</div>
              <w3m-button size="sm" />
            </div>
          </div>
        </div>

        {/* Simple Investment Summary */}
        {user && clientProfile && tokens.length > 0 && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-1">
                  Available Investment Opportunities
                </h3>
                <p className="text-blue-700 text-sm">
                  You have access to {tokens.length} investment {tokens.length === 1 ? 'opportunity' : 'opportunities'}
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-900">
                  {tokens.length}
                </div>
                <div className="text-xs text-blue-600">Available Tokens</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading tokens</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tokens Grid */}
      {!loading && !error && (
        <>
          {tokens.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🪙</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No tokens available</h3>
              <p className="text-gray-600">Check back later for new investment opportunities.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tokens.map((token) => (
                <div
                  key={token.id}
                  className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden"
                >
                  {/* Token Image/Icon */}
                  <div className="h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                    {token.imageUrl ? (
                      <img
                        src={token.imageUrl}
                        alt={token.name}
                        className="w-24 h-24 object-cover rounded-full"
                      />
                    ) : (
                      <div className="text-6xl">
                        {getDefaultImage(token.category)}
                      </div>
                    )}
                  </div>

                  {/* Token Details */}
                  <div className="p-6">
                    {/* Header */}
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {token.name}
                        </h3>
                        <p className="text-sm text-gray-500 font-mono">
                          {token.symbol}
                        </p>
                      </div>
                      <div className="flex flex-col gap-1 items-end">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(token.category)}`}>
                          {token.category}
                        </span>

                        {/* Qualification Status */}
                        {token.needsQualification ? (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                            🔒 NEEDS QUALIFICATION
                          </span>
                        ) : token.hasRequiredClaims ? (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            ✅ QUALIFIED
                          </span>
                        ) : (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                            ⏳ CHECKING...
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Price */}
                    <div className="mb-4">
                      <div className="text-2xl font-bold text-green-600">
                        {formatPrice(token.price, token.currency)}
                      </div>
                      <div className="text-sm text-gray-500">per token</div>
                    </div>

                    {/* Supply Info */}
                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <div className="text-gray-500">Total Supply</div>
                        <div className="font-medium">
                          {formatSupply(token.totalSupply, token.decimals)}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-500">Max Supply</div>
                        <div className="font-medium">
                          {formatSupply(token.maxSupply, token.decimals)}
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    {token.description && (
                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                        {token.description}
                      </p>
                    )}

                    {/* Action Button */}
                    {user && clientProfile ? (
                      // User is logged in and has profile
                      token.needsQualification ? (
                        // Token requires qualification
                        <button
                          onClick={() => {
                            // Redirect to qualification page for this specific token
                            const params = new URLSearchParams({
                              token: token.address,
                              tokenName: token.name,
                              tokenSymbol: token.symbol
                            });
                            window.location.href = `/qualification?${params.toString()}`;
                          }}
                          className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium"
                        >
                          🔒 Complete Qualification
                        </button>
                      ) : token.hasRequiredClaims ? (
                        // User is qualified for this token
                        <button
                          onClick={() => {
                            setSelectedTokenForOrder(token);
                            setShowOrderModal(true);
                          }}
                          className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
                        >
                          🚀 Invest Now
                        </button>
                      ) : (
                        // Checking qualification status
                        <button
                          disabled
                          className="w-full bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium"
                        >
                          ⏳ Checking Qualification...
                        </button>
                      )
                    ) : (
                      // User not logged in or no profile
                      <button
                        onClick={() => {
                          if (!user) {
                            // Redirect to login
                            window.location.href = '/api/auth/login';
                          } else {
                            // Show KYC modal
                            setShowKYCModal(true);
                          }
                        }}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                      >
                        {!user ? 'Sign In to Invest' : 'Complete Initial Qualification'}
                      </button>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="px-6 py-3 bg-gray-50 border-t">
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span>Network: {token.network}</span>
                      <span>Decimals: {token.decimals}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}

      {/* KYC Modal */}
      {showKYCModal && (
        <KYCModal
          onClose={() => setShowKYCModal(false)}
          existingProfile={clientProfile}
        />
      )}

      {/* Order Modal */}
      {showOrderModal && selectedTokenForOrder && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
          <div className="relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Header */}
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Order {selectedTokenForOrder.name} ({selectedTokenForOrder.symbol})
                </h3>
                <button
                  onClick={() => {
                    setShowOrderModal(false);
                    setSelectedTokenForOrder(null);
                    setOrderAmount('');
                    setOrderError(null);
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className="space-y-4">
                {/* Token Details */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Token Details
                  </label>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Token:</span> {selectedTokenForOrder.name} ({selectedTokenForOrder.symbol})
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Price:</span> {formatPrice(selectedTokenForOrder.price, selectedTokenForOrder.currency)} per token
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Available:</span> {selectedTokenForOrder.maxSupply} tokens
                    </p>
                  </div>
                </div>

                {/* Order Amount Input */}
                <div>
                  <label htmlFor="order-amount" className="block text-sm font-medium text-gray-700 mb-1">
                    Number of Tokens to Order
                  </label>
                  <input
                    id="order-amount"
                    type="number"
                    min="1"
                    step="1"
                    value={orderAmount}
                    onChange={(e) => {
                      const value = e.target.value;
                      setOrderAmount(value);
                      setOrderError(null);
                    }}
                    placeholder="Enter amount of tokens"
                    className="block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                {/* Total Calculation */}
                {orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && (
                  <div className="bg-blue-50 p-3 rounded-md">
                    <p className="text-sm text-blue-800">
                      <span className="font-medium">Total Amount:</span> {(() => {
                        const tokenPrice = Number(selectedTokenForOrder.price);
                        const orderQty = Number(orderAmount);

                        if (isNaN(tokenPrice) || isNaN(orderQty)) {
                          return `Error: Price=${selectedTokenForOrder.price}, Qty=${orderAmount}`;
                        }

                        const total = orderQty * tokenPrice;
                        return formatPrice(total.toString(), selectedTokenForOrder.currency);
                      })()}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      This order will be submitted for admin approval
                    </p>
                  </div>
                )}

                {/* Error Message */}
                {orderError && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-sm text-red-600">{orderError}</p>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="mt-6 flex flex-col space-y-3">
                <button
                  onClick={handleOrderSubmit}
                  disabled={isSubmittingOrder || !orderAmount}
                  className="w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmittingOrder ? 'Submitting...' : 'Submit Order'}
                </button>
                <button
                  onClick={() => {
                    setShowOrderModal(false);
                    setSelectedTokenForOrder(null);
                    setOrderAmount('');
                    setOrderError(null);
                  }}
                  className="w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Wallet Connection Section */}
      <div className="fixed bottom-4 right-4">
        <w3m-button />
      </div>
    </div>
  );
}
