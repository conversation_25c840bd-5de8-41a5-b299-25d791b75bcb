'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { Navbar } from './Navbar';
import { KYCModal } from './KYCModal';

import { useApiClient } from '@/lib/api-client';
import { useMockUser } from './providers/MockAuthProvider';
import Link from 'next/link';

export function Dashboard() {
  const useMockAuth = process.env.NEXT_PUBLIC_USE_MOCK_AUTH === 'true';

  // Use mock auth or real Auth0 based on environment
  const auth0User = useUser();
  const mockAuth = useMockAuth ? useMockUser() : { user: undefined, isLoading: false };

  const user = useMockAuth ? mockAuth.user : auth0User.user;
  const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;

  const [showKYCModal, setShowKYCModal] = useState(false);
  const apiClient = useApiClient();

  // Fetch client profile
  const { data: clientProfile } = useQuery({
    queryKey: ['client-profile'],
    queryFn: () => apiClient.getClientProfile(),
    enabled: !!user,
  });

  if (userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="flex items-center justify-center min-h-screen">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                TokenDev KYC Portal
              </h1>
              <p className="text-gray-600">
                Complete your KYC qualification to access investment opportunities
              </p>
            </div>

            <div className="space-y-4">
              {useMockAuth ? (
                <button
                  onClick={mockAuth.login}
                  disabled={mockAuth.isLoading}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {mockAuth.isLoading ? 'Signing In...' : 'Sign In (Demo)'}
                </button>
              ) : (
                <a
                  href="/api/auth/login"
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Sign In with Auth0
                </a>
              )}

              <div className="text-sm text-gray-500">
                New to TokenDev?{' '}
                {useMockAuth ? (
                  <button
                    onClick={mockAuth.login}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Create an account (Demo)
                  </button>
                ) : (
                  <a
                    href="/api/auth/login?screen_hint=signup"
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Create an account with Auth0
                  </a>
                )}
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">🔒</div>
                  <div className="text-xs text-gray-600 mt-1">Secure</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">✓</div>
                  <div className="text-xs text-gray-600 mt-1">KYC Compliant</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">📊</div>
                  <div className="text-xs text-gray-600 mt-1">Real-time</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar
        user={user}
        clientProfile={clientProfile}
        onGetQualified={() => setShowKYCModal(true)}
        onLogout={useMockAuth ? mockAuth.logout : undefined}
        useMockAuth={useMockAuth}
      />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, {user.name || user.email}
              </h1>
              <p className="text-gray-600 mt-2">
                Complete your KYC qualification to access investment opportunities
              </p>
            </div>
            <Link
              href="/qualification"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              View Full Qualification
            </Link>
          </div>
        </div>

        {/* KYC Status Banner */}
        {clientProfile ? (
          <div className="mb-8">
            <div className={`p-6 rounded-lg border ${
              clientProfile.kycStatus === 'APPROVED'
                ? 'bg-green-50 border-green-200 text-green-800'
                : clientProfile.kycStatus === 'REJECTED'
                ? 'bg-red-50 border-red-200 text-red-800'
                : clientProfile.kycStatus === 'IN_REVIEW'
                ? 'bg-yellow-50 border-yellow-200 text-yellow-800'
                : 'bg-blue-50 border-blue-200 text-blue-800'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium">
                    KYC Status: {clientProfile.kycStatus.replace('_', ' ')}
                  </h3>
                  <p className="text-sm mt-1">
                    {clientProfile.kycStatus === 'APPROVED' && 'Your account is fully verified and ready for investment opportunities.'}
                    {clientProfile.kycStatus === 'REJECTED' && 'Your KYC application was rejected. Please try again with automated verification.'}
                    {clientProfile.kycStatus === 'IN_REVIEW' && 'Your KYC application is being reviewed by our compliance team.'}
                    {clientProfile.kycStatus === 'PENDING' && 'Please complete your KYC application to qualify for investment opportunities.'}
                  </p>


                </div>

                <div className="flex space-x-3">
                  {clientProfile.kycStatus !== 'APPROVED' && (
                    <button
                      onClick={() => setShowKYCModal(true)}
                      className="bg-white px-6 py-3 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors border"
                    >
                      {clientProfile.kycStatus === 'PENDING' ? 'Complete KYC' : 'Update KYC'}
                    </button>
                  )}

                  <Link
                    href="/qualification"
                    className="bg-white px-6 py-3 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors border"
                  >
                    Full Process
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-8">
            <div className="p-6 rounded-lg border bg-blue-50 border-blue-200 text-blue-800">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium">
                    KYC Qualification Required
                  </h3>
                  <p className="text-sm mt-1">
                    To access investment opportunities, please complete your KYC qualification process.
                  </p>


                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowKYCModal(true)}
                    className="bg-blue-600 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    Start KYC Process
                  </button>

                  <Link
                    href="/qualification"
                    className="bg-white px-6 py-3 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors border"
                  >
                    Full Process
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* KYC Information Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">KYC Qualification Process</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Personal Information</h3>
              <p className="text-sm text-gray-600">Provide your basic personal details and contact information</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">2</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Identity Verification</h3>
              <p className="text-sm text-gray-600">Upload identification documents for verification</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">3</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Financial Information</h3>
              <p className="text-sm text-gray-600">Provide financial details and source of funds</p>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <h3 className="font-medium text-gray-900 mb-3">Required Documents</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                Government-issued photo ID (passport, driver's license, or national ID)
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                Proof of address (utility bill or bank statement, less than 3 months old)
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                Financial information and source of funds documentation
              </li>
            </ul>
          </div>

          {!clientProfile && (
            <div className="mt-6 text-center space-y-4">
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setShowKYCModal(true)}
                  className="bg-blue-600 text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 transition-colors"
                >
                  Begin KYC Process
                </button>

                <Link
                  href="/qualification"
                  className="bg-gray-600 text-white px-8 py-3 rounded-md font-medium hover:bg-gray-700 transition-colors"
                >
                  Full Qualification Process
                </Link>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* KYC Modals */}
      {showKYCModal && (
        <KYCModal
          onClose={() => setShowKYCModal(false)}
          existingProfile={clientProfile}
        />
      )}


    </div>
  );
}
