// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "./base/BaseIdentityRegistry.sol";
import "./interfaces/IIdentityRegistry.sol";
import "./interfaces/IKYCRegistry.sol";
import "./interfaces/ICompleteWhitelist.sol";

/**
 * @title Whitelist
 * @dev Implementation of the whitelist/identity registry functionality for ERC-3643 compliant tokens
 * This contract serves as both a whitelist and an identity registry for a security token
 * Unlike WhitelistWithKYC, this contract has minimal KYC functionality but maintains interface compatibility
 */
contract Whitelist is 
    BaseIdentityRegistry,
    ICompleteWhitelist
{
    // Mapping of KYC approved addresses (minimal implementation for interface compatibility)
    mapping(address => bool) private _kycApproved;
    
    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     */
    function initialize(address admin) public initializer {
        __BaseIdentityRegistry_init(admin);
    }
    
    /**
     * @dev Special initialization function for factory deployment
     * This function can only be called during initialization
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE and AGENT_ROLE
     */
    function initializeWithAgent(address admin) external initializer {
        __BaseIdentityRegistry_init(admin);
        _grantRole(AGENT_ROLE, admin);
    }
    
    // ---------------------------------------------------------------------------
    // The following functions are implemented for IKYCRegistry interface compatibility
    // They provide minimal KYC functionality for contracts that don't need full KYC
    // ---------------------------------------------------------------------------

    /**
     * @dev Check if an address is KYC approved
     * In this simplified implementation, all whitelisted addresses are considered KYC approved
     * @param account The address to check
     * @return bool True if the address is KYC approved, false otherwise
     */
    function isKycApproved(address account) public view override returns (bool) {
        return _kycApproved[account];
    }

    /**
     * @dev Approve KYC for an address
     * @param account The address to approve KYC for
     */
    function approveKyc(address account) public override onlyRole(AGENT_ROLE) {
        require(account != address(0), "Whitelist: cannot approve KYC for zero address");
        require(!_kycApproved[account], "Whitelist: address already KYC approved");
        
        _kycApproved[account] = true;
        
        // Also whitelist the address if not already whitelisted
        if (!isWhitelisted(account)) {
            addToWhitelist(account);
        }
        
        emit KycApproved(account);
    }

    /**
     * @dev Revoke KYC approval for an address
     * @param account The address to revoke KYC approval from
     */
    function revokeKyc(address account) public override onlyRole(AGENT_ROLE) {
        require(account != address(0), "Whitelist: cannot revoke KYC for zero address");
        require(_kycApproved[account], "Whitelist: address not KYC approved");
        
        _kycApproved[account] = false;
        emit KycRevoked(account);
    }

    /**
     * @dev Batch approve KYC for addresses
     * @param accounts The addresses to approve KYC for
     */
    function batchApproveKyc(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "Whitelist: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && !_kycApproved[addr]) {
                _kycApproved[addr] = true;
                
                // Also whitelist the address if not already whitelisted
                if (!isWhitelisted(addr)) {
                    addToWhitelist(addr);
                }
                
                emit KycApproved(addr);
            }
        }
    }

    /**
     * @dev Batch revoke KYC approval for addresses
     * @param accounts The addresses to revoke KYC approval from
     */
    function batchRevokeKyc(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "Whitelist: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && _kycApproved[addr]) {
                _kycApproved[addr] = false;
                emit KycRevoked(addr);
            }
        }
    }
}