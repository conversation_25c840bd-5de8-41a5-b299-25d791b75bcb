@echo off
echo ========================================
echo TokenDev Client Portal Setup
echo ========================================
echo.

echo Step 1: Checking dependencies...
call npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js/npm not found. Please install Node.js first.
    pause
    exit /b 1
)
echo Node.js/npm found!
echo.

echo Step 2: Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)
echo Dependencies installed successfully!
echo.

echo Step 3: Setting up environment variables...
if not exist .env.local (
    if exist .env.example (
        copy .env.example .env.local
        echo Environment file created from example.
        echo.
        echo IMPORTANT: Please edit .env.local with your Auth0 configuration:
        echo - AUTH0_SECRET (generate with: openssl rand -hex 32)
        echo - AUTH0_ISSUER_BASE_URL (your Auth0 domain)
        echo - AUTH0_CLIENT_ID (your Auth0 client ID)
        echo - AUTH0_CLIENT_SECRET (your Auth0 client secret)
        echo.
    ) else (
        echo Warning: .env.example not found. Please create .env.local manually.
    )
) else (
    echo .env.local already exists.
)
echo.

echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Configure Auth0 application at https://manage.auth0.com/
echo 2. Update .env.local with your Auth0 credentials
echo 3. Ensure admin panel is running on port 3000
echo 4. Start the client application: npm run dev
echo 5. Navigate to http://localhost:7788
echo.
echo Auth0 Configuration Required:
echo - Application Type: Regular Web Application
echo - Allowed Callback URLs: http://localhost:7788/api/auth/callback
echo - Allowed Logout URLs: http://localhost:7788
echo - Allowed Web Origins: http://localhost:7788
echo.
pause
