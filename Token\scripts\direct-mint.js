// Direct Minting Script for Amoy Testnet
// Uses optimized gas parameters and multiple RPC fallbacks

const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

// List of RPC endpoints to try
const RPC_ENDPOINTS = [
  "https://polygon-amoy.blockpi.network/v1/rpc/public",
  "https://polygon-amoy-rpc.publicnode.com",
  "https://polygon-amoy.drpc.org",
  "https://rpc-amoy.polygon.technology"
];

// Helper function to sleep
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to try multiple RPC endpoints
async function withFallbackRpc(operation) {
  let lastError;
  
  // Try each RPC endpoint
  for (const rpcUrl of RPC_ENDPOINTS) {
    try {
      console.log(`Trying RPC endpoint: ${rpcUrl}`);
      
      // Update the RPC URL environment variable
      process.env.AMOY_RPC_URL = rpcUrl;
      
      // Try the operation
      return await operation();
    } catch (error) {
      console.log(`RPC ${rpcUrl} failed: ${error.message}`);
      lastError = error;
      // Wait before trying next endpoint
      await sleep(1000);
    }
  }
  
  // If all endpoints fail, throw the last error
  throw new Error(`All RPC endpoints failed. Last error: ${lastError.message}`);
}

// Helper function for retries
async function withRetry(operation, name, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`${name} - Attempt ${attempt}/${maxRetries}`);
      return await operation();
    } catch (error) {
      console.log(`${name} failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
      lastError = error;
      
      if (attempt < maxRetries) {
        const delay = 3000 * attempt;
        console.log(`Retrying in ${delay/1000} seconds...`);
        await sleep(delay);
      }
    }
  }
  
  throw new Error(`${name} failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

async function main() {
  try {
    // Get arguments from environment variables
    const tokenAddress = process.env.TOKEN_ADDRESS;
    if (!tokenAddress) {
      throw new Error("TOKEN_ADDRESS environment variable not set");
    }

    const toAddress = process.env.TO_ADDRESS;
    if (!toAddress) {
      throw new Error("TO_ADDRESS environment variable not set");
    }

    const amount = process.env.AMOUNT;
    if (!amount) {
      throw new Error("AMOUNT environment variable not set");
    }

    // Gas parameters - use optimized values that worked for deployment
    const gasLimit = process.env.GAS_LIMIT ? BigInt(process.env.GAS_LIMIT) : BigInt(5000000);
    const gasPrice = process.env.GAS_PRICE 
      ? ethers.parseUnits(process.env.GAS_PRICE, "gwei") 
      : ethers.parseUnits("50", "gwei");

    console.log("\n=== DIRECT TOKEN MINT FOR AMOY ===");
    console.log(`Token Address: ${tokenAddress}`);
    console.log(`Recipient: ${toAddress}`);
    console.log(`Amount: ${amount}`);
    console.log(`Gas Limit: ${gasLimit.toString()}`);
    console.log(`Gas Price: ${ethers.formatUnits(gasPrice, "gwei")} gwei`);

    // Try to get signer with fallback
    let signer;
    await withFallbackRpc(async () => {
      [signer] = await ethers.getSigners();
      console.log(`Signer: ${signer.address}`);
      return signer;
    });

    // ABI for the mint function
    const mintAbi = [
      "function mint(address to, uint256 amount) returns (bool)"
    ];

    // Encode the function call
    const iface = new ethers.Interface(mintAbi);
    const data = iface.encodeFunctionData("mint", [
      toAddress,
      ethers.parseEther(amount)
    ]);

    // Get nonce with retry and fallback
    let nonce;
    await withRetry(async () => {
      await withFallbackRpc(async () => {
        nonce = await signer.getNonce();
        console.log(`Using nonce: ${nonce}`);
        return nonce;
      });
    }, "Nonce retrieval");

    // Create transaction object with optimized parameters
    const tx = {
      to: tokenAddress,
      data: data,
      gasLimit: gasLimit,
      gasPrice: gasPrice,
      nonce: nonce,
      value: ethers.parseEther("0")
    };

    // Get chain ID if possible
    try {
      const network = await ethers.provider.getNetwork();
      tx.chainId = network.chainId;
      console.log(`Setting chainId: ${tx.chainId}`);
    } catch (error) {
      console.log(`Could not get chainId: ${error.message}`);
    }

    console.log("Transaction prepared:", JSON.stringify(tx, (_, v) => 
      typeof v === 'bigint' ? v.toString() : v, 2));

    // Send transaction with retry and fallback
    console.log("\nSending mint transaction...");
    let txResponse;

    await withRetry(async () => {
      await withFallbackRpc(async () => {
        txResponse = await signer.sendTransaction(tx);
        console.log(`Transaction sent! Hash: ${txResponse.hash}`);
        return txResponse;
      });
    }, "Transaction sending", 3);

    // Log the transaction hash
    console.log(`\nMint transaction submitted: ${txResponse.hash}`);
    console.log(`You can check the status at: https://amoy.polygonscan.com/tx/${txResponse.hash}`);

    // Wait for receipt with fallback RPCs
    console.log("\nWaiting for transaction confirmation...");
    let receipt = null;
    let confirmed = false;
    let timeout = 300000; // 5 minutes
    let startWaitTime = Date.now();

    while (!confirmed && (Date.now() - startWaitTime < timeout)) {
      // Try all RPC endpoints for confirmation
      for (const rpcUrl of RPC_ENDPOINTS) {
        try {
          console.log(`Checking tx status using RPC: ${rpcUrl} (${Math.round((Date.now() - startWaitTime)/1000)}s elapsed)`);
          
          // Update RPC URL
          process.env.AMOY_RPC_URL = rpcUrl;
          
          // Check receipt
          receipt = await ethers.provider.getTransactionReceipt(txResponse.hash);
          
          if (receipt) {
            confirmed = true;
            console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
            console.log(`Gas used: ${receipt.gasUsed.toString()}`);
            break;
          } else {
            console.log("Transaction not yet confirmed");
          }
        } catch (error) {
          console.log(`Error checking receipt with ${rpcUrl}: ${error.message}`);
        }
        
        // Don't hammer the RPC endpoints
        await sleep(3000);
      }
      
      if (!confirmed) {
        // Wait between polling cycles
        await sleep(10000);
      }
    }

    if (confirmed) {
      console.log(`\n✅ Successfully minted ${amount} tokens to ${toAddress}`);
    } else {
      console.log("\n⚠️ Transaction confirmation timed out, but transaction was submitted");
      console.log("Check the transaction status manually:");
      console.log(`https://amoy.polygonscan.com/tx/${txResponse.hash}`);
    }

    return {
      transactionHash: txResponse.hash,
      confirmed: confirmed,
      blockNumber: receipt ? receipt.blockNumber : undefined,
      recipient: toAddress,
      amount: amount,
      success: true
    };
  } catch (error) {
    console.error("\n❌ ERROR during token minting:");
    console.error(error.message);
    
    console.log("\n=== TROUBLESHOOTING SUGGESTIONS ===");
    console.log("1. Check that your account has the AGENT_ROLE required to mint");
    console.log("2. Ensure the recipient address is whitelisted");
    console.log("3. Verify that the token is not paused");
    console.log("4. Try different RPC endpoints if Amoy testnet is unstable");

    throw error;
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then((result) => {
      console.log("\nOperation completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\nOperation failed!");
      process.exit(1);
    });
} else {
  // Export for use in other scripts or APIs
  module.exports = main;
} 