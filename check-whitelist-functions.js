const { ethers } = require("hardhat");

async function main() {
  const proxyAddress = "******************************************";
  
  // Connect to the contract
  const whitelist = await ethers.getContractAt("Whitelist", proxyAddress);
  
  // Get the contract functions
  console.log("Checking contract functions...");
  
  // Check if KYC functions exist
  let hasKycFunctions = false;
  try {
    // Try to access the function to check if it exists
    const functionFragment = whitelist.interface.getFunction("approveKyc");
    console.log("approveKyc function exists:", !!functionFragment);
    hasKycFunctions = true;
  } catch (error) {
    console.log("approveKyc function does not exist in the contract");
  }
  
  try {
    // Try to access the function to check if it exists
    const functionFragment = whitelist.interface.getFunction("isKycApproved");
    console.log("isKycApproved function exists:", !!functionFragment);
    hasKycFunctions = true;
  } catch (error) {
    console.log("isKycApproved function does not exist in the contract");
  }
  
  if (!hasKycFunctions) {
    console.log("The contract does not have KYC functions. The upgrade might not have been successful.");
  }
  
  // Print all available functions for reference
  console.log("\nAll available functions in the contract:");
  try {
    const functions = Object.keys(whitelist.interface.functions || {});
    if (functions.length > 0) {
      functions.forEach(func => {
        console.log(`- ${func}`);
      });
    } else {
      console.log("No functions found in the interface.");
    }
  } catch (error) {
    console.log("Error listing functions:", error.message);
    
    // Alternative approach to list functions
    console.log("\nTrying alternative approach to list functions:");
    try {
      for (const key in whitelist) {
        if (typeof whitelist[key] === 'function' && !key.startsWith('_')) {
          console.log(`- ${key}`);
        }
      }
    } catch (altError) {
      console.log("Alternative approach also failed:", altError.message);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 