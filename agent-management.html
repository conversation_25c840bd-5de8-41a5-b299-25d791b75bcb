<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Management</title>
    <style>
        .agent-management {
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            margin-bottom: 30px;
        }
        
        .agent-management h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .agent-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        
        .agent-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .agent-item:last-child {
            border-bottom: none;
        }
        
        .agent-address {
            font-family: monospace;
            font-size: 0.9em;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 70%;
        }
        
        .agent-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn-remove {
            background-color: #ff4d4d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-remove:hover {
            background-color: #ff3333;
        }
        
        .agent-add-form {
            display: flex;
            margin-top: 20px;
            gap: 10px;
        }
        
        .agent-add-form input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .btn-add {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-add:hover {
            background-color: #45a049;
        }
        
        .status-message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status-message.success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-message.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .agent-address {
                max-width: 50%;
            }
        }
    </style>
</head>
<body>
    <div class="agent-management" id="agentManagement">
        <h2>Token Agents Management</h2>
        <p>Agents are authorized addresses that can mint tokens and manage the whitelist.</p>
        
        <div class="agent-list" id="agentList">
            <!-- Agent list will be populated here -->
            <div class="agent-item">
                <span class="agent-address">Loading agents...</span>
            </div>
        </div>
        
        <div class="agent-add-form">
            <input type="text" id="newAgentAddress" placeholder="Enter ethereum address (0x...)" />
            <button class="btn-add" id="addAgentBtn">Add Agent</button>
        </div>
        
        <div id="statusMessage" class="status-message" style="display: none;"></div>
    </div>

    <script>
        // Agent Management Script
        document.addEventListener('DOMContentLoaded', function() {
            // Replace with your actual token contract address
            let tokenAddress = window.tokenContractAddress || localStorage.getItem('tokenAddress');
            let userAddress = window.userAddress || localStorage.getItem('userAddress');
            
            // Reference to web3 if already available in the page
            let web3 = window.web3;
            
            // Elements
            const agentList = document.getElementById('agentList');
            const newAgentInput = document.getElementById('newAgentAddress');
            const addAgentBtn = document.getElementById('addAgentBtn');
            const statusMessage = document.getElementById('statusMessage');
            
            // ABI for agent-related functions (simplified for this example)
            const tokenABI = [
                // Get all agents
                {
                    "inputs": [],
                    "name": "getAllAgents",
                    "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}],
                    "stateMutability": "view",
                    "type": "function"
                },
                // Add agent
                {
                    "inputs": [{"internalType": "address", "name": "agent", "type": "address"}],
                    "name": "addAgent",
                    "outputs": [],
                    "stateMutability": "nonpayable",
                    "type": "function"
                },
                // Remove agent
                {
                    "inputs": [{"internalType": "address", "name": "agent", "type": "address"}],
                    "name": "removeAgent",
                    "outputs": [],
                    "stateMutability": "nonpayable",
                    "type": "function"
                },
                // Check if address is an agent
                {
                    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
                    "name": "isAgent",
                    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
                    "stateMutability": "view",
                    "type": "function"
                }
            ];
            
            // Initialize token contract
            let tokenContract;
            
            // Function to initialize the agent management interface
            async function initAgentManagement() {
                if (!web3 || !tokenAddress) {
                    showStatus('Web3 or token address not available. Please connect your wallet.', 'error');
                    return;
                }
                
                try {
                    tokenContract = new web3.eth.Contract(tokenABI, tokenAddress);
                    
                    // Load agents
                    await loadAgents();
                    
                    // Setup events
                    setupEvents();
                } catch (error) {
                    console.error('Error initializing agent management:', error);
                    showStatus('Error initializing agent management. See console for details.', 'error');
                }
            }
            
            // Load agents from contract
            async function loadAgents() {
                try {
                    const agents = await tokenContract.methods.getAllAgents().call();
                    
                    // Clear loading message
                    agentList.innerHTML = '';
                    
                    if (agents.length === 0) {
                        agentList.innerHTML = '<div class="agent-item"><span class="agent-address">No agents found</span></div>';
                        return;
                    }
                    
                    // Populate agent list
                    agents.forEach(async (agent) => {
                        const agentItem = document.createElement('div');
                        agentItem.className = 'agent-item';
                        
                        const agentAddress = document.createElement('span');
                        agentAddress.className = 'agent-address';
                        agentAddress.textContent = agent;
                        
                        const agentControls = document.createElement('div');
                        agentControls.className = 'agent-controls';
                        
                        // Only show remove button if not the current user
                        if (agent.toLowerCase() !== userAddress.toLowerCase()) {
                            const removeBtn = document.createElement('button');
                            removeBtn.className = 'btn-remove';
                            removeBtn.textContent = 'Remove';
                            removeBtn.setAttribute('data-address', agent);
                            agentControls.appendChild(removeBtn);
                        } else {
                            const youLabel = document.createElement('span');
                            youLabel.textContent = '(You)';
                            youLabel.style.marginLeft = '10px';
                            youLabel.style.color = '#888';
                            agentAddress.appendChild(youLabel);
                        }
                        
                        agentItem.appendChild(agentAddress);
                        agentItem.appendChild(agentControls);
                        agentList.appendChild(agentItem);
                    });
                } catch (error) {
                    console.error('Error loading agents:', error);
                    showStatus('Error loading agents. See console for details.', 'error');
                    agentList.innerHTML = '<div class="agent-item"><span class="agent-address">Error loading agents</span></div>';
                }
            }
            
            // Setup event listeners
            function setupEvents() {
                // Add agent button
                addAgentBtn.addEventListener('click', async () => {
                    const newAgentAddress = newAgentInput.value.trim();
                    
                    if (!newAgentAddress || !web3.utils.isAddress(newAgentAddress)) {
                        showStatus('Please enter a valid Ethereum address', 'error');
                        return;
                    }
                    
                    try {
                        // Check if already an agent
                        const isAlreadyAgent = await tokenContract.methods.isAgent(newAgentAddress).call();
                        if (isAlreadyAgent) {
                            showStatus('This address is already an agent', 'error');
                            return;
                        }
                        
                        // Add agent
                        await tokenContract.methods.addAgent(newAgentAddress).send({ from: userAddress });
                        
                        showStatus('Agent added successfully!', 'success');
                        newAgentInput.value = '';
                        
                        // Refresh agent list
                        await loadAgents();
                    } catch (error) {
                        console.error('Error adding agent:', error);
                        showStatus('Error adding agent. See console for details.', 'error');
                    }
                });
                
                // Remove agent buttons (delegated event)
                agentList.addEventListener('click', async (e) => {
                    if (e.target.className === 'btn-remove') {
                        const agentAddress = e.target.getAttribute('data-address');
                        
                        if (!agentAddress) return;
                        
                        try {
                            // Remove agent
                            await tokenContract.methods.removeAgent(agentAddress).send({ from: userAddress });
                            
                            showStatus('Agent removed successfully!', 'success');
                            
                            // Refresh agent list
                            await loadAgents();
                        } catch (error) {
                            console.error('Error removing agent:', error);
                            showStatus('Error removing agent. See console for details.', 'error');
                        }
                    }
                });
            }
            
            // Show status message
            function showStatus(message, type) {
                statusMessage.textContent = message;
                statusMessage.className = 'status-message ' + type;
                statusMessage.style.display = 'block';
                
                setTimeout(() => {
                    statusMessage.style.display = 'none';
                }, 5000);
            }
            
            // Initialize on page load
            initAgentManagement();
        });
    </script>
</body>
</html> 