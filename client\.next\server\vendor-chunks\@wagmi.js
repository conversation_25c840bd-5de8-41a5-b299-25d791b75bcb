"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wagmi";
exports.ids = ["vendor-chunks/@wagmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* binding */ coinbaseWallet)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\ncoinbaseWallet.type = 'coinbaseWallet';\nfunction coinbaseWallet(parameters = {}) {\n    if (parameters.version === '3' || parameters.headlessMode)\n        return version3(parameters);\n    return version4(parameters);\n}\nfunction version4(parameters) {\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                    params: 'instantOnboarding' in rest && rest.instantOnboarding\n                        ? [{ onboarding: 'instant' }]\n                        : [],\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close?.();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = (await provider.request({\n                method: 'eth_chainId',\n            }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                const preference = (() => {\n                    if (typeof parameters.preference === 'string')\n                        return { options: parameters.preference };\n                    return {\n                        ...parameters.preference,\n                        options: parameters.preference?.options ?? 'all',\n                    };\n                })();\n                const { createCoinbaseWalletSDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/@coinbase\")]).then(__webpack_require__.bind(__webpack_require__, /*! @coinbase/wallet-sdk */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/index.js\"));\n                const sdk = createCoinbaseWalletSDK({\n                    ...parameters,\n                    appChainIds: config.chains.map((x) => x.id),\n                    preference,\n                });\n                walletProvider = sdk.getProvider();\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\nfunction version3(parameters) {\n    const reloadOnDisconnect = false;\n    let sdk;\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = await provider.request({\n                method: 'eth_chainId',\n            });\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const CoinbaseWalletSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@metamask\"), __webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/cbw-sdk\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/eth-block-tracker\"), __webpack_require__.e(\"vendor-chunks/readable-stream\"), __webpack_require__.e(\"vendor-chunks/eth-json-rpc-filters\"), __webpack_require__.e(\"vendor-chunks/sha.js\"), __webpack_require__.e(\"vendor-chunks/json-rpc-engine\"), __webpack_require__.e(\"vendor-chunks/keccak\"), __webpack_require__.e(\"vendor-chunks/eth-rpc-errors\"), __webpack_require__.e(\"vendor-chunks/async-mutex\"), __webpack_require__.e(\"vendor-chunks/inherits\"), __webpack_require__.e(\"vendor-chunks/superstruct\"), __webpack_require__.e(\"vendor-chunks/xtend\"), __webpack_require__.e(\"vendor-chunks/util-deprecate\"), __webpack_require__.e(\"vendor-chunks/string_decoder\"), __webpack_require__.e(\"vendor-chunks/safe-buffer\"), __webpack_require__.e(\"vendor-chunks/pify\"), __webpack_require__.e(\"vendor-chunks/json-rpc-random-id\"), __webpack_require__.e(\"vendor-chunks/fast-safe-stringify\"), __webpack_require__.e(\"vendor-chunks/eth-query\"), __webpack_require__.e(\"vendor-chunks/bn.js\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! cbw-sdk */ \"(ssr)/./node_modules/cbw-sdk/dist/index.js\", 19));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                sdk = new CoinbaseWalletSDK({ ...parameters, reloadOnDisconnect });\n                // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.\n                const walletExtensionChainId = sdk.walletExtension?.getChainId();\n                const chain = config.chains.find((chain) => parameters.chainId\n                    ? chain.id === parameters.chainId\n                    : chain.id === walletExtensionChainId) || config.chains[0];\n                const chainId = parameters.chainId || chain?.id;\n                const jsonRpcUrl = parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0];\n                walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId);\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\n//# sourceMappingURL=coinbaseWallet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/exports/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* reexport safe */ _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_2__.coinbaseWallet),\n/* harmony export */   injected: () => (/* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_0__.injected),\n/* harmony export */   metaMask: () => (/* reexport safe */ _metaMask_js__WEBPACK_IMPORTED_MODULE_3__.metaMask),\n/* harmony export */   mock: () => (/* reexport safe */ _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.mock),\n/* harmony export */   safe: () => (/* reexport safe */ _safe_js__WEBPACK_IMPORTED_MODULE_4__.safe),\n/* harmony export */   version: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_6__.version),\n/* harmony export */   walletConnect: () => (/* reexport safe */ _walletConnect_js__WEBPACK_IMPORTED_MODULE_5__.walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\");\n/* harmony import */ var _coinbaseWallet_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../coinbaseWallet.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\");\n/* harmony import */ var _metaMask_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../metaMask.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\");\n/* harmony import */ var _safe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../safe.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/safe.js\");\n/* harmony import */ var _walletConnect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../walletConnect.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/version.js\");\n// biome-ignore lint/performance/noBarrelFile: entrypoint module\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2Nvbm5lY3RvcnMvZGlzdC9lc20vZXhwb3J0cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQzhDO0FBQ1M7QUFDYjtBQUNSO0FBQ21CO0FBQ2I7QUFDeEMiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29ubmVjdG9yc1xcZGlzdFxcZXNtXFxleHBvcnRzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBiaW9tZS1pZ25vcmUgbGludC9wZXJmb3JtYW5jZS9ub0JhcnJlbEZpbGU6IGVudHJ5cG9pbnQgbW9kdWxlXG5leHBvcnQgeyBpbmplY3RlZCwgbW9jaywgfSBmcm9tICdAd2FnbWkvY29yZSc7XG5leHBvcnQgeyBjb2luYmFzZVdhbGxldCwgfSBmcm9tICcuLi9jb2luYmFzZVdhbGxldC5qcyc7XG5leHBvcnQgeyBtZXRhTWFzayB9IGZyb20gJy4uL21ldGFNYXNrLmpzJztcbmV4cG9ydCB7IHNhZmUgfSBmcm9tICcuLi9zYWZlLmpzJztcbmV4cG9ydCB7IHdhbGxldENvbm5lY3QsIH0gZnJvbSAnLi4vd2FsbGV0Q29ubmVjdC5qcyc7XG5leHBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/exports/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: () => (/* binding */ metaMask)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@metamask\"), __webpack_require__.e(\"vendor-chunks/engine.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-parser\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/engine.io-parser\"), __webpack_require__.e(\"vendor-chunks/@socket.io\"), __webpack_require__.e(\"vendor-chunks/xmlhttprequest-ssl\"), __webpack_require__.e(\"vendor-chunks/eventemitter2\"), __webpack_require__.e(\"vendor-chunks/cross-fetch\")]).then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(ssr)/./node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/safe.js":
/*!*********************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/safe.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n\n\nsafe.type = 'safe';\nfunction safe(parameters = {}) {\n    const { shimDisconnect = false } = parameters;\n    let provider_;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'safe',\n        name: 'Safe',\n        type: safe.type,\n        async connect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await this.getAccounts();\n            const chainId = await this.getChainId();\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n            // Remove disconnected shim if it exists\n            if (shimDisconnect)\n                await config.storage?.removeItem('safe.disconnected');\n            return { accounts, chainId };\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect)\n                await config.storage?.setItem('safe.disconnected', true);\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return (await provider.request({ method: 'eth_accounts' })).map(viem__WEBPACK_IMPORTED_MODULE_2__.getAddress);\n        },\n        async getProvider() {\n            // Only allowed in iframe context\n            const isIframe = typeof window !== 'undefined' && window?.parent !== window;\n            if (!isIframe)\n                return;\n            if (!provider_) {\n                const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/viem\"), __webpack_require__.e(\"vendor-chunks/@safe-global\")]).then(__webpack_require__.bind(__webpack_require__, /*! @safe-global/safe-apps-sdk */ \"(ssr)/./node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\"));\n                const sdk = new SDK(parameters);\n                // `getInfo` hangs when not used in Safe App iFrame\n                // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********\n                const safe = await (0,viem__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => sdk.safe.getInfo(), {\n                    timeout: parameters.unstable_getInfoTimeout ?? 10,\n                });\n                if (!safe)\n                    throw new Error('Could not load Safe information');\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const SafeAppProvider = await (async () => {\n                    const Provider = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/viem\"), __webpack_require__.e(\"vendor-chunks/ox\"), __webpack_require__.e(\"vendor-chunks/abitype\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@safe-global\"), __webpack_require__.e(\"vendor-chunks/isows\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @safe-global/safe-apps-provider */ \"(ssr)/./node_modules/@safe-global/safe-apps-provider/dist/index.js\", 19));\n                    if (typeof Provider.SafeAppProvider !== 'function' &&\n                        typeof Provider.default.SafeAppProvider === 'function')\n                        return Provider.default.SafeAppProvider;\n                    return Provider.SafeAppProvider;\n                })();\n                provider_ = new SafeAppProvider(safe, sdk);\n            }\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return Number(provider.chainId);\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem('safe.disconnected'));\n                if (isDisconnected)\n                    return false;\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        onAccountsChanged() {\n            // Not relevant for Safe because changing account requires app reload.\n        },\n        onChainChanged() {\n            // Not relevant for Safe because Safe smart contract wallets only exist on single chain.\n        },\n        onDisconnect() {\n            config.emitter.emit('disconnect');\n        },\n    }));\n}\n//# sourceMappingURL=safe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/safe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/version.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/version.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '5.8.3';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2Nvbm5lY3RvcnMvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb25uZWN0b3JzXFxkaXN0XFxlc21cXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnNS44LjMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/walletConnect.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnect: () => (/* binding */ walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\nwalletConnect.type = 'walletConnect';\nfunction walletConnect(parameters) {\n    const isNewChainsStale = parameters.isNewChainsStale ?? true;\n    let provider_;\n    let providerPromise;\n    const NAMESPACE = 'eip155';\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let sessionDelete;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'walletConnect',\n        name: 'WalletConnect',\n        type: walletConnect.type,\n        async setup() {\n            const provider = await this.getProvider().catch(() => null);\n            if (!provider)\n                return;\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            if (!sessionDelete) {\n                sessionDelete = this.onSessionDelete.bind(this);\n                provider.on('session_delete', sessionDelete);\n            }\n        },\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                if (!displayUri) {\n                    displayUri = this.onDisplayUri;\n                    provider.on('display_uri', displayUri);\n                }\n                let targetChainId = chainId;\n                if (!targetChainId) {\n                    const state = (await config.storage?.getItem('state')) ?? {};\n                    const isChainSupported = config.chains.some((x) => x.id === state.chainId);\n                    if (isChainSupported)\n                        targetChainId = state.chainId;\n                    else\n                        targetChainId = config.chains[0]?.id;\n                }\n                if (!targetChainId)\n                    throw new Error('No chains found on connector.');\n                const isChainsStale = await this.isChainsStale();\n                // If there is an active session with stale chains, disconnect current session.\n                if (provider.session && isChainsStale)\n                    await provider.disconnect();\n                // If there isn't an active session or chains are stale, connect.\n                if (!provider.session || isChainsStale) {\n                    const optionalChains = config.chains\n                        .filter((chain) => chain.id !== targetChainId)\n                        .map((optionalChain) => optionalChain.id);\n                    await provider.connect({\n                        optionalChains: [targetChainId, ...optionalChains],\n                        ...('pairingTopic' in rest\n                            ? { pairingTopic: rest.pairingTopic }\n                            : {}),\n                    });\n                    this.setRequestedChainsIds(config.chains.map((x) => x.id));\n                }\n                // If session exists and chains are authorized, enable provider for required chain\n                const accounts = (await provider.enable()).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                const currentChainId = await this.getChainId();\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                if (!sessionDelete) {\n                    sessionDelete = this.onSessionDelete.bind(this);\n                    provider.on('session_delete', sessionDelete);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user rejected|connection request reset)/i.test(error?.message)) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            try {\n                await provider?.disconnect();\n            }\n            catch (error) {\n                if (!/No matching key/i.test(error.message))\n                    throw error;\n            }\n            finally {\n                if (chainChanged) {\n                    provider?.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider?.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider?.on('connect', connect);\n                }\n                if (accountsChanged) {\n                    provider?.removeListener('accountsChanged', accountsChanged);\n                    accountsChanged = undefined;\n                }\n                if (sessionDelete) {\n                    provider?.removeListener('session_delete', sessionDelete);\n                    sessionDelete = undefined;\n                }\n                this.setRequestedChainsIds([]);\n            }\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return provider.accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getProvider({ chainId } = {}) {\n            async function initProvider() {\n                const optionalChains = config.chains.map((x) => x.id);\n                if (!optionalChains.length)\n                    return;\n                const { EthereumProvider } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@walletconnect\").then(__webpack_require__.bind(__webpack_require__, /*! @walletconnect/ethereum-provider */ \"(ssr)/./node_modules/@walletconnect/ethereum-provider/dist/index.es.js\"));\n                return await EthereumProvider.init({\n                    ...parameters,\n                    disableProviderPing: true,\n                    optionalChains,\n                    projectId: parameters.projectId,\n                    rpcMap: Object.fromEntries(config.chains.map((chain) => {\n                        const [url] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                            chain,\n                            transports: config.transports,\n                        });\n                        return [chain.id, url];\n                    })),\n                    showQrModal: parameters.showQrModal ?? true,\n                });\n            }\n            if (!provider_) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider_ = await providerPromise;\n                provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY);\n            }\n            if (chainId)\n                await this.switchChain?.({ chainId });\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            return provider.chainId;\n        },\n        async isAuthorized() {\n            try {\n                const [accounts, provider] = await Promise.all([\n                    this.getAccounts(),\n                    this.getProvider(),\n                ]);\n                // If an account does not exist on the session, then the connector is unauthorized.\n                if (!accounts.length)\n                    return false;\n                // If the chains are stale on the session, then the connector is unauthorized.\n                const isChainsStale = await this.isChainsStale();\n                if (isChainsStale && provider.session) {\n                    await provider.disconnect().catch(() => { });\n                    return false;\n                }\n                return true;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ChainNotConfiguredError());\n            try {\n                await Promise.all([\n                    new Promise((resolve) => {\n                        const listener = ({ chainId: currentChainId, }) => {\n                            if (currentChainId === chainId) {\n                                config.emitter.off('change', listener);\n                                resolve();\n                            }\n                        };\n                        config.emitter.on('change', listener);\n                    }),\n                    provider.request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId) }],\n                    }),\n                ]);\n                const requestedChains = await this.getRequestedChainsIds();\n                this.setRequestedChainsIds([...requestedChains, chainId]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (/(user rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                try {\n                    let blockExplorerUrls;\n                    if (addEthereumChainParameter?.blockExplorerUrls)\n                        blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                    else\n                        blockExplorerUrls = chain.blockExplorers?.default.url\n                            ? [chain.blockExplorers?.default.url]\n                            : [];\n                    let rpcUrls;\n                    if (addEthereumChainParameter?.rpcUrls?.length)\n                        rpcUrls = addEthereumChainParameter.rpcUrls;\n                    else\n                        rpcUrls = [...chain.rpcUrls.default.http];\n                    const addEthereumChain = {\n                        blockExplorerUrls,\n                        chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId),\n                        chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                        iconUrls: addEthereumChainParameter?.iconUrls,\n                        nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,\n                        rpcUrls,\n                    };\n                    await provider.request({\n                        method: 'wallet_addEthereumChain',\n                        params: [addEthereumChain],\n                    });\n                    const requestedChains = await this.getRequestedChainsIds();\n                    this.setRequestedChainsIds([...requestedChains, chainId]);\n                    return chain;\n                }\n                catch (error) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const chainId = Number(connectInfo.chainId);\n            const accounts = await this.getAccounts();\n            config.emitter.emit('connect', { accounts, chainId });\n        },\n        async onDisconnect(_error) {\n            this.setRequestedChainsIds([]);\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (sessionDelete) {\n                provider.removeListener('session_delete', sessionDelete);\n                sessionDelete = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n        onSessionDelete() {\n            this.onDisconnect();\n        },\n        getNamespaceChainsIds() {\n            if (!provider_)\n                return [];\n            const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map((account) => Number.parseInt(account.split(':')[1] || ''));\n            return chainIds ?? [];\n        },\n        async getRequestedChainsIds() {\n            return ((await config.storage?.getItem(this.requestedChainsStorageKey)) ?? []);\n        },\n        /**\n         * Checks if the target chains match the chains that were\n         * initially requested by the connector for the WalletConnect session.\n         * If there is a mismatch, this means that the chains on the connector\n         * are considered stale, and need to be revalidated at a later point (via\n         * connection).\n         *\n         * There may be a scenario where a dapp adds a chain to the\n         * connector later on, however, this chain will not have been approved or rejected\n         * by the wallet. In this case, the chain is considered stale.\n         */\n        async isChainsStale() {\n            if (!isNewChainsStale)\n                return false;\n            const connectorChains = config.chains.map((x) => x.id);\n            const namespaceChains = this.getNamespaceChainsIds();\n            if (namespaceChains.length &&\n                !namespaceChains.some((id) => connectorChains.includes(id)))\n                return false;\n            const requestedChains = await this.getRequestedChainsIds();\n            return !connectorChains.every((id) => requestedChains.includes(id));\n        },\n        async setRequestedChainsIds(chains) {\n            await config.storage?.setItem(this.requestedChainsStorageKey, chains);\n        },\n        get requestedChainsStorageKey() {\n            return `${this.id}.requestedChains`;\n        },\n    }));\n}\n//# sourceMappingURL=walletConnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/walletConnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/connect.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connect: () => (/* binding */ connect)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n/** https://wagmi.sh/core/api/actions/connect */\nasync function connect(config, parameters) {\n    // \"Register\" connector if not already created\n    let connector;\n    if (typeof parameters.connector === 'function') {\n        connector = config._internal.connectors.setup(parameters.connector);\n    }\n    else\n        connector = parameters.connector;\n    // Check if connector is already connected\n    if (connector.uid === config.state.current)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAlreadyConnectedError();\n    try {\n        config.setState((x) => ({ ...x, status: 'connecting' }));\n        connector.emitter.emit('message', { type: 'connecting' });\n        const { connector: _, ...rest } = parameters;\n        const data = await connector.connect(rest);\n        const accounts = data.accounts;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        await config.storage?.setItem('recentConnectorId', connector.id);\n        config.setState((x) => ({\n            ...x,\n            connections: new Map(x.connections).set(connector.uid, {\n                accounts,\n                chainId: data.chainId,\n                connector: connector,\n            }),\n            current: connector.uid,\n            status: 'connected',\n        }));\n        return { accounts, chainId: data.chainId };\n    }\n    catch (error) {\n        config.setState((x) => ({\n            ...x,\n            // Keep existing connector connected in case of error\n            status: x.current ? 'connected' : 'disconnected',\n        }));\n        throw error;\n    }\n}\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/disconnect.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnect: () => (/* binding */ disconnect)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/disconnect */\nasync function disconnect(config, parameters = {}) {\n    let connector;\n    if (parameters.connector)\n        connector = parameters.connector;\n    else {\n        const { connections, current } = config.state;\n        const connection = connections.get(current);\n        connector = connection?.connector;\n    }\n    const connections = config.state.connections;\n    if (connector) {\n        await connector.disconnect();\n        connector.emitter.off('change', config._internal.events.change);\n        connector.emitter.off('disconnect', config._internal.events.disconnect);\n        connector.emitter.on('connect', config._internal.events.connect);\n        connections.delete(connector.uid);\n    }\n    config.setState((x) => {\n        // if no connections exist, move to disconnected state\n        if (connections.size === 0)\n            return {\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            };\n        // switch over to another connection\n        const nextConnection = connections.values().next().value;\n        return {\n            ...x,\n            connections: new Map(connections),\n            current: nextConnection.connector.uid,\n        };\n    });\n    // Set recent connector if exists\n    {\n        const current = config.state.current;\n        if (!current)\n            return;\n        const connector = config.state.connections.get(current)?.connector;\n        if (!connector)\n            return;\n        await config.storage?.setItem('recentConnectorId', connector.id);\n    }\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/estimateGas.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/estimateGas.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   estimateGas: () => (/* binding */ estimateGas)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/estimateGas.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/estimateGas */\nasync function estimateGas(config, parameters) {\n    const { chainId, connector, ...rest } = parameters;\n    let account;\n    if (parameters.account)\n        account = parameters.account;\n    else {\n        const connectorClient = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: parameters.account,\n            chainId,\n            connector,\n        });\n        account = connectorClient.account;\n    }\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.estimateGas, 'estimateGas');\n    return action({ ...rest, account });\n}\n//# sourceMappingURL=estimateGas.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9lc3RpbWF0ZUdhcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdFO0FBQ2Q7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSw4QkFBOEI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsMEVBQWtCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0MsbUJBQW1CLDhEQUFTLFNBQVMscURBQWdCO0FBQ3JELG9CQUFvQixrQkFBa0I7QUFDdEM7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXGVzdGltYXRlR2FzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGVzdGltYXRlR2FzIGFzIHZpZW1fZXN0aW1hdGVHYXMsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG5pbXBvcnQgeyBnZXRDb25uZWN0b3JDbGllbnQsIH0gZnJvbSAnLi9nZXRDb25uZWN0b3JDbGllbnQuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9lc3RpbWF0ZUdhcyAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGVzdGltYXRlR2FzKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgY2hhaW5JZCwgY29ubmVjdG9yLCAuLi5yZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGxldCBhY2NvdW50O1xuICAgIGlmIChwYXJhbWV0ZXJzLmFjY291bnQpXG4gICAgICAgIGFjY291bnQgPSBwYXJhbWV0ZXJzLmFjY291bnQ7XG4gICAgZWxzZSB7XG4gICAgICAgIGNvbnN0IGNvbm5lY3RvckNsaWVudCA9IGF3YWl0IGdldENvbm5lY3RvckNsaWVudChjb25maWcsIHtcbiAgICAgICAgICAgIGFjY291bnQ6IHBhcmFtZXRlcnMuYWNjb3VudCxcbiAgICAgICAgICAgIGNoYWluSWQsXG4gICAgICAgICAgICBjb25uZWN0b3IsXG4gICAgICAgIH0pO1xuICAgICAgICBhY2NvdW50ID0gY29ubmVjdG9yQ2xpZW50LmFjY291bnQ7XG4gICAgfVxuICAgIGNvbnN0IGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fZXN0aW1hdGVHYXMsICdlc3RpbWF0ZUdhcycpO1xuICAgIHJldHVybiBhY3Rpb24oeyAuLi5yZXN0LCBhY2NvdW50IH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXN0aW1hdGVHYXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/estimateGas.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getAccount.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccount: () => (/* binding */ getAccount)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getAccount */\nfunction getAccount(config) {\n    const uid = config.state.current;\n    const connection = config.state.connections.get(uid);\n    const addresses = connection?.accounts;\n    const address = addresses?.[0];\n    const chain = config.chains.find((chain) => chain.id === connection?.chainId);\n    const status = config.state.status;\n    switch (status) {\n        case 'connected':\n            return {\n                address: address,\n                addresses: addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: true,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'reconnecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: !!address,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: true,\n                status,\n            };\n        case 'connecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: false,\n                isConnecting: true,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'disconnected':\n            return {\n                address: undefined,\n                addresses: undefined,\n                chain: undefined,\n                chainId: undefined,\n                connector: undefined,\n                isConnected: false,\n                isConnecting: false,\n                isDisconnected: true,\n                isReconnecting: false,\n                status,\n            };\n    }\n}\n//# sourceMappingURL=getAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getBalance.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBalance: () => (/* binding */ getBalance)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/data/trim.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/unit/formatUnits.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getBalance.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/getUnit.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js\");\n/* harmony import */ var _readContracts_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./readContracts.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js\");\n\n\n\n\n\n/** https://wagmi.sh/core/api/actions/getBalance */\nasync function getBalance(config, parameters) {\n    const { address, blockNumber, blockTag, chainId, token: tokenAddress, unit = 'ether', } = parameters;\n    if (tokenAddress) {\n        try {\n            return await getTokenBalance(config, {\n                balanceAddress: address,\n                chainId,\n                symbolType: 'string',\n                tokenAddress,\n            });\n        }\n        catch (error) {\n            // In the chance that there is an error upon decoding the contract result,\n            // it could be likely that the contract data is represented as bytes32 instead\n            // of a string.\n            if (error.name ===\n                'ContractFunctionExecutionError') {\n                const balance = await getTokenBalance(config, {\n                    balanceAddress: address,\n                    chainId,\n                    symbolType: 'bytes32',\n                    tokenAddress,\n                });\n                const symbol = (0,viem__WEBPACK_IMPORTED_MODULE_0__.hexToString)((0,viem__WEBPACK_IMPORTED_MODULE_1__.trim)(balance.symbol, { dir: 'right' }));\n                return { ...balance, symbol };\n            }\n            throw error;\n        }\n    }\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.getBalance, 'getBalance');\n    const value = await action(blockNumber ? { address, blockNumber } : { address, blockTag });\n    const chain = config.chains.find((x) => x.id === chainId) ?? client.chain;\n    return {\n        decimals: chain.nativeCurrency.decimals,\n        formatted: (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value, (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit)),\n        symbol: chain.nativeCurrency.symbol,\n        value,\n    };\n}\nasync function getTokenBalance(config, parameters) {\n    const { balanceAddress, chainId, symbolType, tokenAddress, unit } = parameters;\n    const contract = {\n        abi: [\n            {\n                type: 'function',\n                name: 'balanceOf',\n                stateMutability: 'view',\n                inputs: [{ type: 'address' }],\n                outputs: [{ type: 'uint256' }],\n            },\n            {\n                type: 'function',\n                name: 'decimals',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: 'uint8' }],\n            },\n            {\n                type: 'function',\n                name: 'symbol',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: symbolType }],\n            },\n        ],\n        address: tokenAddress,\n    };\n    const [value, decimals, symbol] = await (0,_readContracts_js__WEBPACK_IMPORTED_MODULE_6__.readContracts)(config, {\n        allowFailure: false,\n        contracts: [\n            {\n                ...contract,\n                functionName: 'balanceOf',\n                args: [balanceAddress],\n                chainId,\n            },\n            { ...contract, functionName: 'decimals', chainId },\n            { ...contract, functionName: 'symbol', chainId },\n        ],\n    });\n    const formatted = (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value ?? '0', (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit ?? decimals));\n    return { decimals, formatted, symbol, value };\n}\n//# sourceMappingURL=getBalance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnections.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnections: () => (/* binding */ getConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousConnections = [];\n/** https://wagmi.sh/core/api/actions/getConnections */\nfunction getConnections(config) {\n    const connections = [...config.state.connections.values()];\n    if (config.state.status === 'reconnecting')\n        return previousConnections;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousConnections, connections))\n        return previousConnections;\n    previousConnections = connections;\n    return connections;\n}\n//# sourceMappingURL=getConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDb25uZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFTO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxhY3Rpb25zXFxnZXRDb25uZWN0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWVwRXF1YWwgfSBmcm9tICcuLi91dGlscy9kZWVwRXF1YWwuanMnO1xubGV0IHByZXZpb3VzQ29ubmVjdGlvbnMgPSBbXTtcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvZ2V0Q29ubmVjdGlvbnMgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRDb25uZWN0aW9ucyhjb25maWcpIHtcbiAgICBjb25zdCBjb25uZWN0aW9ucyA9IFsuLi5jb25maWcuc3RhdGUuY29ubmVjdGlvbnMudmFsdWVzKCldO1xuICAgIGlmIChjb25maWcuc3RhdGUuc3RhdHVzID09PSAncmVjb25uZWN0aW5nJylcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzQ29ubmVjdGlvbnM7XG4gICAgaWYgKGRlZXBFcXVhbChwcmV2aW91c0Nvbm5lY3Rpb25zLCBjb25uZWN0aW9ucykpXG4gICAgICAgIHJldHVybiBwcmV2aW91c0Nvbm5lY3Rpb25zO1xuICAgIHByZXZpb3VzQ29ubmVjdGlvbnMgPSBjb25uZWN0aW9ucztcbiAgICByZXR1cm4gY29ubmVjdGlvbnM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDb25uZWN0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectorClient: () => (/* binding */ getConnectorClient)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nasync function getConnectorClient(config, parameters = {}) {\n    // Get connection\n    let connection;\n    if (parameters.connector) {\n        const { connector } = parameters;\n        if (config.state.status === 'reconnecting' &&\n            !connector.getAccounts &&\n            !connector.getChainId)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorUnavailableReconnectingError({ connector });\n        const [accounts, chainId] = await Promise.all([\n            connector.getAccounts().catch((e) => {\n                if (parameters.account === null)\n                    return [];\n                throw e;\n            }),\n            connector.getChainId(),\n        ]);\n        connection = {\n            accounts: accounts,\n            chainId,\n            connector,\n        };\n    }\n    else\n        connection = config.state.connections.get(config.state.current);\n    if (!connection)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorNotConnectedError();\n    const chainId = parameters.chainId ?? connection.chainId;\n    // Check connector using same chainId as connection\n    const connectorChainId = await connection.connector.getChainId();\n    if (connectorChainId !== connection.chainId)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorChainMismatchError({\n            connectionChainId: connection.chainId,\n            connectorChainId,\n        });\n    const connector = connection.connector;\n    if (connector.getClient)\n        return connector.getClient({ chainId });\n    // Default using `custom` transport\n    const account = (0,viem_utils__WEBPACK_IMPORTED_MODULE_1__.parseAccount)(parameters.account ?? connection.accounts[0]);\n    if (account)\n        account.address = (0,viem_utils__WEBPACK_IMPORTED_MODULE_2__.getAddress)(account.address); // TODO: Checksum address as part of `parseAccount`?\n    // If account was provided, check that it exists on the connector\n    if (parameters.account &&\n        !connection.accounts.some((x) => x.toLowerCase() === account.address.toLowerCase()))\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAccountNotFoundError({\n            address: account.address,\n            connector,\n        });\n    const chain = config.chains.find((chain) => chain.id === chainId);\n    const provider = (await connection.connector.getProvider({ chainId }));\n    return (0,viem__WEBPACK_IMPORTED_MODULE_3__.createClient)({\n        account,\n        chain,\n        name: 'Connector Client',\n        transport: (opts) => (0,viem__WEBPACK_IMPORTED_MODULE_4__.custom)(provider)({ ...opts, retryCount: 0 }),\n    });\n}\n//# sourceMappingURL=getConnectorClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDb25uZWN0b3JDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBQ1M7QUFDK0c7QUFDcks7QUFDTyx5REFBeUQ7QUFDaEU7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFlBQVk7QUFDNUI7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLG9GQUFxQyxHQUFHLFdBQVc7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHlFQUEwQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwwRUFBMkI7QUFDN0M7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EscUNBQXFDLFNBQVM7QUFDOUM7QUFDQSxvQkFBb0Isd0RBQVk7QUFDaEM7QUFDQSwwQkFBMEIsc0RBQVUsbUJBQW1CO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiw0RUFBNkI7QUFDL0M7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLCtEQUErRCxTQUFTO0FBQ3hFLFdBQVcsa0RBQVk7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLDRDQUFNLGFBQWEsd0JBQXdCO0FBQ3hFLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcZ2V0Q29ubmVjdG9yQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNsaWVudCwgY3VzdG9tLCB9IGZyb20gJ3ZpZW0nO1xuaW1wb3J0IHsgZ2V0QWRkcmVzcywgcGFyc2VBY2NvdW50IH0gZnJvbSAndmllbS91dGlscyc7XG5pbXBvcnQgeyBDb25uZWN0b3JBY2NvdW50Tm90Rm91bmRFcnJvciwgQ29ubmVjdG9yQ2hhaW5NaXNtYXRjaEVycm9yLCBDb25uZWN0b3JOb3RDb25uZWN0ZWRFcnJvciwgQ29ubmVjdG9yVW5hdmFpbGFibGVSZWNvbm5lY3RpbmdFcnJvciwgfSBmcm9tICcuLi9lcnJvcnMvY29uZmlnLmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvZ2V0Q29ubmVjdG9yQ2xpZW50ICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Q29ubmVjdG9yQ2xpZW50KGNvbmZpZywgcGFyYW1ldGVycyA9IHt9KSB7XG4gICAgLy8gR2V0IGNvbm5lY3Rpb25cbiAgICBsZXQgY29ubmVjdGlvbjtcbiAgICBpZiAocGFyYW1ldGVycy5jb25uZWN0b3IpIHtcbiAgICAgICAgY29uc3QgeyBjb25uZWN0b3IgfSA9IHBhcmFtZXRlcnM7XG4gICAgICAgIGlmIChjb25maWcuc3RhdGUuc3RhdHVzID09PSAncmVjb25uZWN0aW5nJyAmJlxuICAgICAgICAgICAgIWNvbm5lY3Rvci5nZXRBY2NvdW50cyAmJlxuICAgICAgICAgICAgIWNvbm5lY3Rvci5nZXRDaGFpbklkKVxuICAgICAgICAgICAgdGhyb3cgbmV3IENvbm5lY3RvclVuYXZhaWxhYmxlUmVjb25uZWN0aW5nRXJyb3IoeyBjb25uZWN0b3IgfSk7XG4gICAgICAgIGNvbnN0IFthY2NvdW50cywgY2hhaW5JZF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgICAgICBjb25uZWN0b3IuZ2V0QWNjb3VudHMoKS5jYXRjaCgoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChwYXJhbWV0ZXJzLmFjY291bnQgPT09IG51bGwpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBbXTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlO1xuICAgICAgICAgICAgfSksXG4gICAgICAgICAgICBjb25uZWN0b3IuZ2V0Q2hhaW5JZCgpLFxuICAgICAgICBdKTtcbiAgICAgICAgY29ubmVjdGlvbiA9IHtcbiAgICAgICAgICAgIGFjY291bnRzOiBhY2NvdW50cyxcbiAgICAgICAgICAgIGNoYWluSWQsXG4gICAgICAgICAgICBjb25uZWN0b3IsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGVsc2VcbiAgICAgICAgY29ubmVjdGlvbiA9IGNvbmZpZy5zdGF0ZS5jb25uZWN0aW9ucy5nZXQoY29uZmlnLnN0YXRlLmN1cnJlbnQpO1xuICAgIGlmICghY29ubmVjdGlvbilcbiAgICAgICAgdGhyb3cgbmV3IENvbm5lY3Rvck5vdENvbm5lY3RlZEVycm9yKCk7XG4gICAgY29uc3QgY2hhaW5JZCA9IHBhcmFtZXRlcnMuY2hhaW5JZCA/PyBjb25uZWN0aW9uLmNoYWluSWQ7XG4gICAgLy8gQ2hlY2sgY29ubmVjdG9yIHVzaW5nIHNhbWUgY2hhaW5JZCBhcyBjb25uZWN0aW9uXG4gICAgY29uc3QgY29ubmVjdG9yQ2hhaW5JZCA9IGF3YWl0IGNvbm5lY3Rpb24uY29ubmVjdG9yLmdldENoYWluSWQoKTtcbiAgICBpZiAoY29ubmVjdG9yQ2hhaW5JZCAhPT0gY29ubmVjdGlvbi5jaGFpbklkKVxuICAgICAgICB0aHJvdyBuZXcgQ29ubmVjdG9yQ2hhaW5NaXNtYXRjaEVycm9yKHtcbiAgICAgICAgICAgIGNvbm5lY3Rpb25DaGFpbklkOiBjb25uZWN0aW9uLmNoYWluSWQsXG4gICAgICAgICAgICBjb25uZWN0b3JDaGFpbklkLFxuICAgICAgICB9KTtcbiAgICBjb25zdCBjb25uZWN0b3IgPSBjb25uZWN0aW9uLmNvbm5lY3RvcjtcbiAgICBpZiAoY29ubmVjdG9yLmdldENsaWVudClcbiAgICAgICAgcmV0dXJuIGNvbm5lY3Rvci5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIC8vIERlZmF1bHQgdXNpbmcgYGN1c3RvbWAgdHJhbnNwb3J0XG4gICAgY29uc3QgYWNjb3VudCA9IHBhcnNlQWNjb3VudChwYXJhbWV0ZXJzLmFjY291bnQgPz8gY29ubmVjdGlvbi5hY2NvdW50c1swXSk7XG4gICAgaWYgKGFjY291bnQpXG4gICAgICAgIGFjY291bnQuYWRkcmVzcyA9IGdldEFkZHJlc3MoYWNjb3VudC5hZGRyZXNzKTsgLy8gVE9ETzogQ2hlY2tzdW0gYWRkcmVzcyBhcyBwYXJ0IG9mIGBwYXJzZUFjY291bnRgP1xuICAgIC8vIElmIGFjY291bnQgd2FzIHByb3ZpZGVkLCBjaGVjayB0aGF0IGl0IGV4aXN0cyBvbiB0aGUgY29ubmVjdG9yXG4gICAgaWYgKHBhcmFtZXRlcnMuYWNjb3VudCAmJlxuICAgICAgICAhY29ubmVjdGlvbi5hY2NvdW50cy5zb21lKCh4KSA9PiB4LnRvTG93ZXJDYXNlKCkgPT09IGFjY291bnQuYWRkcmVzcy50b0xvd2VyQ2FzZSgpKSlcbiAgICAgICAgdGhyb3cgbmV3IENvbm5lY3RvckFjY291bnROb3RGb3VuZEVycm9yKHtcbiAgICAgICAgICAgIGFkZHJlc3M6IGFjY291bnQuYWRkcmVzcyxcbiAgICAgICAgICAgIGNvbm5lY3RvcixcbiAgICAgICAgfSk7XG4gICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKGNoYWluKSA9PiBjaGFpbi5pZCA9PT0gY2hhaW5JZCk7XG4gICAgY29uc3QgcHJvdmlkZXIgPSAoYXdhaXQgY29ubmVjdGlvbi5jb25uZWN0b3IuZ2V0UHJvdmlkZXIoeyBjaGFpbklkIH0pKTtcbiAgICByZXR1cm4gY3JlYXRlQ2xpZW50KHtcbiAgICAgICAgYWNjb3VudCxcbiAgICAgICAgY2hhaW4sXG4gICAgICAgIG5hbWU6ICdDb25uZWN0b3IgQ2xpZW50JyxcbiAgICAgICAgdHJhbnNwb3J0OiAob3B0cykgPT4gY3VzdG9tKHByb3ZpZGVyKSh7IC4uLm9wdHMsIHJldHJ5Q291bnQ6IDAgfSksXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDb25uZWN0b3JDbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/multicall.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   multicall: () => (/* binding */ multicall)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/multicall.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\nasync function multicall(config, parameters) {\n    const { allowFailure = true, chainId, contracts, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.multicall, 'multicall');\n    return action({\n        allowFailure,\n        contracts,\n        ...rest,\n    });\n}\n//# sourceMappingURL=multicall.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9tdWx0aWNhbGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ1Q7QUFDM0M7QUFDUCxZQUFZLG1EQUFtRDtBQUMvRCxzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxtREFBYztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcbXVsdGljYWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG11bHRpY2FsbCBhcyB2aWVtX211bHRpY2FsbCB9IGZyb20gJ3ZpZW0vYWN0aW9ucyc7XG5pbXBvcnQgeyBnZXRBY3Rpb24gfSBmcm9tICcuLi91dGlscy9nZXRBY3Rpb24uanMnO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG11bHRpY2FsbChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGFsbG93RmFpbHVyZSA9IHRydWUsIGNoYWluSWQsIGNvbnRyYWN0cywgLi4ucmVzdCB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHsgY2hhaW5JZCB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX211bHRpY2FsbCwgJ211bHRpY2FsbCcpO1xuICAgIHJldHVybiBhY3Rpb24oe1xuICAgICAgICBhbGxvd0ZhaWx1cmUsXG4gICAgICAgIGNvbnRyYWN0cyxcbiAgICAgICAgLi4ucmVzdCxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW11bHRpY2FsbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/prepareTransactionRequest.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/prepareTransactionRequest.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareTransactionRequest: () => (/* binding */ prepareTransactionRequest)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/prepareTransactionRequest.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/prepareTransactionRequest */\nasync function prepareTransactionRequest(config, parameters) {\n    const { account: account_, chainId, ...rest } = parameters;\n    const account = account_ ?? (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config).address;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.prepareTransactionRequest, 'prepareTransactionRequest');\n    return action({\n        ...rest,\n        ...(account ? { account } : {}),\n    });\n}\n//# sourceMappingURL=prepareTransactionRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9wcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMkY7QUFDekM7QUFDTDtBQUM3QztBQUNPO0FBQ1AsWUFBWSxzQ0FBc0M7QUFDbEQsZ0NBQWdDLDBEQUFVO0FBQzFDLHNDQUFzQyxTQUFTO0FBQy9DLG1CQUFtQiw4REFBUyxTQUFTLG1FQUE4QjtBQUNuRTtBQUNBO0FBQ0Esd0JBQXdCLFVBQVUsSUFBSTtBQUN0QyxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXHByZXBhcmVUcmFuc2FjdGlvblJlcXVlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJlcGFyZVRyYW5zYWN0aW9uUmVxdWVzdCBhcyB2aWVtX3ByZXBhcmVUcmFuc2FjdGlvblJlcXVlc3QgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmltcG9ydCB7IGdldEFjY291bnQgfSBmcm9tICcuL2dldEFjY291bnQuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9wcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0ICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcHJlcGFyZVRyYW5zYWN0aW9uUmVxdWVzdChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGFjY291bnQ6IGFjY291bnRfLCBjaGFpbklkLCAuLi5yZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGFjY291bnQgPSBhY2NvdW50XyA/PyBnZXRBY2NvdW50KGNvbmZpZykuYWRkcmVzcztcbiAgICBjb25zdCBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHsgY2hhaW5JZCB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX3ByZXBhcmVUcmFuc2FjdGlvblJlcXVlc3QsICdwcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0Jyk7XG4gICAgcmV0dXJuIGFjdGlvbih7XG4gICAgICAgIC4uLnJlc3QsXG4gICAgICAgIC4uLihhY2NvdW50ID8geyBhY2NvdW50IH0gOiB7fSksXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wcmVwYXJlVHJhbnNhY3Rpb25SZXF1ZXN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/prepareTransactionRequest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContract.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContract: () => (/* binding */ readContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/readContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/readContract */\nfunction readContract(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.readContract, 'readContract');\n    return action(rest);\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9yZWFkQ29udHJhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtFO0FBQ2hCO0FBQ2xEO0FBQ087QUFDUCxZQUFZLG1CQUFtQjtBQUMvQixzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxzREFBaUI7QUFDdEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xccmVhZENvbnRyYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlYWRDb250cmFjdCBhcyB2aWVtX3JlYWRDb250cmFjdCwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvcmVhZENvbnRyYWN0ICovXG5leHBvcnQgZnVuY3Rpb24gcmVhZENvbnRyYWN0KGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgY2hhaW5JZCwgLi4ucmVzdCB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHsgY2hhaW5JZCB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX3JlYWRDb250cmFjdCwgJ3JlYWRDb250cmFjdCcpO1xuICAgIHJldHVybiBhY3Rpb24ocmVzdCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWFkQ29udHJhY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContracts.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContracts: () => (/* binding */ readContracts)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/contract.js\");\n/* harmony import */ var _multicall_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./multicall.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js\");\n/* harmony import */ var _readContract_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./readContract.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\");\n\n\n\nasync function readContracts(config, parameters) {\n    const { allowFailure = true, blockNumber, blockTag, ...rest } = parameters;\n    const contracts = parameters.contracts;\n    try {\n        const contractsByChainId = {};\n        for (const [index, contract] of contracts.entries()) {\n            const chainId = contract.chainId ?? config.state.chainId;\n            if (!contractsByChainId[chainId])\n                contractsByChainId[chainId] = [];\n            contractsByChainId[chainId]?.push({ contract, index });\n        }\n        const promises = () => Object.entries(contractsByChainId).map(([chainId, contracts]) => (0,_multicall_js__WEBPACK_IMPORTED_MODULE_0__.multicall)(config, {\n            ...rest,\n            allowFailure,\n            blockNumber,\n            blockTag,\n            chainId: Number.parseInt(chainId),\n            contracts: contracts.map(({ contract }) => contract),\n        }));\n        const multicallResults = (await Promise.all(promises())).flat();\n        // Reorder the contract results back to the order they were\n        // provided in.\n        const resultIndexes = Object.values(contractsByChainId).flatMap((contracts) => contracts.map(({ index }) => index));\n        return multicallResults.reduce((results, result, index) => {\n            if (results)\n                results[resultIndexes[index]] = result;\n            return results;\n        }, []);\n    }\n    catch (error) {\n        if (error instanceof viem__WEBPACK_IMPORTED_MODULE_1__.ContractFunctionExecutionError)\n            throw error;\n        const promises = () => contracts.map((contract) => (0,_readContract_js__WEBPACK_IMPORTED_MODULE_2__.readContract)(config, { ...contract, blockNumber, blockTag }));\n        if (allowFailure)\n            return (await Promise.allSettled(promises())).map((result) => {\n                if (result.status === 'fulfilled')\n                    return { result: result.value, status: 'success' };\n                return { error: result.reason, result: undefined, status: 'failure' };\n            });\n        return (await Promise.all(promises()));\n    }\n}\n//# sourceMappingURL=readContracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/reconnect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reconnect: () => (/* binding */ reconnect)\n/* harmony export */ });\nlet isReconnecting = false;\n/** https://wagmi.sh/core/api/actions/reconnect */\nasync function reconnect(config, parameters = {}) {\n    // If already reconnecting, do nothing\n    if (isReconnecting)\n        return [];\n    isReconnecting = true;\n    config.setState((x) => ({\n        ...x,\n        status: x.current ? 'reconnecting' : 'connecting',\n    }));\n    const connectors = [];\n    if (parameters.connectors?.length) {\n        for (const connector_ of parameters.connectors) {\n            let connector;\n            // \"Register\" connector if not already created\n            if (typeof connector_ === 'function')\n                connector = config._internal.connectors.setup(connector_);\n            else\n                connector = connector_;\n            connectors.push(connector);\n        }\n    }\n    else\n        connectors.push(...config.connectors);\n    // Try recently-used connectors first\n    let recentConnectorId;\n    try {\n        recentConnectorId = await config.storage?.getItem('recentConnectorId');\n    }\n    catch { }\n    const scores = {};\n    for (const [, connection] of config.state.connections) {\n        scores[connection.connector.id] = 1;\n    }\n    if (recentConnectorId)\n        scores[recentConnectorId] = 0;\n    const sorted = Object.keys(scores).length > 0\n        ? // .toSorted()\n            [...connectors].sort((a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10))\n        : connectors;\n    // Iterate through each connector and try to connect\n    let connected = false;\n    const connections = [];\n    const providers = [];\n    for (const connector of sorted) {\n        const provider = await connector.getProvider().catch(() => undefined);\n        if (!provider)\n            continue;\n        // If we already have an instance of this connector's provider,\n        // then we have already checked it (ie. injected connectors can\n        // share the same `window.ethereum` instance, so we don't want to\n        // connect to it again).\n        if (providers.some((x) => x === provider))\n            continue;\n        const isAuthorized = await connector.isAuthorized();\n        if (!isAuthorized)\n            continue;\n        const data = await connector\n            .connect({ isReconnecting: true })\n            .catch(() => null);\n        if (!data)\n            continue;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        config.setState((x) => {\n            const connections = new Map(connected ? x.connections : new Map()).set(connector.uid, { accounts: data.accounts, chainId: data.chainId, connector });\n            return {\n                ...x,\n                current: connected ? x.current : connector.uid,\n                connections,\n            };\n        });\n        connections.push({\n            accounts: data.accounts,\n            chainId: data.chainId,\n            connector,\n        });\n        providers.push(provider);\n        connected = true;\n    }\n    // Prevent overwriting connected status from race condition\n    if (config.state.status === 'reconnecting' ||\n        config.state.status === 'connecting') {\n        // If connecting didn't succeed, set to disconnected\n        if (!connected)\n            config.setState((x) => ({\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            }));\n        else\n            config.setState((x) => ({ ...x, status: 'connected' }));\n    }\n    isReconnecting = false;\n    return connections;\n}\n//# sourceMappingURL=reconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/sendTransaction.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/sendTransaction.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendTransaction: () => (/* binding */ sendTransaction)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/sendTransaction.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/sendTransaction */\nasync function sendTransaction(config, parameters) {\n    const { account, chainId, connector, ...rest } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.sendTransaction, 'sendTransaction');\n    const hash = await action({\n        ...rest,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n        gas: rest.gas ?? undefined,\n    });\n    return hash;\n}\n//# sourceMappingURL=sendTransaction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/sendTransaction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/signMessage.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/signMessage.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signMessage: () => (/* binding */ signMessage)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/signMessage.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/signMessage */\nasync function signMessage(config, parameters) {\n    const { account, connector, ...rest } = parameters;\n    let client;\n    if (typeof account === 'object' && account.type === 'local')\n        client = config.getClient();\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, { account, connector });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.signMessage, 'signMessage');\n    return action({\n        ...rest,\n        ...(account ? { account } : {}),\n    });\n}\n//# sourceMappingURL=signMessage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zaWduTWVzc2FnZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdFO0FBQ2Q7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSw4QkFBOEI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsMEVBQWtCLFdBQVcsb0JBQW9CO0FBQ3hFLG1CQUFtQiw4REFBUyxTQUFTLHFEQUFnQjtBQUNyRDtBQUNBO0FBQ0Esd0JBQXdCLFVBQVUsSUFBSTtBQUN0QyxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXHNpZ25NZXNzYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNpZ25NZXNzYWdlIGFzIHZpZW1fc2lnbk1lc3NhZ2UsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG5pbXBvcnQgeyBnZXRDb25uZWN0b3JDbGllbnQsIH0gZnJvbSAnLi9nZXRDb25uZWN0b3JDbGllbnQuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9zaWduTWVzc2FnZSAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNpZ25NZXNzYWdlKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgYWNjb3VudCwgY29ubmVjdG9yLCAuLi5yZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGxldCBjbGllbnQ7XG4gICAgaWYgKHR5cGVvZiBhY2NvdW50ID09PSAnb2JqZWN0JyAmJiBhY2NvdW50LnR5cGUgPT09ICdsb2NhbCcpXG4gICAgICAgIGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoKTtcbiAgICBlbHNlXG4gICAgICAgIGNsaWVudCA9IGF3YWl0IGdldENvbm5lY3RvckNsaWVudChjb25maWcsIHsgYWNjb3VudCwgY29ubmVjdG9yIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fc2lnbk1lc3NhZ2UsICdzaWduTWVzc2FnZScpO1xuICAgIHJldHVybiBhY3Rpb24oe1xuICAgICAgICAuLi5yZXN0LFxuICAgICAgICAuLi4oYWNjb3VudCA/IHsgYWNjb3VudCB9IDoge30pLFxuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2lnbk1lc3NhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/signMessage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/switchChain.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/switchChain.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   switchChain: () => (/* binding */ switchChain)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n\n\n/** https://wagmi.sh/core/api/actions/switchChain */\nasync function switchChain(config, parameters) {\n    const { addEthereumChainParameter, chainId } = parameters;\n    const connection = config.state.connections.get(parameters.connector?.uid ?? config.state.current);\n    if (connection) {\n        const connector = connection.connector;\n        if (!connector.switchChain)\n            throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainNotSupportedError({ connector });\n        const chain = await connector.switchChain({\n            addEthereumChainParameter,\n            chainId,\n        });\n        return chain;\n    }\n    const chain = config.chains.find((x) => x.id === chainId);\n    if (!chain)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_1__.ChainNotConfiguredError();\n    config.setState((x) => ({ ...x, chainId }));\n    return chain;\n}\n//# sourceMappingURL=switchChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zd2l0Y2hDaGFpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0Q7QUFDUTtBQUN2RTtBQUNPO0FBQ1AsWUFBWSxxQ0FBcUM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOEVBQTRCLEdBQUcsV0FBVztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isc0VBQXVCO0FBQ3pDLDhCQUE4QixlQUFlO0FBQzdDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXHN3aXRjaENoYWluLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENoYWluTm90Q29uZmlndXJlZEVycm9yLCB9IGZyb20gJy4uL2Vycm9ycy9jb25maWcuanMnO1xuaW1wb3J0IHsgU3dpdGNoQ2hhaW5Ob3RTdXBwb3J0ZWRFcnJvciwgfSBmcm9tICcuLi9lcnJvcnMvY29ubmVjdG9yLmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvc3dpdGNoQ2hhaW4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzd2l0Y2hDaGFpbihjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIsIGNoYWluSWQgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgY29ubmVjdGlvbiA9IGNvbmZpZy5zdGF0ZS5jb25uZWN0aW9ucy5nZXQocGFyYW1ldGVycy5jb25uZWN0b3I/LnVpZCA/PyBjb25maWcuc3RhdGUuY3VycmVudCk7XG4gICAgaWYgKGNvbm5lY3Rpb24pIHtcbiAgICAgICAgY29uc3QgY29ubmVjdG9yID0gY29ubmVjdGlvbi5jb25uZWN0b3I7XG4gICAgICAgIGlmICghY29ubmVjdG9yLnN3aXRjaENoYWluKVxuICAgICAgICAgICAgdGhyb3cgbmV3IFN3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3IoeyBjb25uZWN0b3IgfSk7XG4gICAgICAgIGNvbnN0IGNoYWluID0gYXdhaXQgY29ubmVjdG9yLnN3aXRjaENoYWluKHtcbiAgICAgICAgICAgIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIsXG4gICAgICAgICAgICBjaGFpbklkLFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGNoYWluO1xuICAgIH1cbiAgICBjb25zdCBjaGFpbiA9IGNvbmZpZy5jaGFpbnMuZmluZCgoeCkgPT4geC5pZCA9PT0gY2hhaW5JZCk7XG4gICAgaWYgKCFjaGFpbilcbiAgICAgICAgdGhyb3cgbmV3IENoYWluTm90Q29uZmlndXJlZEVycm9yKCk7XG4gICAgY29uZmlnLnNldFN0YXRlKCh4KSA9PiAoeyAuLi54LCBjaGFpbklkIH0pKTtcbiAgICByZXR1cm4gY2hhaW47XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zd2l0Y2hDaGFpbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/switchChain.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceipt: () => (/* binding */ waitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/waitForTransactionReceipt.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getTransaction.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n\nasync function waitForTransactionReceipt(config, parameters) {\n    const { chainId, timeout = 0, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.waitForTransactionReceipt, 'waitForTransactionReceipt');\n    const receipt = await action({ ...rest, timeout });\n    if (receipt.status === 'reverted') {\n        const action_getTransaction = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.getTransaction, 'getTransaction');\n        const txn = await action_getTransaction({ hash: receipt.transactionHash });\n        const action_call = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.call, 'call');\n        const code = await action_call({\n            ...txn,\n            data: txn.input,\n            gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n            maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n            maxPriorityFeePerGas: txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n        });\n        const reason = code?.data\n            ? (0,viem__WEBPACK_IMPORTED_MODULE_4__.hexToString)(`0x${code.data.substring(138)}`)\n            : 'unknown reason';\n        throw new Error(reason);\n    }\n    return {\n        ...receipt,\n        chainId: client.chain.id,\n    };\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchAccount: () => (/* binding */ watchAccount)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nfunction watchAccount(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config), onChange, {\n        equalityFn(a, b) {\n            const { connector: aConnector, ...aRest } = a;\n            const { connector: bConnector, ...bRest } = b;\n            return ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual)(aRest, bRest) &&\n                // check connector separately\n                aConnector?.id === bConnector?.id &&\n                aConnector?.uid === bConnector?.uid);\n        },\n    });\n}\n//# sourceMappingURL=watchAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaEFjY291bnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ0w7QUFDN0M7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QixrQ0FBa0MsMERBQVU7QUFDNUM7QUFDQSxvQkFBb0Isa0NBQWtDO0FBQ3RELG9CQUFvQixrQ0FBa0M7QUFDdEQsb0JBQW9CLDhEQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXHdhdGNoQWNjb3VudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWVwRXF1YWwgfSBmcm9tICcuLi91dGlscy9kZWVwRXF1YWwuanMnO1xuaW1wb3J0IHsgZ2V0QWNjb3VudCB9IGZyb20gJy4vZ2V0QWNjb3VudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dhdGNoQWNjb3VudCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQWNjb3VudChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuc3Vic2NyaWJlKCgpID0+IGdldEFjY291bnQoY29uZmlnKSwgb25DaGFuZ2UsIHtcbiAgICAgICAgZXF1YWxpdHlGbihhLCBiKSB7XG4gICAgICAgICAgICBjb25zdCB7IGNvbm5lY3RvcjogYUNvbm5lY3RvciwgLi4uYVJlc3QgfSA9IGE7XG4gICAgICAgICAgICBjb25zdCB7IGNvbm5lY3RvcjogYkNvbm5lY3RvciwgLi4uYlJlc3QgfSA9IGI7XG4gICAgICAgICAgICByZXR1cm4gKGRlZXBFcXVhbChhUmVzdCwgYlJlc3QpICYmXG4gICAgICAgICAgICAgICAgLy8gY2hlY2sgY29ubmVjdG9yIHNlcGFyYXRlbHlcbiAgICAgICAgICAgICAgICBhQ29ubmVjdG9yPy5pZCA9PT0gYkNvbm5lY3Rvcj8uaWQgJiZcbiAgICAgICAgICAgICAgICBhQ29ubmVjdG9yPy51aWQgPT09IGJDb25uZWN0b3I/LnVpZCk7XG4gICAgICAgIH0sXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaEFjY291bnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnections: () => (/* binding */ watchConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getConnections_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnections.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchConnections */\nfunction watchConnections(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getConnections_js__WEBPACK_IMPORTED_MODULE_0__.getConnections)(config), onChange, {\n        equalityFn: _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual,\n    });\n}\n//# sourceMappingURL=watchConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENvbm5lY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNJO0FBQ3REO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkIsa0NBQWtDLGtFQUFjO0FBQ2hELG9CQUFvQiwwREFBUztBQUM3QixLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGFjdGlvbnNcXHdhdGNoQ29ubmVjdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVlcEVxdWFsIH0gZnJvbSAnLi4vdXRpbHMvZGVlcEVxdWFsLmpzJztcbmltcG9ydCB7IGdldENvbm5lY3Rpb25zLCB9IGZyb20gJy4vZ2V0Q29ubmVjdGlvbnMuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy93YXRjaENvbm5lY3Rpb25zICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hDb25uZWN0aW9ucyhjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuc3Vic2NyaWJlKCgpID0+IGdldENvbm5lY3Rpb25zKGNvbmZpZyksIG9uQ2hhbmdlLCB7XG4gICAgICAgIGVxdWFsaXR5Rm46IGRlZXBFcXVhbCxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhdGNoQ29ubmVjdGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnectors: () => (/* binding */ watchConnectors)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchConnectors */\nfunction watchConnectors(config, parameters) {\n    const { onChange } = parameters;\n    return config._internal.connectors.subscribe((connectors, prevConnectors) => {\n        onChange(Object.values(connectors), prevConnectors);\n    });\n}\n//# sourceMappingURL=watchConnectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENvbm5lY3RvcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcYWN0aW9uc1xcd2F0Y2hDb25uZWN0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd2F0Y2hDb25uZWN0b3JzICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hDb25uZWN0b3JzKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgb25DaGFuZ2UgfSA9IHBhcmFtZXRlcnM7XG4gICAgcmV0dXJuIGNvbmZpZy5faW50ZXJuYWwuY29ubmVjdG9ycy5zdWJzY3JpYmUoKGNvbm5lY3RvcnMsIHByZXZDb25uZWN0b3JzKSA9PiB7XG4gICAgICAgIG9uQ2hhbmdlKE9iamVjdC52YWx1ZXMoY29ubmVjdG9ycyksIHByZXZDb25uZWN0b3JzKTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhdGNoQ29ubmVjdG9ycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchPendingTransactions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchPendingTransactions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchPendingTransactions: () => (/* binding */ watchPendingTransactions)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/watchPendingTransactions.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchPendingTransactions */\nfunction watchPendingTransactions(config, parameters) {\n    const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } = parameters;\n    let unwatch;\n    const listener = (chainId) => {\n        if (unwatch)\n            unwatch();\n        const client = config.getClient({ chainId });\n        const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.watchPendingTransactions, 'watchPendingTransactions');\n        unwatch = action(rest);\n        return unwatch;\n    };\n    // set up listener for transaction changes\n    const unlisten = listener(parameters.chainId);\n    // set up subscriber for connected chain changes\n    let unsubscribe;\n    if (syncConnectedChain && !parameters.chainId)\n        unsubscribe = config.subscribe(({ chainId }) => chainId, async (chainId) => listener(chainId));\n    return () => {\n        unlisten?.();\n        unsubscribe?.();\n    };\n}\n//# sourceMappingURL=watchPendingTransactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchPendingTransactions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/writeContract.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContract: () => (/* binding */ writeContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/writeContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nasync function writeContract(config, parameters) {\n    const { account, chainId, connector, ...request } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.writeContract, 'writeContract');\n    const hash = await action({\n        ...request,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n    });\n    return hash;\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93cml0ZUNvbnRyYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0U7QUFDbEI7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSwwQ0FBMEM7QUFDdEQ7QUFDQTtBQUNBLG9DQUFvQyxTQUFTO0FBQzdDO0FBQ0EsdUJBQXVCLDBFQUFrQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUJBQW1CLDhEQUFTLFNBQVMsdURBQWtCO0FBQ3ZEO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVSxJQUFJO0FBQ3RDLDJCQUEyQixjQUFjO0FBQ3pDLEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxhY3Rpb25zXFx3cml0ZUNvbnRyYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdyaXRlQ29udHJhY3QgYXMgdmllbV93cml0ZUNvbnRyYWN0LCB9IGZyb20gJ3ZpZW0vYWN0aW9ucyc7XG5pbXBvcnQgeyBnZXRBY3Rpb24gfSBmcm9tICcuLi91dGlscy9nZXRBY3Rpb24uanMnO1xuaW1wb3J0IHsgZ2V0Q29ubmVjdG9yQ2xpZW50LCB9IGZyb20gJy4vZ2V0Q29ubmVjdG9yQ2xpZW50LmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd3JpdGVDb250cmFjdCAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHdyaXRlQ29udHJhY3QoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhY2NvdW50LCBjaGFpbklkLCBjb25uZWN0b3IsIC4uLnJlcXVlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgbGV0IGNsaWVudDtcbiAgICBpZiAodHlwZW9mIGFjY291bnQgPT09ICdvYmplY3QnICYmIGFjY291bnQ/LnR5cGUgPT09ICdsb2NhbCcpXG4gICAgICAgIGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGVsc2VcbiAgICAgICAgY2xpZW50ID0gYXdhaXQgZ2V0Q29ubmVjdG9yQ2xpZW50KGNvbmZpZywge1xuICAgICAgICAgICAgYWNjb3VudDogYWNjb3VudCA/PyB1bmRlZmluZWQsXG4gICAgICAgICAgICBjaGFpbklkLFxuICAgICAgICAgICAgY29ubmVjdG9yLFxuICAgICAgICB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX3dyaXRlQ29udHJhY3QsICd3cml0ZUNvbnRyYWN0Jyk7XG4gICAgY29uc3QgaGFzaCA9IGF3YWl0IGFjdGlvbih7XG4gICAgICAgIC4uLnJlcXVlc3QsXG4gICAgICAgIC4uLihhY2NvdW50ID8geyBhY2NvdW50IH0gOiB7fSksXG4gICAgICAgIGNoYWluOiBjaGFpbklkID8geyBpZDogY2hhaW5JZCB9IDogbnVsbCxcbiAgICB9KTtcbiAgICByZXR1cm4gaGFzaDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdyaXRlQ29udHJhY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConnector: () => (/* binding */ createConnector)\n/* harmony export */ });\nfunction createConnector(createConnectorFn) {\n    return createConnectorFn;\n}\n//# sourceMappingURL=createConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vY29ubmVjdG9ycy9jcmVhdGVDb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcY29ubmVjdG9yc1xcY3JlYXRlQ29ubmVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDb25uZWN0b3IoY3JlYXRlQ29ubmVjdG9yRm4pIHtcbiAgICByZXR1cm4gY3JlYXRlQ29ubmVjdG9yRm47XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGVDb25uZWN0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/injected.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injected: () => (/* binding */ injected)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\ninjected.type = 'injected';\nfunction injected(parameters = {}) {\n    const { shimDisconnect = true, unstable_shimAsyncInject } = parameters;\n    function getTarget() {\n        const target = parameters.target;\n        if (typeof target === 'function') {\n            const result = target();\n            if (result)\n                return result;\n        }\n        if (typeof target === 'object')\n            return target;\n        if (typeof target === 'string')\n            return {\n                ...(targetMap[target] ?? {\n                    id: target,\n                    name: `${target[0].toUpperCase()}${target.slice(1)}`,\n                    provider: `is${target[0].toUpperCase()}${target.slice(1)}`,\n                }),\n            };\n        return {\n            id: 'injected',\n            name: 'Injected',\n            provider(window) {\n                return window?.ethereum;\n            },\n        };\n    }\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let disconnect;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        get icon() {\n            return getTarget().icon;\n        },\n        get id() {\n            return getTarget().id;\n        },\n        get name() {\n            return getTarget().name;\n        },\n        /** @deprecated */\n        get supportsSimulation() {\n            return true;\n        },\n        type: injected.type,\n        async setup() {\n            const provider = await this.getProvider();\n            // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n            if (provider?.on && parameters.target) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            else if (shimDisconnect) {\n                // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n                try {\n                    const permissions = await provider.request({\n                        method: 'wallet_requestPermissions',\n                        params: [{ eth_accounts: {} }],\n                    });\n                    accounts = permissions[0]?.caveats?.[0]?.value?.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                    // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n                    // switch to `'eth_accounts'` ordering if more than one account is connected\n                    // https://github.com/wevm/wagmi/issues/4140\n                    if (accounts.length > 0) {\n                        const sortedAccounts = await this.getAccounts();\n                        accounts = sortedAccounts;\n                    }\n                }\n                catch (err) {\n                    const error = err;\n                    // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n                    // Only bubble up error if user rejects request\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    // Or prompt is already open\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                        throw error;\n                }\n            }\n            try {\n                if (!accounts?.length && !isReconnecting) {\n                    const requestedAccounts = await provider.request({\n                        method: 'eth_requestAccounts',\n                    });\n                    accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                }\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n                // Add connected shim if no target exists\n                if (!parameters.target)\n                    await config.storage?.setItem('injected.connected', true);\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            // Experimental support for MetaMask disconnect\n            // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n            try {\n                // Adding timeout as not all wallets support this method and can hang\n                // https://github.com/wevm/wagmi/issues/4064\n                await (0,viem__WEBPACK_IMPORTED_MODULE_4__.withTimeout)(() => \n                // TODO: Remove explicit type for viem@3\n                provider.request({\n                    // `'wallet_revokePermissions'` added in `viem@2.10.3`\n                    method: 'wallet_revokePermissions',\n                    params: [{ eth_accounts: {} }],\n                }), { timeout: 100 });\n            }\n            catch { }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect) {\n                await config.storage?.setItem(`${this.id}.disconnected`, true);\n            }\n            if (!parameters.target)\n                await config.storage?.removeItem('injected.connected');\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return Number(hexChainId);\n        },\n        async getProvider() {\n            if (typeof window === 'undefined')\n                return undefined;\n            let provider;\n            const target = getTarget();\n            if (typeof target.provider === 'function')\n                provider = target.provider(window);\n            else if (typeof target.provider === 'string')\n                provider = findProvider(window, target.provider);\n            else\n                provider = target.provider;\n            // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n            // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n            if (provider && !provider.removeListener) {\n                // Try using `off` handler if it exists, otherwise noop\n                if ('off' in provider && typeof provider.off === 'function')\n                    provider.removeListener =\n                        provider.off;\n                else\n                    provider.removeListener = () => { };\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem(`${this.id}.disconnected`));\n                if (isDisconnected)\n                    return false;\n                // Don't allow injected connector to connect if no target is set and it hasn't already connected\n                // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n                // automatically whenever there is a targeted connector configured.\n                if (!parameters.target) {\n                    const connected = await config.storage?.getItem('injected.connected');\n                    if (!connected)\n                        return false;\n                }\n                const provider = await this.getProvider();\n                if (!provider) {\n                    if (unstable_shimAsyncInject !== undefined &&\n                        unstable_shimAsyncInject !== false) {\n                        // If no provider is found, check for async injection\n                        // https://github.com/wevm/references/issues/167\n                        // https://github.com/MetaMask/detect-provider\n                        const handleEthereum = async () => {\n                            if (typeof window !== 'undefined')\n                                window.removeEventListener('ethereum#initialized', handleEthereum);\n                            const provider = await this.getProvider();\n                            return !!provider;\n                        };\n                        const timeout = typeof unstable_shimAsyncInject === 'number'\n                            ? unstable_shimAsyncInject\n                            : 1_000;\n                        const res = await Promise.race([\n                            ...(typeof window !== 'undefined'\n                                ? [\n                                    new Promise((resolve) => window.addEventListener('ethereum#initialized', () => resolve(handleEthereum()), { once: true })),\n                                ]\n                                : []),\n                            new Promise((resolve) => setTimeout(() => resolve(handleEthereum()), timeout)),\n                        ]);\n                        if (res)\n                            return true;\n                    }\n                    throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                }\n                // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n                // immediately resolve JSON-RPC requests on page load.\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_5__.withRetry)(() => this.getAccounts());\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError());\n            const promise = new Promise((resolve) => {\n                const listener = ((data) => {\n                    if ('chainId' in data && data.chainId === chainId) {\n                        config.emitter.off('change', listener);\n                        resolve();\n                    }\n                });\n                config.emitter.on('change', listener);\n            });\n            try {\n                await Promise.all([\n                    provider\n                        .request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId) }],\n                    })\n                        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                        // this callback or an externally emitted `'chainChanged'` event.\n                        // https://github.com/MetaMask/metamask-extension/issues/24247\n                        .then(async () => {\n                        const currentChainId = await this.getChainId();\n                        if (currentChainId === chainId)\n                            config.emitter.emit('change', { chainId });\n                    }),\n                    promise,\n                ]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else if (blockExplorer)\n                            blockExplorerUrls = [\n                                blockExplorer.url,\n                                ...Object.values(blockExplorers).map((x) => x.url),\n                            ];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await Promise.all([\n                            provider\n                                .request({\n                                method: 'wallet_addEthereumChain',\n                                params: [addEthereumChain],\n                            })\n                                .then(async () => {\n                                const currentChainId = await this.getChainId();\n                                if (currentChainId === chainId)\n                                    config.emitter.emit('change', { chainId });\n                                else\n                                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(new Error('User rejected switch after adding network.'));\n                            }),\n                            promise,\n                        ]);\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    }\n                }\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(error);\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0)\n                this.onDisconnect();\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            // Manage EIP-1193 event listeners\n            const provider = await this.getProvider();\n            if (provider) {\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n            // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n            // actually disconnected and we don't need to simulate it.\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (provider) {\n                if (chainChanged) {\n                    provider.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n            }\n        },\n    }));\n}\nconst targetMap = {\n    coinbaseWallet: {\n        id: 'coinbaseWallet',\n        name: 'Coinbase Wallet',\n        provider(window) {\n            if (window?.coinbaseWalletExtension)\n                return window.coinbaseWalletExtension;\n            return findProvider(window, 'isCoinbaseWallet');\n        },\n    },\n    metaMask: {\n        id: 'metaMask',\n        name: 'MetaMask',\n        provider(window) {\n            return findProvider(window, (provider) => {\n                if (!provider.isMetaMask)\n                    return false;\n                // Brave tries to make itself look like MetaMask\n                // Could also try RPC `web3_clientVersion` if following is unreliable\n                if (provider.isBraveWallet && !provider._events && !provider._state)\n                    return false;\n                // Other wallets that try to look like MetaMask\n                const flags = [\n                    'isApexWallet',\n                    'isAvalanche',\n                    'isBitKeep',\n                    'isBlockWallet',\n                    'isKuCoinWallet',\n                    'isMathWallet',\n                    'isOkxWallet',\n                    'isOKExWallet',\n                    'isOneInchIOSWallet',\n                    'isOneInchAndroidWallet',\n                    'isOpera',\n                    'isPhantom',\n                    'isPortal',\n                    'isRabby',\n                    'isTokenPocket',\n                    'isTokenary',\n                    'isUniswapWallet',\n                    'isZerion',\n                ];\n                for (const flag of flags)\n                    if (provider[flag])\n                        return false;\n                return true;\n            });\n        },\n    },\n    phantom: {\n        id: 'phantom',\n        name: 'Phantom',\n        provider(window) {\n            if (window?.phantom?.ethereum)\n                return window.phantom?.ethereum;\n            return findProvider(window, 'isPhantom');\n        },\n    },\n};\nfunction findProvider(window, select) {\n    function isProvider(provider) {\n        if (typeof select === 'function')\n            return select(provider);\n        if (typeof select === 'string')\n            return provider[select];\n        return true;\n    }\n    const ethereum = window.ethereum;\n    if (ethereum?.providers)\n        return ethereum.providers.find((provider) => isProvider(provider));\n    if (ethereum && isProvider(ethereum))\n        return ethereum;\n    return undefined;\n}\n//# sourceMappingURL=injected.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/mock.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mock: () => (/* binding */ mock)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/utils/rpc/compat.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\nmock.type = 'mock';\nfunction mock(parameters) {\n    const transactionCache = new Map();\n    const features = parameters.features ??\n        { defaultConnected: false };\n    let connected = features.defaultConnected;\n    let connectedChainId;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'mock',\n        name: 'Mock Connector',\n        type: mock.type,\n        async setup() {\n            connectedChainId = config.chains[0].id;\n        },\n        async connect({ chainId } = {}) {\n            if (features.connectError) {\n                if (typeof features.connectError === 'boolean')\n                    throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to connect.'));\n                throw features.connectError;\n            }\n            const provider = await this.getProvider();\n            const accounts = await provider.request({\n                method: 'eth_requestAccounts',\n            });\n            let currentChainId = await this.getChainId();\n            if (chainId && currentChainId !== chainId) {\n                const chain = await this.switchChain({ chainId });\n                currentChainId = chain.id;\n            }\n            connected = true;\n            return {\n                accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                chainId: currentChainId,\n            };\n        },\n        async disconnect() {\n            connected = false;\n        },\n        async getAccounts() {\n            if (!connected)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ConnectorNotConnectedError();\n            const provider = await this.getProvider();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(hexChainId, 'number');\n        },\n        async isAuthorized() {\n            if (!features.reconnect)\n                return false;\n            if (!connected)\n                return false;\n            const accounts = await this.getAccounts();\n            return !!accounts.length;\n        },\n        async switchChain({ chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_1__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            await provider.request({\n                method: 'wallet_switchEthereumChain',\n                params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(chainId) }],\n            });\n            return chain;\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            connected = false;\n        },\n        async getProvider({ chainId } = {}) {\n            const chain = config.chains.find((x) => x.id === chainId) ?? config.chains[0];\n            const url = chain.rpcUrls.default.http[0];\n            const request = async ({ method, params }) => {\n                // eth methods\n                if (method === 'eth_chainId')\n                    return (0,viem__WEBPACK_IMPORTED_MODULE_5__.numberToHex)(connectedChainId);\n                if (method === 'eth_requestAccounts')\n                    return parameters.accounts;\n                if (method === 'eth_signTypedData_v4')\n                    if (features.signTypedDataError) {\n                        if (typeof features.signTypedDataError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign typed data.'));\n                        throw features.signTypedDataError;\n                    }\n                // wallet methods\n                if (method === 'wallet_switchEthereumChain') {\n                    if (features.switchChainError) {\n                        if (typeof features.switchChainError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.switchChainError;\n                    }\n                    connectedChainId = (0,viem__WEBPACK_IMPORTED_MODULE_4__.fromHex)(params[0].chainId, 'number');\n                    this.onChainChanged(connectedChainId.toString());\n                    return;\n                }\n                if (method === 'wallet_watchAsset') {\n                    if (features.watchAssetError) {\n                        if (typeof features.watchAssetError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to switch chain.'));\n                        throw features.watchAssetError;\n                    }\n                    return connected;\n                }\n                if (method === 'wallet_getCapabilities')\n                    return {\n                        '0x2105': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                            sessionKeys: {\n                                supported: true,\n                            },\n                        },\n                        '0x14A34': {\n                            paymasterService: {\n                                supported: params[0] ===\n                                    '******************************************',\n                            },\n                        },\n                    };\n                if (method === 'wallet_sendCalls') {\n                    const hashes = [];\n                    const calls = params[0].calls;\n                    for (const call of calls) {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_sendTransaction',\n                                params: [call],\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        hashes.push(result);\n                    }\n                    const id = (0,viem__WEBPACK_IMPORTED_MODULE_8__.keccak256)((0,viem__WEBPACK_IMPORTED_MODULE_5__.stringToHex)(JSON.stringify(calls)));\n                    transactionCache.set(id, hashes);\n                    return { id };\n                }\n                if (method === 'wallet_getCallsStatus') {\n                    const hashes = transactionCache.get(params[0]);\n                    if (!hashes)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    const receipts = await Promise.all(hashes.map(async (hash) => {\n                        const { result, error } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, {\n                            body: {\n                                method: 'eth_getTransactionReceipt',\n                                params: [hash],\n                                id: 0,\n                            },\n                        });\n                        if (error)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({\n                                body: { method, params },\n                                error,\n                                url,\n                            });\n                        if (!result)\n                            return null;\n                        return {\n                            blockHash: result.blockHash,\n                            blockNumber: result.blockNumber,\n                            gasUsed: result.gasUsed,\n                            logs: result.logs,\n                            status: result.status,\n                            transactionHash: result.transactionHash,\n                        };\n                    }));\n                    const receipts_ = receipts.filter((x) => x !== null);\n                    if (receipts_.length === 0)\n                        return {\n                            atomic: false,\n                            chainId: '0x1',\n                            id: params[0],\n                            status: 100,\n                            receipts: [],\n                            version: '2.0.0',\n                        };\n                    return {\n                        atomic: false,\n                        chainId: '0x1',\n                        id: params[0],\n                        status: 200,\n                        receipts: receipts_,\n                        version: '2.0.0',\n                    };\n                }\n                if (method === 'wallet_showCallsStatus')\n                    return;\n                // other methods\n                if (method === 'personal_sign') {\n                    if (features.signMessageError) {\n                        if (typeof features.signMessageError === 'boolean')\n                            throw new viem__WEBPACK_IMPORTED_MODULE_1__.UserRejectedRequestError(new Error('Failed to sign message.'));\n                        throw features.signMessageError;\n                    }\n                    // Change `personal_sign` to `eth_sign` and swap params\n                    method = 'eth_sign';\n                    params = [params[1], params[0]];\n                }\n                const body = { method, params };\n                const { error, result } = await viem_utils__WEBPACK_IMPORTED_MODULE_6__.rpc.http(url, { body });\n                if (error)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_7__.RpcRequestError({ body, error, url });\n                return result;\n            };\n            return (0,viem__WEBPACK_IMPORTED_MODULE_9__.custom)({ request })({ retryCount: 0 });\n        },\n    }));\n}\n//# sourceMappingURL=mock.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/mock.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfig: () => (/* binding */ createConfig)\n/* harmony export */ });\n/* harmony import */ var mipd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mipd */ \"(ssr)/./node_modules/mipd/dist/esm/store.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var _connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors/injected.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _createEmitter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createEmitter.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\");\n/* harmony import */ var _createStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createStorage.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/uid.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\n\n\n\n\n\n\n\n\n\nfunction createConfig(parameters) {\n    const { multiInjectedProviderDiscovery = true, storage = (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.createStorage)({\n        storage: (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultStorage)(),\n    }), syncConnectedChain = true, ssr = false, ...rest } = parameters;\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Set up connectors, clients, etc.\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    const mipd = typeof window !== 'undefined' && multiInjectedProviderDiscovery\n        ? (0,mipd__WEBPACK_IMPORTED_MODULE_1__.createStore)()\n        : undefined;\n    const chains = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => rest.chains);\n    const connectors = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => {\n        const collection = [];\n        const rdnsSet = new Set();\n        for (const connectorFns of rest.connectors ?? []) {\n            const connector = setup(connectorFns);\n            collection.push(connector);\n            if (!ssr && connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    rdnsSet.add(rdns);\n                }\n            }\n        }\n        if (!ssr && mipd) {\n            const providers = mipd.getProviders();\n            for (const provider of providers) {\n                if (rdnsSet.has(provider.info.rdns))\n                    continue;\n                collection.push(setup(providerDetailToConnector(provider)));\n            }\n        }\n        return collection;\n    });\n    function setup(connectorFn) {\n        // Set up emitter with uid and add to connector so they are \"linked\" together.\n        const emitter = (0,_createEmitter_js__WEBPACK_IMPORTED_MODULE_3__.createEmitter)((0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_4__.uid)());\n        const connector = {\n            ...connectorFn({\n                emitter,\n                chains: chains.getState(),\n                storage,\n                transports: rest.transports,\n            }),\n            emitter,\n            uid: emitter.uid,\n        };\n        // Start listening for `connect` events on connector setup\n        // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n        emitter.on('connect', connect);\n        connector.setup?.();\n        return connector;\n    }\n    function providerDetailToConnector(providerDetail) {\n        const { info } = providerDetail;\n        const provider = providerDetail.provider;\n        return (0,_connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__.injected)({ target: { ...info, id: info.rdns, provider } });\n    }\n    const clients = new Map();\n    function getClient(config = {}) {\n        const chainId = config.chainId ?? store.getState().chainId;\n        const chain = chains.getState().find((x) => x.id === chainId);\n        // chainId specified and not configured\n        if (config.chainId && !chain)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        {\n            const client = clients.get(store.getState().chainId);\n            if (client && !chain)\n                return client;\n            if (!chain)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        }\n        // If a memoized client exists for a chain id, use that.\n        {\n            const client = clients.get(chainId);\n            if (client)\n                return client;\n        }\n        let client;\n        if (rest.client)\n            client = rest.client({ chain });\n        else {\n            const chainId = chain.id;\n            const chainIds = chains.getState().map((x) => x.id);\n            // Grab all properties off `rest` and resolve for use in `createClient`\n            const properties = {};\n            const entries = Object.entries(rest);\n            for (const [key, value] of entries) {\n                if (key === 'chains' ||\n                    key === 'client' ||\n                    key === 'connectors' ||\n                    key === 'transports')\n                    continue;\n                if (typeof value === 'object') {\n                    // check if value is chainId-specific since some values can be objects\n                    // e.g. { batch: { multicall: { batchSize: 1024 } } }\n                    if (chainId in value)\n                        properties[key] = value[chainId];\n                    else {\n                        // check if value is chainId-specific, but does not have value for current chainId\n                        const hasChainSpecificValue = chainIds.some((x) => x in value);\n                        if (hasChainSpecificValue)\n                            continue;\n                        properties[key] = value;\n                    }\n                }\n                else\n                    properties[key] = value;\n            }\n            client = (0,viem__WEBPACK_IMPORTED_MODULE_7__.createClient)({\n                ...properties,\n                chain,\n                batch: properties.batch ?? { multicall: true },\n                transport: (parameters) => rest.transports[chainId]({ ...parameters, connectors }),\n            });\n        }\n        clients.set(chainId, client);\n        return client;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Create store\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function getInitialState() {\n        return {\n            chainId: chains.getState()[0].id,\n            connections: new Map(),\n            current: null,\n            status: 'disconnected',\n        };\n    }\n    let currentVersion;\n    const prefix = '0.0.0-canary-';\n    if (_version_js__WEBPACK_IMPORTED_MODULE_8__.version.startsWith(prefix))\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.replace(prefix, ''));\n    // use package major version to version store\n    else\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.split('.')[0] ?? '0');\n    const store = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.subscribeWithSelector)(\n    // only use persist middleware if storage exists\n    storage\n        ? (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.persist)(getInitialState, {\n            migrate(persistedState, version) {\n                if (version === currentVersion)\n                    return persistedState;\n                const initialState = getInitialState();\n                const chainId = validatePersistedChainId(persistedState, initialState.chainId);\n                return { ...initialState, chainId };\n            },\n            name: 'store',\n            partialize(state) {\n                // Only persist \"critical\" store properties to preserve storage size.\n                return {\n                    connections: {\n                        __type: 'Map',\n                        value: Array.from(state.connections.entries()).map(([key, connection]) => {\n                            const { id, name, type, uid } = connection.connector;\n                            const connector = { id, name, type, uid };\n                            return [key, { ...connection, connector }];\n                        }),\n                    },\n                    chainId: state.chainId,\n                    current: state.current,\n                };\n            },\n            merge(persistedState, currentState) {\n                // `status` should not be persisted as it messes with reconnection\n                if (typeof persistedState === 'object' &&\n                    persistedState &&\n                    'status' in persistedState)\n                    delete persistedState.status;\n                // Make sure persisted `chainId` is valid\n                const chainId = validatePersistedChainId(persistedState, currentState.chainId);\n                return {\n                    ...currentState,\n                    ...persistedState,\n                    chainId,\n                };\n            },\n            skipHydration: ssr,\n            storage: storage,\n            version: currentVersion,\n        })\n        : getInitialState));\n    store.setState(getInitialState());\n    function validatePersistedChainId(persistedState, defaultChainId) {\n        return persistedState &&\n            typeof persistedState === 'object' &&\n            'chainId' in persistedState &&\n            typeof persistedState.chainId === 'number' &&\n            chains.getState().some((x) => x.id === persistedState.chainId)\n            ? persistedState.chainId\n            : defaultChainId;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Subscribe to changes\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Update default chain when connector chain changes\n    if (syncConnectedChain)\n        store.subscribe(({ connections, current }) => current ? connections.get(current)?.chainId : undefined, (chainId) => {\n            // If chain is not configured, then don't switch over to it.\n            const isChainConfigured = chains\n                .getState()\n                .some((x) => x.id === chainId);\n            if (!isChainConfigured)\n                return;\n            return store.setState((x) => ({\n                ...x,\n                chainId: chainId ?? x.chainId,\n            }));\n        });\n    // EIP-6963 subscribe for new wallet providers\n    mipd?.subscribe((providerDetails) => {\n        const connectorIdSet = new Set();\n        const connectorRdnsSet = new Set();\n        for (const connector of connectors.getState()) {\n            connectorIdSet.add(connector.id);\n            if (connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    connectorRdnsSet.add(rdns);\n                }\n            }\n        }\n        const newConnectors = [];\n        for (const providerDetail of providerDetails) {\n            if (connectorRdnsSet.has(providerDetail.info.rdns))\n                continue;\n            const connector = setup(providerDetailToConnector(providerDetail));\n            if (connectorIdSet.has(connector.id))\n                continue;\n            newConnectors.push(connector);\n        }\n        if (storage && !store.persist.hasHydrated())\n            return;\n        connectors.setState((x) => [...x, ...newConnectors], true);\n    });\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Emitter listeners\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function change(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (!connection)\n                return x;\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts ??\n                        connection.accounts,\n                    chainId: data.chainId ?? connection.chainId,\n                    connector: connection.connector,\n                }),\n            };\n        });\n    }\n    function connect(data) {\n        // Disable handling if reconnecting/connecting\n        if (store.getState().status === 'connecting' ||\n            store.getState().status === 'reconnecting')\n            return;\n        store.setState((x) => {\n            const connector = connectors.getState().find((x) => x.uid === data.uid);\n            if (!connector)\n                return x;\n            if (connector.emitter.listenerCount('connect'))\n                connector.emitter.off('connect', change);\n            if (!connector.emitter.listenerCount('change'))\n                connector.emitter.on('change', change);\n            if (!connector.emitter.listenerCount('disconnect'))\n                connector.emitter.on('disconnect', disconnect);\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts,\n                    chainId: data.chainId,\n                    connector: connector,\n                }),\n                current: data.uid,\n                status: 'connected',\n            };\n        });\n    }\n    function disconnect(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (connection) {\n                const connector = connection.connector;\n                if (connector.emitter.listenerCount('change'))\n                    connection.connector.emitter.off('change', change);\n                if (connector.emitter.listenerCount('disconnect'))\n                    connection.connector.emitter.off('disconnect', disconnect);\n                if (!connector.emitter.listenerCount('connect'))\n                    connection.connector.emitter.on('connect', connect);\n            }\n            x.connections.delete(data.uid);\n            if (x.connections.size === 0)\n                return {\n                    ...x,\n                    connections: new Map(),\n                    current: null,\n                    status: 'disconnected',\n                };\n            const nextConnection = x.connections.values().next().value;\n            return {\n                ...x,\n                connections: new Map(x.connections),\n                current: nextConnection.connector.uid,\n            };\n        });\n    }\n    return {\n        get chains() {\n            return chains.getState();\n        },\n        get connectors() {\n            return connectors.getState();\n        },\n        storage,\n        getClient,\n        get state() {\n            return store.getState();\n        },\n        setState(value) {\n            let newState;\n            if (typeof value === 'function')\n                newState = value(store.getState());\n            else\n                newState = value;\n            // Reset state if it got set to something not matching the base state\n            const initialState = getInitialState();\n            if (typeof newState !== 'object')\n                newState = initialState;\n            const isCorrupt = Object.keys(initialState).some((x) => !(x in newState));\n            if (isCorrupt)\n                newState = initialState;\n            store.setState(newState, true);\n        },\n        subscribe(selector, listener, options) {\n            return store.subscribe(selector, listener, options\n                ? {\n                    ...options,\n                    fireImmediately: options.emitImmediately,\n                    // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n                }\n                : undefined);\n        },\n        _internal: {\n            mipd,\n            store,\n            ssr: Boolean(ssr),\n            syncConnectedChain,\n            transports: rest.transports,\n            chains: {\n                setState(value) {\n                    const nextChains = (typeof value === 'function' ? value(chains.getState()) : value);\n                    if (nextChains.length === 0)\n                        return;\n                    return chains.setState(nextChains, true);\n                },\n                subscribe(listener) {\n                    return chains.subscribe(listener);\n                },\n            },\n            connectors: {\n                providerDetailToConnector,\n                setup: setup,\n                setState(value) {\n                    return connectors.setState(typeof value === 'function' ? value(connectors.getState()) : value, true);\n                },\n                subscribe(listener) {\n                    return connectors.subscribe(listener);\n                },\n            },\n            events: { change, connect, disconnect },\n        },\n    };\n}\n//# sourceMappingURL=createConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vY3JlYXRlQ29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2I7QUFDK0I7QUFDdEI7QUFDTTtBQUNEO0FBQ29CO0FBQ1Y7QUFDeEI7QUFDRTtBQUNoQztBQUNQLFlBQVksaURBQWlELGdFQUFhO0FBQzFFLGlCQUFpQixvRUFBaUI7QUFDbEMsS0FBSyxxREFBcUQ7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGlEQUFVO0FBQ3BCO0FBQ0EsbUJBQW1CLDREQUFXO0FBQzlCLHVCQUF1Qiw0REFBVztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHdCQUF3QixnRUFBYSxDQUFDLGtEQUFHO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixPQUFPO0FBQ3ZCO0FBQ0EsZUFBZSxpRUFBUSxHQUFHLFVBQVUsb0NBQW9DO0FBQ3hFO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isc0VBQXVCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsc0VBQXVCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxPQUFPO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsU0FBUyxhQUFhO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGtEQUFZO0FBQ2pDO0FBQ0E7QUFDQSw2Q0FBNkMsaUJBQWlCO0FBQzlELHNFQUFzRSwyQkFBMkI7QUFDakcsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdEQUFPO0FBQ2YseUNBQXlDLGdEQUFPO0FBQ2hEO0FBQ0E7QUFDQSx5Q0FBeUMsZ0RBQU87QUFDaEQsa0JBQWtCLDREQUFXLENBQUMseUVBQXFCO0FBQ25EO0FBQ0E7QUFDQSxVQUFVLDJEQUFPO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNCQUFzQjtBQUMxRCxnREFBZ0Q7QUFDaEQsMkNBQTJDLDBCQUEwQjtBQUNyRSx5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsc0JBQXNCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2Isc0JBQXNCLDZCQUE2QjtBQUNuRCxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxcY3JlYXRlQ29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVN0b3JlIGFzIGNyZWF0ZU1pcGQsIH0gZnJvbSAnbWlwZCc7XG5pbXBvcnQgeyBjcmVhdGVDbGllbnQsIH0gZnJvbSAndmllbSc7XG5pbXBvcnQgeyBwZXJzaXN0LCBzdWJzY3JpYmVXaXRoU2VsZWN0b3IgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnO1xuaW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tICd6dXN0YW5kL3ZhbmlsbGEnO1xuaW1wb3J0IHsgaW5qZWN0ZWQgfSBmcm9tICcuL2Nvbm5lY3RvcnMvaW5qZWN0ZWQuanMnO1xuaW1wb3J0IHsgY3JlYXRlRW1pdHRlciB9IGZyb20gJy4vY3JlYXRlRW1pdHRlci5qcyc7XG5pbXBvcnQgeyBjcmVhdGVTdG9yYWdlLCBnZXREZWZhdWx0U3RvcmFnZSwgfSBmcm9tICcuL2NyZWF0ZVN0b3JhZ2UuanMnO1xuaW1wb3J0IHsgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IgfSBmcm9tICcuL2Vycm9ycy9jb25maWcuanMnO1xuaW1wb3J0IHsgdWlkIH0gZnJvbSAnLi91dGlscy91aWQuanMnO1xuaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbi5qcyc7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ29uZmlnKHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG11bHRpSW5qZWN0ZWRQcm92aWRlckRpc2NvdmVyeSA9IHRydWUsIHN0b3JhZ2UgPSBjcmVhdGVTdG9yYWdlKHtcbiAgICAgICAgc3RvcmFnZTogZ2V0RGVmYXVsdFN0b3JhZ2UoKSxcbiAgICB9KSwgc3luY0Nvbm5lY3RlZENoYWluID0gdHJ1ZSwgc3NyID0gZmFsc2UsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuICAgIC8vIFNldCB1cCBjb25uZWN0b3JzLCBjbGllbnRzLCBldGMuXG4gICAgLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuICAgIGNvbnN0IG1pcGQgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiBtdWx0aUluamVjdGVkUHJvdmlkZXJEaXNjb3ZlcnlcbiAgICAgICAgPyBjcmVhdGVNaXBkKClcbiAgICAgICAgOiB1bmRlZmluZWQ7XG4gICAgY29uc3QgY2hhaW5zID0gY3JlYXRlU3RvcmUoKCkgPT4gcmVzdC5jaGFpbnMpO1xuICAgIGNvbnN0IGNvbm5lY3RvcnMgPSBjcmVhdGVTdG9yZSgoKSA9PiB7XG4gICAgICAgIGNvbnN0IGNvbGxlY3Rpb24gPSBbXTtcbiAgICAgICAgY29uc3QgcmRuc1NldCA9IG5ldyBTZXQoKTtcbiAgICAgICAgZm9yIChjb25zdCBjb25uZWN0b3JGbnMgb2YgcmVzdC5jb25uZWN0b3JzID8/IFtdKSB7XG4gICAgICAgICAgICBjb25zdCBjb25uZWN0b3IgPSBzZXR1cChjb25uZWN0b3JGbnMpO1xuICAgICAgICAgICAgY29sbGVjdGlvbi5wdXNoKGNvbm5lY3Rvcik7XG4gICAgICAgICAgICBpZiAoIXNzciAmJiBjb25uZWN0b3IucmRucykge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJkbnNWYWx1ZXMgPSB0eXBlb2YgY29ubmVjdG9yLnJkbnMgPT09ICdzdHJpbmcnID8gW2Nvbm5lY3Rvci5yZG5zXSA6IGNvbm5lY3Rvci5yZG5zO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgcmRucyBvZiByZG5zVmFsdWVzKSB7XG4gICAgICAgICAgICAgICAgICAgIHJkbnNTZXQuYWRkKHJkbnMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIXNzciAmJiBtaXBkKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlcnMgPSBtaXBkLmdldFByb3ZpZGVycygpO1xuICAgICAgICAgICAgZm9yIChjb25zdCBwcm92aWRlciBvZiBwcm92aWRlcnMpIHtcbiAgICAgICAgICAgICAgICBpZiAocmRuc1NldC5oYXMocHJvdmlkZXIuaW5mby5yZG5zKSlcbiAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgY29sbGVjdGlvbi5wdXNoKHNldHVwKHByb3ZpZGVyRGV0YWlsVG9Db25uZWN0b3IocHJvdmlkZXIpKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbGxlY3Rpb247XG4gICAgfSk7XG4gICAgZnVuY3Rpb24gc2V0dXAoY29ubmVjdG9yRm4pIHtcbiAgICAgICAgLy8gU2V0IHVwIGVtaXR0ZXIgd2l0aCB1aWQgYW5kIGFkZCB0byBjb25uZWN0b3Igc28gdGhleSBhcmUgXCJsaW5rZWRcIiB0b2dldGhlci5cbiAgICAgICAgY29uc3QgZW1pdHRlciA9IGNyZWF0ZUVtaXR0ZXIodWlkKCkpO1xuICAgICAgICBjb25zdCBjb25uZWN0b3IgPSB7XG4gICAgICAgICAgICAuLi5jb25uZWN0b3JGbih7XG4gICAgICAgICAgICAgICAgZW1pdHRlcixcbiAgICAgICAgICAgICAgICBjaGFpbnM6IGNoYWlucy5nZXRTdGF0ZSgpLFxuICAgICAgICAgICAgICAgIHN0b3JhZ2UsXG4gICAgICAgICAgICAgICAgdHJhbnNwb3J0czogcmVzdC50cmFuc3BvcnRzLFxuICAgICAgICAgICAgfSksXG4gICAgICAgICAgICBlbWl0dGVyLFxuICAgICAgICAgICAgdWlkOiBlbWl0dGVyLnVpZCxcbiAgICAgICAgfTtcbiAgICAgICAgLy8gU3RhcnQgbGlzdGVuaW5nIGZvciBgY29ubmVjdGAgZXZlbnRzIG9uIGNvbm5lY3RvciBzZXR1cFxuICAgICAgICAvLyBUaGlzIGFsbG93cyBjb25uZWN0b3JzIHRvIFwiY29ubmVjdFwiIHRoZW1zZWx2ZXMgd2l0aG91dCB1c2VyIGludGVyYWN0aW9uIChlLmcuIE1ldGFNYXNrJ3MgXCJNYW51YWxseSBjb25uZWN0IHRvIGN1cnJlbnQgc2l0ZVwiKVxuICAgICAgICBlbWl0dGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgIGNvbm5lY3Rvci5zZXR1cD8uKCk7XG4gICAgICAgIHJldHVybiBjb25uZWN0b3I7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHByb3ZpZGVyRGV0YWlsVG9Db25uZWN0b3IocHJvdmlkZXJEZXRhaWwpIHtcbiAgICAgICAgY29uc3QgeyBpbmZvIH0gPSBwcm92aWRlckRldGFpbDtcbiAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBwcm92aWRlckRldGFpbC5wcm92aWRlcjtcbiAgICAgICAgcmV0dXJuIGluamVjdGVkKHsgdGFyZ2V0OiB7IC4uLmluZm8sIGlkOiBpbmZvLnJkbnMsIHByb3ZpZGVyIH0gfSk7XG4gICAgfVxuICAgIGNvbnN0IGNsaWVudHMgPSBuZXcgTWFwKCk7XG4gICAgZnVuY3Rpb24gZ2V0Q2xpZW50KGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIGNvbnN0IGNoYWluSWQgPSBjb25maWcuY2hhaW5JZCA/PyBzdG9yZS5nZXRTdGF0ZSgpLmNoYWluSWQ7XG4gICAgICAgIGNvbnN0IGNoYWluID0gY2hhaW5zLmdldFN0YXRlKCkuZmluZCgoeCkgPT4geC5pZCA9PT0gY2hhaW5JZCk7XG4gICAgICAgIC8vIGNoYWluSWQgc3BlY2lmaWVkIGFuZCBub3QgY29uZmlndXJlZFxuICAgICAgICBpZiAoY29uZmlnLmNoYWluSWQgJiYgIWNoYWluKVxuICAgICAgICAgICAgdGhyb3cgbmV3IENoYWluTm90Q29uZmlndXJlZEVycm9yKCk7XG4gICAgICAgIHtcbiAgICAgICAgICAgIGNvbnN0IGNsaWVudCA9IGNsaWVudHMuZ2V0KHN0b3JlLmdldFN0YXRlKCkuY2hhaW5JZCk7XG4gICAgICAgICAgICBpZiAoY2xpZW50ICYmICFjaGFpbilcbiAgICAgICAgICAgICAgICByZXR1cm4gY2xpZW50O1xuICAgICAgICAgICAgaWYgKCFjaGFpbilcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IoKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBJZiBhIG1lbW9pemVkIGNsaWVudCBleGlzdHMgZm9yIGEgY2hhaW4gaWQsIHVzZSB0aGF0LlxuICAgICAgICB7XG4gICAgICAgICAgICBjb25zdCBjbGllbnQgPSBjbGllbnRzLmdldChjaGFpbklkKTtcbiAgICAgICAgICAgIGlmIChjbGllbnQpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNsaWVudDtcbiAgICAgICAgfVxuICAgICAgICBsZXQgY2xpZW50O1xuICAgICAgICBpZiAocmVzdC5jbGllbnQpXG4gICAgICAgICAgICBjbGllbnQgPSByZXN0LmNsaWVudCh7IGNoYWluIH0pO1xuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IGNoYWluSWQgPSBjaGFpbi5pZDtcbiAgICAgICAgICAgIGNvbnN0IGNoYWluSWRzID0gY2hhaW5zLmdldFN0YXRlKCkubWFwKCh4KSA9PiB4LmlkKTtcbiAgICAgICAgICAgIC8vIEdyYWIgYWxsIHByb3BlcnRpZXMgb2ZmIGByZXN0YCBhbmQgcmVzb2x2ZSBmb3IgdXNlIGluIGBjcmVhdGVDbGllbnRgXG4gICAgICAgICAgICBjb25zdCBwcm9wZXJ0aWVzID0ge307XG4gICAgICAgICAgICBjb25zdCBlbnRyaWVzID0gT2JqZWN0LmVudHJpZXMocmVzdCk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBlbnRyaWVzKSB7XG4gICAgICAgICAgICAgICAgaWYgKGtleSA9PT0gJ2NoYWlucycgfHxcbiAgICAgICAgICAgICAgICAgICAga2V5ID09PSAnY2xpZW50JyB8fFxuICAgICAgICAgICAgICAgICAgICBrZXkgPT09ICdjb25uZWN0b3JzJyB8fFxuICAgICAgICAgICAgICAgICAgICBrZXkgPT09ICd0cmFuc3BvcnRzJylcbiAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gY2hlY2sgaWYgdmFsdWUgaXMgY2hhaW5JZC1zcGVjaWZpYyBzaW5jZSBzb21lIHZhbHVlcyBjYW4gYmUgb2JqZWN0c1xuICAgICAgICAgICAgICAgICAgICAvLyBlLmcuIHsgYmF0Y2g6IHsgbXVsdGljYWxsOiB7IGJhdGNoU2l6ZTogMTAyNCB9IH0gfVxuICAgICAgICAgICAgICAgICAgICBpZiAoY2hhaW5JZCBpbiB2YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb3BlcnRpZXNba2V5XSA9IHZhbHVlW2NoYWluSWRdO1xuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNoZWNrIGlmIHZhbHVlIGlzIGNoYWluSWQtc3BlY2lmaWMsIGJ1dCBkb2VzIG5vdCBoYXZlIHZhbHVlIGZvciBjdXJyZW50IGNoYWluSWRcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc0NoYWluU3BlY2lmaWNWYWx1ZSA9IGNoYWluSWRzLnNvbWUoKHgpID0+IHggaW4gdmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGhhc0NoYWluU3BlY2lmaWNWYWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb3BlcnRpZXNba2V5XSA9IHZhbHVlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICAgICAgcHJvcGVydGllc1trZXldID0gdmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjbGllbnQgPSBjcmVhdGVDbGllbnQoe1xuICAgICAgICAgICAgICAgIC4uLnByb3BlcnRpZXMsXG4gICAgICAgICAgICAgICAgY2hhaW4sXG4gICAgICAgICAgICAgICAgYmF0Y2g6IHByb3BlcnRpZXMuYmF0Y2ggPz8geyBtdWx0aWNhbGw6IHRydWUgfSxcbiAgICAgICAgICAgICAgICB0cmFuc3BvcnQ6IChwYXJhbWV0ZXJzKSA9PiByZXN0LnRyYW5zcG9ydHNbY2hhaW5JZF0oeyAuLi5wYXJhbWV0ZXJzLCBjb25uZWN0b3JzIH0pLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2xpZW50cy5zZXQoY2hhaW5JZCwgY2xpZW50KTtcbiAgICAgICAgcmV0dXJuIGNsaWVudDtcbiAgICB9XG4gICAgLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuICAgIC8vIENyZWF0ZSBzdG9yZVxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cbiAgICBmdW5jdGlvbiBnZXRJbml0aWFsU3RhdGUoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBjaGFpbklkOiBjaGFpbnMuZ2V0U3RhdGUoKVswXS5pZCxcbiAgICAgICAgICAgIGNvbm5lY3Rpb25zOiBuZXcgTWFwKCksXG4gICAgICAgICAgICBjdXJyZW50OiBudWxsLFxuICAgICAgICAgICAgc3RhdHVzOiAnZGlzY29ubmVjdGVkJyxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgbGV0IGN1cnJlbnRWZXJzaW9uO1xuICAgIGNvbnN0IHByZWZpeCA9ICcwLjAuMC1jYW5hcnktJztcbiAgICBpZiAodmVyc2lvbi5zdGFydHNXaXRoKHByZWZpeCkpXG4gICAgICAgIGN1cnJlbnRWZXJzaW9uID0gTnVtYmVyLnBhcnNlSW50KHZlcnNpb24ucmVwbGFjZShwcmVmaXgsICcnKSk7XG4gICAgLy8gdXNlIHBhY2thZ2UgbWFqb3IgdmVyc2lvbiB0byB2ZXJzaW9uIHN0b3JlXG4gICAgZWxzZVxuICAgICAgICBjdXJyZW50VmVyc2lvbiA9IE51bWJlci5wYXJzZUludCh2ZXJzaW9uLnNwbGl0KCcuJylbMF0gPz8gJzAnKTtcbiAgICBjb25zdCBzdG9yZSA9IGNyZWF0ZVN0b3JlKHN1YnNjcmliZVdpdGhTZWxlY3RvcihcbiAgICAvLyBvbmx5IHVzZSBwZXJzaXN0IG1pZGRsZXdhcmUgaWYgc3RvcmFnZSBleGlzdHNcbiAgICBzdG9yYWdlXG4gICAgICAgID8gcGVyc2lzdChnZXRJbml0aWFsU3RhdGUsIHtcbiAgICAgICAgICAgIG1pZ3JhdGUocGVyc2lzdGVkU3RhdGUsIHZlcnNpb24pIHtcbiAgICAgICAgICAgICAgICBpZiAodmVyc2lvbiA9PT0gY3VycmVudFZlcnNpb24pXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBwZXJzaXN0ZWRTdGF0ZTtcbiAgICAgICAgICAgICAgICBjb25zdCBpbml0aWFsU3RhdGUgPSBnZXRJbml0aWFsU3RhdGUoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBjaGFpbklkID0gdmFsaWRhdGVQZXJzaXN0ZWRDaGFpbklkKHBlcnNpc3RlZFN0YXRlLCBpbml0aWFsU3RhdGUuY2hhaW5JZCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uaW5pdGlhbFN0YXRlLCBjaGFpbklkIH07XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgbmFtZTogJ3N0b3JlJyxcbiAgICAgICAgICAgIHBhcnRpYWxpemUoc3RhdGUpIHtcbiAgICAgICAgICAgICAgICAvLyBPbmx5IHBlcnNpc3QgXCJjcml0aWNhbFwiIHN0b3JlIHByb3BlcnRpZXMgdG8gcHJlc2VydmUgc3RvcmFnZSBzaXplLlxuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbm5lY3Rpb25zOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfX3R5cGU6ICdNYXAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IEFycmF5LmZyb20oc3RhdGUuY29ubmVjdGlvbnMuZW50cmllcygpKS5tYXAoKFtrZXksIGNvbm5lY3Rpb25dKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBpZCwgbmFtZSwgdHlwZSwgdWlkIH0gPSBjb25uZWN0aW9uLmNvbm5lY3RvcjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb25uZWN0b3IgPSB7IGlkLCBuYW1lLCB0eXBlLCB1aWQgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gW2tleSwgeyAuLi5jb25uZWN0aW9uLCBjb25uZWN0b3IgfV07XG4gICAgICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgY2hhaW5JZDogc3RhdGUuY2hhaW5JZCxcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudDogc3RhdGUuY3VycmVudCxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG1lcmdlKHBlcnNpc3RlZFN0YXRlLCBjdXJyZW50U3RhdGUpIHtcbiAgICAgICAgICAgICAgICAvLyBgc3RhdHVzYCBzaG91bGQgbm90IGJlIHBlcnNpc3RlZCBhcyBpdCBtZXNzZXMgd2l0aCByZWNvbm5lY3Rpb25cbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHBlcnNpc3RlZFN0YXRlID09PSAnb2JqZWN0JyAmJlxuICAgICAgICAgICAgICAgICAgICBwZXJzaXN0ZWRTdGF0ZSAmJlxuICAgICAgICAgICAgICAgICAgICAnc3RhdHVzJyBpbiBwZXJzaXN0ZWRTdGF0ZSlcbiAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHBlcnNpc3RlZFN0YXRlLnN0YXR1cztcbiAgICAgICAgICAgICAgICAvLyBNYWtlIHN1cmUgcGVyc2lzdGVkIGBjaGFpbklkYCBpcyB2YWxpZFxuICAgICAgICAgICAgICAgIGNvbnN0IGNoYWluSWQgPSB2YWxpZGF0ZVBlcnNpc3RlZENoYWluSWQocGVyc2lzdGVkU3RhdGUsIGN1cnJlbnRTdGF0ZS5jaGFpbklkKTtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAuLi5jdXJyZW50U3RhdGUsXG4gICAgICAgICAgICAgICAgICAgIC4uLnBlcnNpc3RlZFN0YXRlLFxuICAgICAgICAgICAgICAgICAgICBjaGFpbklkLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc2tpcEh5ZHJhdGlvbjogc3NyLFxuICAgICAgICAgICAgc3RvcmFnZTogc3RvcmFnZSxcbiAgICAgICAgICAgIHZlcnNpb246IGN1cnJlbnRWZXJzaW9uLFxuICAgICAgICB9KVxuICAgICAgICA6IGdldEluaXRpYWxTdGF0ZSkpO1xuICAgIHN0b3JlLnNldFN0YXRlKGdldEluaXRpYWxTdGF0ZSgpKTtcbiAgICBmdW5jdGlvbiB2YWxpZGF0ZVBlcnNpc3RlZENoYWluSWQocGVyc2lzdGVkU3RhdGUsIGRlZmF1bHRDaGFpbklkKSB7XG4gICAgICAgIHJldHVybiBwZXJzaXN0ZWRTdGF0ZSAmJlxuICAgICAgICAgICAgdHlwZW9mIHBlcnNpc3RlZFN0YXRlID09PSAnb2JqZWN0JyAmJlxuICAgICAgICAgICAgJ2NoYWluSWQnIGluIHBlcnNpc3RlZFN0YXRlICYmXG4gICAgICAgICAgICB0eXBlb2YgcGVyc2lzdGVkU3RhdGUuY2hhaW5JZCA9PT0gJ251bWJlcicgJiZcbiAgICAgICAgICAgIGNoYWlucy5nZXRTdGF0ZSgpLnNvbWUoKHgpID0+IHguaWQgPT09IHBlcnNpc3RlZFN0YXRlLmNoYWluSWQpXG4gICAgICAgICAgICA/IHBlcnNpc3RlZFN0YXRlLmNoYWluSWRcbiAgICAgICAgICAgIDogZGVmYXVsdENoYWluSWQ7XG4gICAgfVxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cbiAgICAvLyBTdWJzY3JpYmUgdG8gY2hhbmdlc1xuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cbiAgICAvLyBVcGRhdGUgZGVmYXVsdCBjaGFpbiB3aGVuIGNvbm5lY3RvciBjaGFpbiBjaGFuZ2VzXG4gICAgaWYgKHN5bmNDb25uZWN0ZWRDaGFpbilcbiAgICAgICAgc3RvcmUuc3Vic2NyaWJlKCh7IGNvbm5lY3Rpb25zLCBjdXJyZW50IH0pID0+IGN1cnJlbnQgPyBjb25uZWN0aW9ucy5nZXQoY3VycmVudCk/LmNoYWluSWQgOiB1bmRlZmluZWQsIChjaGFpbklkKSA9PiB7XG4gICAgICAgICAgICAvLyBJZiBjaGFpbiBpcyBub3QgY29uZmlndXJlZCwgdGhlbiBkb24ndCBzd2l0Y2ggb3ZlciB0byBpdC5cbiAgICAgICAgICAgIGNvbnN0IGlzQ2hhaW5Db25maWd1cmVkID0gY2hhaW5zXG4gICAgICAgICAgICAgICAgLmdldFN0YXRlKClcbiAgICAgICAgICAgICAgICAuc29tZSgoeCkgPT4geC5pZCA9PT0gY2hhaW5JZCk7XG4gICAgICAgICAgICBpZiAoIWlzQ2hhaW5Db25maWd1cmVkKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIHJldHVybiBzdG9yZS5zZXRTdGF0ZSgoeCkgPT4gKHtcbiAgICAgICAgICAgICAgICAuLi54LFxuICAgICAgICAgICAgICAgIGNoYWluSWQ6IGNoYWluSWQgPz8geC5jaGFpbklkLFxuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9KTtcbiAgICAvLyBFSVAtNjk2MyBzdWJzY3JpYmUgZm9yIG5ldyB3YWxsZXQgcHJvdmlkZXJzXG4gICAgbWlwZD8uc3Vic2NyaWJlKChwcm92aWRlckRldGFpbHMpID0+IHtcbiAgICAgICAgY29uc3QgY29ubmVjdG9ySWRTZXQgPSBuZXcgU2V0KCk7XG4gICAgICAgIGNvbnN0IGNvbm5lY3RvclJkbnNTZXQgPSBuZXcgU2V0KCk7XG4gICAgICAgIGZvciAoY29uc3QgY29ubmVjdG9yIG9mIGNvbm5lY3RvcnMuZ2V0U3RhdGUoKSkge1xuICAgICAgICAgICAgY29ubmVjdG9ySWRTZXQuYWRkKGNvbm5lY3Rvci5pZCk7XG4gICAgICAgICAgICBpZiAoY29ubmVjdG9yLnJkbnMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCByZG5zVmFsdWVzID0gdHlwZW9mIGNvbm5lY3Rvci5yZG5zID09PSAnc3RyaW5nJyA/IFtjb25uZWN0b3IucmRuc10gOiBjb25uZWN0b3IucmRucztcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHJkbnMgb2YgcmRuc1ZhbHVlcykge1xuICAgICAgICAgICAgICAgICAgICBjb25uZWN0b3JSZG5zU2V0LmFkZChyZG5zKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgbmV3Q29ubmVjdG9ycyA9IFtdO1xuICAgICAgICBmb3IgKGNvbnN0IHByb3ZpZGVyRGV0YWlsIG9mIHByb3ZpZGVyRGV0YWlscykge1xuICAgICAgICAgICAgaWYgKGNvbm5lY3RvclJkbnNTZXQuaGFzKHByb3ZpZGVyRGV0YWlsLmluZm8ucmRucykpXG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjb25zdCBjb25uZWN0b3IgPSBzZXR1cChwcm92aWRlckRldGFpbFRvQ29ubmVjdG9yKHByb3ZpZGVyRGV0YWlsKSk7XG4gICAgICAgICAgICBpZiAoY29ubmVjdG9ySWRTZXQuaGFzKGNvbm5lY3Rvci5pZCkpXG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBuZXdDb25uZWN0b3JzLnB1c2goY29ubmVjdG9yKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc3RvcmFnZSAmJiAhc3RvcmUucGVyc2lzdC5oYXNIeWRyYXRlZCgpKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25uZWN0b3JzLnNldFN0YXRlKCh4KSA9PiBbLi4ueCwgLi4ubmV3Q29ubmVjdG9yc10sIHRydWUpO1xuICAgIH0pO1xuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cbiAgICAvLyBFbWl0dGVyIGxpc3RlbmVyc1xuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cbiAgICBmdW5jdGlvbiBjaGFuZ2UoZGF0YSkge1xuICAgICAgICBzdG9yZS5zZXRTdGF0ZSgoeCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgY29ubmVjdGlvbiA9IHguY29ubmVjdGlvbnMuZ2V0KGRhdGEudWlkKTtcbiAgICAgICAgICAgIGlmICghY29ubmVjdGlvbilcbiAgICAgICAgICAgICAgICByZXR1cm4geDtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgLi4ueCxcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9uczogbmV3IE1hcCh4LmNvbm5lY3Rpb25zKS5zZXQoZGF0YS51aWQsIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjb3VudHM6IGRhdGEuYWNjb3VudHMgPz9cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbm5lY3Rpb24uYWNjb3VudHMsXG4gICAgICAgICAgICAgICAgICAgIGNoYWluSWQ6IGRhdGEuY2hhaW5JZCA/PyBjb25uZWN0aW9uLmNoYWluSWQsXG4gICAgICAgICAgICAgICAgICAgIGNvbm5lY3RvcjogY29ubmVjdGlvbi5jb25uZWN0b3IsXG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICB9O1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZnVuY3Rpb24gY29ubmVjdChkYXRhKSB7XG4gICAgICAgIC8vIERpc2FibGUgaGFuZGxpbmcgaWYgcmVjb25uZWN0aW5nL2Nvbm5lY3RpbmdcbiAgICAgICAgaWYgKHN0b3JlLmdldFN0YXRlKCkuc3RhdHVzID09PSAnY29ubmVjdGluZycgfHxcbiAgICAgICAgICAgIHN0b3JlLmdldFN0YXRlKCkuc3RhdHVzID09PSAncmVjb25uZWN0aW5nJylcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgc3RvcmUuc2V0U3RhdGUoKHgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbm5lY3RvciA9IGNvbm5lY3RvcnMuZ2V0U3RhdGUoKS5maW5kKCh4KSA9PiB4LnVpZCA9PT0gZGF0YS51aWQpO1xuICAgICAgICAgICAgaWYgKCFjb25uZWN0b3IpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHg7XG4gICAgICAgICAgICBpZiAoY29ubmVjdG9yLmVtaXR0ZXIubGlzdGVuZXJDb3VudCgnY29ubmVjdCcpKVxuICAgICAgICAgICAgICAgIGNvbm5lY3Rvci5lbWl0dGVyLm9mZignY29ubmVjdCcsIGNoYW5nZSk7XG4gICAgICAgICAgICBpZiAoIWNvbm5lY3Rvci5lbWl0dGVyLmxpc3RlbmVyQ291bnQoJ2NoYW5nZScpKVxuICAgICAgICAgICAgICAgIGNvbm5lY3Rvci5lbWl0dGVyLm9uKCdjaGFuZ2UnLCBjaGFuZ2UpO1xuICAgICAgICAgICAgaWYgKCFjb25uZWN0b3IuZW1pdHRlci5saXN0ZW5lckNvdW50KCdkaXNjb25uZWN0JykpXG4gICAgICAgICAgICAgICAgY29ubmVjdG9yLmVtaXR0ZXIub24oJ2Rpc2Nvbm5lY3QnLCBkaXNjb25uZWN0KTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgLi4ueCxcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9uczogbmV3IE1hcCh4LmNvbm5lY3Rpb25zKS5zZXQoZGF0YS51aWQsIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjb3VudHM6IGRhdGEuYWNjb3VudHMsXG4gICAgICAgICAgICAgICAgICAgIGNoYWluSWQ6IGRhdGEuY2hhaW5JZCxcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdG9yOiBjb25uZWN0b3IsXG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgY3VycmVudDogZGF0YS51aWQsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnY29ubmVjdGVkJyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBmdW5jdGlvbiBkaXNjb25uZWN0KGRhdGEpIHtcbiAgICAgICAgc3RvcmUuc2V0U3RhdGUoKHgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbm5lY3Rpb24gPSB4LmNvbm5lY3Rpb25zLmdldChkYXRhLnVpZCk7XG4gICAgICAgICAgICBpZiAoY29ubmVjdGlvbikge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNvbm5lY3RvciA9IGNvbm5lY3Rpb24uY29ubmVjdG9yO1xuICAgICAgICAgICAgICAgIGlmIChjb25uZWN0b3IuZW1pdHRlci5saXN0ZW5lckNvdW50KCdjaGFuZ2UnKSlcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdGlvbi5jb25uZWN0b3IuZW1pdHRlci5vZmYoJ2NoYW5nZScsIGNoYW5nZSk7XG4gICAgICAgICAgICAgICAgaWYgKGNvbm5lY3Rvci5lbWl0dGVyLmxpc3RlbmVyQ291bnQoJ2Rpc2Nvbm5lY3QnKSlcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdGlvbi5jb25uZWN0b3IuZW1pdHRlci5vZmYoJ2Rpc2Nvbm5lY3QnLCBkaXNjb25uZWN0KTtcbiAgICAgICAgICAgICAgICBpZiAoIWNvbm5lY3Rvci5lbWl0dGVyLmxpc3RlbmVyQ291bnQoJ2Nvbm5lY3QnKSlcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdGlvbi5jb25uZWN0b3IuZW1pdHRlci5vbignY29ubmVjdCcsIGNvbm5lY3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgeC5jb25uZWN0aW9ucy5kZWxldGUoZGF0YS51aWQpO1xuICAgICAgICAgICAgaWYgKHguY29ubmVjdGlvbnMuc2l6ZSA9PT0gMClcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAuLi54LFxuICAgICAgICAgICAgICAgICAgICBjb25uZWN0aW9uczogbmV3IE1hcCgpLFxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50OiBudWxsLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6ICdkaXNjb25uZWN0ZWQnLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCBuZXh0Q29ubmVjdGlvbiA9IHguY29ubmVjdGlvbnMudmFsdWVzKCkubmV4dCgpLnZhbHVlO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAuLi54LFxuICAgICAgICAgICAgICAgIGNvbm5lY3Rpb25zOiBuZXcgTWFwKHguY29ubmVjdGlvbnMpLFxuICAgICAgICAgICAgICAgIGN1cnJlbnQ6IG5leHRDb25uZWN0aW9uLmNvbm5lY3Rvci51aWQsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgZ2V0IGNoYWlucygpIHtcbiAgICAgICAgICAgIHJldHVybiBjaGFpbnMuZ2V0U3RhdGUoKTtcbiAgICAgICAgfSxcbiAgICAgICAgZ2V0IGNvbm5lY3RvcnMoKSB7XG4gICAgICAgICAgICByZXR1cm4gY29ubmVjdG9ycy5nZXRTdGF0ZSgpO1xuICAgICAgICB9LFxuICAgICAgICBzdG9yYWdlLFxuICAgICAgICBnZXRDbGllbnQsXG4gICAgICAgIGdldCBzdGF0ZSgpIHtcbiAgICAgICAgICAgIHJldHVybiBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgICAgICB9LFxuICAgICAgICBzZXRTdGF0ZSh2YWx1ZSkge1xuICAgICAgICAgICAgbGV0IG5ld1N0YXRlO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICBuZXdTdGF0ZSA9IHZhbHVlKHN0b3JlLmdldFN0YXRlKCkpO1xuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIG5ld1N0YXRlID0gdmFsdWU7XG4gICAgICAgICAgICAvLyBSZXNldCBzdGF0ZSBpZiBpdCBnb3Qgc2V0IHRvIHNvbWV0aGluZyBub3QgbWF0Y2hpbmcgdGhlIGJhc2Ugc3RhdGVcbiAgICAgICAgICAgIGNvbnN0IGluaXRpYWxTdGF0ZSA9IGdldEluaXRpYWxTdGF0ZSgpO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBuZXdTdGF0ZSAhPT0gJ29iamVjdCcpXG4gICAgICAgICAgICAgICAgbmV3U3RhdGUgPSBpbml0aWFsU3RhdGU7XG4gICAgICAgICAgICBjb25zdCBpc0NvcnJ1cHQgPSBPYmplY3Qua2V5cyhpbml0aWFsU3RhdGUpLnNvbWUoKHgpID0+ICEoeCBpbiBuZXdTdGF0ZSkpO1xuICAgICAgICAgICAgaWYgKGlzQ29ycnVwdClcbiAgICAgICAgICAgICAgICBuZXdTdGF0ZSA9IGluaXRpYWxTdGF0ZTtcbiAgICAgICAgICAgIHN0b3JlLnNldFN0YXRlKG5ld1N0YXRlLCB0cnVlKTtcbiAgICAgICAgfSxcbiAgICAgICAgc3Vic2NyaWJlKHNlbGVjdG9yLCBsaXN0ZW5lciwgb3B0aW9ucykge1xuICAgICAgICAgICAgcmV0dXJuIHN0b3JlLnN1YnNjcmliZShzZWxlY3RvciwgbGlzdGVuZXIsIG9wdGlvbnNcbiAgICAgICAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgICAgICAgICAgZmlyZUltbWVkaWF0ZWx5OiBvcHRpb25zLmVtaXRJbW1lZGlhdGVseSxcbiAgICAgICAgICAgICAgICAgICAgLy8gV29ya2Fyb3VuZCBjYXN0IHNpbmNlIFp1c3RhbmQgZG9lcyBub3Qgc3VwcG9ydCBgJ2V4YWN0T3B0aW9uYWxQcm9wZXJ0eVR5cGVzJ2BcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgOiB1bmRlZmluZWQpO1xuICAgICAgICB9LFxuICAgICAgICBfaW50ZXJuYWw6IHtcbiAgICAgICAgICAgIG1pcGQsXG4gICAgICAgICAgICBzdG9yZSxcbiAgICAgICAgICAgIHNzcjogQm9vbGVhbihzc3IpLFxuICAgICAgICAgICAgc3luY0Nvbm5lY3RlZENoYWluLFxuICAgICAgICAgICAgdHJhbnNwb3J0czogcmVzdC50cmFuc3BvcnRzLFxuICAgICAgICAgICAgY2hhaW5zOiB7XG4gICAgICAgICAgICAgICAgc2V0U3RhdGUodmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dENoYWlucyA9ICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicgPyB2YWx1ZShjaGFpbnMuZ2V0U3RhdGUoKSkgOiB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChuZXh0Q2hhaW5zLmxlbmd0aCA9PT0gMClcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNoYWlucy5zZXRTdGF0ZShuZXh0Q2hhaW5zLCB0cnVlKTtcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW5zLnN1YnNjcmliZShsaXN0ZW5lcik7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjb25uZWN0b3JzOiB7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXJEZXRhaWxUb0Nvbm5lY3RvcixcbiAgICAgICAgICAgICAgICBzZXR1cDogc2V0dXAsXG4gICAgICAgICAgICAgICAgc2V0U3RhdGUodmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNvbm5lY3RvcnMuc2V0U3RhdGUodHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nID8gdmFsdWUoY29ubmVjdG9ycy5nZXRTdGF0ZSgpKSA6IHZhbHVlLCB0cnVlKTtcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY29ubmVjdG9ycy5zdWJzY3JpYmUobGlzdGVuZXIpO1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZXZlbnRzOiB7IGNoYW5nZSwgY29ubmVjdCwgZGlzY29ubmVjdCB9LFxuICAgICAgICB9LFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGVDb25maWcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createEmitter.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter),\n/* harmony export */   createEmitter: () => (/* binding */ createEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/eventemitter3/index.mjs\");\n\nclass Emitter {\n    constructor(uid) {\n        Object.defineProperty(this, \"uid\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: uid\n        });\n        Object.defineProperty(this, \"_emitter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter()\n        });\n    }\n    on(eventName, fn) {\n        this._emitter.on(eventName, fn);\n    }\n    once(eventName, fn) {\n        this._emitter.once(eventName, fn);\n    }\n    off(eventName, fn) {\n        this._emitter.off(eventName, fn);\n    }\n    emit(eventName, ...params) {\n        const data = params[0];\n        this._emitter.emit(eventName, { uid: this.uid, ...data });\n    }\n    listenerCount(eventName) {\n        return this._emitter.listenerCount(eventName);\n    }\n}\nfunction createEmitter(uid) {\n    return new Emitter(uid);\n}\n//# sourceMappingURL=createEmitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createStorage.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   getDefaultStorage: () => (/* binding */ getDefaultStorage),\n/* harmony export */   noopStorage: () => (/* binding */ noopStorage)\n/* harmony export */ });\n/* harmony import */ var _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/deserialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\");\n/* harmony import */ var _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/serialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\");\n\n\nfunction createStorage(parameters) {\n    const { deserialize = _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize, key: prefix = 'wagmi', serialize = _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize, storage = noopStorage, } = parameters;\n    function unwrap(value) {\n        if (value instanceof Promise)\n            return value.then((x) => x).catch(() => null);\n        return value;\n    }\n    return {\n        ...storage,\n        key: prefix,\n        async getItem(key, defaultValue) {\n            const value = storage.getItem(`${prefix}.${key}`);\n            const unwrapped = await unwrap(value);\n            if (unwrapped)\n                return deserialize(unwrapped) ?? null;\n            return (defaultValue ?? null);\n        },\n        async setItem(key, value) {\n            const storageKey = `${prefix}.${key}`;\n            if (value === null)\n                await unwrap(storage.removeItem(storageKey));\n            else\n                await unwrap(storage.setItem(storageKey, serialize(value)));\n        },\n        async removeItem(key) {\n            await unwrap(storage.removeItem(`${prefix}.${key}`));\n        },\n    };\n}\nconst noopStorage = {\n    getItem: () => null,\n    setItem: () => { },\n    removeItem: () => { },\n};\nfunction getDefaultStorage() {\n    const storage = (() => {\n        if (typeof window !== 'undefined' && window.localStorage)\n            return window.localStorage;\n        return noopStorage;\n    })();\n    return {\n        getItem(key) {\n            return storage.getItem(key);\n        },\n        removeItem(key) {\n            storage.removeItem(key);\n        },\n        setItem(key, value) {\n            try {\n                storage.setItem(key, value);\n                // silence errors by default (QuotaExceededError, SecurityError, etc.)\n            }\n            catch { }\n        },\n    };\n}\n//# sourceMappingURL=createStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/base.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\");\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _BaseError_instances, _BaseError_walk;\n\nclass BaseError extends Error {\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/core';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)();\n    }\n    constructor(shortMessage, options = {}) {\n        super();\n        _BaseError_instances.add(this);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiCoreError'\n        });\n        const details = options.cause instanceof BaseError\n            ? options.cause.details\n            : options.cause?.message\n                ? options.cause.message\n                : options.details;\n        const docsPath = options.cause instanceof BaseError\n            ? options.cause.docsPath || options.docsPath\n            : options.docsPath;\n        this.message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n            ...(docsPath\n                ? [\n                    `Docs: ${this.docsBaseUrl}${docsPath}.html${options.docsSlug ? `#${options.docsSlug}` : ''}`,\n                ]\n                : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: ${this.version}`,\n        ].join('\\n');\n        if (options.cause)\n            this.cause = options.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = options.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, this, fn);\n    }\n}\n_BaseError_instances = new WeakSet(), _BaseError_walk = function _BaseError_walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err.cause)\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, err.cause, fn);\n    return err;\n};\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Jhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSw4QkFBOEIsU0FBSSxJQUFJLFNBQUk7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNvRDtBQUM3QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxnRUFBVTtBQUN6QjtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsaUJBQWlCLEVBQUUsU0FBUyxPQUFPLHVCQUF1QixpQkFBaUIsT0FBTztBQUMvRztBQUNBO0FBQ0EsdUNBQXVDLFFBQVE7QUFDL0Msd0JBQXdCLGFBQWE7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGVycm9yc1xcYmFzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19jbGFzc1ByaXZhdGVGaWVsZEdldCA9ICh0aGlzICYmIHRoaXMuX19jbGFzc1ByaXZhdGVGaWVsZEdldCkgfHwgZnVuY3Rpb24gKHJlY2VpdmVyLCBzdGF0ZSwga2luZCwgZikge1xuICAgIGlmIChraW5kID09PSBcImFcIiAmJiAhZikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlByaXZhdGUgYWNjZXNzb3Igd2FzIGRlZmluZWQgd2l0aG91dCBhIGdldHRlclwiKTtcbiAgICBpZiAodHlwZW9mIHN0YXRlID09PSBcImZ1bmN0aW9uXCIgPyByZWNlaXZlciAhPT0gc3RhdGUgfHwgIWYgOiAhc3RhdGUuaGFzKHJlY2VpdmVyKSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCByZWFkIHByaXZhdGUgbWVtYmVyIGZyb20gYW4gb2JqZWN0IHdob3NlIGNsYXNzIGRpZCBub3QgZGVjbGFyZSBpdFwiKTtcbiAgICByZXR1cm4ga2luZCA9PT0gXCJtXCIgPyBmIDoga2luZCA9PT0gXCJhXCIgPyBmLmNhbGwocmVjZWl2ZXIpIDogZiA/IGYudmFsdWUgOiBzdGF0ZS5nZXQocmVjZWl2ZXIpO1xufTtcbnZhciBfQmFzZUVycm9yX2luc3RhbmNlcywgX0Jhc2VFcnJvcl93YWxrO1xuaW1wb3J0IHsgZ2V0VmVyc2lvbiB9IGZyb20gJy4uL3V0aWxzL2dldFZlcnNpb24uanMnO1xuZXhwb3J0IGNsYXNzIEJhc2VFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBnZXQgZG9jc0Jhc2VVcmwoKSB7XG4gICAgICAgIHJldHVybiAnaHR0cHM6Ly93YWdtaS5zaC9jb3JlJztcbiAgICB9XG4gICAgZ2V0IHZlcnNpb24oKSB7XG4gICAgICAgIHJldHVybiBnZXRWZXJzaW9uKCk7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKHNob3J0TWVzc2FnZSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIF9CYXNlRXJyb3JfaW5zdGFuY2VzLmFkZCh0aGlzKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwiZGV0YWlsc1wiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogdm9pZCAwXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJkb2NzUGF0aFwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogdm9pZCAwXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJtZXRhTWVzc2FnZXNcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwic2hvcnRNZXNzYWdlXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdXYWdtaUNvcmVFcnJvcidcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGRldGFpbHMgPSBvcHRpb25zLmNhdXNlIGluc3RhbmNlb2YgQmFzZUVycm9yXG4gICAgICAgICAgICA/IG9wdGlvbnMuY2F1c2UuZGV0YWlsc1xuICAgICAgICAgICAgOiBvcHRpb25zLmNhdXNlPy5tZXNzYWdlXG4gICAgICAgICAgICAgICAgPyBvcHRpb25zLmNhdXNlLm1lc3NhZ2VcbiAgICAgICAgICAgICAgICA6IG9wdGlvbnMuZGV0YWlscztcbiAgICAgICAgY29uc3QgZG9jc1BhdGggPSBvcHRpb25zLmNhdXNlIGluc3RhbmNlb2YgQmFzZUVycm9yXG4gICAgICAgICAgICA/IG9wdGlvbnMuY2F1c2UuZG9jc1BhdGggfHwgb3B0aW9ucy5kb2NzUGF0aFxuICAgICAgICAgICAgOiBvcHRpb25zLmRvY3NQYXRoO1xuICAgICAgICB0aGlzLm1lc3NhZ2UgPSBbXG4gICAgICAgICAgICBzaG9ydE1lc3NhZ2UgfHwgJ0FuIGVycm9yIG9jY3VycmVkLicsXG4gICAgICAgICAgICAnJyxcbiAgICAgICAgICAgIC4uLihvcHRpb25zLm1ldGFNZXNzYWdlcyA/IFsuLi5vcHRpb25zLm1ldGFNZXNzYWdlcywgJyddIDogW10pLFxuICAgICAgICAgICAgLi4uKGRvY3NQYXRoXG4gICAgICAgICAgICAgICAgPyBbXG4gICAgICAgICAgICAgICAgICAgIGBEb2NzOiAke3RoaXMuZG9jc0Jhc2VVcmx9JHtkb2NzUGF0aH0uaHRtbCR7b3B0aW9ucy5kb2NzU2x1ZyA/IGAjJHtvcHRpb25zLmRvY3NTbHVnfWAgOiAnJ31gLFxuICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICA6IFtdKSxcbiAgICAgICAgICAgIC4uLihkZXRhaWxzID8gW2BEZXRhaWxzOiAke2RldGFpbHN9YF0gOiBbXSksXG4gICAgICAgICAgICBgVmVyc2lvbjogJHt0aGlzLnZlcnNpb259YCxcbiAgICAgICAgXS5qb2luKCdcXG4nKTtcbiAgICAgICAgaWYgKG9wdGlvbnMuY2F1c2UpXG4gICAgICAgICAgICB0aGlzLmNhdXNlID0gb3B0aW9ucy5jYXVzZTtcbiAgICAgICAgdGhpcy5kZXRhaWxzID0gZGV0YWlscztcbiAgICAgICAgdGhpcy5kb2NzUGF0aCA9IGRvY3NQYXRoO1xuICAgICAgICB0aGlzLm1ldGFNZXNzYWdlcyA9IG9wdGlvbnMubWV0YU1lc3NhZ2VzO1xuICAgICAgICB0aGlzLnNob3J0TWVzc2FnZSA9IHNob3J0TWVzc2FnZTtcbiAgICB9XG4gICAgd2Fsayhmbikge1xuICAgICAgICByZXR1cm4gX19jbGFzc1ByaXZhdGVGaWVsZEdldCh0aGlzLCBfQmFzZUVycm9yX2luc3RhbmNlcywgXCJtXCIsIF9CYXNlRXJyb3Jfd2FsaykuY2FsbCh0aGlzLCB0aGlzLCBmbik7XG4gICAgfVxufVxuX0Jhc2VFcnJvcl9pbnN0YW5jZXMgPSBuZXcgV2Vha1NldCgpLCBfQmFzZUVycm9yX3dhbGsgPSBmdW5jdGlvbiBfQmFzZUVycm9yX3dhbGsoZXJyLCBmbikge1xuICAgIGlmIChmbj8uKGVycikpXG4gICAgICAgIHJldHVybiBlcnI7XG4gICAgaWYgKGVyci5jYXVzZSlcbiAgICAgICAgcmV0dXJuIF9fY2xhc3NQcml2YXRlRmllbGRHZXQodGhpcywgX0Jhc2VFcnJvcl9pbnN0YW5jZXMsIFwibVwiLCBfQmFzZUVycm9yX3dhbGspLmNhbGwodGhpcywgZXJyLmNhdXNlLCBmbik7XG4gICAgcmV0dXJuIGVycjtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iYXNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/config.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChainNotConfiguredError: () => (/* binding */ ChainNotConfiguredError),\n/* harmony export */   ConnectorAccountNotFoundError: () => (/* binding */ ConnectorAccountNotFoundError),\n/* harmony export */   ConnectorAlreadyConnectedError: () => (/* binding */ ConnectorAlreadyConnectedError),\n/* harmony export */   ConnectorChainMismatchError: () => (/* binding */ ConnectorChainMismatchError),\n/* harmony export */   ConnectorNotConnectedError: () => (/* binding */ ConnectorNotConnectedError),\n/* harmony export */   ConnectorNotFoundError: () => (/* binding */ ConnectorNotFoundError),\n/* harmony export */   ConnectorUnavailableReconnectingError: () => (/* binding */ ConnectorUnavailableReconnectingError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ChainNotConfiguredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Chain not configured.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ChainNotConfiguredError'\n        });\n    }\n}\nclass ConnectorAlreadyConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector already connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAlreadyConnectedError'\n        });\n    }\n}\nclass ConnectorNotConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotConnectedError'\n        });\n    }\n}\nclass ConnectorNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotFoundError'\n        });\n    }\n}\nclass ConnectorAccountNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address, connector, }) {\n        super(`Account \"${address}\" not found for connector \"${connector.name}\".`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAccountNotFoundError'\n        });\n    }\n}\nclass ConnectorChainMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connectionChainId, connectorChainId, }) {\n        super(`The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`, {\n            metaMessages: [\n                `Current Chain ID:  ${connectorChainId}`,\n                `Expected Chain ID: ${connectionChainId}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorChainMismatchError'\n        });\n    }\n}\nclass ConnectorUnavailableReconnectingError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n            details: [\n                'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n                'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n                'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n            ].join(' '),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorUnavailableReconnectingError'\n        });\n    }\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQztBQUMvQixzQ0FBc0MsK0NBQVM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLDZDQUE2QywrQ0FBUztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ08seUNBQXlDLCtDQUFTO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTyxxQ0FBcUMsK0NBQVM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLDRDQUE0QywrQ0FBUztBQUM1RCxrQkFBa0IscUJBQXFCO0FBQ3ZDLDBCQUEwQixRQUFRLDZCQUE2QixlQUFlO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLDBDQUEwQywrQ0FBUztBQUMxRCxrQkFBa0Isc0NBQXNDO0FBQ3hELHlEQUF5RCxpQkFBaUIsK0NBQStDLGtCQUFrQjtBQUMzSTtBQUNBLHNDQUFzQyxpQkFBaUI7QUFDdkQsc0NBQXNDLGtCQUFrQjtBQUN4RDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTyxvREFBb0QsK0NBQVM7QUFDcEUsa0JBQWtCLFdBQVc7QUFDN0IsNEJBQTRCLGVBQWU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXGVycm9yc1xcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VFcnJvciB9IGZyb20gJy4vYmFzZS5qcyc7XG5leHBvcnQgY2xhc3MgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcignQ2hhaW4gbm90IGNvbmZpZ3VyZWQuJyk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDaGFpbk5vdENvbmZpZ3VyZWRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIENvbm5lY3RvckFscmVhZHlDb25uZWN0ZWRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdDb25uZWN0b3IgYWxyZWFkeSBjb25uZWN0ZWQuJyk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDb25uZWN0b3JBbHJlYWR5Q29ubmVjdGVkRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBDb25uZWN0b3JOb3RDb25uZWN0ZWRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdDb25uZWN0b3Igbm90IGNvbm5lY3RlZC4nKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0Nvbm5lY3Rvck5vdENvbm5lY3RlZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgQ29ubmVjdG9yTm90Rm91bmRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdDb25uZWN0b3Igbm90IGZvdW5kLicpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnQ29ubmVjdG9yTm90Rm91bmRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIENvbm5lY3RvckFjY291bnROb3RGb3VuZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGFkZHJlc3MsIGNvbm5lY3RvciwgfSkge1xuICAgICAgICBzdXBlcihgQWNjb3VudCBcIiR7YWRkcmVzc31cIiBub3QgZm91bmQgZm9yIGNvbm5lY3RvciBcIiR7Y29ubmVjdG9yLm5hbWV9XCIuYCk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDb25uZWN0b3JBY2NvdW50Tm90Rm91bmRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIENvbm5lY3RvckNoYWluTWlzbWF0Y2hFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBjb25uZWN0aW9uQ2hhaW5JZCwgY29ubmVjdG9yQ2hhaW5JZCwgfSkge1xuICAgICAgICBzdXBlcihgVGhlIGN1cnJlbnQgY2hhaW4gb2YgdGhlIGNvbm5lY3RvciAoaWQ6ICR7Y29ubmVjdG9yQ2hhaW5JZH0pIGRvZXMgbm90IG1hdGNoIHRoZSBjb25uZWN0aW9uJ3MgY2hhaW4gKGlkOiAke2Nvbm5lY3Rpb25DaGFpbklkfSkuYCwge1xuICAgICAgICAgICAgbWV0YU1lc3NhZ2VzOiBbXG4gICAgICAgICAgICAgICAgYEN1cnJlbnQgQ2hhaW4gSUQ6ICAke2Nvbm5lY3RvckNoYWluSWR9YCxcbiAgICAgICAgICAgICAgICBgRXhwZWN0ZWQgQ2hhaW4gSUQ6ICR7Y29ubmVjdGlvbkNoYWluSWR9YCxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnQ29ubmVjdG9yQ2hhaW5NaXNtYXRjaEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgQ29ubmVjdG9yVW5hdmFpbGFibGVSZWNvbm5lY3RpbmdFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBjb25uZWN0b3IgfSkge1xuICAgICAgICBzdXBlcihgQ29ubmVjdG9yIFwiJHtjb25uZWN0b3IubmFtZX1cIiB1bmF2YWlsYWJsZSB3aGlsZSByZWNvbm5lY3RpbmcuYCwge1xuICAgICAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgICAgICAgICdEdXJpbmcgdGhlIHJlY29ubmVjdGlvbiBzdGVwLCB0aGUgb25seSBjb25uZWN0b3IgbWV0aG9kcyBndWFyYW50ZWVkIHRvIGJlIGF2YWlsYWJsZSBhcmU6IGBpZGAsIGBuYW1lYCwgYHR5cGVgLCBgdWlkYC4nLFxuICAgICAgICAgICAgICAgICdBbGwgb3RoZXIgbWV0aG9kcyBhcmUgbm90IGd1YXJhbnRlZWQgdG8gYmUgYXZhaWxhYmxlIHVudGlsIHJlY29ubmVjdGlvbiBjb21wbGV0ZXMgYW5kIGNvbm5lY3RvcnMgYXJlIGZ1bGx5IHJlc3RvcmVkLicsXG4gICAgICAgICAgICAgICAgJ1RoaXMgZXJyb3IgY29tbW9ubHkgb2NjdXJzIGZvciBjb25uZWN0b3JzIHRoYXQgYXN5bmNocm9ub3VzbHkgaW5qZWN0IGFmdGVyIHJlY29ubmVjdGlvbiBoYXMgYWxyZWFkeSBzdGFydGVkLicsXG4gICAgICAgICAgICBdLmpvaW4oJyAnKSxcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDb25uZWN0b3JVbmF2YWlsYWJsZVJlY29ubmVjdGluZ0Vycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25maWcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/connector.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderNotFoundError: () => (/* binding */ ProviderNotFoundError),\n/* harmony export */   SwitchChainNotSupportedError: () => (/* binding */ SwitchChainNotSupportedError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Provider not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ProviderNotFoundError'\n        });\n    }\n}\nclass SwitchChainNotSupportedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`\"${connector.name}\" does not support programmatic chain switching.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SwitchChainNotSupportedError'\n        });\n    }\n}\n//# sourceMappingURL=connector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Nvbm5lY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0Isb0NBQW9DLCtDQUFTO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTywyQ0FBMkMsK0NBQVM7QUFDM0Qsa0JBQWtCLFdBQVc7QUFDN0Isa0JBQWtCLGVBQWU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFxlcnJvcnNcXGNvbm5lY3Rvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlRXJyb3IgfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNsYXNzIFByb3ZpZGVyTm90Rm91bmRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdQcm92aWRlciBub3QgZm91bmQuJyk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdQcm92aWRlck5vdEZvdW5kRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGNvbm5lY3RvciB9KSB7XG4gICAgICAgIHN1cGVyKGBcIiR7Y29ubmVjdG9yLm5hbWV9XCIgZG9lcyBub3Qgc3VwcG9ydCBwcm9ncmFtbWF0aWMgY2hhaW4gc3dpdGNoaW5nLmApO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnU3dpdGNoQ2hhaW5Ob3RTdXBwb3J0ZWRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29ubmVjdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/reconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\");\n\nfunction hydrate(config, parameters) {\n    const { initialState, reconnectOnMount } = parameters;\n    if (initialState && !config._internal.store.persist.hasHydrated())\n        config.setState({\n            ...initialState,\n            chainId: config.chains.some((x) => x.id === initialState.chainId)\n                ? initialState.chainId\n                : config.chains[0].id,\n            connections: reconnectOnMount ? initialState.connections : new Map(),\n            status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n        });\n    return {\n        async onMount() {\n            if (config._internal.ssr) {\n                await config._internal.store.persist.rehydrate();\n                if (config._internal.mipd) {\n                    config._internal.connectors.setState((connectors) => {\n                        const rdnsSet = new Set();\n                        for (const connector of connectors ?? []) {\n                            if (connector.rdns) {\n                                const rdnsValues = Array.isArray(connector.rdns)\n                                    ? connector.rdns\n                                    : [connector.rdns];\n                                for (const rdns of rdnsValues) {\n                                    rdnsSet.add(rdns);\n                                }\n                            }\n                        }\n                        const mipdConnectors = [];\n                        const providers = config._internal.mipd?.getProviders() ?? [];\n                        for (const provider of providers) {\n                            if (rdnsSet.has(provider.info.rdns))\n                                continue;\n                            const connectorFn = config._internal.connectors.providerDetailToConnector(provider);\n                            const connector = config._internal.connectors.setup(connectorFn);\n                            mipdConnectors.push(connector);\n                        }\n                        return [...connectors, ...mipdConnectors];\n                    });\n                }\n            }\n            if (reconnectOnMount)\n                (0,_actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__.reconnect)(config);\n            else if (config.storage)\n                // Reset connections that may have been hydrated from storage.\n                config.setState((x) => ({\n                    ...x,\n                    connections: new Map(),\n                }));\n        },\n    };\n}\n//# sourceMappingURL=hydrate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/disconnect.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/disconnect.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectMutationOptions: () => (/* binding */ disconnectMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_disconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/disconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js\");\n\nfunction disconnectMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_disconnect_js__WEBPACK_IMPORTED_MODULE_0__.disconnect)(config, variables);\n        },\n        mutationKey: ['disconnect'],\n    };\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvZGlzY29ubmVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1RDtBQUNoRDtBQUNQO0FBQ0E7QUFDQSxtQkFBbUIsa0VBQVU7QUFDN0IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcbm9kZV9tb2R1bGVzXFxAd2FnbWlcXGNvcmVcXGRpc3RcXGVzbVxccXVlcnlcXGRpc2Nvbm5lY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGlzY29ubmVjdCwgfSBmcm9tICcuLi9hY3Rpb25zL2Rpc2Nvbm5lY3QuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGRpc2Nvbm5lY3RNdXRhdGlvbk9wdGlvbnMoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRpb25Gbih2YXJpYWJsZXMpIHtcbiAgICAgICAgICAgIHJldHVybiBkaXNjb25uZWN0KGNvbmZpZywgdmFyaWFibGVzKTtcbiAgICAgICAgfSxcbiAgICAgICAgbXV0YXRpb25LZXk6IFsnZGlzY29ubmVjdCddLFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kaXNjb25uZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/signMessage.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/signMessage.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signMessageMutationOptions: () => (/* binding */ signMessageMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_signMessage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/signMessage.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/signMessage.js\");\n\nfunction signMessageMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_signMessage_js__WEBPACK_IMPORTED_MODULE_0__.signMessage)(config, variables);\n        },\n        mutationKey: ['signMessage'],\n    };\n}\n//# sourceMappingURL=signMessage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvc2lnbk1lc3NhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUQ7QUFDbEQ7QUFDUDtBQUNBO0FBQ0EsbUJBQW1CLG9FQUFXO0FBQzlCLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHF1ZXJ5XFxzaWduTWVzc2FnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzaWduTWVzc2FnZSwgfSBmcm9tICcuLi9hY3Rpb25zL3NpZ25NZXNzYWdlLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBzaWduTWVzc2FnZU11dGF0aW9uT3B0aW9ucyhjb25maWcpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBtdXRhdGlvbkZuKHZhcmlhYmxlcykge1xuICAgICAgICAgICAgcmV0dXJuIHNpZ25NZXNzYWdlKGNvbmZpZywgdmFyaWFibGVzKTtcbiAgICAgICAgfSxcbiAgICAgICAgbXV0YXRpb25LZXk6IFsnc2lnbk1lc3NhZ2UnXSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2lnbk1lc3NhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/signMessage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/** Forked from https://github.com/epoberezkin/fast-deep-equal */\nfunction deepEqual(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n        if (a.constructor !== b.constructor)\n            return false;\n        let length;\n        let i;\n        if (Array.isArray(a) && Array.isArray(b)) {\n            length = a.length;\n            if (length !== b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!deepEqual(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (a.valueOf !== Object.prototype.valueOf)\n            return a.valueOf() === b.valueOf();\n        if (a.toString !== Object.prototype.toString)\n            return a.toString() === b.toString();\n        const keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            const key = keys[i];\n            if (key && !deepEqual(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    // true if both NaN, false otherwise\n    // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n    return a !== a && b !== b;\n}\n//# sourceMappingURL=deepEqual.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deserialize.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\nfunction deserialize(value, reviver) {\n    return JSON.parse(value, (key, value_) => {\n        let value = value_;\n        if (value?.__type === 'bigint')\n            value = BigInt(value.value);\n        if (value?.__type === 'Map')\n            value = new Map(value.value);\n        return reviver?.(key, value) ?? value;\n    });\n}\n//# sourceMappingURL=deserialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxkZXNlcmlhbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGVzZXJpYWxpemUodmFsdWUsIHJldml2ZXIpIHtcbiAgICByZXR1cm4gSlNPTi5wYXJzZSh2YWx1ZSwgKGtleSwgdmFsdWVfKSA9PiB7XG4gICAgICAgIGxldCB2YWx1ZSA9IHZhbHVlXztcbiAgICAgICAgaWYgKHZhbHVlPy5fX3R5cGUgPT09ICdiaWdpbnQnKVxuICAgICAgICAgICAgdmFsdWUgPSBCaWdJbnQodmFsdWUudmFsdWUpO1xuICAgICAgICBpZiAodmFsdWU/Ll9fdHlwZSA9PT0gJ01hcCcpXG4gICAgICAgICAgICB2YWx1ZSA9IG5ldyBNYXAodmFsdWUudmFsdWUpO1xuICAgICAgICByZXR1cm4gcmV2aXZlcj8uKGtleSwgdmFsdWUpID8/IHZhbHVlO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVzZXJpYWxpemUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: () => (/* binding */ extractRpcUrls)\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZXh0cmFjdFJwY1VybHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AsWUFBWSxRQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxPQUFPO0FBQ25FO0FBQ0EsNkJBQTZCLE9BQU87QUFDcEM7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxleHRyYWN0UnBjVXJscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZXh0cmFjdFJwY1VybHMocGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgY2hhaW4gfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgZmFsbGJhY2tVcmwgPSBjaGFpbi5ycGNVcmxzLmRlZmF1bHQuaHR0cFswXTtcbiAgICBpZiAoIXBhcmFtZXRlcnMudHJhbnNwb3J0cylcbiAgICAgICAgcmV0dXJuIFtmYWxsYmFja1VybF07XG4gICAgY29uc3QgdHJhbnNwb3J0ID0gcGFyYW1ldGVycy50cmFuc3BvcnRzPy5bY2hhaW4uaWRdPy4oeyBjaGFpbiB9KTtcbiAgICBjb25zdCB0cmFuc3BvcnRzID0gdHJhbnNwb3J0Py52YWx1ZT8udHJhbnNwb3J0cyB8fCBbdHJhbnNwb3J0XTtcbiAgICByZXR1cm4gdHJhbnNwb3J0cy5tYXAoKHsgdmFsdWUgfSkgPT4gdmFsdWU/LnVybCB8fCBmYWxsYmFja1VybCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1leHRyYWN0UnBjVXJscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getAction.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAction: () => (/* binding */ getAction)\n/* harmony export */ });\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nfunction getAction(client, actionFn, \n// Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n// meaning that `actionFn.name` will not always work. For that case, the consumer\n// needs to pass the name explicitly.\nname) {\n    const action_implicit = client[actionFn.name];\n    if (typeof action_implicit === 'function')\n        return action_implicit;\n    const action_explicit = client[name];\n    if (typeof action_explicit === 'function')\n        return action_explicit;\n    return (params) => actionFn(client, params);\n}\n//# sourceMappingURL=getAction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0QWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFx1dGlsc1xcZ2V0QWN0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmV0cmlldmVzIGFuZCByZXR1cm5zIGFuIGFjdGlvbiBmcm9tIHRoZSBjbGllbnQgKGlmIGV4aXN0cyksIGFuZCBmYWxsc1xuICogYmFjayB0byB0aGUgdHJlZS1zaGFrYWJsZSBhY3Rpb24uXG4gKlxuICogVXNlZnVsIGZvciBleHRyYWN0aW5nIG92ZXJyaWRkZW4gYWN0aW9ucyBmcm9tIGEgY2xpZW50IChpZS4gaWYgYSBjb25zdW1lclxuICogd2FudHMgdG8gb3ZlcnJpZGUgdGhlIGBzZW5kVHJhbnNhY3Rpb25gIGltcGxlbWVudGF0aW9uKS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEFjdGlvbihjbGllbnQsIGFjdGlvbkZuLCBcbi8vIFNvbWUgbWluaWZpZXJzIGRyb3AgYEZ1bmN0aW9uLnByb3RvdHlwZS5uYW1lYCwgb3IgcmVwbGFjZSBpdCB3aXRoIHNob3J0IGxldHRlcnMsXG4vLyBtZWFuaW5nIHRoYXQgYGFjdGlvbkZuLm5hbWVgIHdpbGwgbm90IGFsd2F5cyB3b3JrLiBGb3IgdGhhdCBjYXNlLCB0aGUgY29uc3VtZXJcbi8vIG5lZWRzIHRvIHBhc3MgdGhlIG5hbWUgZXhwbGljaXRseS5cbm5hbWUpIHtcbiAgICBjb25zdCBhY3Rpb25faW1wbGljaXQgPSBjbGllbnRbYWN0aW9uRm4ubmFtZV07XG4gICAgaWYgKHR5cGVvZiBhY3Rpb25faW1wbGljaXQgPT09ICdmdW5jdGlvbicpXG4gICAgICAgIHJldHVybiBhY3Rpb25faW1wbGljaXQ7XG4gICAgY29uc3QgYWN0aW9uX2V4cGxpY2l0ID0gY2xpZW50W25hbWVdO1xuICAgIGlmICh0eXBlb2YgYWN0aW9uX2V4cGxpY2l0ID09PSAnZnVuY3Rpb24nKVxuICAgICAgICByZXR1cm4gYWN0aW9uX2V4cGxpY2l0O1xuICAgIHJldHVybiAocGFyYW1zKSA9PiBhY3Rpb25GbihjbGllbnQsIHBhcmFtcyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRBY3Rpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getUnit.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnit: () => (/* binding */ getUnit)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/constants/unit.js\");\n\nfunction getUnit(unit) {\n    if (typeof unit === 'number')\n        return unit;\n    if (unit === 'wei')\n        return 0;\n    return Math.abs(viem__WEBPACK_IMPORTED_MODULE_0__.weiUnits[unit]);\n}\n//# sourceMappingURL=getUnit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VW5pdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQztBQUN6QjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDBDQUFRO0FBQzVCO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcY2xpZW50XFxub2RlX21vZHVsZXNcXEB3YWdtaVxcY29yZVxcZGlzdFxcZXNtXFx1dGlsc1xcZ2V0VW5pdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB3ZWlVbml0cyB9IGZyb20gJ3ZpZW0nO1xuZXhwb3J0IGZ1bmN0aW9uIGdldFVuaXQodW5pdCkge1xuICAgIGlmICh0eXBlb2YgdW5pdCA9PT0gJ251bWJlcicpXG4gICAgICAgIHJldHVybiB1bml0O1xuICAgIGlmICh1bml0ID09PSAnd2VpJylcbiAgICAgICAgcmV0dXJuIDA7XG4gICAgcmV0dXJuIE1hdGguYWJzKHdlaVVuaXRzW3VuaXRdKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFVuaXQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getVersion.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\nconst getVersion = () => `@wagmi/core@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQyx3Q0FBd0MsZ0RBQU8sQ0FBQztBQUN2RCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHV0aWxzXFxnZXRWZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuLi92ZXJzaW9uLmpzJztcbmV4cG9ydCBjb25zdCBnZXRWZXJzaW9uID0gKCkgPT4gYEB3YWdtaS9jb3JlQCR7dmVyc2lvbn1gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0VmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/serialize.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys, cutoff) {\n    return keys.slice(0, cutoff).join('.') || '.';\n}\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array, value) {\n    const { length } = array;\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n    return 0;\n}\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(replacer, circularReplacer) {\n    const hasReplacer = typeof replacer === 'function';\n    const hasCircularReplacer = typeof circularReplacer === 'function';\n    const cache = [];\n    const keys = [];\n    return function replace(key, value) {\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                }\n                else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n                keys[keys.length] = key;\n                const valueCutoff = getCutoff(cache, value);\n                if (valueCutoff !== 0) {\n                    return hasCircularReplacer\n                        ? circularReplacer.call(this, key, value, getReferenceKey(keys, valueCutoff))\n                        : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n                }\n            }\n            else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n        }\n        return hasReplacer ? replacer.call(this, key, value) : value;\n    };\n}\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nfunction serialize(value, replacer, indent, circularReplacer) {\n    return JSON.stringify(value, createReplacer((key, value_) => {\n        let value = value_;\n        if (typeof value === 'bigint')\n            value = { __type: 'bigint', value: value_.toString() };\n        if (value instanceof Map)\n            value = { __type: 'Map', value: Array.from(value_.entries()) };\n        return replacer?.(key, value) ?? value;\n    }, circularReplacer), indent ?? undefined);\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js":
/*!********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/uid.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHV0aWxzXFx1aWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2l6ZSA9IDI1NjtcbmxldCBpbmRleCA9IHNpemU7XG5sZXQgYnVmZmVyO1xuZXhwb3J0IGZ1bmN0aW9uIHVpZChsZW5ndGggPSAxMSkge1xuICAgIGlmICghYnVmZmVyIHx8IGluZGV4ICsgbGVuZ3RoID4gc2l6ZSAqIDIpIHtcbiAgICAgICAgYnVmZmVyID0gJyc7XG4gICAgICAgIGluZGV4ID0gMDtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzaXplOyBpKyspIHtcbiAgICAgICAgICAgIGJ1ZmZlciArPSAoKDI1NiArIE1hdGgucmFuZG9tKCkgKiAyNTYpIHwgMCkudG9TdHJpbmcoMTYpLnN1YnN0cmluZygxKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gYnVmZmVyLnN1YnN0cmluZyhpbmRleCwgaW5kZXgrKyArIGxlbmd0aCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11aWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/version.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/version.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.17.2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcQHdhZ21pXFxjb3JlXFxkaXN0XFxlc21cXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi4xNy4yJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\n");

/***/ })

};
;