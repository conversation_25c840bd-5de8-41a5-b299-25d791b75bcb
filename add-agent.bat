@echo off
echo Adding Agent Role to Whitelist Contract
echo =======================================

if "%1"=="" (
  echo Error: Missing agent address
  echo Usage: add-agent.bat [AGENT_ADDRESS] [TOKEN_ADDRESS] [NETWORK]
  echo.
  echo AGENT_ADDRESS: Address to add as an agent
  echo TOKEN_ADDRESS: Token contract address (default: use last upgraded token)
  echo NETWORK: Network to connect to (default: amoy)
  exit /b 1
)

set AGENT_ADDRESS=%1

if not "%2"=="" (
  set TOKEN_ADDRESS=%2
) else (
  set TOKEN_ADDRESS=0x324AB4526d55630bf8AfA86F479697B23c6792A4
)

if not "%3"=="" (
  set NETWORK=%3
) else (
  set NETWORK=amoy
)

echo Agent Address: %AGENT_ADDRESS%
echo Token Address: %TOKEN_ADDRESS%
echo Network: %NETWORK%
echo.

npx hardhat run scripts/add-agent.js --network %NETWORK% 