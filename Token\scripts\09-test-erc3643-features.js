const { ethers } = require("hardhat");

async function main() {
    console.log("🧪 Testing ERC-3643 Features...");
    console.log("===============================");

    const [deployer, user1, user2, user3] = await ethers.getSigners();
    console.log("Testing with accounts:");
    console.log("- Deployer:", deployer.address);
    console.log("- User1:", user1.address);
    console.log("- User2:", user2.address);
    console.log("- User3:", user3.address);

    // Get contract addresses from environment or use test deployment
    let claimRegistryAddress = process.env.TEST_CLAIM_REGISTRY_ADDRESS || process.env.CLAIM_REGISTRY_ADDRESS;
    let identityRegistryAddress = process.env.TEST_IDENTITY_REGISTRY_ADDRESS || process.env.IDENTITY_REGISTRY_ADDRESS;
    let complianceAddress = process.env.TEST_COMPLIANCE_ADDRESS || process.env.COMPLIANCE_ADDRESS;
    let tokenAddress = process.env.TEST_TOKEN_ADDRESS || process.env.TOKEN_ADDRESS;

    // If no addresses, deploy test system
    if (!claimRegistryAddress || !identityRegistryAddress || !complianceAddress || !tokenAddress) {
        console.log("⚠️ Missing contract addresses. Deploying test system...");
        const deployScript = require('./08-test-deployment.js');
        const deployment = await deployScript();
        
        claimRegistryAddress = deployment.claimRegistry;
        identityRegistryAddress = deployment.identityRegistry;
        complianceAddress = deployment.compliance;
        tokenAddress = deployment.token;
    }

    console.log("\n📋 Using Contracts:");
    console.log("===================");
    console.log("ClaimRegistry:", claimRegistryAddress);
    console.log("IdentityRegistry:", identityRegistryAddress);
    console.log("Compliance:", complianceAddress);
    console.log("SecurityToken:", tokenAddress);

    // Get contract instances
    const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
    const claimRegistry = ClaimRegistry.attach(claimRegistryAddress);

    const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
    const identityRegistry = IdentityRegistry.attach(identityRegistryAddress);

    const Compliance = await ethers.getContractFactory("Compliance");
    const compliance = Compliance.attach(complianceAddress);

    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const token = SecurityToken.attach(tokenAddress);

    // Test Results Tracking
    let testResults = {
        passed: 0,
        failed: 0,
        tests: []
    };

    function logTest(name, success, details = "") {
        const status = success ? "✅" : "❌";
        console.log(`${status} ${name}${details ? ` - ${details}` : ""}`);
        testResults.tests.push({ name, success, details });
        if (success) testResults.passed++;
        else testResults.failed++;
    }

    // Test 1: Identity Registry Features
    console.log("\n1️⃣ Testing Identity Registry Features...");
    console.log("========================================");

    try {
        // Register identities with different countries
        await identityRegistry.registerIdentity(user1.address, 840); // USA
        await identityRegistry.registerIdentity(user2.address, 124); // Canada
        logTest("Register identities", true, "USA and Canada");

        // Check verification status
        const user1Verified = await identityRegistry.isVerified(user1.address);
        const user1Country = await identityRegistry.investorCountry(user1.address);
        logTest("Identity verification", user1Verified && user1Country == 840, `User1: ${user1Country}`);

        // Test whitelist management
        await identityRegistry.addToWhitelist(user1.address);
        const user1Whitelisted = await identityRegistry.isWhitelisted(user1.address);
        logTest("Whitelist management", user1Whitelisted, "User1 whitelisted");

        // Test KYC management
        await identityRegistry.approveKyc(user1.address);
        const user1KycApproved = await identityRegistry.isKycApproved(user1.address);
        logTest("KYC management", user1KycApproved, "User1 KYC approved");

        // Test country restrictions
        await identityRegistry.restrictCountry(999); // Fictional country
        const isRestricted = await identityRegistry.isCountryRestricted(999);
        logTest("Country restrictions", isRestricted, "Country 999 restricted");

        // Test batch operations
        await identityRegistry.batchAddToWhitelist([user2.address]);
        await identityRegistry.batchApproveKyc([user2.address]);
        const user2Status = await identityRegistry.isWhitelisted(user2.address) && 
                           await identityRegistry.isKycApproved(user2.address);
        logTest("Batch operations", user2Status, "User2 batch processed");

    } catch (error) {
        logTest("Identity Registry Features", false, error.message);
    }

    // Test 2: Claims Integration
    console.log("\n2️⃣ Testing Claims Integration...");
    console.log("=================================");

    try {
        // Issue claims for users
        const kycClaimData = ethers.AbiCoder.defaultAbiCoder().encode(
            ["string", "uint256"], 
            ["KYC_APPROVED", Math.floor(Date.now() / 1000)]
        );

        await claimRegistry.issueClaim(
            user1.address,
            1, // KYC_CLAIM
            "0x", // empty signature
            kycClaimData,
            "", // empty URI
            0 // never expires
        );

        const qualificationClaimData = ethers.AbiCoder.defaultAbiCoder().encode(
            ["string", "uint256"], 
            ["QUALIFIED", Math.floor(Date.now() / 1000)]
        );

        await claimRegistry.issueClaim(
            user1.address,
            4, // QUALIFICATION_CLAIM
            "0x",
            qualificationClaimData,
            "",
            0
        );

        logTest("Issue claims", true, "KYC and Qualification claims issued");

        // Test claim verification
        const hasValidClaims = await identityRegistry.hasValidClaims(user1.address);
        logTest("Claim verification", hasValidClaims, "User1 has valid claims");

        // Test claims API
        const userClaims = await claimRegistry.getClaimsByAddress(user1.address);
        logTest("Claims retrieval", userClaims.length >= 2, `Found ${userClaims.length} claims`);

    } catch (error) {
        logTest("Claims Integration", false, error.message);
    }

    // Test 3: Compliance Rules
    console.log("\n3️⃣ Testing Compliance Rules...");
    console.log("===============================");

    try {
        // Test default compliance rule
        const defaultRuleId = ethers.keccak256(ethers.toUtf8Bytes("DEFAULT_RULE"));
        const defaultRule = await compliance.getComplianceRule(defaultRuleId);
        logTest("Default compliance rule", defaultRule[0], "Rule is active"); // isActive

        // Add custom compliance rule
        const customRuleId = ethers.keccak256(ethers.toUtf8Bytes("TEST_RULE"));
        await compliance.addComplianceRule(
            customRuleId,
            "Test Rule",
            50, // max holders
            ethers.parseUnits("10000", 0), // max tokens per holder
            ethers.parseUnits("500000", 0) // max total supply
        );

        const customRule = await compliance.getComplianceRule(customRuleId);
        logTest("Custom compliance rule", customRule[0] && customRule[1] == 50, "Max 50 holders");

        // Test country limits
        await compliance.setCountryLimit(customRuleId, 840, 25); // USA limit
        const countryLimit = await compliance.getCountryLimit(customRuleId, 840);
        logTest("Country limits", countryLimit == 25, "USA limit: 25 holders");

        // Test transfer compliance
        const canTransfer = await compliance.canTransfer(user1.address, user2.address, 100);
        logTest("Transfer compliance", canTransfer, "Transfer allowed between compliant users");

    } catch (error) {
        logTest("Compliance Rules", false, error.message);
    }

    // Test 4: SecurityToken Integration
    console.log("\n4️⃣ Testing SecurityToken Integration...");
    console.log("======================================");

    try {
        // Test token info
        const name = await token.name();
        const version = await token.version();
        logTest("Token info", name.includes("Test") && version === "3.0.0", `${name} v${version}`);

        // Test minting to compliant address
        const mintAmount = ethers.parseUnits("1000", 0);
        await token.mint(user1.address, mintAmount);
        const user1Balance = await token.balanceOf(user1.address);
        logTest("Mint to compliant address", user1Balance == mintAmount, `Minted ${user1Balance} tokens`);

        // Test transfer between compliant addresses
        await token.connect(user1).transfer(user2.address, 500);
        const user2Balance = await token.balanceOf(user2.address);
        logTest("Transfer between compliant", user2Balance == 500, "500 tokens transferred");

        // Test rejection of transfer to non-compliant address
        try {
            await token.connect(user1).transfer(user3.address, 100);
            logTest("Reject non-compliant transfer", false, "Should have failed");
        } catch (error) {
            logTest("Reject non-compliant transfer", true, "Correctly rejected");
        }

        // Test forced transfer
        await token.forcedTransfer(user1.address, user2.address, 100);
        const user2BalanceAfterForced = await token.balanceOf(user2.address);
        logTest("Forced transfer", user2BalanceAfterForced == 600, "Forced transfer successful");

        // Test compliance integration
        const tokenCanTransfer = await token.canTransfer(user1.address, user2.address, 100);
        logTest("Token compliance check", tokenCanTransfer, "canTransfer works");

    } catch (error) {
        logTest("SecurityToken Integration", false, error.message);
    }

    // Test 5: Advanced Features
    console.log("\n5️⃣ Testing Advanced Features...");
    console.log("=================================");

    try {
        // Test holder tracking
        const totalHolders = await compliance.getTotalHolders();
        logTest("Holder tracking", totalHolders >= 2, `${totalHolders} holders tracked`);

        // Test country holder count
        const usaHolders = await compliance.getCountryHolderCount(840);
        logTest("Country holder count", usaHolders >= 1, `${usaHolders} USA holders`);

        // Test verified address enumeration
        const verifiedCount = await identityRegistry.getVerifiedAddressCount();
        logTest("Address enumeration", verifiedCount >= 2, `${verifiedCount} verified addresses`);

        // Test freezing functionality
        await identityRegistry.freezeAddress(user3.address);
        const isFrozen = await identityRegistry.isFrozen(user3.address);
        logTest("Address freezing", isFrozen, "User3 frozen");

        // Test transfer stats
        const [totalTransfers, lastTransferTime] = await compliance.getTransferStats();
        logTest("Transfer statistics", totalTransfers > 0, `${totalTransfers} transfers recorded`);

    } catch (error) {
        logTest("Advanced Features", false, error.message);
    }

    // Test Summary
    console.log("\n🎉 Test Summary");
    console.log("===============");
    console.log(`Total Tests: ${testResults.passed + testResults.failed}`);
    console.log(`Passed: ${testResults.passed} ✅`);
    console.log(`Failed: ${testResults.failed} ❌`);
    console.log(`Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

    if (testResults.failed > 0) {
        console.log("\n❌ Failed Tests:");
        testResults.tests.filter(t => !t.success).forEach(test => {
            console.log(`  - ${test.name}: ${test.details}`);
        });
    }

    // Next Steps
    console.log("\n🔄 Next Steps:");
    console.log("==============");
    if (testResults.failed === 0) {
        console.log("✅ All tests passed! System is ready for:");
        console.log("  1. Production deployment");
        console.log("  2. Admin panel integration");
        console.log("  3. User acceptance testing");
    } else {
        console.log("⚠️ Some tests failed. Please:");
        console.log("  1. Review failed tests above");
        console.log("  2. Fix any issues");
        console.log("  3. Re-run tests");
    }

    return testResults;
}

if (require.main === module) {
    main()
        .then((results) => {
            process.exit(results.failed === 0 ? 0 : 1);
        })
        .catch((error) => {
            console.error("❌ Testing failed:", error);
            process.exit(1);
        });
}

module.exports = main;
