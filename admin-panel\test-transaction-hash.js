const { PrismaClient } = require('@prisma/client');

async function testTransactionHashField() {
  console.log('🧪 Testing Transaction Hash Field');
  console.log('==================================');

  const prisma = new PrismaClient();

  try {
    // Test if we can create an order with transaction hash
    console.log('1️⃣ Testing transaction hash field...');
    
    // First, let's see what tables exist
    const result = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    
    console.log('Available tables:', result);

    // Check if orders table has transactionHash column
    const columns = await prisma.$queryRaw`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'orders' AND table_schema = 'public'
    `;
    
    console.log('Orders table columns:', columns);

    // Check if transactionHash column exists
    const hasTransactionHash = columns.some(col => col.column_name === 'transactionHash');
    console.log('Has transactionHash field:', hasTransactionHash ? '✅ YES' : '❌ NO');

    if (hasTransactionHash) {
      console.log('✅ Transaction hash field is available in the database!');
      console.log('✅ Ready for minting with transaction hash storage!');
    } else {
      console.log('❌ Transaction hash field is missing from the database');
      console.log('💡 Run: npx prisma db push');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testTransactionHashField().catch(console.error);
