'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { useState } from 'react';

export default function AuthTestPage() {
  const { user, error, isLoading } = useUser();
  const [logoutMethod, setLogoutMethod] = useState<'link' | 'button'>('button');

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error: {error.message}
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    // Clear local storage and session storage
    localStorage.clear();
    sessionStorage.clear();

    // Navigate to logout URL with explicit returnTo
    window.location.href = '/api/auth/logout?returnTo=' + encodeURIComponent(window.location.origin);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Auth0 Authentication Test</h1>

          {user ? (
            <div className="space-y-6">
              {/* User Information */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-green-900 mb-4">✅ Authenticated User</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Name:</strong> {user.name || 'N/A'}
                  </div>
                  <div>
                    <strong>Email:</strong> {user.email || 'N/A'}
                  </div>
                  <div>
                    <strong>Sub:</strong> {user.sub || 'N/A'}
                  </div>
                  <div>
                    <strong>Email Verified:</strong> {user.email_verified ? 'Yes' : 'No'}
                  </div>
                  <div>
                    <strong>Updated At:</strong> {user.updated_at || 'N/A'}
                  </div>
                  <div>
                    <strong>Picture:</strong> {user.picture ? 'Available' : 'N/A'}
                  </div>
                </div>

                {user.picture && (
                  <div className="mt-4">
                    <img
                      src={user.picture}
                      alt="User avatar"
                      className="w-16 h-16 rounded-full"
                    />
                  </div>
                )}
              </div>

              {/* Logout Testing */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-blue-900 mb-4">Logout Testing</h2>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Choose logout method:
                    </label>
                    <div className="space-x-4">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          value="button"
                          checked={logoutMethod === 'button'}
                          onChange={(e) => setLogoutMethod(e.target.value as 'button')}
                          className="form-radio"
                        />
                        <span className="ml-2">JavaScript Button (Recommended)</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          value="link"
                          checked={logoutMethod === 'link'}
                          onChange={(e) => setLogoutMethod(e.target.value as 'link')}
                          className="form-radio"
                        />
                        <span className="ml-2">Direct Link</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-x-4">
                    {logoutMethod === 'button' ? (
                      <button
                        onClick={handleLogout}
                        className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
                      >
                        Logout (JS Button)
                      </button>
                    ) : (
                      <a
                        href={`/api/auth/logout?returnTo=${process.env.NEXT_PUBLIC_AUTH0_RETURN_TO_URL || process.env.AUTH0_BASE_URL}`}
                        className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors inline-block"
                      >
                        Logout (Direct Link)
                      </a>
                    )}

                    <a
                      href="/"
                      className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors inline-block"
                    >
                      Back to Dashboard
                    </a>
                  </div>
                </div>
              </div>

              {/* Session Information */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Session Information</h2>
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Not Authenticated */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-yellow-900 mb-4">⚠️ Not Authenticated</h2>
                <p className="text-yellow-800 mb-4">
                  You are not currently logged in. Use the buttons below to test the Auth0 Universal Login.
                </p>

                <div className="space-x-4">
                  <a
                    href="/api/auth/login"
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-block"
                  >
                    Login with Auth0
                  </a>

                  <a
                    href="/api/auth/login?screen_hint=signup"
                    className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors inline-block"
                  >
                    Sign Up with Auth0
                  </a>

                  <a
                    href="/"
                    className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors inline-block"
                  >
                    Back to Dashboard
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
