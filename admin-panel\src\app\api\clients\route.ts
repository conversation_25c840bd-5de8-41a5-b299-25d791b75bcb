import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createClientSchema, clientQuerySchema } from '@/lib/validations/client';
import { Prisma } from '@prisma/client';

// GET /api/clients - List clients with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = clientQuerySchema.parse(Object.fromEntries(searchParams));

    const { page, limit, search, kycStatus, isWhitelisted, sortBy, sortOrder } = query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.ClientWhereInput = {};

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phoneNumber: { contains: search } },
        { taxIdentificationNumber: { contains: search } },
      ];
    }

    if (kycStatus) {
      where.kycStatus = kycStatus;
    }

    if (isWhitelisted !== undefined) {
      where.isWhitelisted = isWhitelisted;
    }

    // Get total count for pagination
    const total = await prisma.client.count({ where });

    // Get clients
    const clients = await prisma.client.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phoneNumber: true,
        nationality: true,
        kycStatus: true,
        kycNotes: true,
        isWhitelisted: true,
        walletAddress: true,
        walletVerifiedAt: true,
        createdAt: true,
        updatedAt: true,

        // Personal Information
        gender: true,
        birthday: true,
        birthPlace: true,

        // Identification & Contact
        identificationType: true,
        passportNumber: true,
        idCardNumber: true,
        documentExpiration: true,

        // Professional Information
        occupation: true,
        sectorOfActivity: true,
        pepStatus: true,
        pepDetails: true,

        // Address Information
        street: true,
        buildingNumber: true,
        city: true,
        state: true,
        country: true,
        zipCode: true,

        // Financial Information
        sourceOfWealth: true,
        bankAccountNumber: true,
        sourceOfFunds: true,
        taxIdentificationNumber: true,

        // Agreement fields
        agreementAccepted: true,
        agreementAcceptedAt: true,
      },
    });

    return NextResponse.json({
      clients,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch clients' },
      { status: 500 }
    );
  }
}

// POST /api/clients - Create a new client
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createClientSchema.parse(body);

    // Convert string dates to Date objects
    const clientData = {
      ...validatedData,
      birthday: new Date(validatedData.birthday),
      documentExpiration: new Date(validatedData.documentExpiration),
    };

    const client = await prisma.client.create({
      data: clientData,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phoneNumber: true,
        nationality: true,
        kycStatus: true,
        kycNotes: true,
        isWhitelisted: true,
        createdAt: true,

        // Personal Information
        gender: true,
        birthday: true,
        birthPlace: true,

        // Identification & Contact
        identificationType: true,
        passportNumber: true,
        idCardNumber: true,
        documentExpiration: true,

        // Professional Information
        occupation: true,
        sectorOfActivity: true,
        pepStatus: true,
        pepDetails: true,

        // Address Information
        street: true,
        buildingNumber: true,
        city: true,
        state: true,
        country: true,
        zipCode: true,

        // Financial Information
        sourceOfWealth: true,
        bankAccountNumber: true,
        sourceOfFunds: true,
        taxIdentificationNumber: true,
      },
    });

    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('Error creating client:', error);

    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Client with this email or tax ID already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create client' },
      { status: 500 }
    );
  }
}

// PUT /api/clients - Update client by email (for wallet updates)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, walletAddress, walletSignature, walletVerifiedAt, ...otherData } = body;

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Find client by email
    const existingClient = await prisma.client.findUnique({
      where: { email },
    });

    if (!existingClient) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Prepare update data
    const updateData: any = { ...otherData };

    if (walletAddress !== undefined) updateData.walletAddress = walletAddress;
    if (walletSignature !== undefined) updateData.walletSignature = walletSignature;
    if (walletVerifiedAt !== undefined) updateData.walletVerifiedAt = new Date(walletVerifiedAt);

    updateData.updatedAt = new Date();

    const client = await prisma.client.update({
      where: { email },
      data: updateData,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phoneNumber: true,
        nationality: true,
        kycStatus: true,
        kycNotes: true,
        isWhitelisted: true,
        walletAddress: true,
        walletVerifiedAt: true,
        createdAt: true,
        updatedAt: true,

        // Personal Information
        gender: true,
        birthday: true,
        birthPlace: true,

        // Identification & Contact
        identificationType: true,
        passportNumber: true,
        idCardNumber: true,
        documentExpiration: true,

        // Professional Information
        occupation: true,
        sectorOfActivity: true,
        pepStatus: true,
        pepDetails: true,

        // Address Information
        street: true,
        buildingNumber: true,
        city: true,
        state: true,
        country: true,
        zipCode: true,

        // Financial Information
        sourceOfWealth: true,
        bankAccountNumber: true,
        sourceOfFunds: true,
        taxIdentificationNumber: true,
      },
    });

    return NextResponse.json(client);
  } catch (error) {
    console.error('Error updating client:', error);

    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Wallet address already exists for another client' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update client' },
      { status: 500 }
    );
  }
}
