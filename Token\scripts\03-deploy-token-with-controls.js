const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Security Token with Advanced Transfer Controls...");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Deploy Whitelist first
  console.log("\n📋 Deploying Whitelist...");
  const Whitelist = await ethers.getContractFactory("Whitelist");
  const whitelist = await upgrades.deployProxy(
    Whitelist,
    [deployer.address],
    { initializer: "initialize" }
  );
  await whitelist.waitForDeployment();
  const whitelistAddress = await whitelist.getAddress();
  console.log("✅ Whitelist deployed to:", whitelistAddress);

  // Deploy Security Token
  console.log("\n🪙 Deploying Security Token...");
  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  const token = await upgrades.deployProxy(
    SecurityToken,
    [
      "Advanced Control Token", // name
      "ACT", // symbol
      0, // decimals (commodity token)
      ethers.parseUnits("1000000", 0), // maxSupply (1M tokens with 0 decimals)
      whitelistAddress, // whitelist address
      deployer.address, // admin
      "25 USD", // price
      "Tier 1: 10%, Tier 2: 20%", // bonus tiers
      "Security token with advanced transfer controls for testing", // description
      "" // image URL
    ],
    { initializer: "initialize" }
  );
  await token.waitForDeployment();
  const tokenAddress = await token.getAddress();
  console.log("✅ Security Token deployed to:", tokenAddress);

  // Add deployer as agent to both contracts (check if not already agent)
  console.log("\n👤 Setting up roles...");

  // Check if deployer is already an agent in whitelist
  try {
    const isWhitelistAgent = await whitelist.hasRole(await whitelist.AGENT_ROLE(), deployer.address);
    if (!isWhitelistAgent) {
      await whitelist.addAgent(deployer.address);
      console.log("✅ Deployer added as agent to whitelist");
    } else {
      console.log("✅ Deployer already an agent in whitelist");
    }
  } catch (error) {
    console.log("⚠️ Could not check whitelist agent role, trying to add anyway...");
    try {
      await whitelist.addAgent(deployer.address);
      console.log("✅ Deployer added as agent to whitelist");
    } catch (addError) {
      console.log("ℹ️ Deployer might already be an agent in whitelist");
    }
  }

  // Check if deployer is already an agent in token
  try {
    const isTokenAgent = await token.hasRole(await token.AGENT_ROLE(), deployer.address);
    if (!isTokenAgent) {
      await token.addAgent(deployer.address);
      console.log("✅ Deployer added as agent to token");
    } else {
      console.log("✅ Deployer already an agent in token");
    }
  } catch (error) {
    console.log("⚠️ Could not check token agent role, trying to add anyway...");
    try {
      await token.addAgent(deployer.address);
      console.log("✅ Deployer added as agent to token");
    } catch (addError) {
      console.log("ℹ️ Deployer might already be an agent in token");
    }
  }

  // Whitelist the deployer
  try {
    const isWhitelisted = await whitelist.isWhitelisted(deployer.address);
    if (!isWhitelisted) {
      await whitelist.addToWhitelist(deployer.address);
      console.log("✅ Deployer whitelisted");
    } else {
      console.log("✅ Deployer already whitelisted");
    }
  } catch (error) {
    console.log("⚠️ Could not check whitelist status, trying to add anyway...");
    try {
      await whitelist.addToWhitelist(deployer.address);
      console.log("✅ Deployer whitelisted");
    } catch (addError) {
      console.log("ℹ️ Deployer might already be whitelisted");
    }
  }

  // Mint some tokens to deployer
  console.log("\n💰 Minting initial tokens...");
  await token.mint(deployer.address, ethers.parseUnits("10000", 0)); // 10k tokens
  console.log("✅ Minted 10,000 tokens to deployer");

  // Configure advanced transfer controls
  console.log("\n🔒 Configuring Advanced Transfer Controls...");

  // Enable conditional transfers
  await token.setConditionalTransfers(true);
  console.log("✅ Conditional transfers enabled");

  // Enable transfer whitelisting
  await token.setTransferWhitelist(true);
  console.log("✅ Transfer whitelisting enabled");

  // Whitelist the deployer for transfers
  await token.setTransferWhitelistAddress(deployer.address, true);
  console.log("✅ Deployer whitelisted for transfers");

  // Enable transfer fees (1% = 100 basis points)
  await token.setTransferFees(true, 100, deployer.address);
  console.log("✅ Transfer fees enabled (1% to deployer)");

  // Verify the configuration
  console.log("\n🔍 Verifying configuration...");
  const conditionalEnabled = await token.conditionalTransfersEnabled();
  const whitelistEnabled = await token.transferWhitelistEnabled();
  const feesEnabled = await token.transferFeesEnabled();
  const [feePercentage, feeCollector] = await token.getTransferFeeConfig();
  const isTransferWhitelisted = await token.isTransferWhitelisted(deployer.address);

  console.log("Conditional Transfers:", conditionalEnabled ? "✅ Enabled" : "❌ Disabled");
  console.log("Transfer Whitelisting:", whitelistEnabled ? "✅ Enabled" : "❌ Disabled");
  console.log("Transfer Fees:", feesEnabled ? `✅ Enabled (${Number(feePercentage) / 100}%)` : "❌ Disabled");
  console.log("Fee Collector:", feeCollector);
  console.log("Deployer Transfer Whitelisted:", isTransferWhitelisted ? "✅ Yes" : "❌ No");

  // Display summary
  console.log("\n📊 DEPLOYMENT SUMMARY");
  console.log("====================");
  console.log("Network:", (await ethers.provider.getNetwork()).name);
  console.log("Deployer:", deployer.address);
  console.log("Whitelist Contract:", whitelistAddress);
  console.log("Security Token:", tokenAddress);
  console.log("Token Name:", "Advanced Control Token (ACT)");
  console.log("Max Supply:", "1,000,000 tokens");
  console.log("Decimals:", "0 (commodity token)");
  console.log("Initial Mint:", "10,000 tokens to deployer");
  console.log("");
  console.log("🔒 Advanced Transfer Controls:");
  console.log("- Conditional Transfers: ✅ Enabled");
  console.log("- Transfer Whitelisting: ✅ Enabled");
  console.log("- Transfer Fees: ✅ Enabled (1%)");
  console.log("");
  console.log("🌐 Admin Panel URLs:");
  console.log(`- Token Details: http://localhost:7788/tokens/${tokenAddress}`);
  console.log(`- Transfer Controls: http://localhost:7788/transfer-controls?token=${tokenAddress}`);
  console.log("");
  console.log("🧪 Test the features:");
  console.log("1. Try a regular transfer (should fail due to conditional transfers)");
  console.log("2. Approve a transfer using the admin panel");
  console.log("3. Execute the approved transfer");
  console.log("4. Check that fees were collected");

  // Save deployment info
  const deploymentInfo = {
    network: (await ethers.provider.getNetwork()).name,
    deployer: deployer.address,
    whitelist: whitelistAddress,
    token: tokenAddress,
    timestamp: new Date().toISOString(),
    features: {
      conditionalTransfers: conditionalEnabled,
      transferWhitelisting: whitelistEnabled,
      transferFees: feesEnabled,
      feePercentage: Number(feePercentage),
      feeCollector: feeCollector
    }
  };

  console.log("\n💾 Saving deployment info to deployments/advanced-controls-token.json");
  const fs = require('fs');
  const path = require('path');

  const deploymentsDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  fs.writeFileSync(
    path.join(deploymentsDir, 'advanced-controls-token.json'),
    JSON.stringify(deploymentInfo, null, 2)
  );

  console.log("✅ Deployment complete!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
