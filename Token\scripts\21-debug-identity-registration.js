const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Debug Identity Registration for Claim_002...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Using account:", deployer.address);

  try {
    const identityRegistryAddress = "******************************************";
    const walletToRegister = "******************************************";

    console.log("\n1️⃣ Connecting to IdentityRegistry...");
    console.log("   Identity Registry:", identityRegistryAddress);
    console.log("   Wallet to register:", walletToRegister);
    
    // Full ABI for debugging
    const identityABI = [
      'function isVerified(address account) external view returns (bool)',
      'function isWhitelisted(address account) external view returns (bool)',
      'function isFrozen(address account) external view returns (bool)',
      'function registerIdentity(address userAddress, uint16 country) external',
      'function addToWhitelist(address account) external',
      'function hasRole(bytes32 role, address account) external view returns (bool)',
      'function AGENT_ROLE() external view returns (bytes32)',
      'function DEFAULT_ADMIN_ROLE() external view returns (bytes32)',
      // Add more functions for debugging
      'function getIdentity(address account) external view returns (tuple(bool isVerified, bool isWhitelisted, bool isFrozen, uint16 country, uint256 createdAt, uint256 updatedAt))'
    ];
    
    const identityRegistry = new ethers.Contract(identityRegistryAddress, identityABI, deployer);

    console.log("\n2️⃣ Checking Roles and Permissions...");
    
    const agentRole = await identityRegistry.AGENT_ROLE();
    const defaultAdminRole = await identityRegistry.DEFAULT_ADMIN_ROLE();
    const hasAgentRole = await identityRegistry.hasRole(agentRole, deployer.address);
    const hasAdminRole = await identityRegistry.hasRole(defaultAdminRole, deployer.address);
    
    console.log("   AGENT_ROLE:", agentRole);
    console.log("   DEFAULT_ADMIN_ROLE:", defaultAdminRole);
    console.log("   Deployer has AGENT_ROLE:", hasAgentRole);
    console.log("   Deployer has DEFAULT_ADMIN_ROLE:", hasAdminRole);

    console.log("\n3️⃣ Checking Current Identity Status...");
    
    try {
      // Try to get the identity details
      const identity = await identityRegistry.getIdentity(walletToRegister);
      console.log("   Identity exists:");
      console.log("     Verified:", identity.isVerified);
      console.log("     Whitelisted:", identity.isWhitelisted);
      console.log("     Frozen:", identity.isFrozen);
      console.log("     Country:", identity.country);
      console.log("     Created At:", identity.createdAt.toString());
      console.log("     Updated At:", identity.updatedAt.toString());
      
      if (identity.isVerified) {
        console.log("   ✅ Identity already registered!");
        
        if (!identity.isWhitelisted) {
          console.log("\n4️⃣ Adding to Whitelist (identity already registered)...");
          const whitelistTx = await identityRegistry.addToWhitelist(walletToRegister, {
            gasLimit: 300000,
            maxFeePerGas: ethers.parseUnits('100', 'gwei'),
            maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
          });
          console.log("   Whitelist transaction sent:", whitelistTx.hash);
          const receipt = await whitelistTx.wait();
          console.log("   ✅ Added to whitelist successfully!");
          console.log("   Gas used:", receipt.gasUsed.toString());
        } else {
          console.log("   ✅ Already whitelisted!");
        }
        
        return;
      }
      
    } catch (identityError) {
      console.log("   Identity check failed:", identityError.message);
      console.log("   This might be normal if identity doesn't exist yet");
    }

    console.log("\n4️⃣ Attempting to Register Identity...");
    
    // Try with different gas settings and error handling
    try {
      console.log("   Estimating gas for registerIdentity...");
      const gasEstimate = await identityRegistry.registerIdentity.estimateGas(walletToRegister, 840);
      console.log("   Estimated gas:", gasEstimate.toString());
      
      console.log("   Sending registerIdentity transaction...");
      const registerTx = await identityRegistry.registerIdentity(walletToRegister, 840, {
        gasLimit: gasEstimate * 2n, // Double the estimated gas
        maxFeePerGas: ethers.parseUnits('100', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
      });
      
      console.log("   Transaction sent:", registerTx.hash);
      console.log("   Waiting for confirmation...");
      
      const receipt = await registerTx.wait();
      console.log("   ✅ Registration successful!");
      console.log("   Gas used:", receipt.gasUsed.toString());
      console.log("   Block number:", receipt.blockNumber);
      
      // Now add to whitelist
      console.log("\n5️⃣ Adding to Whitelist...");
      const whitelistTx = await identityRegistry.addToWhitelist(walletToRegister, {
        gasLimit: 300000,
        maxFeePerGas: ethers.parseUnits('100', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
      });
      console.log("   Whitelist transaction sent:", whitelistTx.hash);
      const whitelistReceipt = await whitelistTx.wait();
      console.log("   ✅ Added to whitelist successfully!");
      console.log("   Gas used:", whitelistReceipt.gasUsed.toString());
      
    } catch (registerError) {
      console.log("   Registration failed:", registerError.message);
      
      if (registerError.message.includes('already registered')) {
        console.log("   Identity already registered, trying to whitelist...");
        
        const whitelistTx = await identityRegistry.addToWhitelist(walletToRegister, {
          gasLimit: 300000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        });
        console.log("   Whitelist transaction sent:", whitelistTx.hash);
        await whitelistTx.wait();
        console.log("   ✅ Added to whitelist successfully!");
      } else {
        console.log("   Detailed error:", registerError);
      }
    }

    console.log("\n6️⃣ Final Verification...");
    
    try {
      const finalIdentity = await identityRegistry.getIdentity(walletToRegister);
      console.log("   Final Status:");
      console.log("     Verified:", finalIdentity.isVerified);
      console.log("     Whitelisted:", finalIdentity.isWhitelisted);
      console.log("     Frozen:", finalIdentity.isFrozen);
      console.log("     Can Access Token:", finalIdentity.isVerified && finalIdentity.isWhitelisted && !finalIdentity.isFrozen);
    } catch (finalError) {
      console.log("   Final check failed:", finalError.message);
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 DEBUG PROCESS COMPLETE!");
    console.log("=" .repeat(60));

  } catch (error) {
    console.error("❌ Debug failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
