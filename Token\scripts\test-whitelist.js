// We require the Hardhat Runtime Environment explicitly here. This is optional
// but useful for running the script in a standalone fashion through `node <script>`.
//
// When running the script with `npx hardhat run <script>` you'll find the Hardhat
// Runtime Environment's members available in the global scope.
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    // Get signers and network information
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;
    
    console.log("Testing whitelist with the account:", deployer.address);
    console.log("Network:", networkName);
    
    // Load token information from tokens directory
    const tokensDir = path.join(__dirname, "../tokens");
    const indexFile = path.join(tokensDir, "index.json");
    
    if (!fs.existsSync(indexFile)) {
      console.error("Token index file not found");
      console.error("Please deploy a token first");
      process.exit(1);
    }
    
    const tokenIndex = JSON.parse(fs.readFileSync(indexFile, "utf8"));
    
    // Use the first token in the index
    const tokenSymbol = Object.keys(tokenIndex)[0];
    if (!tokenSymbol) {
      console.error("No tokens found in the index");
      process.exit(1);
    }
    
    const tokenInfo = tokenIndex[tokenSymbol];
    const tokenAddress = tokenInfo.address;
    const identityRegistryAddress = tokenInfo.identityRegistry;
    
    console.log(`Using token: ${tokenSymbol} (${tokenAddress})`);
    console.log(`Identity Registry: ${identityRegistryAddress}`);
    
    // Get contract factories
    const Whitelist = await hre.ethers.getContractFactory("Whitelist");
    const WhitelistWithKYC = await hre.ethers.getContractFactory("WhitelistWithKYC");
    
    // Deploy a new whitelist for testing
    console.log("Deploying a new whitelist for testing...");
    const whitelist = await hre.upgrades.deployProxy(
      Whitelist,
      [deployer.address],
      { initializer: 'initializeWithAgent' }
    );
    await whitelist.waitForDeployment();
    
    const whitelistAddress = await whitelist.getAddress();
    console.log("Test whitelist deployed to:", whitelistAddress);
    
    // Test basic whitelist functions
    const testAddress = "******************************************";
    
    console.log("\nTesting whitelist functions...");
    
    // Check if address is whitelisted (should be false)
    console.log("Checking if address is whitelisted (before)...");
    const isWhitelistedBefore = await whitelist.isWhitelisted(testAddress);
    console.log("Is whitelisted (before):", isWhitelistedBefore);
    
    // Add address to whitelist
    console.log("Adding address to whitelist...");
    const addTx = await whitelist.addToWhitelist(testAddress);
    await addTx.wait();
    console.log("Address added to whitelist");
    
    // Check if address is whitelisted (should be true)
    console.log("Checking if address is whitelisted (after)...");
    const isWhitelistedAfter = await whitelist.isWhitelisted(testAddress);
    console.log("Is whitelisted (after):", isWhitelistedAfter);
    
    // Check if address is frozen (should be false)
    console.log("Checking if address is frozen (before)...");
    const isFrozenBefore = await whitelist.isFrozen(testAddress);
    console.log("Is frozen (before):", isFrozenBefore);
    
    // Freeze address
    console.log("Freezing address...");
    const freezeTx = await whitelist.freezeAddress(testAddress);
    await freezeTx.wait();
    console.log("Address frozen");
    
    // Check if address is frozen (should be true)
    console.log("Checking if address is frozen (after)...");
    const isFrozenAfter = await whitelist.isFrozen(testAddress);
    console.log("Is frozen (after):", isFrozenAfter);
    
    // Check if address is KYC approved (should be false)
    console.log("Checking if address is KYC approved (before)...");
    const isKycApprovedBefore = await whitelist.isKycApproved(testAddress);
    console.log("Is KYC approved (before):", isKycApprovedBefore);
    
    // Approve KYC for address
    console.log("Approving KYC for address...");
    const approveTx = await whitelist.approveKyc(testAddress);
    await approveTx.wait();
    console.log("KYC approved for address");
    
    // Check if address is KYC approved (should be true)
    console.log("Checking if address is KYC approved (after)...");
    const isKycApprovedAfter = await whitelist.isKycApproved(testAddress);
    console.log("Is KYC approved (after):", isKycApprovedAfter);
    
    console.log("\nTest completed successfully!");
    
  } catch (error) {
    console.error("Error during whitelist testing:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });