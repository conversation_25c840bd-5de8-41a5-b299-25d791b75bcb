const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 Admin Override: Direct Whitelist for Same Wallet...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Using account:", deployer.address);
  console.log("⚠️  Note: This is the same wallet as the one being whitelisted");

  try {
    const identityRegistryAddress = "******************************************";
    const walletToWhitelist = "******************************************";

    console.log("\n1️⃣ Connecting to IdentityRegistry...");
    console.log("   Identity Registry:", identityRegistryAddress);
    console.log("   Wallet to whitelist:", walletToWhitelist);
    console.log("   Same as deployer:", walletToWhitelist === deployer.address);
    
    // Try admin functions that might bypass normal registration
    const identityABI = [
      'function isVerified(address account) external view returns (bool)',
      'function isWhitelisted(address account) external view returns (bool)',
      'function isFrozen(address account) external view returns (bool)',
      'function registerIdentity(address userAddress, uint16 country) external',
      'function addToWhitelist(address account) external',
      'function hasRole(bytes32 role, address account) external view returns (bool)',
      'function AGENT_ROLE() external view returns (bytes32)',
      'function DEFAULT_ADMIN_ROLE() external view returns (bytes32)',
      'function grantRole(bytes32 role, address account) external',
      // Try admin override functions
      'function adminRegisterIdentity(address userAddress, uint16 country) external',
      'function forceAddToWhitelist(address account) external',
      'function adminAddToWhitelist(address account) external'
    ];
    
    const identityRegistry = new ethers.Contract(identityRegistryAddress, identityABI, deployer);

    console.log("\n2️⃣ Trying Alternative Approach: Admin Self-Registration...");
    
    // Since it's the same wallet, let's try a different strategy
    // Maybe we can grant the wallet AGENT_ROLE and then register
    
    const agentRole = await identityRegistry.AGENT_ROLE();
    const hasAgentRole = await identityRegistry.hasRole(agentRole, walletToWhitelist);
    
    console.log("   AGENT_ROLE:", agentRole);
    console.log("   Wallet has AGENT_ROLE:", hasAgentRole);
    
    if (!hasAgentRole) {
      console.log("   Granting AGENT_ROLE to wallet...");
      try {
        const grantRoleTx = await identityRegistry.grantRole(agentRole, walletToWhitelist, {
          gasLimit: 200000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        });
        await grantRoleTx.wait();
        console.log("   ✅ AGENT_ROLE granted");
      } catch (roleError) {
        console.log("   Role grant failed:", roleError.message);
      }
    }

    console.log("\n3️⃣ Trying Direct Identity Creation...");
    
    // Try to manually create the identity record
    // This might work if we use a different approach
    
    try {
      // First, let's try to call the function with different parameters
      console.log("   Attempting registerIdentity with country code 1 (USA)...");
      
      const registerTx = await identityRegistry.registerIdentity(walletToWhitelist, 1, {
        gasLimit: 500000,
        maxFeePerGas: ethers.parseUnits('50', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('20', 'gwei')
      });
      
      console.log("   Transaction sent:", registerTx.hash);
      const receipt = await registerTx.wait();
      console.log("   ✅ Registration successful!");
      console.log("   Gas used:", receipt.gasUsed.toString());
      
      // Now add to whitelist
      console.log("\n4️⃣ Adding to Whitelist...");
      const whitelistTx = await identityRegistry.addToWhitelist(walletToWhitelist, {
        gasLimit: 300000,
        maxFeePerGas: ethers.parseUnits('50', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('20', 'gwei')
      });
      
      console.log("   Whitelist transaction sent:", whitelistTx.hash);
      await whitelistTx.wait();
      console.log("   ✅ Added to whitelist successfully!");
      
    } catch (registerError) {
      console.log("   Registration still failed:", registerError.message);
      
      // Last resort: Try to check if there's a different function or if the identity already exists
      console.log("\n🔍 Investigating Contract State...");
      
      // Let's see what functions are actually available
      try {
        // Try to call isVerified to see if it gives us more info
        const isVerified = await identityRegistry.isVerified(walletToWhitelist);
        console.log("   isVerified result:", isVerified);
        
        if (isVerified) {
          console.log("   Identity is already verified! Trying to whitelist...");
          const whitelistTx = await identityRegistry.addToWhitelist(walletToWhitelist, {
            gasLimit: 300000,
            maxFeePerGas: ethers.parseUnits('50', 'gwei'),
            maxPriorityFeePerGas: ethers.parseUnits('20', 'gwei')
          });
          await whitelistTx.wait();
          console.log("   ✅ Successfully whitelisted!");
        }
        
      } catch (verifyError) {
        console.log("   Verification check failed:", verifyError.message);
        
        // Ultimate fallback: Maybe the contract doesn't allow self-registration
        console.log("\n💡 Possible Solution: Use a Different Wallet");
        console.log("   The issue might be that the same wallet cannot register itself");
        console.log("   Consider using a different wallet address for testing");
        console.log("   Or check if there's an admin bypass function");
      }
    }

    console.log("\n5️⃣ Final Status Check...");
    
    try {
      const finalVerified = await identityRegistry.isVerified(walletToWhitelist);
      const finalWhitelisted = await identityRegistry.isWhitelisted(walletToWhitelist);
      const finalFrozen = await identityRegistry.isFrozen(walletToWhitelist);
      
      console.log("   Final Status:");
      console.log("   Verified:", finalVerified);
      console.log("   Whitelisted:", finalWhitelisted);
      console.log("   Frozen:", finalFrozen);
      console.log("   Can Access Token:", finalVerified && finalWhitelisted && !finalFrozen);
      
      if (finalVerified && finalWhitelisted && !finalFrozen) {
        console.log("\n🎉 SUCCESS! You should now be able to access the token!");
      } else {
        console.log("\n❌ Still not able to access the token");
        console.log("   Recommendation: Try using a different wallet address for testing");
      }
      
    } catch (finalError) {
      console.log("   Final check failed:", finalError.message);
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🔧 ADMIN OVERRIDE COMPLETE!");
    console.log("=" .repeat(60));

  } catch (error) {
    console.error("❌ Admin override failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
