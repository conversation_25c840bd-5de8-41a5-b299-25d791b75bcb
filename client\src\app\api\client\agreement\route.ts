import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@auth0/nextjs-auth0'

// Mock database - in production, this would be a real database
let agreementAcceptances: Record<string, {
  userId: string
  accepted: boolean
  acceptedAt: string
}> = {}

// GET /api/client/agreement - Get agreement acceptance status
export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next())

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const userId = session.user.sub
    const userEmail = session.user.email

    console.log('📋 Fetching agreement status for:', { userId, userEmail })

    // First try to get from admin panel database
    try {
      const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
      const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`, {
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (searchResponse.ok) {
        const searchData = await searchResponse.json()
        const client = searchData.clients?.[0]

        if (client && client.agreementAccepted !== undefined) {
          console.log('✅ Found agreement status in admin panel:', client.agreementAccepted)
          return NextResponse.json({
            accepted: client.agreementAccepted,
            acceptedAt: client.agreementAcceptedAt
          })
        }
      }
    } catch (adminError) {
      if (adminError instanceof Error) {
        console.log('⚠️ Could not fetch from admin panel, falling back to local storage:', adminError.message)
      } else {
        console.log('⚠️ Could not fetch from admin panel, falling back to local storage:', adminError)
      }
    }

    // Fallback to local storage
    const acceptance = agreementAcceptances[userId]

    if (!acceptance) {
      console.log('📋 No agreement status found')
      return NextResponse.json({
        accepted: false,
        acceptedAt: null
      })
    }

    console.log('📋 Found agreement status in local storage:', acceptance.accepted)
    return NextResponse.json({
      accepted: acceptance.accepted,
      acceptedAt: acceptance.acceptedAt
    })
  } catch (error) {
    console.error('Error fetching agreement status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/client/agreement - Accept agreement
export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next())

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { accepted } = body

    if (typeof accepted !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      )
    }

    const userId = session.user.sub
    const userEmail = session.user.email

    console.log('📝 Agreement acceptance request:', { userId, userEmail, accepted })

    // Store agreement acceptance locally (for immediate response)
    const acceptedAt = new Date().toISOString()
    agreementAcceptances[userId] = {
      userId,
      accepted,
      acceptedAt
    }

    // Sync with admin panel database
    try {
      const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
      console.log('🔄 Syncing agreement acceptance to admin panel:', adminApiUrl)

      // First, get the client by email to get their ID
      const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`, {
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (searchResponse.ok) {
        const searchData = await searchResponse.json()
        const client = searchData.clients?.[0]

        if (client) {
          console.log('✅ Found client in admin panel:', client.id)

          // Update the client's agreement status
          const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agreementAccepted: accepted,
              agreementAcceptedAt: acceptedAt,
            }),
          })

          if (updateResponse.ok) {
            console.log('✅ Agreement acceptance synced to admin panel')
          } else {
            console.log('❌ Failed to sync agreement to admin panel:', updateResponse.status)
          }
        } else {
          console.log('⚠️ Client not found in admin panel, creating new client record...')

          // Create a basic client record with agreement acceptance
          const createResponse = await fetch(`${adminApiUrl}/clients`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: userEmail,
              firstName: session.user.given_name || 'Unknown',
              lastName: session.user.family_name || 'User',
              agreementAccepted: accepted,
              agreementAcceptedAt: acceptedAt,
              // Add minimal required fields for client creation that pass validation
              gender: 'PREFER_NOT_TO_SAY',
              nationality: 'Unknown',
              birthday: new Date('1990-01-01').toISOString().split('T')[0], // Format as YYYY-MM-DD
              birthPlace: 'Unknown',
              identificationType: 'PASSPORT',
              passportNumber: `TEMP_${Date.now()}`, // Required for PASSPORT type
              documentExpiration: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000 * 10).toISOString().split('T')[0], // 10 years from now
              phoneNumber: '******-000-0000', // Valid phone format
              occupation: 'Not provided',
              sectorOfActivity: 'Not provided',
              pepStatus: 'NOT_PEP',
              street: 'Not provided',
              buildingNumber: '1',
              city: 'Not provided',
              country: 'Unknown',
              zipCode: '00000',
              sourceOfWealth: 'Not provided',
              bankAccountNumber: `TEMP_${Date.now()}`,
              sourceOfFunds: 'Not provided',
              taxIdentificationNumber: `TEMP_${Date.now()}`,
            }),
          })

          if (createResponse.ok) {
            console.log('✅ Created new client record with agreement acceptance')
          } else {
            const errorText = await createResponse.text()
            console.log('❌ Failed to create client record:', createResponse.status, errorText)
          }
        }
      } else {
        console.log('❌ Failed to search for client in admin panel:', searchResponse.status)
      }
    } catch (syncError) {
      if (syncError instanceof Error) {
        console.error('❌ Error syncing with admin panel:', syncError.message)
      } else {
        console.error('❌ Error syncing with admin panel:', syncError)
      }
      // Don't fail the request if sync fails - agreement is still stored locally
    }

    return NextResponse.json({
      success: true,
      accepted: true,
      acceptedAt: acceptedAt
    })
  } catch (error) {
    console.error('Error accepting agreement:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
