{"_format": "hh-sol-artifact-1", "contractName": "WhitelistV2", "sourceName": "contracts/WhitelistV2.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "KycRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "approveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchAdd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchApproveKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchFreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchRemoveFrom<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchRevokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_addresses", "type": "address[]"}], "name": "batchUnfreezeAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "freezeAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_admin", "type": "address"}], "name": "initializeWithAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "isKycApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "revokeKyc", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "unfreeze<PERSON>ddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}