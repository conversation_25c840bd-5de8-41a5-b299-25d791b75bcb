# Factory Token Enumeration

This document explains how to use the enhanced SecurityTokenFactory with token enumeration functionality.

## Overview

The updated SecurityTokenFactory contract now includes methods to enumerate all deployed tokens, making it easy to fetch and display tokens on dashboards and other applications.

## New Features

### Contract Methods

#### `getTokenCount()`
Returns the total number of tokens deployed by this factory.

```solidity
function getTokenCount() external view returns (uint256)
```

#### `getAllDeployedTokens()`
Returns an array of all deployed token addresses.

```solidity
function getAllDeployedTokens() external view returns (address[] memory)
```

#### `getDeployedToken(uint256 index)`
Returns the token address at a specific index.

```solidity
function getDeployedToken(uint256 index) external view returns (address)
```

#### `deployedTokens(uint256 index)`
Public array accessor for deployed tokens (automatically generated).

```solidity
address[] public deployedTokens;
```

### Dashboard Integration

The admin panel dashboard now automatically fetches all tokens from your factory contract:

1. **Automatic Loading**: When you connect your wallet, the dashboard automatically queries your factory
2. **Real-time Updates**: Use the "Refresh Token List" button to get the latest tokens
3. **Fallback Support**: If enumeration fails (older factory), falls back to manual token addition

## Usage Examples

### JavaScript/Web3 Integration

```javascript
// Connect to factory
const factory = new ethers.Contract(factoryAddress, factoryABI, provider);

// Get total token count
const tokenCount = await factory.getTokenCount();
console.log(`Total tokens: ${tokenCount}`);

// Get all tokens at once
const allTokens = await factory.getAllDeployedTokens();
console.log('All tokens:', allTokens);

// Get individual token
const firstToken = await factory.getDeployedToken(0);
console.log('First token:', firstToken);

// Load token details
for (const tokenAddress of allTokens) {
  const token = new ethers.Contract(tokenAddress, tokenABI, provider);
  const name = await token.name();
  const symbol = await token.symbol();
  console.log(`${name} (${symbol}): ${tokenAddress}`);
}
```

### Hardhat Script Example

```javascript
const factory = await ethers.getContractAt("SecurityTokenFactory", factoryAddress);
const tokenCount = await factory.getTokenCount();

for (let i = 0; i < tokenCount; i++) {
  const tokenAddress = await factory.getDeployedToken(i);
  const token = await ethers.getContractAt("SecurityToken", tokenAddress);
  
  const name = await token.name();
  const symbol = await token.symbol();
  const totalSupply = await token.totalSupply();
  
  console.log(`${i + 1}. ${name} (${symbol})`);
  console.log(`   Address: ${tokenAddress}`);
  console.log(`   Total Supply: ${totalSupply}`);
}
```

## Deployment

### Upgrading Existing Factory

To upgrade your existing factory with enumeration support:

```bash
# Deploy new factory with enumeration
npx hardhat run scripts/upgrade-factory-with-enumeration.js --network amoy

# Test the enumeration functionality
FACTORY_ADDRESS=0x... npx hardhat run scripts/test-factory-enumeration.js --network amoy
```

### Fresh Deployment

For new deployments, use the standard factory deployment script:

```bash
npx hardhat run scripts/01-deploy-factory.js --network amoy
```

## Dashboard Configuration

Update your admin panel configuration with the new factory address:

```typescript
// admin-panel/src/config.ts
export const getContractAddresses = (network: string) => {
  switch (network) {
    case 'amoy':
      return {
        factory: '0x...', // Your new factory address
        // ... other addresses
      };
    // ... other networks
  }
};
```

## Backward Compatibility

- **Existing Tokens**: Tokens deployed with older factories can still be added manually
- **Mixed Environment**: You can have both old and new factories, the dashboard handles both
- **Gradual Migration**: No need to migrate existing tokens, just deploy new ones with the updated factory

## Performance Considerations

### Gas Costs
- `getTokenCount()`: ~2,300 gas
- `getAllDeployedTokens()`: ~2,300 + (700 × number of tokens) gas
- `getDeployedToken(index)`: ~2,500 gas

### Best Practices

1. **Batch Loading**: Use `getAllDeployedTokens()` for better performance when loading many tokens
2. **Caching**: Cache token lists in your application to reduce RPC calls
3. **Pagination**: For factories with many tokens, consider implementing pagination
4. **Error Handling**: Always handle cases where enumeration might fail

## Troubleshooting

### Common Issues

1. **"Function not found" errors**: You're connected to an older factory without enumeration
2. **Empty token list**: Factory has no tokens deployed yet
3. **Out of bounds errors**: Trying to access token index >= token count

### Solutions

```javascript
// Check if factory supports enumeration
try {
  const tokenCount = await factory.getTokenCount();
  // Factory supports enumeration
} catch (error) {
  // Fall back to manual token addition
  console.log('Factory does not support enumeration');
}

// Safe token access
const tokenCount = await factory.getTokenCount();
if (tokenCount > 0) {
  const tokens = await factory.getAllDeployedTokens();
  // Process tokens...
}
```

## Testing

Run the enumeration test script to verify functionality:

```bash
FACTORY_ADDRESS=0x... npx hardhat run scripts/test-factory-enumeration.js --network amoy
```

This will test:
- Token count retrieval
- Individual token access
- Bulk token retrieval
- Boundary conditions
- Error handling
