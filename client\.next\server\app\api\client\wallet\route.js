/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/wallet/route";
exports.ids = ["app/api/client/wallet/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fwallet%2Froute&page=%2Fapi%2Fclient%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fwallet%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fwallet%2Froute&page=%2Fapi%2Fclient%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fwallet%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_wallet_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/wallet/route.ts */ \"(rsc)/./src/app/api/client/wallet/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/wallet/route\",\n        pathname: \"/api/client/wallet\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/wallet/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\wallet\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_wallet_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fwallet%2Froute&page=%2Fapi%2Fclient%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fwallet%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/wallet/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/client/wallet/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(rsc)/./node_modules/viem/_esm/utils/signature/verifyMessage.js\");\n\n\n\n// POST /api/client/wallet - Save wallet address and signature\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { address, signature, message } = await request.json();\n        if (!address || !signature || !message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Address, signature, and message are required'\n            }, {\n                status: 400\n            });\n        }\n        // Verify the signature using the exact message that was signed\n        console.log('Verifying signature:', {\n            address,\n            message: message.substring(0, 100) + '...',\n            signature: signature.substring(0, 20) + '...'\n        });\n        try {\n            const isValid = await (0,viem__WEBPACK_IMPORTED_MODULE_2__.verifyMessage)({\n                address: address,\n                message,\n                signature: signature\n            });\n            console.log('Signature verification result:', isValid);\n            if (!isValid) {\n                console.error('Signature verification failed - invalid signature');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid signature'\n                }, {\n                    status: 400\n                });\n            }\n        } catch (error) {\n            console.error('Signature verification error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Signature verification failed'\n            }, {\n                status: 400\n            });\n        }\n        const userEmail = session.user.email;\n        console.log('Updating client profile for email:', userEmail);\n        // Update client profile with wallet information\n        const response = await fetch(`${process.env.ADMIN_API_BASE_URL}/clients`, {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                email: userEmail,\n                walletAddress: address,\n                walletSignature: signature,\n                walletVerifiedAt: new Date().toISOString()\n            })\n        });\n        console.log('Admin API response status:', response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error('Admin API error:', errorData);\n            throw new Error(errorData.error || `Admin API error: ${response.status}`);\n        }\n        const updatedClient = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address,\n            signature,\n            verified: true,\n            verifiedAt: updatedClient.walletVerifiedAt\n        });\n    } catch (error) {\n        console.error('Error saving wallet:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Failed to save wallet'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/client/wallet - Get current user's wallet status\nasync function GET(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const userEmail = session.user.email;\n        // Get client profile from admin panel\n        let response;\n        try {\n            response = await fetch(`${process.env.ADMIN_API_BASE_URL}/clients?search=${encodeURIComponent(userEmail)}&limit=1`);\n            if (!response.ok) {\n                throw new Error(`Admin API error: ${response.status}`);\n            }\n        } catch (error) {\n            // If admin panel is not running, return 404 (no wallet found)\n            console.log('Admin panel not available, returning no wallet');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        const data = await response.json();\n        const client = data.clients?.[0];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address: client.walletAddress,\n            verified: !!client.walletVerifiedAt,\n            verifiedAt: client.walletVerifiedAt\n        });\n    } catch (error) {\n        console.error('Error fetching wallet status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch wallet status'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/wallet/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/viem","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fwallet%2Froute&page=%2Fapi%2Fclient%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fwallet%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();