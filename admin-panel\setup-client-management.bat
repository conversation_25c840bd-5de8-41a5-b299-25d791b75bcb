@echo off
echo ========================================
echo Client Management System Setup
echo ========================================
echo.

echo Step 1: Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)
echo Dependencies installed successfully!
echo.

echo Step 2: Setting up environment variables...
if not exist .env.local (
    if exist .env.example (
        copy .env.example .env.local
        echo Environment file created from example.
        echo Please edit .env.local with your database configuration.
    ) else (
        echo Warning: .env.example not found. Please create .env.local manually.
    )
) else (
    echo .env.local already exists.
)
echo.

echo Step 3: Generating Prisma client...
call npm run db:generate
if %errorlevel% neq 0 (
    echo Error: Failed to generate Prisma client
    echo Please ensure your DATABASE_URL is configured in .env.local
    pause
    exit /b 1
)
echo Prisma client generated successfully!
echo.

echo Step 4: Setting up database schema...
call npm run db:push
if %errorlevel% neq 0 (
    echo Error: Failed to push database schema
    echo Please ensure:
    echo 1. PostgreSQL is running
    echo 2. Database exists
    echo 3. DATABASE_URL is correct in .env.local
    pause
    exit /b 1
)
echo Database schema created successfully!
echo.

echo Step 5: Seeding database with sample data...
set /p seed_choice="Do you want to add sample client data? (y/n): "
if /i "%seed_choice%"=="y" (
    call npm run db:seed
    if %errorlevel% neq 0 (
        echo Warning: Failed to seed database. This is optional.
    ) else (
        echo Sample data added successfully!
    )
)
echo.

echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env.local with your configuration
echo 2. Start the development server: npm run dev
echo 3. Navigate to http://localhost:3000/clients
echo.
echo Database management commands:
echo - npm run db:studio  (Open database browser)
echo - npm run db:push   (Update schema)
echo - npm run db:seed   (Add sample data)
echo.
pause
