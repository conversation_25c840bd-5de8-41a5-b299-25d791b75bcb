const { execSync } = require('child_process');
const path = require('path');

async function main() {
    console.log("🧪 Running ERC-3643 System Tests...");
    console.log("===================================");

    const testFiles = [
        'test/ERC3643-Integration.test.js',
        // Add more test files here as needed
    ];

    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;

    for (const testFile of testFiles) {
        console.log(`\n📋 Running ${testFile}...`);
        console.log("=".repeat(50));

        try {
            const output = execSync(`npx hardhat test ${testFile}`, {
                encoding: 'utf8',
                cwd: process.cwd()
            });

            console.log(output);

            // Parse test results (basic parsing)
            const lines = output.split('\n');
            for (const line of lines) {
                if (line.includes('passing')) {
                    const match = line.match(/(\d+) passing/);
                    if (match) {
                        const passed = parseInt(match[1]);
                        passedTests += passed;
                        totalTests += passed;
                    }
                }
                if (line.includes('failing')) {
                    const match = line.match(/(\d+) failing/);
                    if (match) {
                        const failed = parseInt(match[1]);
                        failedTests += failed;
                        totalTests += failed;
                    }
                }
            }

        } catch (error) {
            console.error(`❌ Test failed: ${error.message}`);
            failedTests++;
            totalTests++;
        }
    }

    // Summary
    console.log("\n🎉 Test Summary");
    console.log("===============");
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success Rate: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`);

    if (failedTests === 0) {
        console.log("\n🎊 All tests passed! ERC-3643 system is working correctly.");
    } else {
        console.log("\n⚠️ Some tests failed. Please review the output above.");
    }

    // Additional system checks
    console.log("\n🔍 System Health Checks");
    console.log("=======================");

    try {
        // Check if contracts compile
        console.log("📦 Compiling contracts...");
        execSync('npx hardhat compile', { encoding: 'utf8' });
        console.log("✅ Contracts compile successfully");

        // Check contract sizes
        console.log("📏 Checking contract sizes...");
        const sizeOutput = execSync('npx hardhat size-contracts', { encoding: 'utf8' });
        console.log(sizeOutput);

    } catch (error) {
        console.log("❌ System check failed:", error.message);
    }

    // Deployment readiness check
    console.log("\n🚀 Deployment Readiness");
    console.log("=======================");

    const requiredEnvVars = [
        'POLYGON_AMOY_RPC_URL',
        'CONTRACT_ADMIN_PRIVATE_KEY'
    ];

    let deploymentReady = true;
    for (const envVar of requiredEnvVars) {
        if (process.env[envVar]) {
            console.log(`✅ ${envVar}: Set`);
        } else {
            console.log(`❌ ${envVar}: Missing`);
            deploymentReady = false;
        }
    }

    if (deploymentReady) {
        console.log("✅ System is ready for deployment!");
    } else {
        console.log("⚠️ Missing environment variables. Check .env.local file.");
    }

    // Next steps
    console.log("\n🔄 Next Steps");
    console.log("=============");
    
    if (failedTests === 0) {
        console.log("1. Deploy the system using:");
        console.log("   npx hardhat run scripts/03-deploy-claims.js --network amoy");
        console.log("   npx hardhat run scripts/04-deploy-identity-compliance.js --network amoy");
        console.log("   npx hardhat run scripts/06-deploy-token-erc3643.js --network amoy");
        console.log("");
        console.log("2. Migrate existing data:");
        console.log("   npx hardhat run scripts/05-migrate-whitelist-data.js --network amoy");
        console.log("");
        console.log("3. Update admin panel to use new contracts");
        console.log("");
        console.log("4. Test with real transactions on testnet");
    } else {
        console.log("1. Fix failing tests first");
        console.log("2. Re-run tests to ensure everything works");
        console.log("3. Then proceed with deployment");
    }

    return {
        totalTests,
        passedTests,
        failedTests,
        successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
        deploymentReady
    };
}

// Handle script execution
if (require.main === module) {
    main()
        .then((result) => {
            if (result.failedTests === 0) {
                process.exit(0);
            } else {
                process.exit(1);
            }
        })
        .catch((error) => {
            console.error("❌ Test runner failed:", error);
            process.exit(1);
        });
}

module.exports = main;
