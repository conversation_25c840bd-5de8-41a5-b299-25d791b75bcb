// <PERSON>ript to clean the OpenZeppelin storage
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("Cleaning OpenZeppelin storage...");

  // Find the .openzeppelin directory
  const openzeppelinDir = path.join(__dirname, "../.openzeppelin");
  
  if (!fs.existsSync(openzeppelinDir)) {
    console.log(".openzeppelin directory not found");
    return;
  }

  const networkId = "80002"; // Amoy testnet
  const storagePath = path.join(openzeppelinDir, `unknown-${networkId}.json`);
  
  if (fs.existsSync(storagePath)) {
    console.log(`Found storage file: unknown-${networkId}.json`);
    
    // Create a backup
    const backupPath = `${storagePath}.backup-${Date.now()}`;
    fs.copyFileSync(storagePath, backupPath);
    console.log(`Created backup at: ${backupPath}`);
    
    // Create a clean storage file with just the proxy record
    const cleanStorage = {
      "manifestVersion": "3.2",
      "proxies": [
        {
          "address": "0x324AB4526d55630bf8AfA86F479697B23c6792A4",
          "kind": "uups"
        }
      ],
      "impls": {}
    };
    
    // Write the clean storage
    fs.writeFileSync(storagePath, JSON.stringify(cleanStorage, null, 2));
    console.log("Created clean storage file without implementation references");
  } else {
    console.log(`Storage file not found: unknown-${networkId}.json`);
  }
  
  console.log("Cleaning completed!");
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 