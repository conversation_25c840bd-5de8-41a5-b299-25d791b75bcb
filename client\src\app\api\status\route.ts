import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Test connection to admin panel
    const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
    let adminPanelStatus = 'disconnected';
    let adminPanelError = null;

    try {
      const response = await fetch(`${adminApiUrl}/status`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        adminPanelStatus = 'connected';
      } else {
        adminPanelStatus = 'error';
        adminPanelError = `HTTP ${response.status}`;
      }
    } catch (error) {
      adminPanelStatus = 'error';
      adminPanelError = error instanceof Error ? error.message : 'Unknown error';
    }

    // Get environment info
    const envInfo = {
      nodeEnv: process.env.NODE_ENV,
      adminApiBaseUrl: process.env.ADMIN_API_BASE_URL,
      auth0BaseUrl: process.env.AUTH0_BASE_URL ? 'configured' : 'not configured',
      auth0ClientId: process.env.AUTH0_CLIENT_ID ? 'configured' : 'not configured',
    };

    const status = {
      status: 'running',
      timestamp: new Date().toISOString(),
      adminPanel: {
        status: adminPanelStatus,
        url: adminApiUrl,
        error: adminPanelError,
      },
      environment: envInfo,
    };

    return NextResponse.json(status);
  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
