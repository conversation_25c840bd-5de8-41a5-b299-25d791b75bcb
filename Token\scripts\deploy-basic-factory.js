require("dotenv").config();
const hre = require("hardhat");

async function main() {
  try {
    console.log("Deploying SecurityTokenFactory with KYC support...");
    
    // Get signer info
    const [deployer] = await hre.ethers.getSigners();
    console.log(`Deployer address: ${deployer.address}`);
    
    // Deploy the contract with a more step-by-step approach
    console.log("Deploying contract...");
    
    // First, create the contract factory
    const SecurityTokenFactory = await hre.ethers.getContractFactory("SecurityTokenFactory");
    console.log("Contract factory created successfully");
    
    // Deploy with retry mechanism
    let factory;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        console.log(`Deployment attempt ${attempts + 1}/${maxAttempts}...`);
        
        // Deploy the factory contract
        factory = await SecurityTokenFactory.deploy(deployer.address);
        console.log("Deployment transaction sent:", factory.target);
        
        // Wait for deployment to complete (using waitForDeployment instead of deployed)
        console.log("Waiting for deployment confirmation...");
        await factory.waitForDeployment();
        const deployedAddress = await factory.getAddress();
        
        console.log("Factory deployed successfully");
        console.log(`SecurityTokenFactory deployed to: ${deployedAddress}`);
        
        // Store for later use
        factory.address = deployedAddress;
        break;
      } catch (err) {
        attempts++;
        console.error(`Deployment attempt ${attempts} failed:`, err.message);
        
        if (attempts >= maxAttempts) {
          throw new Error(`Failed to deploy after ${maxAttempts} attempts`);
        }
        
        // Wait before retrying
        console.log("Waiting 10 seconds before retrying...");
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    
    console.log(`\nDeployment complete! Use this address in your admin-panel configuration.`);
    console.log(`FACTORY_ADDRESS=${factory.address}`);
    
  } catch (error) {
    console.error("Error deploying factory:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  }); 