const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 Upgrading Factory Implementations with Advanced Transfer Controls...");

  const [deployer] = await ethers.getSigners();
  console.log("Upgrading with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Factory address from config - check network
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);

  let factoryAddress;
  if (network.chainId === 80002n) { // Amoy testnet
    factoryAddress = "******************************************";
  } else {
    console.log("⚠️ Unknown network, using default factory address");
    factoryAddress = "******************************************";
  }

  console.log("Factory address:", factoryAddress);

  // Get factory contract
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  // Deploy new implementations with advanced transfer controls
  console.log("\n🚀 Deploying new implementations...");

  // Deploy new SecurityToken implementation
  console.log("📄 Deploying new SecurityToken implementation...");
  const SecurityToken = await ethers.getContractFactory("SecurityToken");
  const newTokenImplementation = await SecurityToken.deploy();
  await newTokenImplementation.waitForDeployment();
  const newTokenImplAddress = await newTokenImplementation.getAddress();
  console.log("✅ New SecurityToken implementation:", newTokenImplAddress);

  // Deploy new Whitelist implementation
  console.log("📄 Deploying new Whitelist implementation...");
  const Whitelist = await ethers.getContractFactory("Whitelist");
  const newWhitelistImplementation = await Whitelist.deploy();
  await newWhitelistImplementation.waitForDeployment();
  const newWhitelistImplAddress = await newWhitelistImplementation.getAddress();
  console.log("✅ New Whitelist implementation:", newWhitelistImplAddress);

  // Deploy new WhitelistWithKYC implementation
  console.log("📄 Deploying new WhitelistWithKYC implementation...");
  const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
  const newWhitelistWithKYCImplementation = await WhitelistWithKYC.deploy();
  await newWhitelistWithKYCImplementation.waitForDeployment();
  const newWhitelistWithKYCImplAddress = await newWhitelistWithKYCImplementation.getAddress();
  console.log("✅ New WhitelistWithKYC implementation:", newWhitelistWithKYCImplAddress);

  // Get current implementations for comparison
  console.log("\n🔍 Current implementations:");
  const currentTokenImpl = await factory.securityTokenImplementation();
  const currentWhitelistImpl = await factory.whitelistImplementation();
  const currentWhitelistWithKYCImpl = await factory.whitelistWithKYCImplementation();

  console.log("Current SecurityToken:", currentTokenImpl);
  console.log("Current Whitelist:", currentWhitelistImpl);
  console.log("Current WhitelistWithKYC:", currentWhitelistWithKYCImpl);

  // Update factory implementations
  console.log("\n🔄 Updating factory implementations...");
  const updateTx = await factory.updateImplementations(
    newTokenImplAddress,
    newWhitelistImplAddress,
    newWhitelistWithKYCImplAddress
  );

  console.log("Transaction hash:", updateTx.hash);
  console.log("⏳ Waiting for confirmation...");
  await updateTx.wait();
  console.log("✅ Factory implementations updated!");

  // Verify the update
  console.log("\n✅ Verifying update...");
  const updatedTokenImpl = await factory.securityTokenImplementation();
  const updatedWhitelistImpl = await factory.whitelistImplementation();
  const updatedWhitelistWithKYCImpl = await factory.whitelistWithKYCImplementation();

  console.log("Updated SecurityToken:", updatedTokenImpl);
  console.log("Updated Whitelist:", updatedWhitelistImpl);
  console.log("Updated WhitelistWithKYC:", updatedWhitelistWithKYCImpl);

  // Verify the new SecurityToken implementation has advanced features
  console.log("\n🔍 Verifying advanced transfer controls in new implementation...");
  try {
    const newTokenContract = SecurityToken.attach(newTokenImplAddress);

    // Try to call the advanced transfer control functions
    // Note: These will revert because it's not initialized, but we just want to check the functions exist
    try {
      await newTokenContract.conditionalTransfersEnabled.staticCall();
      console.log("✅ conditionalTransfersEnabled function exists");
    } catch (error) {
      if (error.message.includes("function selector was not recognized")) {
        console.log("❌ conditionalTransfersEnabled function missing");
      } else {
        console.log("✅ conditionalTransfersEnabled function exists (expected revert)");
      }
    }

    try {
      await newTokenContract.transferWhitelistEnabled.staticCall();
      console.log("✅ transferWhitelistEnabled function exists");
    } catch (error) {
      if (error.message.includes("function selector was not recognized")) {
        console.log("❌ transferWhitelistEnabled function missing");
      } else {
        console.log("✅ transferWhitelistEnabled function exists (expected revert)");
      }
    }

    try {
      await newTokenContract.transferFeesEnabled.staticCall();
      console.log("✅ transferFeesEnabled function exists");
    } catch (error) {
      if (error.message.includes("function selector was not recognized")) {
        console.log("❌ transferFeesEnabled function missing");
      } else {
        console.log("✅ transferFeesEnabled function exists (expected revert)");
      }
    }

  } catch (error) {
    console.log("⚠️ Could not verify advanced features:", error.message);
  }

  // Test deployment with new implementation
  console.log("\n🧪 Testing deployment with new implementation...");
  try {
    const testTx = await factory.deploySecurityToken(
      "Test Advanced Token",
      "TAT",
      0, // decimals
      ethers.parseUnits("1000000", 0), // maxSupply
      deployer.address, // admin
      "50 USD", // price
      "Tier 1: 5%", // bonus tiers
      "Test token with advanced transfer controls", // details
      "" // image URL
    );

    console.log("Test deployment transaction:", testTx.hash);
    const receipt = await testTx.wait();

    // Find the TokenDeployed event
    const tokenDeployedEvent = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (tokenDeployedEvent) {
      const parsed = factory.interface.parseLog(tokenDeployedEvent);
      const newTokenAddress = parsed.args.tokenAddress;
      console.log("✅ Test token deployed successfully:", newTokenAddress);

      // Verify the new token has advanced features
      const testToken = SecurityToken.attach(newTokenAddress);
      try {
        const conditionalEnabled = await testToken.conditionalTransfersEnabled();
        const whitelistEnabled = await testToken.transferWhitelistEnabled();
        const feesEnabled = await testToken.transferFeesEnabled();

        console.log("🎉 Advanced features verified:");
        console.log("   Conditional Transfers:", conditionalEnabled ? "✅ Available" : "❌ Available");
        console.log("   Transfer Whitelisting:", whitelistEnabled ? "✅ Available" : "❌ Available");
        console.log("   Transfer Fees:", feesEnabled ? "✅ Available" : "❌ Available");

        console.log("\n🌐 Test this token in admin panel:");
        console.log(`http://localhost:7788/transfer-controls?token=${newTokenAddress}`);

      } catch (error) {
        console.log("❌ Advanced features not available in deployed token:", error.message);
      }
    } else {
      console.log("⚠️ Could not find TokenDeployed event");
    }

  } catch (error) {
    console.log("❌ Test deployment failed:", error.message);
  }

  console.log("\n📊 UPGRADE SUMMARY");
  console.log("==================");
  console.log("Factory Address:", factoryAddress);
  console.log("New SecurityToken Implementation:", newTokenImplAddress);
  console.log("New Whitelist Implementation:", newWhitelistImplAddress);
  console.log("New WhitelistWithKYC Implementation:", newWhitelistWithKYCImplAddress);
  console.log("");
  console.log("🎉 All new tokens deployed through the factory will now have advanced transfer controls!");
  console.log("🌐 Admin Panel: http://localhost:7788/create-token");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Upgrade failed:", error);
    process.exit(1);
  });
