/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tokens/route";
exports.ids = ["app/api/tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tokens/route.ts */ \"(rsc)/./src/app/api/tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tokens/route\",\n        pathname: \"/api/tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tokens/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/tokens/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\n\n// Helper function to check if user has required claims for a token\nasync function checkUserClaims(walletAddress, requiredClaims) {\n    const claimResults = {};\n    if (!walletAddress || !requiredClaims.length) {\n        return claimResults;\n    }\n    try {\n        const claimRegistryAddress = \"******************************************\";\n        if (!claimRegistryAddress) {\n            console.warn('Claim registry not configured');\n            return claimResults;\n        }\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(\"https://rpc-amoy.polygon.technology\");\n        const claimRegistryABI = [\n            \"function hasValidClaim(address subject, uint256 claimType) external view returns (bool)\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryABI, provider);\n        // Check each required claim (now using custom claim type IDs)\n        for (const claimType of requiredClaims){\n            const claimTypeId = parseInt(claimType.trim());\n            if (!isNaN(claimTypeId) && claimTypeId > 0) {\n                try {\n                    const hasValidClaim = await claimRegistry.hasValidClaim(walletAddress, claimTypeId);\n                    claimResults[claimType] = hasValidClaim;\n                    console.log(`🔍 Claim check for ${walletAddress}: Claim Type ${claimTypeId} = ${hasValidClaim ? '✅ VALID' : '❌ INVALID'}`);\n                } catch (error) {\n                    console.warn(`Could not check claim type ${claimTypeId}:`, error);\n                    claimResults[claimType] = false;\n                }\n            } else {\n                console.warn(`Invalid claim type ID: ${claimType}`);\n                claimResults[claimType] = false;\n            }\n        }\n        return claimResults;\n    } catch (error) {\n        console.error('Error checking user claims:', error);\n        return claimResults;\n    }\n}\nasync function GET(request) {\n    try {\n        // Check for test wallet address in query params (for testing purposes)\n        const { searchParams } = new URL(request.url);\n        const testWalletAddress = searchParams.get('testWallet');\n        // Get user session to check wallet address\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        let userWalletAddress = testWalletAddress; // Use test wallet if provided\n        // If no test wallet and user is logged in, try to get their wallet address\n        if (!userWalletAddress && session?.user?.email) {\n            try {\n                const adminPanelUrl = process.env.ADMIN_PANEL_URL;\n                const clientResponse = await fetch(`${adminPanelUrl}/api/clients?search=${encodeURIComponent(session.user.email)}&limit=1`);\n                if (clientResponse.ok) {\n                    const clientData = await clientResponse.json();\n                    const client = clientData.clients?.[0];\n                    userWalletAddress = client?.walletAddress || null;\n                }\n            } catch (error) {\n                console.warn('Could not fetch user wallet address:', error);\n            }\n        }\n        // Fetch tokens from the admin panel API\n        const adminPanelUrl = process.env.ADMIN_PANEL_URL;\n        const response = await fetch(`${adminPanelUrl}/api/tokens?source=database&t=${Date.now()}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n                'Cache-Control': 'no-cache, no-store, must-revalidate',\n                'Pragma': 'no-cache',\n                'Expires': '0'\n            },\n            // Add cache control to ensure fresh data\n            cache: 'no-store'\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to fetch tokens from admin panel: ${response.status}`);\n        }\n        const tokens = await response.json();\n        // Get whitelist status for all tokens if user has a wallet\n        let whitelistStatuses = {};\n        if (userWalletAddress && tokens.length > 0) {\n            try {\n                const tokenAddresses = tokens.map((token)=>token.address);\n                const whitelistResponse = await fetch(`${adminPanelUrl}/api/whitelist/check?t=${Date.now()}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Cache-Control': 'no-cache, no-store, must-revalidate'\n                    },\n                    body: JSON.stringify({\n                        walletAddress: userWalletAddress,\n                        tokenAddresses: tokenAddresses\n                    })\n                });\n                if (whitelistResponse.ok) {\n                    const whitelistData = await whitelistResponse.json();\n                    whitelistData.tokens?.forEach((tokenStatus)=>{\n                        whitelistStatuses[tokenStatus.tokenAddress.toLowerCase()] = tokenStatus.isWhitelisted;\n                    });\n                }\n            } catch (error) {\n                console.warn('Could not fetch whitelist statuses:', error);\n            }\n        }\n        // Check user claims for all tokens if user has a wallet\n        let userClaimsMap = {};\n        let qualifiedTokens = [];\n        if (userWalletAddress && tokens.length > 0) {\n            console.log(`🔍 Checking qualification for wallet: ${userWalletAddress}`);\n            for (const token of tokens){\n                if (token.selectedClaims) {\n                    const requiredClaims = typeof token.selectedClaims === 'string' ? token.selectedClaims.split(',').map((c)=>c.trim()) : token.selectedClaims;\n                    const userClaims = await checkUserClaims(userWalletAddress, requiredClaims);\n                    userClaimsMap[token.address.toLowerCase()] = userClaims;\n                    // Check if user has ALL required claims\n                    const hasAllRequiredClaims = requiredClaims.every((claim)=>userClaims[claim] === true);\n                    if (hasAllRequiredClaims) {\n                        qualifiedTokens.push(token.address.toLowerCase());\n                        console.log(`✅ User qualified for ${token.symbol}`);\n                    } else {\n                        console.log(`❌ User NOT qualified for ${token.symbol}`);\n                    }\n                } else {\n                    // No claims required, user is qualified\n                    qualifiedTokens.push(token.address.toLowerCase());\n                    console.log(`✅ User qualified for ${token.symbol} - no requirements`);\n                }\n            }\n            console.log(`🎯 Showing ${qualifiedTokens.length} out of ${tokens.length} tokens to user`);\n        }\n        // Transform tokens and include qualification status (show ALL tokens, not just qualified ones)\n        const transformedTokens = tokens.map((token)=>{\n            // Extract numeric price and currency from tokenPrice field (e.g., \"1.5 ETH\" -> price: \"1.5\", currency: \"ETH\")\n            let numericPrice = '0';\n            let extractedCurrency = token.currency || 'USD';\n            if (token.tokenPrice) {\n                // Try to extract price and currency from tokenPrice field\n                const priceWithCurrencyMatch = token.tokenPrice.match(/([\\d.]+)\\s*([A-Z]{3,4})/i);\n                if (priceWithCurrencyMatch) {\n                    numericPrice = priceWithCurrencyMatch[1];\n                    extractedCurrency = priceWithCurrencyMatch[2].toUpperCase();\n                } else {\n                    // Fallback to just extracting the number\n                    const priceMatch = token.tokenPrice.match(/[\\d.]+/);\n                    numericPrice = priceMatch ? priceMatch[0] : '0';\n                }\n            }\n            const tokenAddress = token.address?.toLowerCase();\n            // Check if user has the required claims for this token\n            const hasRequiredClaims = qualifiedTokens.includes(tokenAddress);\n            // For now, we'll consider a user \"qualified\" only if they have explicitly gone through\n            // the qualification process for this specific token. Since we don't have that tracking yet,\n            // we'll use a simple heuristic: only consider them qualified for Claim_001 (the original token)\n            // All other tokens require explicit qualification, even if user has the claims\n            const isOriginalToken = token.name === 'Claim_001' || token.symbol === 'Claim_001';\n            const isQualifiedForToken = hasRequiredClaims && userWalletAddress && isOriginalToken;\n            // TODO: Implement proper token-specific qualification tracking in database\n            // This should track when a user completes qualification for each specific token\n            return {\n                id: token.id,\n                name: token.name,\n                symbol: token.symbol,\n                address: token.address,\n                totalSupply: token.totalSupply || '0',\n                maxSupply: token.maxSupply || '0',\n                price: numericPrice,\n                currency: extractedCurrency,\n                category: token.tokenType || 'Unknown',\n                description: token.deploymentNotes || '',\n                imageUrl: token.tokenImageUrl || null,\n                network: token.network || 'amoy',\n                decimals: token.decimals || 0,\n                version: '1.0.0',\n                bonusTiers: token.bonusTiers || '',\n                whitelistAddress: token.whitelistAddress || '',\n                createdAt: token.createdAt || new Date().toISOString(),\n                isWhitelisted: whitelistStatuses[tokenAddress] || false,\n                // Add qualification status\n                hasRequiredClaims: hasRequiredClaims,\n                needsQualification: !isQualifiedForToken && token.selectedClaims,\n                requiredClaims: token.selectedClaims ? typeof token.selectedClaims === 'string' ? token.selectedClaims.split(',').map((c)=>c.trim()) : token.selectedClaims : [],\n                userClaims: userClaimsMap[tokenAddress] || {}\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(transformedTokens);\n    } catch (error) {\n        console.error('Error fetching tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch tokens'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tokens/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();