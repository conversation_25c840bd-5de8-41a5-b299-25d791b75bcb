const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Testing deployment with old method...");

  const [deployer] = await ethers.getSigners();
  console.log("Account:", deployer.address);

  // Factory address
  const factoryAddress = "******************************************";
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  console.log("Factory address:", factoryAddress);

  // Try deploying with the old method (without agreement URL)
  console.log("\n🚀 Testing old deployment method...");
  
  const testSymbol = "OLD" + Date.now().toString().slice(-4);
  
  try {
    const deployTx = await factory.deploySecurityToken(
      "Old Method Test Token",
      testSymbol,
      0,
      ethers.parseUnits("1000", 0),
      deployer.address,
      "10 USD",
      "Tier 1: 5%",
      "Test token with old method",
      ""
    );

    console.log("Deployment transaction:", deployTx.hash);
    const receipt = await deployTx.wait();
    console.log("✅ Old method deployment successful");

    // Find the token address
    const tokenDeployedEvent = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (tokenDeployedEvent) {
      const parsed = factory.interface.parseLog(tokenDeployedEvent);
      const tokenAddress = parsed.args.tokenAddress;
      console.log("Token deployed at:", tokenAddress);

      // Test if the token has agreement functions
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token = SecurityToken.attach(tokenAddress);
      
      try {
        const agreementUrl = await token.getAgreementUrl();
        console.log("✅ Agreement URL (should be empty):", agreementUrl);
        
        const hasAccepted = await token.hasAcceptedAgreement(deployer.address);
        console.log("✅ hasAcceptedAgreement:", hasAccepted);
        
        console.log("🎉 Agreement functions are available in deployed token!");
        
      } catch (error) {
        console.log("❌ Agreement functions not available:", error.message);
      }
    }

  } catch (error) {
    console.log("❌ Old method deployment failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
