'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useApiClient, type ClientProfile } from '@/lib/api-client';

interface QualificationFormProps {
  onComplete: () => void;
  existingProfile?: ClientProfile | null;
}

export function QualificationForm({ onComplete, existingProfile }: QualificationFormProps) {
  // Helper function to format date for input
  const formatDateForInput = (dateString: string | null | undefined) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toISOString().split('T')[0];
    } catch {
      return '';
    }
  };

  // Check if form can be edited
  const canEdit = !existingProfile?.kycStatus ||
                  existingProfile.kycStatus === 'REJECTED' ||
                  existingProfile.kycStatus === 'EXPIRED';

  // Initialize form data with empty values first
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    gender: '',
    nationality: '',
    birthday: '',
    birthPlace: '',
    identificationType: '',
    passportNumber: '',
    idCardNumber: '',
    documentExpiration: '',
    phoneNumber: '',
    email: '',
    occupation: '',
    sectorOfActivity: '',
    pepStatus: existingProfile?.pepStatus || 'NOT_PEP',
    pepDetails: existingProfile?.pepDetails || '',
    street: existingProfile?.street || '',
    buildingNumber: existingProfile?.buildingNumber || '',
    city: existingProfile?.city || '',
    state: existingProfile?.state || '',
    country: existingProfile?.country || '',
    zipCode: existingProfile?.zipCode || '',
    sourceOfWealth: existingProfile?.sourceOfWealth || '',
    bankAccountNumber: existingProfile?.bankAccountNumber || '',
    sourceOfFunds: existingProfile?.sourceOfFunds || '',
    taxIdentificationNumber: existingProfile?.taxIdentificationNumber || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const apiClient = useApiClient();
  const queryClient = useQueryClient();

  // Update form data when existingProfile changes
  useEffect(() => {
    if (existingProfile) {
      console.log('Updating form data with existing profile:', existingProfile);
      setFormData({
        firstName: existingProfile.firstName || '',
        lastName: existingProfile.lastName || '',
        gender: existingProfile.gender || '',
        nationality: existingProfile.nationality || '',
        birthday: formatDateForInput(existingProfile.birthday),
        birthPlace: existingProfile.birthPlace || '',
        identificationType: existingProfile.identificationType || '',
        passportNumber: existingProfile.passportNumber || '',
        idCardNumber: existingProfile.idCardNumber || '',
        documentExpiration: formatDateForInput(existingProfile.documentExpiration),
        phoneNumber: existingProfile.phoneNumber || '',
        email: existingProfile.email || '',
        occupation: existingProfile.occupation || '',
        sectorOfActivity: existingProfile.sectorOfActivity || '',
        pepStatus: existingProfile.pepStatus || 'NOT_PEP',
        pepDetails: existingProfile.pepDetails || '',
        street: existingProfile.street || '',
        buildingNumber: existingProfile.buildingNumber || '',
        city: existingProfile.city || '',
        state: existingProfile.state || '',
        country: existingProfile.country || '',
        zipCode: existingProfile.zipCode || '',
        sourceOfWealth: existingProfile.sourceOfWealth || '',
        bankAccountNumber: existingProfile.bankAccountNumber || '',
        sourceOfFunds: existingProfile.sourceOfFunds || '',
        taxIdentificationNumber: existingProfile.taxIdentificationNumber || '',
      });
    }
  }, [existingProfile]);

  const submitQualificationMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      if (existingProfile) {
        return apiClient.updateClientProfile(data);
      } else {
        return apiClient.createClientProfile(data);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['client-profile'] });
      onComplete();
    },
    onError: (error: any) => {
      setErrors({ general: error.message || 'Failed to submit qualification application' });
    },
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required field validations
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.nationality.trim()) newErrors.nationality = 'Nationality is required';
    if (!formData.birthday) newErrors.birthday = 'Birthday is required';
    if (!formData.birthPlace.trim()) newErrors.birthPlace = 'Birth place is required';
    if (!formData.identificationType) newErrors.identificationType = 'Identification type is required';
    if (!formData.documentExpiration) newErrors.documentExpiration = 'Document expiration is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.occupation.trim()) newErrors.occupation = 'Occupation is required';
    if (!formData.sectorOfActivity.trim()) newErrors.sectorOfActivity = 'Sector of activity is required';
    if (!formData.street.trim()) newErrors.street = 'Street is required';
    if (!formData.buildingNumber.trim()) newErrors.buildingNumber = 'Building number is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.country.trim()) newErrors.country = 'Country is required';
    if (!formData.zipCode.trim()) newErrors.zipCode = 'Zip code is required';
    if (!formData.sourceOfWealth.trim()) newErrors.sourceOfWealth = 'Source of wealth is required';
    if (!formData.bankAccountNumber.trim()) newErrors.bankAccountNumber = 'Bank account number is required';
    if (!formData.sourceOfFunds.trim()) newErrors.sourceOfFunds = 'Source of funds is required';
    if (!formData.taxIdentificationNumber.trim()) newErrors.taxIdentificationNumber = 'Tax identification number is required';

    // Validate birthday (must be 18-120 years old)
    if (formData.birthday) {
      const birthDate = new Date(formData.birthday);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 18 || age > 120) {
        newErrors.birthday = 'Must be between 18 and 120 years old';
      }
    }

    // Validate document expiration (must be in the future)
    if (formData.documentExpiration) {
      const expirationDate = new Date(formData.documentExpiration);
      const today = new Date();
      if (expirationDate <= today) {
        newErrors.documentExpiration = 'Document expiration must be in the future';
      }
    }

    // Validate identification document number based on type
    if (formData.identificationType === 'PASSPORT' && !formData.passportNumber.trim()) {
      newErrors.passportNumber = 'Passport number is required';
    }
    if ((formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE') && !formData.idCardNumber.trim()) {
      newErrors.idCardNumber = 'ID card number is required';
    }

    return newErrors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors({});
    submitQualificationMutation.mutate(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Get status display info
  const getStatusInfo = () => {
    if (!existingProfile?.kycStatus) return null;

    const statusConfig = {
      PENDING: { color: 'yellow', text: 'Pending Review', description: 'Your qualification application is waiting to be reviewed.' },
      IN_REVIEW: { color: 'blue', text: 'Under Review', description: 'Your qualification application is currently being reviewed by our team.' },
      APPROVED: { color: 'green', text: 'Approved', description: 'Your qualification application has been approved.' },
      REJECTED: { color: 'red', text: 'Rejected', description: 'Your qualification application was rejected. Please review the feedback and resubmit.' },
      EXPIRED: { color: 'gray', text: 'Expired', description: 'Your qualification approval has expired. Please resubmit your application.' },
    };

    return statusConfig[existingProfile.kycStatus as keyof typeof statusConfig];
  };

  const statusInfo = getStatusInfo();

  // Helper function to get input styling
  const getInputClassName = (hasError = false) => {
    const baseClasses = "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500";
    if (!canEdit) {
      return `${baseClasses} bg-gray-50 border-gray-200 cursor-not-allowed`;
    }
    return `${baseClasses} ${hasError ? 'border-red-500' : 'border-gray-300'}`;
  };

  return (
    <div className="space-y-6">
      {/* Status Display */}
      {statusInfo && (
        <div className={`p-4 rounded-lg border ${
          statusInfo.color === 'green' ? 'bg-green-50 border-green-200' :
          statusInfo.color === 'red' ? 'bg-red-50 border-red-200' :
          statusInfo.color === 'blue' ? 'bg-blue-50 border-blue-200' :
          statusInfo.color === 'yellow' ? 'bg-yellow-50 border-yellow-200' :
          'bg-gray-50 border-gray-200'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                statusInfo.color === 'green' ? 'text-green-600 bg-green-100' :
                statusInfo.color === 'red' ? 'text-red-600 bg-red-100' :
                statusInfo.color === 'blue' ? 'text-blue-600 bg-blue-100' :
                statusInfo.color === 'yellow' ? 'text-yellow-600 bg-yellow-100' :
                'text-gray-600 bg-gray-100'
              }`}>
                {statusInfo.text}
              </span>
              <span className="text-sm text-gray-600">{statusInfo.description}</span>
            </div>
            {existingProfile?.kycNotes && (
              <div className="text-sm text-gray-500">
                <strong>Admin Notes:</strong> {existingProfile.kycNotes}
              </div>
            )}
          </div>
          {!canEdit && (
            <div className="mt-2 text-sm text-gray-600">
              <strong>Note:</strong> Your qualification application cannot be edited in its current status.
              {existingProfile?.kycStatus === 'APPROVED' && ' Your application has been approved.'}
              {existingProfile?.kycStatus === 'PENDING' && ' Your application is pending review.'}
              {existingProfile?.kycStatus === 'IN_REVIEW' && ' Your application is currently under review.'}
            </div>
          )}
        </div>
      )}

      {/* Form */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {existingProfile ? 'Qualification Information' : 'Complete Your Qualification'}
          </h3>
          <p className="text-gray-600">
            {canEdit ? 'Please provide your information to qualify for security token investment.' : 'View your qualification application details.'}
          </p>
        </div>

        {errors.general && (
          <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {errors.general}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Personal Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.firstName)}
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.lastName)}
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gender *
                </label>
                <select
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                  required={canEdit}
                  disabled={!canEdit}
                  className={getInputClassName(!!errors.gender)}
                >
                  <option value="">Select Gender</option>
                  <option value="MALE">Male</option>
                  <option value="FEMALE">Female</option>
                  <option value="OTHER">Other</option>
                  <option value="PREFER_NOT_TO_SAY">Prefer not to say</option>
                </select>
                {errors.gender && (
                  <p className="mt-1 text-sm text-red-600">{errors.gender}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nationality *
                </label>
                <input
                  type="text"
                  name="nationality"
                  value={formData.nationality}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.nationality)}
                />
                {errors.nationality && (
                  <p className="mt-1 text-sm text-red-600">{errors.nationality}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Birthday *
                </label>
                <input
                  type="date"
                  name="birthday"
                  value={formData.birthday}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.birthday)}
                />
                {errors.birthday && (
                  <p className="mt-1 text-sm text-red-600">{errors.birthday}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Birth Place *
                </label>
                <input
                  type="text"
                  name="birthPlace"
                  value={formData.birthPlace}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.birthPlace)}
                />
                {errors.birthPlace && (
                  <p className="mt-1 text-sm text-red-600">{errors.birthPlace}</p>
                )}
              </div>
            </div>
          </div>

          {/* Identification & Contact */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Identification & Contact</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Identification Type *
                </label>
                <select
                  name="identificationType"
                  value={formData.identificationType}
                  onChange={handleChange}
                  required={canEdit}
                  disabled={!canEdit}
                  className={getInputClassName(!!errors.identificationType)}
                >
                  <option value="">Select Type</option>
                  <option value="PASSPORT">Passport</option>
                  <option value="ID_CARD">ID Card</option>
                  <option value="DRIVERS_LICENSE">Driver's License</option>
                  <option value="OTHER">Other</option>
                </select>
                {errors.identificationType && (
                  <p className="mt-1 text-sm text-red-600">{errors.identificationType}</p>
                )}
              </div>

              {formData.identificationType === 'PASSPORT' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Passport Number *
                  </label>
                  <input
                    type="text"
                    name="passportNumber"
                    value={formData.passportNumber}
                    onChange={handleChange}
                    required={canEdit}
                    readOnly={!canEdit}
                    className={getInputClassName(!!errors.passportNumber)}
                  />
                  {errors.passportNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.passportNumber}</p>
                  )}
                </div>
              )}

              {(formData.identificationType === 'ID_CARD' || formData.identificationType === 'DRIVERS_LICENSE' || formData.identificationType === 'OTHER') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ID Card Number *
                  </label>
                  <input
                    type="text"
                    name="idCardNumber"
                    value={formData.idCardNumber}
                    onChange={handleChange}
                    required={canEdit}
                    readOnly={!canEdit}
                    className={getInputClassName(!!errors.idCardNumber)}
                  />
                  {errors.idCardNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.idCardNumber}</p>
                  )}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Document Expiration *
                </label>
                <input
                  type="date"
                  name="documentExpiration"
                  value={formData.documentExpiration}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.documentExpiration)}
                />
                {errors.documentExpiration && (
                  <p className="mt-1 text-sm text-red-600">{errors.documentExpiration}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.phoneNumber)}
                />
                {errors.phoneNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.email)}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>
            </div>
          </div>

          {/* Professional & Address */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Professional & Address Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Occupation *
                </label>
                <input
                  type="text"
                  name="occupation"
                  value={formData.occupation}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.occupation)}
                />
                {errors.occupation && (
                  <p className="mt-1 text-sm text-red-600">{errors.occupation}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sector of Activity *
                </label>
                <input
                  type="text"
                  name="sectorOfActivity"
                  value={formData.sectorOfActivity}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.sectorOfActivity)}
                />
                {errors.sectorOfActivity && (
                  <p className="mt-1 text-sm text-red-600">{errors.sectorOfActivity}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Street Address *
                </label>
                <input
                  type="text"
                  name="street"
                  value={formData.street}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.street)}
                />
                {errors.street && (
                  <p className="mt-1 text-sm text-red-600">{errors.street}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Building Number *
                </label>
                <input
                  type="text"
                  name="buildingNumber"
                  value={formData.buildingNumber}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.buildingNumber)}
                />
                {errors.buildingNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.buildingNumber}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City *
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.city)}
                />
                {errors.city && (
                  <p className="mt-1 text-sm text-red-600">{errors.city}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  State/Province
                </label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleChange}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.state)}
                />
                {errors.state && (
                  <p className="mt-1 text-sm text-red-600">{errors.state}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Country *
                </label>
                <input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.country)}
                />
                {errors.country && (
                  <p className="mt-1 text-sm text-red-600">{errors.country}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Zip Code *
                </label>
                <input
                  type="text"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.zipCode)}
                />
                {errors.zipCode && (
                  <p className="mt-1 text-sm text-red-600">{errors.zipCode}</p>
                )}
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Financial Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Source of Wealth *
                </label>
                <input
                  type="text"
                  name="sourceOfWealth"
                  value={formData.sourceOfWealth}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.sourceOfWealth)}
                />
                {errors.sourceOfWealth && (
                  <p className="mt-1 text-sm text-red-600">{errors.sourceOfWealth}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Source of Funds *
                </label>
                <input
                  type="text"
                  name="sourceOfFunds"
                  value={formData.sourceOfFunds}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.sourceOfFunds)}
                />
                {errors.sourceOfFunds && (
                  <p className="mt-1 text-sm text-red-600">{errors.sourceOfFunds}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bank Account Number *
                </label>
                <input
                  type="text"
                  name="bankAccountNumber"
                  value={formData.bankAccountNumber}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.bankAccountNumber)}
                />
                {errors.bankAccountNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.bankAccountNumber}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Identification Number *
                </label>
                <input
                  type="text"
                  name="taxIdentificationNumber"
                  value={formData.taxIdentificationNumber}
                  onChange={handleChange}
                  required={canEdit}
                  readOnly={!canEdit}
                  className={getInputClassName(!!errors.taxIdentificationNumber)}
                />
                {errors.taxIdentificationNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.taxIdentificationNumber}</p>
                )}
              </div>
            </div>
          </div>

          {/* PEP Status */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Political Exposure</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Politically Exposed Person (PEP) Status *
                </label>
                <select
                  name="pepStatus"
                  value={formData.pepStatus}
                  onChange={handleChange}
                  required={canEdit}
                  disabled={!canEdit}
                  className={getInputClassName(!!errors.pepStatus)}
                >
                  <option value="NOT_PEP">Not a PEP</option>
                  <option value="PEP">PEP</option>
                  <option value="FAMILY_MEMBER">Family member of PEP</option>
                  <option value="CLOSE_ASSOCIATE">Close associate of PEP</option>
                </select>
                {errors.pepStatus && (
                  <p className="mt-1 text-sm text-red-600">{errors.pepStatus}</p>
                )}
              </div>

              {formData.pepStatus !== 'NOT_PEP' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PEP Details
                  </label>
                  <textarea
                    name="pepDetails"
                    value={formData.pepDetails}
                    onChange={handleChange}
                    readOnly={!canEdit}
                    rows={3}
                    className={getInputClassName(!!errors.pepDetails)}
                    placeholder="Please provide details about your political exposure..."
                  />
                  {errors.pepDetails && (
                    <p className="mt-1 text-sm text-red-600">{errors.pepDetails}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          {canEdit && (
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={submitQualificationMutation.isPending}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitQualificationMutation.isPending ? 'Submitting...' : 'Complete Qualification'}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
