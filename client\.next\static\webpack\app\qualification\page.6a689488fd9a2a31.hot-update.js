"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                // Try to get more recent data from localStorage first\n                const storageKey = \"qualification_progress_\".concat(tokenAddress);\n                let localProgress = null;\n                try {\n                    const stored = localStorage.getItem(storageKey);\n                    if (stored) {\n                        localProgress = JSON.parse(stored);\n                        console.log('📱 Found localStorage progress:', localProgress);\n                    }\n                } catch (error) {\n                    console.error('Error reading localStorage:', error);\n                }\n                // Use localStorage data if it's more recent, otherwise use API data\n                const progressToUse = localProgress || qualificationProgress;\n                const newStepData = {\n                    country: progressToUse.country || '',\n                    agreementAccepted: progressToUse.agreementAccepted || false,\n                    profileCompleted: progressToUse.profileCompleted || !!profile,\n                    walletConnected: progressToUse.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: progressToUse.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = progressToUse.currentStep || 0;\n                // Allow users to progress through all steps without blocking\n                // Only auto-advance to next incomplete step if current step is completed\n                if (calculatedStep === 0 && newStepData.country) {\n                    calculatedStep = 1; // Move to agreement if country is selected\n                } else if (calculatedStep === 1 && newStepData.agreementAccepted) {\n                    calculatedStep = 2; // Move to profile if agreement is accepted\n                } else if (calculatedStep === 2 && newStepData.profileCompleted) {\n                    calculatedStep = 3; // Move to wallet if profile is completed\n                } else if (calculatedStep === 3 && newStepData.walletConnected) {\n                    calculatedStep = 4; // Move to KYC if wallet is connected\n                } else if (calculatedStep === 4 && newStepData.kycCompleted) {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: progressToUse,\n                    source: localProgress ? 'localStorage' : 'API'\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus,\n        tokenAddress\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            // Ensure we have the correct completion flags based on current state\n            // For country: check if it was explicitly completed OR if there's a country value\n            const countryCompleted = updatedStepData.countryCompleted === true || updatedStepData.country && updatedStepData.country !== '' || stepData.countryCompleted === true || stepData.country && stepData.country !== '';\n            const agreementAccepted = updatedStepData.agreementAccepted === true || stepData.agreementAccepted === true;\n            const profileCompleted = updatedStepData.profileCompleted === true || stepData.profileCompleted === true || !!profile;\n            const walletConnected = updatedStepData.walletConnected === true || stepData.walletConnected === true || !!(profile === null || profile === void 0 ? void 0 : profile.walletAddress);\n            const kycCompleted = updatedStepData.kycCompleted === true || stepData.kycCompleted === true || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED';\n            // Calculate completed steps based on actual step completion flags\n            const stepCompletionFlags = [\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted\n            ];\n            const actualCompletedSteps = stepCompletionFlags.filter(Boolean).length;\n            const progressData = {\n                ...updatedStepData,\n                // Ensure all completion flags are explicitly set\n                countryCompleted,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                // Preserve country value if it exists\n                country: updatedStepData.country || stepData.country || '',\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: actualCompletedSteps\n            };\n            console.log('💾 Saving progress to database:', progressData);\n            // Save to backend database via admin panel API\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully to database:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    // Step navigation functions\n    const canNavigateToStep = (stepIndex)=>{\n        // Users can always navigate to completed steps or the next incomplete step\n        if (stepIndex === 0) return true; // Country selection always available\n        if (stepIndex === 1) return stepData.country !== ''; // Agreement if country selected\n        if (stepIndex === 2) return stepData.agreementAccepted; // Profile if agreement accepted\n        if (stepIndex === 3) return stepData.profileCompleted; // Wallet if profile completed\n        if (stepIndex === 4) return stepData.walletConnected; // KYC if wallet connected\n        if (stepIndex === 5) return stepData.kycCompleted; // Completion if KYC done\n        return false;\n    };\n    const handleStepClick = (stepIndex)=>{\n        if (canNavigateToStep(stepIndex)) {\n            setCurrentStep(stepIndex);\n            // Save the current step navigation\n            saveProgress(stepData, stepIndex);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleStepClick(index),\n                        className: \"p-4 rounded-lg border text-center transition-all duration-200 \".concat(getStepColor(step.status), \" \").concat(canNavigateToStep(index) ? 'cursor-pointer hover:shadow-md hover:scale-105' : 'cursor-not-allowed opacity-60'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            canNavigateToStep(index) && index !== currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 font-medium\",\n                                children: \"Click to navigate\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Submitted!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". Your application is now pending admin review and approval.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-yellow-800\",\n                                                    children: \"Pending Admin Approval\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-yellow-700\",\n                                                    children: \"An administrator will review your qualification and approve you for token investment.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});