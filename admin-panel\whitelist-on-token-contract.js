const { ethers } = require('ethers');

async function whitelistOnTokenContract() {
  console.log('🔧 Whitelisting on Token-Specific Contract');
  console.log('==========================================');

  const YOUR_WALLET = '******************************************';
  const WHITELIST_CONTRACT = '******************************************';

  try {
    // Check environment
    if (!process.env.AMOY_RPC_URL) {
      console.log('❌ AMOY_RPC_URL not found in environment');
      return;
    }

    if (!process.env.CONTRACT_ADMIN_PRIVATE_KEY) {
      console.log('❌ CONTRACT_ADMIN_PRIVATE_KEY not found in environment');
      return;
    }

    console.log(`🔗 RPC URL: ${process.env.AMOY_RPC_URL}`);
    console.log(`👤 Target wallet: ${YOUR_WALLET}`);
    console.log(`📋 Whitelist contract: ${WHITELIST_CONTRACT}`);

    // Connect to blockchain
    const provider = new ethers.JsonRpcProvider(process.env.AMOY_RPC_URL);
    const wallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);
    
    console.log(`🔑 Admin wallet: ${wallet.address}`);

    // Load contract
    const WhitelistABI = require('./src/contracts/Whitelist.json');
    const whitelistContract = new ethers.Contract(
      WHITELIST_CONTRACT,
      WhitelistABI.abi,
      wallet
    );

    // 1. Check current status
    console.log('\n1️⃣ Checking current whitelist status...');
    const isCurrentlyWhitelisted = await whitelistContract.isWhitelisted(YOUR_WALLET);
    console.log(`   Current status: ${isCurrentlyWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED'}`);

    if (isCurrentlyWhitelisted) {
      console.log('✅ Already whitelisted! The issue might be elsewhere.');
      return;
    }

    // 2. Check admin permissions
    console.log('\n2️⃣ Checking admin permissions...');
    try {
      const AGENT_ROLE = await whitelistContract.AGENT_ROLE();
      const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, wallet.address);
      console.log(`   Admin has AGENT_ROLE: ${hasAgentRole ? '✅ YES' : '❌ NO'}`);

      const DEFAULT_ADMIN_ROLE = await whitelistContract.DEFAULT_ADMIN_ROLE();
      const hasDefaultAdminRole = await whitelistContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);
      console.log(`   Admin has DEFAULT_ADMIN_ROLE: ${hasDefaultAdminRole ? '✅ YES' : '❌ NO'}`);

      if (!hasAgentRole && !hasDefaultAdminRole) {
        console.log('❌ Admin wallet does not have permission to whitelist');
        return;
      }
    } catch (error) {
      console.log(`   ⚠️  Could not check roles: ${error.message}`);
    }

    // 3. Add to whitelist
    console.log('\n3️⃣ Adding to whitelist...');
    
    try {
      // Check if already whitelisted (double-check to avoid revert)
      const isWhitelistedBeforeTx = await whitelistContract.isWhitelisted(YOUR_WALLET);
      if (isWhitelistedBeforeTx) {
        console.log('✅ Already whitelisted (double-check)');
        return;
      }

      console.log('   Submitting transaction...');
      const tx = await whitelistContract.addToWhitelist(YOUR_WALLET, {
        gasLimit: BigInt(300000),
        gasPrice: ethers.parseUnits("100", "gwei")
      });

      console.log(`   Transaction hash: ${tx.hash}`);
      console.log('   Waiting for confirmation...');
      
      const receipt = await tx.wait();
      console.log(`   ✅ Transaction confirmed in block: ${receipt.blockNumber}`);
      console.log(`   Gas used: ${receipt.gasUsed.toString()}`);

      // 4. Verify the result
      console.log('\n4️⃣ Verifying whitelist status...');
      const isNowWhitelisted = await whitelistContract.isWhitelisted(YOUR_WALLET);
      console.log(`   New status: ${isNowWhitelisted ? '✅ WHITELISTED' : '❌ STILL NOT WHITELISTED'}`);

      if (isNowWhitelisted) {
        console.log('\n🎉 SUCCESS! You are now whitelisted on this token contract!');
        console.log('\n📋 Next steps:');
        console.log('1. Refresh your browser');
        console.log('2. Try placing an order again');
        console.log('3. The API should now return "isWhitelisted": true');
      } else {
        console.log('\n❌ Transaction succeeded but still not whitelisted. Check contract logic.');
      }

    } catch (error) {
      console.log(`   ❌ Transaction failed: ${error.message}`);
      
      if (error.message.includes('already whitelisted')) {
        console.log('   💡 Address is already whitelisted according to contract');
      } else if (error.message.includes('AccessControl')) {
        console.log('   💡 Access control error - check admin permissions');
      } else {
        console.log('   💡 Check contract state and try again');
      }
    }

  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

whitelistOnTokenContract().catch(console.error);
