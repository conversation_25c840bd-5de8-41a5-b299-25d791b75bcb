const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("WhitelistWithKYC", function () {
  let WhitelistWithKYC;
  let whitelist;
  let owner;
  let admin;
  let agent;
  let kycVerifier;
  let user1;
  let user2;

  beforeEach(async function () {
    // Get signers
    [owner, admin, agent, kycVerifier, user1, user2] = await ethers.getSigners();

    // Deploy whitelist with KYC
    WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
    whitelist = await upgrades.deployProxy(WhitelistWithKYC, [admin.address], { initializer: "initialize" });
    await whitelist.deployed();

    // Setup roles
    await whitelist.connect(admin).addAgent(agent.address);
    await whitelist.connect(admin).addKycVerifier(kycVerifier.address);
  });

  describe("Deployment", function () {
    it("Should set the right admin", async function () {
      expect(await whitelist.hasRole(await whitelist.DEFAULT_ADMIN_ROLE(), admin.address)).to.equal(true);
    });

    it("Should assign the agent role correctly", async function () {
      expect(await whitelist.hasRole(await whitelist.AGENT_ROLE(), agent.address)).to.equal(true);
    });

    it("Should assign the KYC verifier role correctly", async function () {
      expect(await whitelist.hasRole(await whitelist.KYC_VERIFIER_ROLE(), kycVerifier.address)).to.equal(true);
    });
  });

  describe("Identity Registry Functions", function () {
    it("Should add to whitelist correctly", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
    });

    it("Should not allow non-agent to add to whitelist", async function () {
      await expect(whitelist.connect(user1).addToWhitelist(user2.address)).to.be.reverted;
    });

    it("Should remove from whitelist correctly", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      await whitelist.connect(agent).removeFromWhitelist(user1.address);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(false);
    });

    it("Should freeze address correctly", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      await whitelist.connect(agent).freezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
    });

    it("Should unfreeze address correctly", async function () {
      await whitelist.connect(agent).addToWhitelist(user1.address);
      await whitelist.connect(agent).freezeAddress(user1.address);
      await whitelist.connect(agent).unfreezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(false);
    });

    it("Should handle batch operations correctly", async function () {
      const addresses = [user1.address, user2.address];
      
      // Batch add to whitelist
      await whitelist.connect(agent).batchAddToWhitelist(addresses);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
      expect(await whitelist.isWhitelisted(user2.address)).to.equal(true);
      
      // Batch freeze
      await whitelist.connect(agent).batchFreezeAddresses(addresses);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
      expect(await whitelist.isFrozen(user2.address)).to.equal(true);
      
      // Batch unfreeze
      await whitelist.connect(agent).batchUnfreezeAddresses(addresses);
      expect(await whitelist.isFrozen(user1.address)).to.equal(false);
      expect(await whitelist.isFrozen(user2.address)).to.equal(false);
      
      // Batch remove from whitelist
      await whitelist.connect(agent).batchRemoveFromWhitelist(addresses);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(false);
      expect(await whitelist.isWhitelisted(user2.address)).to.equal(false);
    });
  });

  describe("KYC Registry Functions", function () {
    it("Should approve KYC correctly by KYC verifier", async function () {
      await whitelist.connect(kycVerifier).approveKyc(user1.address);
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      
      // Should also whitelist the address automatically
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
    });

    it("Should approve KYC correctly by agent", async function () {
      await whitelist.connect(agent).approveKyc(user1.address);
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      
      // Should also whitelist the address automatically
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
    });

    it("Should not allow non-verifier/non-agent to approve KYC", async function () {
      await expect(whitelist.connect(user1).approveKyc(user2.address)).to.be.reverted;
    });

    it("Should revoke KYC correctly and freeze the address", async function () {
      await whitelist.connect(kycVerifier).approveKyc(user1.address);
      await whitelist.connect(kycVerifier).revokeKyc(user1.address);
      
      expect(await whitelist.isKycApproved(user1.address)).to.equal(false);
      
      // Should also freeze the address automatically
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
      
      // But it should still be whitelisted
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
    });

    it("Should handle batch KYC operations correctly", async function () {
      const addresses = [user1.address, user2.address];
      
      // Batch approve KYC
      await whitelist.connect(kycVerifier).batchApproveKyc(addresses);
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      expect(await whitelist.isKycApproved(user2.address)).to.equal(true);
      
      // Should also whitelist the addresses automatically
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(true);
      expect(await whitelist.isWhitelisted(user2.address)).to.equal(true);
      
      // Batch revoke KYC
      await whitelist.connect(kycVerifier).batchRevokeKyc(addresses);
      expect(await whitelist.isKycApproved(user1.address)).to.equal(false);
      expect(await whitelist.isKycApproved(user2.address)).to.equal(false);
      
      // Should also freeze the addresses automatically
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
      expect(await whitelist.isFrozen(user2.address)).to.equal(true);
    });
  });

  describe("Integration of Identity and KYC", function () {
    it("Should maintain KYC status when address is frozen and unfrozen", async function () {
      await whitelist.connect(kycVerifier).approveKyc(user1.address);
      await whitelist.connect(agent).freezeAddress(user1.address);
      
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
      
      await whitelist.connect(agent).unfreezeAddress(user1.address);
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      expect(await whitelist.isFrozen(user1.address)).to.equal(false);
    });

    it("Should maintain freeze status when KYC is approved and revoked", async function () {
      // First, whitelist and freeze
      await whitelist.connect(agent).addToWhitelist(user1.address);
      await whitelist.connect(agent).freezeAddress(user1.address);
      
      // Then approve KYC - should not unfreeze
      await whitelist.connect(kycVerifier).approveKyc(user1.address);
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
    });

    it("Should properly handle removing from whitelist with KYC approved", async function () {
      await whitelist.connect(kycVerifier).approveKyc(user1.address);
      
      // Remove from whitelist
      await whitelist.connect(agent).removeFromWhitelist(user1.address);
      
      // KYC status should remain
      expect(await whitelist.isKycApproved(user1.address)).to.equal(true);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(false);
    });
  });

  describe("Role Management", function () {
    it("Should allow admin to add and remove agents", async function () {
      await whitelist.connect(admin).addAgent(user1.address);
      expect(await whitelist.hasRole(await whitelist.AGENT_ROLE(), user1.address)).to.equal(true);
      
      await whitelist.connect(admin).removeAgent(user1.address);
      expect(await whitelist.hasRole(await whitelist.AGENT_ROLE(), user1.address)).to.equal(false);
    });

    it("Should allow admin to add and remove KYC verifiers", async function () {
      await whitelist.connect(admin).addKycVerifier(user1.address);
      expect(await whitelist.hasRole(await whitelist.KYC_VERIFIER_ROLE(), user1.address)).to.equal(true);
      
      await whitelist.connect(admin).removeKycVerifier(user1.address);
      expect(await whitelist.hasRole(await whitelist.KYC_VERIFIER_ROLE(), user1.address)).to.equal(false);
    });

    it("Should not allow non-admin to add roles", async function () {
      await expect(whitelist.connect(agent).addAgent(user1.address)).to.be.reverted;
      await expect(whitelist.connect(kycVerifier).addKycVerifier(user1.address)).to.be.reverted;
    });
  });

  describe("Version", function () {
    it("Should return the correct version", async function () {
      expect(await whitelist.version()).to.equal("2.0.0");
    });
  });
});