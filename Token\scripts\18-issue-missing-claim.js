const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 Issuing Missing GENERAL_QUALIFICATION Claim...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Using account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "MATIC");

  try {
    // Connect to the ClaimRegistry
    const claimRegistryAddress = "******************************************";
    const SimpleClaimRegistry = await ethers.getContractFactory("SimpleClaimRegistry");
    const claimRegistry = SimpleClaimRegistry.attach(claimRegistryAddress);

    const targetWallet = "******************************************";
    const qualificationClaimId = "**************"; // GENERAL_QUALIFICATION

    console.log("\n1️⃣ Checking Current Claims...");
    
    // Check current claims
    const hasKyc = await claimRegistry.hasValidClaim(targetWallet, "**************");
    const hasQualification = await claimRegistry.hasValidClaim(targetWallet, qualificationClaimId);
    
    console.log(`   KYC_VERIFICATION (**************): ${hasKyc ? '✅ VALID' : '❌ MISSING'}`);
    console.log(`   GENERAL_QUALIFICATION (${qualificationClaimId}): ${hasQualification ? '✅ VALID' : '❌ MISSING'}`);

    if (hasQualification) {
      console.log("\n✅ GENERAL_QUALIFICATION claim already exists!");
      return;
    }

    console.log("\n2️⃣ Issuing Missing GENERAL_QUALIFICATION Claim...");
    
    // Prepare claim data
    const claimData = ethers.toUtf8Bytes(JSON.stringify({
      qualification: "GENERAL_INVESTOR",
      verifiedAt: new Date().toISOString(),
      status: "APPROVED",
      issuer: "Admin Panel",
      level: "BASIC"
    }));

    // Issue the claim
    const tx = await claimRegistry.issueClaim(
      targetWallet,
      qualificationClaimId,
      "0x", // Empty signature for admin-issued claims
      claimData,
      "General qualification claim issued via admin panel",
      0, // Never expires
      {
        gasLimit: 400000,
        maxFeePerGas: ethers.parseUnits('100', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
      }
    );

    console.log("⏳ Transaction sent:", tx.hash);
    const receipt = await tx.wait();
    console.log("✅ GENERAL_QUALIFICATION claim issued successfully!");

    console.log("\n3️⃣ Verifying Claims After Issuance...");
    
    // Verify claims again
    const hasKycAfter = await claimRegistry.hasValidClaim(targetWallet, "**************");
    const hasQualificationAfter = await claimRegistry.hasValidClaim(targetWallet, qualificationClaimId);
    
    console.log(`   KYC_VERIFICATION (**************): ${hasKycAfter ? '✅ VALID' : '❌ MISSING'}`);
    console.log(`   GENERAL_QUALIFICATION (${qualificationClaimId}): ${hasQualificationAfter ? '✅ VALID' : '❌ MISSING'}`);
    console.log(`   Both claims valid: ${hasKycAfter && hasQualificationAfter ? '✅ YES' : '❌ NO'}`);

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 CLAIM ISSUANCE COMPLETE!");
    console.log("=" .repeat(60));
    console.log("📋 Summary:");
    console.log("   Wallet:", targetWallet);
    console.log("   Transaction Hash:", receipt.transactionHash);
    console.log("   Claim Type:", "GENERAL_QUALIFICATION");
    console.log("   Topic ID:", qualificationClaimId);
    console.log("\n🔧 Next Steps:");
    console.log("   1. Refresh the client application");
    console.log("   2. Check if Claim_001 token is now visible");
    console.log("   3. Verify token access with both claims");

  } catch (error) {
    console.error("❌ Failed to issue claim:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
