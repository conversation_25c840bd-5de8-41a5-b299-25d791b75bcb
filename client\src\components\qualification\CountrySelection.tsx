'use client';

import { useState } from 'react';
import { CheckCircleIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

interface CountrySelectionProps {
  onComplete: (country: string) => Promise<void> | void;
  selectedCountry?: string;
  isCompleted?: boolean;
}

// Common countries list - can be expanded
const COUNTRIES = [
  'United States',
  'United Kingdom', 
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Switzerland',
  'Netherlands',
  'Singapore',
  'Japan',
  'South Korea',
  'Hong Kong',
  'United Arab Emirates',
  'Other'
];

export function CountrySelection({ onComplete, selectedCountry, isCompleted }: CountrySelectionProps) {
  const [country, setCountry] = useState(selectedCountry || '');
  const [customCountry, setCustomCountry] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    const finalCountry = country === 'Other' ? customCountry : country;

    if (!finalCountry.trim()) {
      alert('Please select or enter your country');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('🌍 Country selected:', finalCountry);

      // Call the completion handler
      await onComplete(finalCountry);

      console.log('✅ Country selection completed successfully');
    } catch (error) {
      console.error('❌ Error saving country:', error);
      alert('Failed to save country selection. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If already completed, show completion status
  if (isCompleted && selectedCountry) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-green-800">Country Selected</h3>
            <p className="text-green-700">
              Selected country: <strong>{selectedCountry}</strong>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <GlobeAltIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Where are you from?
        </h2>
        <p className="text-gray-600">
          Please select your country of residence to ensure compliance with local regulations.
        </p>
      </div>

      {/* Country Selection */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Select Your Country
        </h3>

        <div className="space-y-4">
          {/* Country Dropdown */}
          <div>
            <label htmlFor="country-select" className="block text-sm font-medium text-gray-700 mb-2">
              Country of Residence *
            </label>
            <select
              id="country-select"
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isSubmitting}
            >
              <option value="">Select your country...</option>
              {COUNTRIES.map((countryOption) => (
                <option key={countryOption} value={countryOption}>
                  {countryOption}
                </option>
              ))}
            </select>
          </div>

          {/* Custom Country Input */}
          {country === 'Other' && (
            <div>
              <label htmlFor="custom-country" className="block text-sm font-medium text-gray-700 mb-2">
                Please specify your country *
              </label>
              <input
                type="text"
                id="custom-country"
                value={customCountry}
                onChange={(e) => setCustomCountry(e.target.value)}
                placeholder="Enter your country name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              />
            </div>
          )}

          {/* Important Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Important:</strong> Your country selection helps us ensure compliance with local 
              securities regulations and determines which investment opportunities are available to you.
            </p>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <button
              onClick={handleSubmit}
              disabled={!country || (country === 'Other' && !customCountry.trim()) || isSubmitting}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
                (country && (country !== 'Other' || customCountry.trim())) && !isSubmitting
                  ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                'Continue to Next Step'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
