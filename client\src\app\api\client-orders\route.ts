import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

// This is the backend-for-frontend (BFF) API route in the client application.
// It will receive order submissions from the client's UI and then call the admin-panel API.

// GET /api/client-orders - Get orders for the authenticated client
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '10';

    const adminPanelApiUrl = process.env.ADMIN_API_BASE_URL || 'http://localhost:6677/api';

    // First, get the client profile to get the clientId
    const clientProfileResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/client/profile`, {
      headers: {
        'Cookie': request.headers.get('cookie') || '',
      },
    });

    if (!clientProfileResponse.ok) {
      console.error('Failed to get client profile:', clientProfileResponse.status);
      return NextResponse.json(
        { error: 'Failed to get client profile' },
        { status: 400 }
      );
    }

    const clientProfile = await clientProfileResponse.json();
    const clientId = clientProfile.id;

    console.log('Client profile found:', { clientId, email: clientProfile.email });

    // Build query parameters for admin API
    const queryParams = new URLSearchParams({
      clientId,
      page,
      limit,
    });

    if (status) {
      queryParams.append('status', status);
    }

    const adminApiEndpoint = `${adminPanelApiUrl}/orders?${queryParams.toString()}`;

    console.log(`Fetching orders from admin panel: ${adminApiEndpoint}`);

    const response = await fetch(adminApiEndpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Admin panel API error:', response.status, errorText);
      return NextResponse.json(
        { error: `Admin panel API error: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Orders fetched successfully:', { count: data.orders?.length || 0 });

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching client orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { tokenId, clientId, tokensOrdered } = body;

    if (!tokenId || !clientId || !tokensOrdered) {
      return NextResponse.json(
        { error: 'Missing required fields: tokenId, clientId, tokensOrdered' },
        { status: 400 }
      );
    }

    // Ensure the clientId from the request matches the authenticated user's ID (or an admin is making the request)
    // For simplicity, we're trusting clientProfile.id passed from the offers page was correctly derived.
    // In a real scenario, you might want to re-verify this against the session user or have more robust checks.

    const adminPanelApiUrl = process.env.ADMIN_API_BASE_URL || 'http://localhost:6677/api';

    const adminApiEndpoint = `${adminPanelApiUrl}/orders`;

    console.log(`Forwarding order to admin panel: ${adminApiEndpoint}`, body);

    const response = await fetch(adminApiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // TODO: Add any necessary authentication headers for the admin-panel API if required.
        // e.g., an API key or a service-to-service auth token.
      },
      body: JSON.stringify({
        tokenId,
        clientId,
        tokensOrdered,
        // The admin-panel /api/orders endpoint should pick up the tokenPrice from the token itself
        // and set the default status to PENDING_APPROVAL.
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('Admin panel API error:', responseData);
      return NextResponse.json(
        { error: responseData.error || 'Failed to create order in admin panel' },
        { status: response.status }
      );
    }

    return NextResponse.json(responseData, { status: response.status });

  } catch (error) {
    console.error('Error in /api/client-orders:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'Failed to submit order', details: errorMessage },
      { status: 500 }
    );
  }
}