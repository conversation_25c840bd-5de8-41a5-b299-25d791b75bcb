'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { OrderStatus } from '@prisma/client';

interface Order {
  id: string;
  status: OrderStatus;
  tokensOrdered: string;
  tokensConfirmed: string;
  amountToPay: string;
  confirmedPayment: string;
  tokenPrice: string;
  paymentReference: string;
  transactionHash?: string;
  blockNumber?: string;
  createdAt: string;
  updatedAt: string;
  client: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    walletAddress?: string;
  };
  token: {
    id: string;
    name: string;
    symbol: string;
    address: string;
    tokenPrice: string;
    currency: string;
  };
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | 'ALL'>('ALL');
  const [isUpdating, setIsUpdating] = useState<string | null>(null);
  const [isMinting, setIsMinting] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchOrders();
  }, [selectedStatus, currentPage]);

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10'
      });

      if (selectedStatus !== 'ALL') {
        params.append('status', selectedStatus);
      }

      const response = await fetch(`/api/orders?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch orders');
      }

      setOrders(data.orders || []);
      setTotalPages(data.pagination?.totalPages || 1);
    } catch (err: any) {
      console.error('Error fetching orders:', err);
      setError(err.message || 'Failed to fetch orders');
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, newStatus: OrderStatus) => {
    try {
      setIsUpdating(orderId);

      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update order');
      }

      // Update the order in the local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );

      alert(`Order status updated to ${newStatus}`);
    } catch (err: any) {
      console.error('Error updating order:', err);
      alert(`Error: ${err.message || 'Failed to update order'}`);
    } finally {
      setIsUpdating(null);
    }
  };

  const mintTokensForOrder = async (order: Order) => {
    try {
      setIsMinting(order.id);

      // Show confirmation dialog
      const confirmed = confirm(
        `Are you sure you want to mint ${order.tokensOrdered} ${order.token.symbol} tokens for ${order.client.firstName} ${order.client.lastName}?\n\n` +
        `This will:\n` +
        `1. Mint ${order.tokensOrdered} tokens on the blockchain\n` +
        `2. Transfer them to the client's wallet\n` +
        `3. Update the order status to MINTED\n\n` +
        `This action cannot be undone.`
      );

      if (!confirmed) {
        return;
      }

      console.log('Starting minting process for order:', order.id);

      const response = await fetch(`/api/orders/${order.id}/mint`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress: order.token.address,
          recipientAddress: order.client.walletAddress || order.client.email, // Fallback to email if no wallet
          amount: order.tokensOrdered,
          orderId: order.id
        }),
      });

      const data = await response.json();
      console.log('Mint API response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to mint tokens');
      }

      // Update the order in the local state with transaction hash
      console.log('Updating order with transaction hash:', data.transactionHash);
      setOrders(prevOrders =>
        prevOrders.map(o =>
          o.id === order.id ? {
            ...o,
            status: OrderStatus.MINTED,
            transactionHash: data.transactionHash,
            blockNumber: data.blockNumber
          } : o
        )
      );

      alert(
        `✅ Tokens minted successfully!\n\n` +
        `Transaction Hash: ${data.transactionHash}\n` +
        `Amount: ${order.tokensOrdered} ${order.token.symbol}\n` +
        `Recipient: ${data.recipientAddress}\n\n` +
        `Order status updated to MINTED.`
      );

    } catch (err: any) {
      console.error('Error minting tokens:', err);
      alert(`❌ Minting failed: ${err.message || 'Unknown error occurred'}`);
    } finally {
      setIsMinting(null);
    }
  };

  const getStatusBadgeColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING_APPROVAL:
        return 'bg-yellow-100 text-yellow-800';
      case OrderStatus.CONFIRMED:
        return 'bg-blue-100 text-blue-800';
      case OrderStatus.MINTED:
        return 'bg-green-100 text-green-800';
      case OrderStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading orders...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading orders</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <button
                onClick={fetchOrders}
                className="mt-2 text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        {/* Header and Filters */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Orders Management</h1>
          <div className="flex items-center space-x-4">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as OrderStatus | 'ALL')}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="ALL">All Statuses</option>
              <option value={OrderStatus.PENDING_APPROVAL}>Pending Approval</option>
              <option value={OrderStatus.CONFIRMED}>Confirmed</option>
              <option value={OrderStatus.MINTED}>Minted</option>
              <option value={OrderStatus.CANCELLED}>Cancelled</option>
            </select>
            <button
              onClick={fetchOrders}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Orders Table */}
        {orders.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No orders found.
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Token
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Investor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tokens Ordered
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount to Pay
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Reference
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Creation Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {order.token.name} ({order.token.symbol})
                          </div>
                          <Link
                            href={`/tokens/${order.token.address}`}
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            View Token
                          </Link>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {order.client.firstName} {order.client.lastName}
                        </div>
                        {order.client.walletAddress && (
                          <div className="text-xs text-gray-500 font-mono">
                            {order.client.walletAddress.slice(0, 6)}...{order.client.walletAddress.slice(-4)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{order.client.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(order.status)}`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.tokensOrdered}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${order.amountToPay}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                        {order.paymentReference}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(order.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        {order.status === OrderStatus.PENDING_APPROVAL && (
                          <button
                            onClick={() => updateOrderStatus(order.id, OrderStatus.CONFIRMED)}
                            disabled={isUpdating === order.id}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                          >
                            {isUpdating === order.id ? 'Updating...' : 'Approve'}
                          </button>
                        )}
                        {order.status === OrderStatus.CONFIRMED && (
                          order.client.walletAddress ? (
                            <button
                              onClick={() => mintTokensForOrder(order)}
                              disabled={isMinting === order.id || isUpdating === order.id}
                              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isMinting === order.id ? 'MINTING...' : 'MINT'}
                            </button>
                          ) : (
                            <span className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                              No wallet address
                            </span>
                          )
                        )}
                        {order.status === OrderStatus.MINTED && order.transactionHash && (
                          <a
                            href={`https://amoy.polygonscan.com/tx/${order.transactionHash}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
                          >
                            Tx
                          </a>
                        )}
                        {order.status !== OrderStatus.CANCELLED && (
                          <button
                            onClick={() => updateOrderStatus(order.id, OrderStatus.CANCELLED)}
                            disabled={isUpdating === order.id}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          >
                            {isUpdating === order.id ? 'Updating...' : 'Cancel'}
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Page <span className="font-medium">{currentPage}</span> of{' '}
                      <span className="font-medium">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        Next
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
