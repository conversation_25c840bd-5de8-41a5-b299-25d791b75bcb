import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientEmail = searchParams.get('clientEmail');
    const clientId = searchParams.get('clientId');
    const tokenAddress = searchParams.get('tokenAddress');
    const tokenSymbol = searchParams.get('tokenSymbol');

    if (!clientEmail && !clientId) {
      return NextResponse.json({ error: 'Client email or ID required' }, { status: 400 });
    }

    if (!tokenAddress && !tokenSymbol) {
      return NextResponse.json({ error: 'Token address or symbol required' }, { status: 400 });
    }

    // Find client
    let client;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else {
      client = await prisma.client.findFirst({
        where: { email: clientEmail }
      });
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find token
    let token = null;
    if (tokenAddress) {
      token = await prisma.token.findFirst({
        where: { address: tokenAddress }
      });
    }

    // Find token agreement
    const whereClause: any = {
      clientId: client.id
    };

    if (token) {
      whereClause.tokenId = token.id;
    } else if (tokenSymbol) {
      whereClause.tokenSymbol = tokenSymbol;
    } else if (tokenAddress) {
      whereClause.tokenAddress = tokenAddress;
    }

    const agreement = await prisma.tokenAgreement.findFirst({
      where: whereClause,
      include: {
        client: true,
        token: true
      }
    });

    if (!agreement) {
      return NextResponse.json({
        accepted: false,
        acceptedAt: null,
        tokenAddress: tokenAddress,
        tokenSymbol: tokenSymbol,
        agreementVersion: '1.0',
      });
    }

    return NextResponse.json({
      accepted: agreement.accepted,
      acceptedAt: agreement.acceptedAt?.toISOString() || null,
      tokenAddress: agreement.token?.address || agreement.tokenAddress,
      tokenSymbol: agreement.token?.symbol || agreement.tokenSymbol,
      agreementVersion: agreement.agreementVersion,
    });

  } catch (error) {
    console.error('Error fetching token agreement:', error);
    return NextResponse.json(
      { error: 'Failed to fetch token agreement' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clientEmail,
      clientId,
      tokenAddress,
      tokenSymbol,
      accepted,
      agreementVersion = '1.0'
    } = body;

    if (!clientEmail && !clientId) {
      return NextResponse.json({ error: 'Client email or ID required' }, { status: 400 });
    }

    if (!tokenAddress && !tokenSymbol) {
      return NextResponse.json({ error: 'Token address or symbol required' }, { status: 400 });
    }

    if (!accepted) {
      return NextResponse.json({ error: 'Agreement must be accepted' }, { status: 400 });
    }

    // Find client
    let client;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else {
      client = await prisma.client.findFirst({
        where: { email: clientEmail }
      });
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find token
    let token = null;
    if (tokenAddress) {
      token = await prisma.token.findFirst({
        where: { address: tokenAddress }
      });
    }

    const acceptedAt = new Date();

    // Create or update token agreement
    const agreementData = {
      clientId: client.id,
      tokenId: token?.id,
      tokenSymbol: tokenSymbol,
      tokenAddress: tokenAddress,
      accepted: true,
      acceptedAt: acceptedAt,
      agreementVersion: agreementVersion,
    };

    const whereClause: any = {
      clientId: client.id
    };

    if (token) {
      whereClause.tokenId = token.id;
    } else if (tokenSymbol) {
      whereClause.tokenSymbol = tokenSymbol;
    }

    const agreement = await prisma.tokenAgreement.upsert({
      where: {
        clientId_tokenId: whereClause
      },
      update: agreementData,
      create: agreementData,
      include: {
        client: true,
        token: true
      }
    });

    console.log('💾 Saved token agreement to database:', {
      clientEmail: client.email,
      tokenAddress: token?.address || tokenAddress,
      tokenSymbol: tokenSymbol,
      accepted: true,
      acceptedAt: acceptedAt.toISOString()
    });

    return NextResponse.json({
      success: true,
      message: 'Token agreement saved successfully',
      data: {
        accepted: true,
        acceptedAt: acceptedAt.toISOString(),
        tokenAddress: token?.address || tokenAddress,
        tokenSymbol: tokenSymbol,
        agreementVersion: agreementVersion,
      }
    });

  } catch (error) {
    console.error('Error saving token agreement:', error);
    return NextResponse.json(
      { error: 'Failed to save token agreement' },
      { status: 500 }
    );
  }
}
