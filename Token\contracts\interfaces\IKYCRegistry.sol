// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

/**
 * @title IKYCRegistry
 * @dev Interface for KYC registry functionality
 * Separates KYC concerns from basic identity management
 */
interface IKYCRegistry {
    /**
     * @dev Emitted when KYC approval is granted to an address
     */
    event <PERSON><PERSON><PERSON>Approved(address indexed account);
    
    /**
     * @dev Emitted when KYC approval is revoked from an address
     */
    event Kyc<PERSON>evoked(address indexed account);

    /**
     * @dev Check if an address is KYC approved
     * @param account The address to check
     * @return bool True if the address is KYC approved, false otherwise
     */
    function isKycApproved(address account) external view returns (bool);

    /**
     * @dev Approve KYC for an address
     * @param account The address to approve KY<PERSON> for
     */
    function approveKyc(address account) external;

    /**
     * @dev Revoke KYC approval for an address
     * @param account The address to revoke KYC approval from
     */
    function revokeKyc(address account) external;

    /**
     * @dev Batch approve KY<PERSON> for addresses
     * @param accounts The addresses to approve <PERSON><PERSON><PERSON> for
     */
    function batchApproveKyc(address[] calldata accounts) external;

    /**
     * @dev Batch revoke KYC approval for addresses
     * @param accounts The addresses to revoke KYC approval from
     */
    function batchRevokeKyc(address[] calldata accounts) external;
}