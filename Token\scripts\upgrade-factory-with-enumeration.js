const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("🚀 Upgrading SecurityTokenFactory with enumeration functionality...");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);

  try {
    // Deploy new SecurityTokenFactory with enumeration
    // Note: The factory constructor automatically deploys implementation contracts
    console.log("\n🏭 Deploying new SecurityTokenFactory with enumeration...");

    // Get current gas price and set reasonable limits
    const feeData = await ethers.provider.getFeeData();
    console.log("Current gas price:", ethers.formatUnits(feeData.gasPrice || 0, 'gwei'), "gwei");

    // Set gas parameters to handle high fees
    const gasOptions = {
      gasLimit: ********, // 15M gas limit
      maxFeePerGas: ethers.parseUnits('200', 'gwei'), // Max 200 gwei
      maxPriorityFeePerGas: ethers.parseUnits('50', 'gwei'), // 50 gwei priority
    };

    console.log("Using gas options:", {
      gasLimit: gasOptions.gasLimit.toString(),
      maxFeePerGas: ethers.formatUnits(gasOptions.maxFeePerGas, 'gwei') + ' gwei',
      maxPriorityFeePerGas: ethers.formatUnits(gasOptions.maxPriorityFeePerGas, 'gwei') + ' gwei'
    });

    const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    const factory = await SecurityTokenFactory.deploy(deployer.address, gasOptions);

    console.log("⏳ Waiting for deployment transaction...");
    await factory.waitForDeployment();
    const factoryAddress = await factory.getAddress();
    console.log("✅ SecurityTokenFactory deployed to:", factoryAddress);

    // Get the implementation addresses that were deployed by the constructor
    console.log("\n📄 Getting implementation addresses...");
    const securityTokenImplAddress = await factory.securityTokenImplementation();
    const whitelistImplAddress = await factory.whitelistImplementation();
    const whitelistWithKYCImplAddress = await factory.whitelistWithKYCImplementation();

    console.log("✅ SecurityToken implementation:", securityTokenImplAddress);
    console.log("✅ Whitelist implementation:", whitelistImplAddress);
    console.log("✅ WhitelistWithKYC implementation:", whitelistWithKYCImplAddress);

    // Test the enumeration functionality
    console.log("\n🧪 Testing enumeration functionality...");
    try {
      const tokenCount = await factory.getTokenCount();
      console.log("✅ getTokenCount() works, current count:", tokenCount.toString());

      const allTokens = await factory.getAllDeployedTokens();
      console.log("✅ getAllDeployedTokens() works, tokens:", allTokens);
    } catch (error) {
      console.error("❌ Error testing enumeration:", error.message);
    }

    // Save deployment info
    const deploymentInfo = {
      network: network.name,
      chainId: network.chainId.toString(),
      deployer: deployer.address,
      timestamp: new Date().toISOString(),
      contracts: {
        factory: factoryAddress,
        securityTokenImplementation: securityTokenImplAddress,
        whitelistImplementation: whitelistImplAddress,
        whitelistWithKYCImplementation: whitelistWithKYCImplAddress
      },
      features: [
        "Token enumeration (getTokenCount, getAllDeployedTokens, getDeployedToken)",
        "Configurable decimals (0-18)",
        "KYC support",
        "Upgradeable implementations"
      ]
    };

    // Save to deployments directory
    const deploymentsDir = path.join(__dirname, '../deployments');
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }

    const deploymentFile = path.join(deploymentsDir, `factory-with-enumeration-${network.name}-${Date.now()}.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    console.log("📁 Deployment info saved to:", deploymentFile);

    // Update config file if it exists
    const configPath = path.join(__dirname, '../admin-panel/src/config.ts');
    if (fs.existsSync(configPath)) {
      console.log("\n📝 Updating admin panel config...");
      let configContent = fs.readFileSync(configPath, 'utf8');

      // Update factory address for the current network
      const networkKey = network.name === 'unknown' ? 'amoy' : network.name;
      const factoryRegex = new RegExp(`(${networkKey}.*?factory:\\s*['"])([^'"]*?)(['"])`);

      if (configContent.match(factoryRegex)) {
        configContent = configContent.replace(factoryRegex, `$1${factoryAddress}$3`);
        fs.writeFileSync(configPath, configContent);
        console.log("✅ Config updated with new factory address");
      } else {
        console.log("⚠️ Could not automatically update config. Please update manually:");
        console.log(`   Factory address: ${factoryAddress}`);
      }
    }

    console.log("\n🎉 Factory upgrade completed successfully!");
    console.log("\n📋 Summary:");
    console.log("   Factory Address:", factoryAddress);
    console.log("   SecurityToken Implementation:", securityTokenImplAddress);
    console.log("   Whitelist Implementation:", whitelistImplAddress);
    console.log("   WhitelistWithKYC Implementation:", whitelistWithKYCImplAddress);
    console.log("\n✨ New Features:");
    console.log("   • Token enumeration (getTokenCount, getAllDeployedTokens)");
    console.log("   • Improved dashboard token loading");
    console.log("   • Backward compatibility with existing tokens");

    console.log("\n🔄 Next Steps:");
    console.log("   1. Update your admin panel config with the new factory address");
    console.log("   2. Test the dashboard token loading functionality");
    console.log("   3. Create new tokens to test enumeration");

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
