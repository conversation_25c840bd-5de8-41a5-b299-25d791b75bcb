const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying SimpleClaimRegistry with Custom Claims Support...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "MATIC");

  try {
    // Deploy the SimpleClaimRegistry
    console.log("\n1️⃣ Deploying SimpleClaimRegistry...");
    
    const SimpleClaimRegistry = await ethers.getContractFactory("SimpleClaimRegistry");
    
    // Deploy with constructor parameter
    const claimRegistry = await SimpleClaimRegistry.deploy(deployer.address, {
      gasLimit: 5000000, // Reduced gas limit
      maxFeePerGas: ethers.parseUnits('100', 'gwei'), // Lower gas price
      maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
    });

    await claimRegistry.waitForDeployment();
    const claimRegistryAddress = await claimRegistry.getAddress();
    
    console.log("✅ SimpleClaimRegistry deployed to:", claimRegistryAddress);

    // Wait for a few block confirmations
    console.log("\n⏳ Waiting for block confirmations...");
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Test the new functions
    console.log("\n2️⃣ Testing New Custom Claims Functions...");
    
    try {
      // Test getTotalClaimTypes (should return 4 default claim types)
      const totalClaimTypes = await claimRegistry.getTotalClaimTypes();
      console.log("📊 Total claim types:", totalClaimTypes.toString());

      // Test getActiveClaimTypes
      const activeClaimTypes = await claimRegistry.getActiveClaimTypes(0, 10);
      console.log("📋 Active claim types:", activeClaimTypes.length);
      
      // Display the default claim types
      for (let i = 0; i < activeClaimTypes.length; i++) {
        const claimType = activeClaimTypes[i];
        console.log(`   ${claimType.id}: ${claimType.name} - ${claimType.description}`);
      }

      // Test creating a new custom claim type
      console.log("\n3️⃣ Creating Test Custom Claim Type...");
      const createTx = await claimRegistry.createClaimType(
        "Test Accredited Investor",
        "Test claim for accredited investor qualification",
        {
          gasLimit: 500000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await createTx.wait();
      console.log("✅ Test claim type created successfully");

      // Check total count again
      const newTotalClaimTypes = await claimRegistry.getTotalClaimTypes();
      console.log("📊 New total claim types:", newTotalClaimTypes.toString());

      // Test issuing a claim
      console.log("\n4️⃣ Testing Claim Issuance...");
      const testWallet = "******************************************"; // Your wallet
      const issueTx = await claimRegistry.issueClaim(
        testWallet,
        1, // KYC Verification claim type
        "0x", // Empty signature for test
        ethers.toUtf8Bytes("Test claim data"),
        "https://example.com/claim",
        0, // No expiration
        {
          gasLimit: 300000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        }
      );
      await issueTx.wait();
      console.log("✅ Test claim issued successfully");

      // Test hasValidClaim
      const hasValidClaim = await claimRegistry.hasValidClaim(testWallet, 1);
      console.log("🔍 Test wallet has valid KYC claim:", hasValidClaim);

    } catch (testError) {
      console.log("⚠️ Error testing functions:", testError.message);
    }

    // Display deployment summary
    console.log("\n" + "=" .repeat(60));
    console.log("🎉 DEPLOYMENT SUCCESSFUL!");
    console.log("=" .repeat(60));
    console.log("📋 Contract Addresses:");
    console.log("   SimpleClaimRegistry:", claimRegistryAddress);
    console.log("\n📝 Environment Variables to Update:");
    console.log("   CLAIM_REGISTRY_ADDRESS=" + claimRegistryAddress);
    console.log("   NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS=" + claimRegistryAddress);
    console.log("\n🔧 Next Steps:");
    console.log("   1. Update admin-panel/.env.local with new CLAIM_REGISTRY_ADDRESS");
    console.log("   2. Update client/.env.local with new NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS");
    console.log("   3. Restart both applications");
    console.log("   4. Test the custom claims functionality");

    // Save addresses to a file for easy reference
    const fs = require('fs');
    const deploymentInfo = {
      network: "amoy",
      timestamp: new Date().toISOString(),
      deployer: deployer.address,
      contracts: {
        SimpleClaimRegistry: claimRegistryAddress
      },
      environmentVariables: {
        CLAIM_REGISTRY_ADDRESS: claimRegistryAddress,
        NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS: claimRegistryAddress
      }
    };

    fs.writeFileSync(
      'deployment-simple-claims-v2.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("\n💾 Deployment info saved to: deployment-simple-claims-v2.json");

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
