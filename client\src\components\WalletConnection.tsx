'use client'

import { useState, useEffect } from 'react'
import { useAccount, useSignMessage, useDisconnect } from 'wagmi'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { WalletIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { type Address } from 'viem'

interface WalletConnectionProps {
  onWalletConnected?: (address: string, signature: string) => void
}

export function WalletConnection({ onWalletConnected }: WalletConnectionProps) {
  const { address, isConnected, isConnecting } = useAccount()
  const { disconnect } = useDisconnect()
  const { signMessageAsync, isPending: isSigningPending } = useSignMessage()
  const queryClient = useQueryClient()

  const [isVerifying, setIsVerifying] = useState(false)
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')

  // Check if wallet is already verified for this user
  const { data: walletStatus } = useQuery({
    queryKey: ['wallet-status', address],
    queryFn: async () => {
      if (!address) return null
      const response = await fetch(`/api/client/wallet/${address}`)
      if (response.ok) {
        return response.json()
      }
      return null
    },
    enabled: !!address,
  })

  // Mutation to save wallet address and signature
  const saveWalletMutation = useMutation({
    mutationFn: async ({ address, signature, message }: { address: string; signature: string; message: string }) => {
      const response = await fetch('/api/client/wallet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ address, signature, message }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save wallet')
      }

      return response.json()
    },
    onSuccess: (data) => {
      console.log('Wallet verification successful, updating status...')
      setVerificationStatus('success')
      setIsVerifying(false)
      // Invalidate all wallet-related queries to refetch updated data
      queryClient.invalidateQueries({ queryKey: ['wallet-status'] })
      queryClient.invalidateQueries({ queryKey: ['wallet-status', address] })
      queryClient.invalidateQueries({ queryKey: ['client-profile'] })
      onWalletConnected?.(data.address, data.signature)
    },
    onError: (error) => {
      setVerificationStatus('error')
      setErrorMessage(error.message)
      setIsVerifying(false)
    },
  })

  const handleVerifyWallet = async () => {
    if (!address) return

    console.log('Starting wallet verification for address:', address)
    setIsVerifying(true)
    setVerificationStatus('idle')
    setErrorMessage('')

    try {
      // Create a simple, consistent message to sign
      const message = `Verify wallet ownership for Security Token Client Portal\n\nWallet: ${address}\n\nBy signing this message, you confirm that you own this wallet address.`

      console.log('Requesting signature for message:', message)

      // Sign the message using signMessageAsync with account parameter
      const signature = await signMessageAsync({
        message,
        account: address as Address
      })

      console.log('Signature received:', signature?.substring(0, 20) + '...')

      if (signature) {
        console.log('Sending verification to backend...')
        // Save to backend with the same message
        await saveWalletMutation.mutateAsync({ address, signature, message })
        console.log('Verification completed successfully')
      } else {
        console.error('No signature received - user may have canceled or signing failed')
        setVerificationStatus('error')
        setErrorMessage('Signature was canceled or failed. Please try again.')
        setIsVerifying(false)
      }
    } catch (error) {
      console.error('Wallet verification error:', error)
      setVerificationStatus('error')

      // Handle specific error types
      if (error instanceof Error) {
        if (error.message.includes('User rejected') || error.message.includes('denied')) {
          setErrorMessage('Signature was rejected. Please approve the signature request to verify your wallet.')
        } else if (error.message.includes('timeout')) {
          setErrorMessage('Signature request timed out. Please try again.')
        } else {
          setErrorMessage(`Signing failed: ${error.message}`)
        }
      } else {
        setErrorMessage('Failed to sign message. Please try again.')
      }

      setIsVerifying(false)
    }
  }

  const handleDisconnect = () => {
    disconnect()
    setVerificationStatus('idle')
    setErrorMessage('')
  }

  if (!isConnected) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <WalletIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Connect Your Wallet</h3>
          <p className="text-gray-600 mb-6">
            Connect your wallet to complete the qualification process and receive your security tokens.
          </p>
          <w3m-button />
          {isConnecting && (
            <p className="mt-4 text-sm text-blue-600">Connecting wallet...</p>
          )}
        </div>
      </div>
    )
  }

  const isVerified = walletStatus?.verified
  const isProcessing = isVerifying || isSigningPending || saveWalletMutation.isPending

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Wallet Connected</h3>
          <p className="text-sm text-gray-600 font-mono break-all">{address}</p>
        </div>
        <button
          onClick={handleDisconnect}
          className="text-sm text-red-600 hover:text-red-700"
        >
          Disconnect
        </button>
      </div>

      {isVerified ? (
        <div className="flex items-center p-4 bg-green-50 rounded-lg">
          <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-green-800">Wallet Verified</p>
            <p className="text-sm text-green-600">Your wallet has been successfully verified and linked to your account.</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center p-4 bg-yellow-50 rounded-lg">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-yellow-800">Verification Required</p>
              <p className="text-sm text-yellow-600">Please verify your wallet ownership by signing a message.</p>
            </div>
          </div>

          <button
            onClick={handleVerifyWallet}
            disabled={isProcessing}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isSigningPending ? 'Sign Message...' : 'Verifying...'}
              </span>
            ) : (
              'Verify Wallet Ownership'
            )}
          </button>

          {verificationStatus === 'error' && (
            <div className="p-4 bg-red-50 rounded-lg">
              <p className="text-sm text-red-800">{errorMessage}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
