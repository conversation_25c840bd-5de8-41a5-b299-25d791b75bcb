{"_format": "hh-sol-artifact-1", "contractName": "Address", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea264697066735822122097869b247dee6450e047b68f73d2f7790bfb981c410db1a6a1fdc06e183a9fb764736f6c63430008160033", "deployedBytecode": "0x600080fdfea264697066735822122097869b247dee6450e047b68f73d2f7790bfb981c410db1a6a1fdc06e183a9fb764736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}