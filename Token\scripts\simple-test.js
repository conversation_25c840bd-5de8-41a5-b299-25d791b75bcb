// Simple test to verify our decimals implementation
const hre = require("hardhat");

async function main() {
  try {
    const [deployer] = await hre.ethers.getSigners();
    console.log("Testing with account:", deployer.address);
    
    // Deploy factory
    console.log("Deploying SecurityTokenFactory...");
    const SecurityTokenFactory = await hre.ethers.getContractFactory("SecurityTokenFactory");
    const factory = await SecurityTokenFactory.deploy(deployer.address);
    await factory.waitForDeployment();
    
    const factoryAddress = await factory.getAddress();
    console.log("Factory deployed to:", factoryAddress);
    
    // Test deploying a token with custom decimals
    console.log("\nDeploying token with 6 decimals...");
    const tx = await factory.deploySecurityToken(
      "Six Decimals Token",
      "SIX",
      6,
      1000000,
      deployer.address,
      "10 USD",
      "Tier 1: 5%",
      "Test token with 6 decimals"
    );
    
    console.log("Transaction hash:", tx.hash);
    const receipt = await tx.wait();
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    
    // Get token address
    const tokenAddress = await factory.getTokenAddressBySymbol("SIX");
    console.log("Token address:", tokenAddress);
    
    // Verify token properties
    const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);
    
    console.log("\n--- Token Properties ---");
    console.log("Name:", await token.name());
    console.log("Symbol:", await token.symbol());
    console.log("Decimals:", await token.decimals());
    console.log("Max Supply:", (await token.maxSupply()).toString());
    
    // Test with 0 decimals
    console.log("\nDeploying token with 0 decimals...");
    const tx2 = await factory.deploySecurityToken(
      "Zero Decimals Token",
      "ZERO",
      0,
      1000000,
      deployer.address,
      "10 USD",
      "Tier 1: 5%",
      "Test token with 0 decimals"
    );
    
    await tx2.wait();
    const tokenAddress2 = await factory.getTokenAddressBySymbol("ZERO");
    const token2 = await hre.ethers.getContractAt("SecurityToken", tokenAddress2);
    
    console.log("\n--- Token 2 Properties ---");
    console.log("Name:", await token2.name());
    console.log("Symbol:", await token2.symbol());
    console.log("Decimals:", await token2.decimals());
    
    console.log("\n✅ All tests passed! Decimals functionality is working correctly.");
    
  } catch (error) {
    console.error("❌ Error during testing:", error);
    process.exitCode = 1;
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
