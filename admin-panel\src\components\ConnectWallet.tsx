'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

const ConnectWallet = () => {
  const [account, setAccount] = useState<string | null>(null);
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [networkName, setNetworkName] = useState<string>('');
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    // Check if already connected
    checkIfWalletIsConnected();
  }, []);

  // Listen for account changes
  useEffect(() => {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', (accounts: string[]) => {
        if (accounts.length > 0) {
          setAccount(accounts[0]);
        } else {
          setAccount(null);
        }
      });

      window.ethereum.on('chainChanged', (_chainId: string) => {
        window.location.reload();
      });
    }

    return () => {
      if (window.ethereum) {
        window.ethereum.removeAllListeners();
      }
    };
  }, []);

  const checkIfWalletIsConnected = async () => {
    try {
      if (!window.ethereum) {
        console.log('Make sure you have MetaMask installed!');
        return;
      }

      // Get the provider
      const web3Provider = new ethers.BrowserProvider(window.ethereum);
      setProvider(web3Provider);

      // Get the network
      const network = await web3Provider.getNetwork();
      setNetworkName(network.name === 'unknown' ? `Chain ID ${network.chainId}` : network.name);

      // Get accounts
      const accounts = await web3Provider.listAccounts();
      if (accounts.length > 0) {
        setAccount(accounts[0].address);
      }
    } catch (error) {
      console.error('Error checking if wallet is connected:', error);
    }
  };

  const connectWallet = async () => {
    try {
      setIsConnecting(true);
      if (!window.ethereum) {
        alert('Please install MetaMask to use this feature!');
        setIsConnecting(false);
        return;
      }

      // Request accounts
      const web3Provider = new ethers.BrowserProvider(window.ethereum);
      await web3Provider.send('eth_requestAccounts', []);
      
      // Get the connected account
      const signer = await web3Provider.getSigner();
      const connectedAccount = await signer.getAddress();
      
      // Get the network
      const network = await web3Provider.getNetwork();
      
      setProvider(web3Provider);
      setAccount(connectedAccount);
      setNetworkName(network.name === 'unknown' ? `Chain ID ${network.chainId.toString()}` : network.name);
      setIsConnecting(false);
    } catch (error) {
      console.error('Error connecting wallet:', error);
      setIsConnecting(false);
    }
  };

  const disconnectWallet = () => {
    setAccount(null);
    setProvider(null);
    setNetworkName('');
  };

  const shortenAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  return (
    <div>
      {account ? (
        <div className="flex items-center space-x-2">
          <span className="text-xs md:text-sm bg-blue-900 px-2 py-1 rounded">
            {networkName}
          </span>
          <div className="relative group">
            <button className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded flex items-center text-sm">
              {shortenAddress(account)}
            </button>
            <div className="absolute z-10 hidden group-hover:block right-0 mt-2 w-48 bg-white text-gray-800 rounded shadow-lg p-2">
              <button
                onClick={disconnectWallet}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 rounded text-sm"
              >
                Disconnect
              </button>
            </div>
          </div>
        </div>
      ) : (
        <button
          onClick={connectWallet}
          disabled={isConnecting}
          className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm flex items-center"
        >
          {isConnecting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Connecting...
            </>
          ) : (
            'Connect Wallet'
          )}
        </button>
      )}
    </div>
  );
};

export default ConnectWallet;

// Add type definition for window.ethereum
declare global {
  interface Window {
    ethereum?: any;
  }
}