// API Token Minting Script
// This script is designed to be called from an API or admin panel
// It will mint tokens with robust error handling and optimized gas parameters

const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

// Import the direct minting function
const directMint = require('./direct-mint');

async function main() {
  try {
    // Log start of minting
    console.log("\n=== API TOKEN MINTING STARTED ===");
    
    // Verify required environment variables
    const requiredVars = ['TOKEN_ADDRESS', 'TO_ADDRESS', 'AMOUNT'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
    
    // Set optimized gas values for Amoy testnet
    if (!process.env.GAS_LIMIT) {
      process.env.GAS_LIMIT = "5000000";
      console.log(`Setting default GAS_LIMIT: ${process.env.GAS_LIMIT}`);
    }
    
    if (!process.env.GAS_PRICE) {
      process.env.GAS_PRICE = "50";
      console.log(`Setting default GAS_PRICE: ${process.env.GAS_PRICE} gwei`);
    }
    
    // Set a better RPC URL for Amoy if not specified
    if (!process.env.AMOY_RPC_URL) {
      process.env.AMOY_RPC_URL = "https://polygon-amoy.blockpi.network/v1/rpc/public";
      console.log(`Using alternative RPC endpoint: ${process.env.AMOY_RPC_URL}`);
    }
    
    // Log key parameters
    console.log(`Minting to address: ${process.env.TO_ADDRESS}`);
    console.log(`Token: ${process.env.TOKEN_ADDRESS}`);
    console.log(`Amount: ${process.env.AMOUNT}`);
    
    // Call the direct mint function
    const result = await directMint();
    
    // Create a JSON file with the mint result
    const outputPath = path.join(__dirname, '../minting-results.json');
    const mintResult = {
      tokenAddress: process.env.TOKEN_ADDRESS,
      recipientAddress: process.env.TO_ADDRESS,
      amount: process.env.AMOUNT,
      transactionHash: result.transactionHash || "",
      confirmed: result.confirmed,
      blockNumber: result.blockNumber,
      status: result.success ? "success" : "failed",
      timestamp: new Date().toISOString()
    };
    
    // Save the result
    if (fs.existsSync(outputPath)) {
      // Append to existing results
      let existingResults = [];
      try {
        const fileContent = fs.readFileSync(outputPath, 'utf8');
        existingResults = JSON.parse(fileContent);
        if (!Array.isArray(existingResults)) {
          existingResults = [existingResults];
        }
      } catch (e) {
        console.warn("Could not read existing minting results, creating new file");
      }
      
      existingResults.push(mintResult);
      fs.writeFileSync(outputPath, JSON.stringify(existingResults, null, 2));
    } else {
      // Create new file
      fs.writeFileSync(outputPath, JSON.stringify([mintResult], null, 2));
    }
    
    console.log("\n=== API TOKEN MINTING COMPLETED ===");
    console.log(`Transaction Hash: ${result.transactionHash || "Unknown"}`);
    if (result.confirmed) {
      console.log(`Confirmed in Block: ${result.blockNumber}`);
    } else {
      console.log("Transaction submitted but not yet confirmed");
    }
    
    // Return the result in a format that can be easily consumed by an API
    return mintResult;
  } catch (error) {
    // Log the error
    console.error("\n=== API TOKEN MINTING FAILED ===");
    console.error(`Error: ${error.message}`);
    
    // Create an error result
    const errorResult = {
      error: true,
      message: error.message,
      details: error.toString(),
      timestamp: new Date().toISOString()
    };
    
    // Save the error to a file
    const errorPath = path.join(__dirname, '../minting-errors.json');
    let errors = [];
    
    if (fs.existsSync(errorPath)) {
      try {
        const fileContent = fs.readFileSync(errorPath, 'utf8');
        errors = JSON.parse(fileContent);
        if (!Array.isArray(errors)) {
          errors = [errors];
        }
      } catch (e) {
        console.warn("Could not read existing error log, creating new file");
      }
    }
    
    errors.push(errorResult);
    fs.writeFileSync(errorPath, JSON.stringify(errors, null, 2));
    
    // Throw the error to be caught by the calling code
    throw error;
  }
}

// Execute the script if called directly
if (require.main === module) {
  main()
    .then((result) => {
      console.log("API Minting result:", result);
      process.exit(0);
    })
    .catch((error) => {
      console.error("API Minting failed:", error);
      process.exit(1);
    });
} else {
  // Export for use in other scripts or APIs
  module.exports = main;
} 