"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/viem";
exports.ids = ["vendor-chunks/viem"];
exports.modules = {

/***/ "(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js":
/*!***************************************************************!*\
  !*** ./node_modules/viem/_esm/accounts/utils/parseAccount.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAccount: () => (/* binding */ parseAccount)\n/* harmony export */ });\nfunction parseAccount(account) {\n    if (typeof account === 'string')\n        return { address: account, type: 'json-rpc' };\n    return account;\n}\n//# sourceMappingURL=parseAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2FjY291bnRzL3V0aWxzL3BhcnNlQWNjb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcYWNjb3VudHNcXHV0aWxzXFxwYXJzZUFjY291bnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHBhcnNlQWNjb3VudChhY2NvdW50KSB7XG4gICAgaWYgKHR5cGVvZiBhY2NvdW50ID09PSAnc3RyaW5nJylcbiAgICAgICAgcmV0dXJuIHsgYWRkcmVzczogYWNjb3VudCwgdHlwZTogJ2pzb24tcnBjJyB9O1xuICAgIHJldHVybiBhY2NvdW50O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFyc2VBY2NvdW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/chains/definitions/polygon.js":
/*!**************************************************************!*\
  !*** ./node_modules/viem/_esm/chains/definitions/polygon.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polygon: () => (/* binding */ polygon)\n/* harmony export */ });\n/* harmony import */ var _utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/chain/defineChain.js */ \"(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\");\n\nconst polygon = /*#__PURE__*/ (0,_utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__.defineChain)({\n    id: 137,\n    name: 'Polygon',\n    nativeCurrency: { name: 'POL', symbol: 'POL', decimals: 18 },\n    rpcUrls: {\n        default: {\n            http: ['https://polygon-rpc.com'],\n        },\n    },\n    blockExplorers: {\n        default: {\n            name: 'PolygonScan',\n            url: 'https://polygonscan.com',\n            apiUrl: 'https://api.polygonscan.com/api',\n        },\n    },\n    contracts: {\n        multicall3: {\n            address: '0xca11bde05977b3631167028862be2a173976ca11',\n            blockCreated: 25770160,\n        },\n    },\n});\n//# sourceMappingURL=polygon.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2NoYWlucy9kZWZpbml0aW9ucy9wb2x5Z29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStEO0FBQ3hELDhCQUE4Qix3RUFBVztBQUNoRDtBQUNBO0FBQ0Esc0JBQXNCLDBDQUEwQztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTCxDQUFDO0FBQ0QiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcY2hhaW5zXFxkZWZpbml0aW9uc1xccG9seWdvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWZpbmVDaGFpbiB9IGZyb20gJy4uLy4uL3V0aWxzL2NoYWluL2RlZmluZUNoYWluLmpzJztcbmV4cG9ydCBjb25zdCBwb2x5Z29uID0gLyojX19QVVJFX18qLyBkZWZpbmVDaGFpbih7XG4gICAgaWQ6IDEzNyxcbiAgICBuYW1lOiAnUG9seWdvbicsXG4gICAgbmF0aXZlQ3VycmVuY3k6IHsgbmFtZTogJ1BPTCcsIHN5bWJvbDogJ1BPTCcsIGRlY2ltYWxzOiAxOCB9LFxuICAgIHJwY1VybHM6IHtcbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgaHR0cDogWydodHRwczovL3BvbHlnb24tcnBjLmNvbSddLFxuICAgICAgICB9LFxuICAgIH0sXG4gICAgYmxvY2tFeHBsb3JlcnM6IHtcbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgbmFtZTogJ1BvbHlnb25TY2FuJyxcbiAgICAgICAgICAgIHVybDogJ2h0dHBzOi8vcG9seWdvbnNjYW4uY29tJyxcbiAgICAgICAgICAgIGFwaVVybDogJ2h0dHBzOi8vYXBpLnBvbHlnb25zY2FuLmNvbS9hcGknLFxuICAgICAgICB9LFxuICAgIH0sXG4gICAgY29udHJhY3RzOiB7XG4gICAgICAgIG11bHRpY2FsbDM6IHtcbiAgICAgICAgICAgIGFkZHJlc3M6ICcweGNhMTFiZGUwNTk3N2IzNjMxMTY3MDI4ODYyYmUyYTE3Mzk3NmNhMTEnLFxuICAgICAgICAgICAgYmxvY2tDcmVhdGVkOiAyNTc3MDE2MCxcbiAgICAgICAgfSxcbiAgICB9LFxufSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wb2x5Z29uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/chains/definitions/polygon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/chains/definitions/polygonAmoy.js":
/*!******************************************************************!*\
  !*** ./node_modules/viem/_esm/chains/definitions/polygonAmoy.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polygonAmoy: () => (/* binding */ polygonAmoy)\n/* harmony export */ });\n/* harmony import */ var _utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/chain/defineChain.js */ \"(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\");\n\nconst polygonAmoy = /*#__PURE__*/ (0,_utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__.defineChain)({\n    id: 80_002,\n    name: 'Polygon Amoy',\n    nativeCurrency: { name: 'POL', symbol: 'POL', decimals: 18 },\n    rpcUrls: {\n        default: {\n            http: ['https://rpc-amoy.polygon.technology'],\n        },\n    },\n    blockExplorers: {\n        default: {\n            name: 'PolygonScan',\n            url: 'https://amoy.polygonscan.com',\n            apiUrl: 'https://api-amoy.polygonscan.com/api',\n        },\n    },\n    contracts: {\n        multicall3: {\n            address: '0xca11bde05977b3631167028862be2a173976ca11',\n            blockCreated: 3127388,\n        },\n    },\n    testnet: true,\n});\n//# sourceMappingURL=polygonAmoy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2NoYWlucy9kZWZpbml0aW9ucy9wb2x5Z29uQW1veS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErRDtBQUN4RCxrQ0FBa0Msd0VBQVc7QUFDcEQ7QUFDQTtBQUNBLHNCQUFzQiwwQ0FBMEM7QUFDaEU7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSxDQUFDO0FBQ0QiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcY2hhaW5zXFxkZWZpbml0aW9uc1xccG9seWdvbkFtb3kuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVmaW5lQ2hhaW4gfSBmcm9tICcuLi8uLi91dGlscy9jaGFpbi9kZWZpbmVDaGFpbi5qcyc7XG5leHBvcnQgY29uc3QgcG9seWdvbkFtb3kgPSAvKiNfX1BVUkVfXyovIGRlZmluZUNoYWluKHtcbiAgICBpZDogODBfMDAyLFxuICAgIG5hbWU6ICdQb2x5Z29uIEFtb3knLFxuICAgIG5hdGl2ZUN1cnJlbmN5OiB7IG5hbWU6ICdQT0wnLCBzeW1ib2w6ICdQT0wnLCBkZWNpbWFsczogMTggfSxcbiAgICBycGNVcmxzOiB7XG4gICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAgIGh0dHA6IFsnaHR0cHM6Ly9ycGMtYW1veS5wb2x5Z29uLnRlY2hub2xvZ3knXSxcbiAgICAgICAgfSxcbiAgICB9LFxuICAgIGJsb2NrRXhwbG9yZXJzOiB7XG4gICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAgIG5hbWU6ICdQb2x5Z29uU2NhbicsXG4gICAgICAgICAgICB1cmw6ICdodHRwczovL2Ftb3kucG9seWdvbnNjYW4uY29tJyxcbiAgICAgICAgICAgIGFwaVVybDogJ2h0dHBzOi8vYXBpLWFtb3kucG9seWdvbnNjYW4uY29tL2FwaScsXG4gICAgICAgIH0sXG4gICAgfSxcbiAgICBjb250cmFjdHM6IHtcbiAgICAgICAgbXVsdGljYWxsMzoge1xuICAgICAgICAgICAgYWRkcmVzczogJzB4Y2ExMWJkZTA1OTc3YjM2MzExNjcwMjg4NjJiZTJhMTczOTc2Y2ExMScsXG4gICAgICAgICAgICBibG9ja0NyZWF0ZWQ6IDMxMjczODgsXG4gICAgICAgIH0sXG4gICAgfSxcbiAgICB0ZXN0bmV0OiB0cnVlLFxufSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wb2x5Z29uQW1veS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/chains/definitions/polygonAmoy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/clients/createClient.js":
/*!********************************************************!*\
  !*** ./node_modules/viem/_esm/clients/createClient.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   rpcSchema: () => (/* binding */ rpcSchema)\n/* harmony export */ });\n/* harmony import */ var _accounts_utils_parseAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../accounts/utils/parseAccount.js */ \"(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/uid.js */ \"(ssr)/./node_modules/viem/_esm/utils/uid.js\");\n\n\nfunction createClient(parameters) {\n    const { batch, cacheTime = parameters.pollingInterval ?? 4_000, ccipRead, key = 'base', name = 'Base Client', pollingInterval = 4_000, type = 'base', } = parameters;\n    const chain = parameters.chain;\n    const account = parameters.account\n        ? (0,_accounts_utils_parseAccount_js__WEBPACK_IMPORTED_MODULE_0__.parseAccount)(parameters.account)\n        : undefined;\n    const { config, request, value } = parameters.transport({\n        chain,\n        pollingInterval,\n    });\n    const transport = { ...config, ...value };\n    const client = {\n        account,\n        batch,\n        cacheTime,\n        ccipRead,\n        chain,\n        key,\n        name,\n        pollingInterval,\n        request,\n        transport,\n        type,\n        uid: (0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_1__.uid)(),\n    };\n    function extend(base) {\n        return (extendFn) => {\n            const extended = extendFn(base);\n            for (const key in client)\n                delete extended[key];\n            const combined = { ...base, ...extended };\n            return Object.assign(combined, { extend: extend(combined) });\n        };\n    }\n    return Object.assign(client, { extend: extend(client) });\n}\n/**\n * Defines a typed JSON-RPC schema for the client.\n * Note: This is a runtime noop function.\n */\nfunction rpcSchema() {\n    return null;\n}\n//# sourceMappingURL=createClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/clients/createClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/clients/transports/createTransport.js":
/*!**********************************************************************!*\
  !*** ./node_modules/viem/_esm/clients/transports/createTransport.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTransport: () => (/* binding */ createTransport)\n/* harmony export */ });\n/* harmony import */ var _utils_buildRequest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/buildRequest.js */ \"(ssr)/./node_modules/viem/_esm/utils/buildRequest.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/uid.js */ \"(ssr)/./node_modules/viem/_esm/utils/uid.js\");\n\n\n/**\n * @description Creates an transport intended to be used with a client.\n */\nfunction createTransport({ key, methods, name, request, retryCount = 3, retryDelay = 150, timeout, type, }, value) {\n    const uid = (0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_0__.uid)();\n    return {\n        config: {\n            key,\n            methods,\n            name,\n            request,\n            retryCount,\n            retryDelay,\n            timeout,\n            type,\n        },\n        request: (0,_utils_buildRequest_js__WEBPACK_IMPORTED_MODULE_1__.buildRequest)(request, { methods, retryCount, retryDelay, uid }),\n        value,\n    };\n}\n//# sourceMappingURL=createTransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2NsaWVudHMvdHJhbnNwb3J0cy9jcmVhdGVUcmFuc3BvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ1Y7QUFDakQ7QUFDQTtBQUNBO0FBQ08sMkJBQTJCLCtFQUErRTtBQUNqSCxnQkFBZ0Isa0RBQUk7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsaUJBQWlCLG9FQUFZLFlBQVksc0NBQXNDO0FBQy9FO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXGNsaWVudHNcXHRyYW5zcG9ydHNcXGNyZWF0ZVRyYW5zcG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBidWlsZFJlcXVlc3QgfSBmcm9tICcuLi8uLi91dGlscy9idWlsZFJlcXVlc3QuanMnO1xuaW1wb3J0IHsgdWlkIGFzIHVpZF8gfSBmcm9tICcuLi8uLi91dGlscy91aWQuanMnO1xuLyoqXG4gKiBAZGVzY3JpcHRpb24gQ3JlYXRlcyBhbiB0cmFuc3BvcnQgaW50ZW5kZWQgdG8gYmUgdXNlZCB3aXRoIGEgY2xpZW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlVHJhbnNwb3J0KHsga2V5LCBtZXRob2RzLCBuYW1lLCByZXF1ZXN0LCByZXRyeUNvdW50ID0gMywgcmV0cnlEZWxheSA9IDE1MCwgdGltZW91dCwgdHlwZSwgfSwgdmFsdWUpIHtcbiAgICBjb25zdCB1aWQgPSB1aWRfKCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY29uZmlnOiB7XG4gICAgICAgICAgICBrZXksXG4gICAgICAgICAgICBtZXRob2RzLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIHJlcXVlc3QsXG4gICAgICAgICAgICByZXRyeUNvdW50LFxuICAgICAgICAgICAgcmV0cnlEZWxheSxcbiAgICAgICAgICAgIHRpbWVvdXQsXG4gICAgICAgICAgICB0eXBlLFxuICAgICAgICB9LFxuICAgICAgICByZXF1ZXN0OiBidWlsZFJlcXVlc3QocmVxdWVzdCwgeyBtZXRob2RzLCByZXRyeUNvdW50LCByZXRyeURlbGF5LCB1aWQgfSksXG4gICAgICAgIHZhbHVlLFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGVUcmFuc3BvcnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/clients/transports/createTransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/clients/transports/http.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/clients/transports/http.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   http: () => (/* binding */ http)\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../errors/request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _errors_transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors/transport.js */ \"(ssr)/./node_modules/viem/_esm/errors/transport.js\");\n/* harmony import */ var _utils_promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/promise/createBatchScheduler.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js\");\n/* harmony import */ var _utils_rpc_http_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/rpc/http.js */ \"(ssr)/./node_modules/viem/_esm/utils/rpc/http.js\");\n/* harmony import */ var _createTransport_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTransport.js */ \"(ssr)/./node_modules/viem/_esm/clients/transports/createTransport.js\");\n\n\n\n\n\n/**\n * @description Creates a HTTP transport that connects to a JSON-RPC API.\n */\nfunction http(\n/** URL of the JSON-RPC API. Defaults to the chain's public RPC URL. */\nurl, config = {}) {\n    const { batch, fetchOptions, key = 'http', methods, name = 'HTTP JSON-RPC', onFetchRequest, onFetchResponse, retryDelay, raw, } = config;\n    return ({ chain, retryCount: retryCount_, timeout: timeout_ }) => {\n        const { batchSize = 1000, wait = 0 } = typeof batch === 'object' ? batch : {};\n        const retryCount = config.retryCount ?? retryCount_;\n        const timeout = timeout_ ?? config.timeout ?? 10_000;\n        const url_ = url || chain?.rpcUrls.default.http[0];\n        if (!url_)\n            throw new _errors_transport_js__WEBPACK_IMPORTED_MODULE_0__.UrlRequiredError();\n        const rpcClient = (0,_utils_rpc_http_js__WEBPACK_IMPORTED_MODULE_1__.getHttpRpcClient)(url_, {\n            fetchOptions,\n            onRequest: onFetchRequest,\n            onResponse: onFetchResponse,\n            timeout,\n        });\n        return (0,_createTransport_js__WEBPACK_IMPORTED_MODULE_2__.createTransport)({\n            key,\n            methods,\n            name,\n            async request({ method, params }) {\n                const body = { method, params };\n                const { schedule } = (0,_utils_promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_3__.createBatchScheduler)({\n                    id: url_,\n                    wait,\n                    shouldSplitBatch(requests) {\n                        return requests.length > batchSize;\n                    },\n                    fn: (body) => rpcClient.request({\n                        body,\n                    }),\n                    sort: (a, b) => a.id - b.id,\n                });\n                const fn = async (body) => batch\n                    ? schedule(body)\n                    : [\n                        await rpcClient.request({\n                            body,\n                        }),\n                    ];\n                const [{ error, result }] = await fn(body);\n                if (raw)\n                    return { error, result };\n                if (error)\n                    throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_4__.RpcRequestError({\n                        body,\n                        error,\n                        url: url_,\n                    });\n                return result;\n            },\n            retryCount,\n            retryDelay,\n            timeout,\n            type: 'http',\n        }, {\n            fetchOptions,\n            url: url_,\n        });\n    };\n}\n//# sourceMappingURL=http.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/clients/transports/http.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/address.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/errors/address.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAddressError: () => (/* binding */ InvalidAddressError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass InvalidAddressError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address }) {\n        super(`Address \"${address}\" is invalid.`, {\n            metaMessages: [\n                '- Address must be a hex value of 20 bytes (40 hex characters).',\n                '- Address must match its checksum counterpart.',\n            ],\n            name: 'InvalidAddressError',\n        });\n    }\n}\n//# sourceMappingURL=address.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy9hZGRyZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQy9CLGtDQUFrQywrQ0FBUztBQUNsRCxrQkFBa0IsU0FBUztBQUMzQiwwQkFBMEIsUUFBUTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFx2aWVtXFxfZXNtXFxlcnJvcnNcXGFkZHJlc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBJbnZhbGlkQWRkcmVzc0Vycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGFkZHJlc3MgfSkge1xuICAgICAgICBzdXBlcihgQWRkcmVzcyBcIiR7YWRkcmVzc31cIiBpcyBpbnZhbGlkLmAsIHtcbiAgICAgICAgICAgIG1ldGFNZXNzYWdlczogW1xuICAgICAgICAgICAgICAgICctIEFkZHJlc3MgbXVzdCBiZSBhIGhleCB2YWx1ZSBvZiAyMCBieXRlcyAoNDAgaGV4IGNoYXJhY3RlcnMpLicsXG4gICAgICAgICAgICAgICAgJy0gQWRkcmVzcyBtdXN0IG1hdGNoIGl0cyBjaGVja3N1bSBjb3VudGVycGFydC4nLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIG5hbWU6ICdJbnZhbGlkQWRkcmVzc0Vycm9yJyxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWRkcmVzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/address.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/base.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/base.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError),\n/* harmony export */   setErrorConfig: () => (/* binding */ setErrorConfig)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/viem/_esm/errors/version.js\");\n\nlet errorConfig = {\n    getDocsUrl: ({ docsBaseUrl, docsPath = '', docsSlug, }) => docsPath\n        ? `${docsBaseUrl ?? 'https://viem.sh'}${docsPath}${docsSlug ? `#${docsSlug}` : ''}`\n        : undefined,\n    version: `viem@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`,\n};\nfunction setErrorConfig(config) {\n    errorConfig = config;\n}\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = (() => {\n            if (args.cause instanceof BaseError)\n                return args.cause.details;\n            if (args.cause?.message)\n                return args.cause.message;\n            return args.details;\n        })();\n        const docsPath = (() => {\n            if (args.cause instanceof BaseError)\n                return args.cause.docsPath || args.docsPath;\n            return args.docsPath;\n        })();\n        const docsUrl = errorConfig.getDocsUrl?.({ ...args, docsPath });\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsUrl ? [`Docs: ${docsUrl}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            ...(errorConfig.version ? [`Version: ${errorConfig.version}`] : []),\n        ].join('\\n');\n        super(message, args.cause ? { cause: args.cause } : undefined);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"version\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'BaseError'\n        });\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.name = args.name ?? this.name;\n        this.shortMessage = shortMessage;\n        this.version = _version_js__WEBPACK_IMPORTED_MODULE_0__.version;\n    }\n    walk(fn) {\n        return walk(this, fn);\n    }\n}\nfunction walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err &&\n        typeof err === 'object' &&\n        'cause' in err &&\n        err.cause !== undefined)\n        return walk(err.cause, fn);\n    return fn ? null : err;\n}\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/data.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/data.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidBytesLengthError: () => (/* binding */ InvalidBytesLengthError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass SliceOffsetOutOfBoundsError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \"${offset}\" is out-of-bounds (size: ${size}).`, { name: 'SliceOffsetOutOfBoundsError' });\n    }\n}\nclass SizeExceedsPaddingSizeError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (${size}) exceeds padding size (${targetSize}).`, { name: 'SizeExceedsPaddingSizeError' });\n    }\n}\nclass InvalidBytesLengthError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} is expected to be ${targetSize} ${type} long, but is ${size} ${type} long.`, { name: 'InvalidBytesLengthError' });\n    }\n}\n//# sourceMappingURL=data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/encoding.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/errors/encoding.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegerOutOfRangeError: () => (/* binding */ IntegerOutOfRangeError),\n/* harmony export */   InvalidBytesBooleanError: () => (/* binding */ InvalidBytesBooleanError),\n/* harmony export */   InvalidHexBooleanError: () => (/* binding */ InvalidHexBooleanError),\n/* harmony export */   InvalidHexValueError: () => (/* binding */ InvalidHexValueError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass IntegerOutOfRangeError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ max, min, signed, size, value, }) {\n        super(`Number \"${value}\" is not in safe ${size ? `${size * 8}-bit ${signed ? 'signed' : 'unsigned'} ` : ''}integer range ${max ? `(${min} to ${max})` : `(above ${min})`}`, { name: 'IntegerOutOfRangeError' });\n    }\n}\nclass InvalidBytesBooleanError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(bytes) {\n        super(`Bytes value \"${bytes}\" is not a valid boolean. The bytes array must contain a single byte of either a 0 or 1 value.`, {\n            name: 'InvalidBytesBooleanError',\n        });\n    }\n}\nclass InvalidHexBooleanError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(hex) {\n        super(`Hex value \"${hex}\" is not a valid boolean. The hex value must be \"0x0\" (false) or \"0x1\" (true).`, { name: 'InvalidHexBooleanError' });\n    }\n}\nclass InvalidHexValueError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(value) {\n        super(`Hex value \"${value}\" is an odd length (${value.length}). It must be an even length.`, { name: 'InvalidHexValueError' });\n    }\n}\nclass SizeOverflowError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed ${maxSize} bytes. Given size: ${givenSize} bytes.`, { name: 'SizeOverflowError' });\n    }\n}\n//# sourceMappingURL=encoding.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/encoding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/request.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/errors/request.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpRequestError: () => (/* binding */ HttpRequestError),\n/* harmony export */   RpcRequestError: () => (/* binding */ RpcRequestError),\n/* harmony export */   SocketClosedError: () => (/* binding */ SocketClosedError),\n/* harmony export */   TimeoutError: () => (/* binding */ TimeoutError),\n/* harmony export */   WebSocketRequestError: () => (/* binding */ WebSocketRequestError)\n/* harmony export */ });\n/* harmony import */ var _utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/stringify.js */ \"(ssr)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/viem/_esm/errors/utils.js\");\n\n\n\nclass HttpRequestError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, cause, details, headers, status, url, }) {\n        super('HTTP request failed.', {\n            cause,\n            details,\n            metaMessages: [\n                status && `Status: ${status}`,\n                `URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                body && `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`,\n            ].filter(Boolean),\n            name: 'HttpRequestError',\n        });\n        Object.defineProperty(this, \"body\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"headers\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"status\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"url\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.body = body;\n        this.headers = headers;\n        this.status = status;\n        this.url = url;\n    }\n}\nclass WebSocketRequestError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, cause, details, url, }) {\n        super('WebSocket request failed.', {\n            cause,\n            details,\n            metaMessages: [\n                `URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                body && `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`,\n            ].filter(Boolean),\n            name: 'WebSocketRequestError',\n        });\n    }\n}\nclass RpcRequestError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, error, url, }) {\n        super('RPC Request failed.', {\n            cause: error,\n            details: error.message,\n            metaMessages: [`URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`, `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`],\n            name: 'RpcRequestError',\n        });\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"data\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.code = error.code;\n        this.data = error.data;\n    }\n}\nclass SocketClosedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ url, } = {}) {\n        super('The socket has been closed.', {\n            metaMessages: [url && `URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`].filter(Boolean),\n            name: 'SocketClosedError',\n        });\n    }\n}\nclass TimeoutError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, url, }) {\n        super('The request took too long to respond.', {\n            details: 'The request timed out.',\n            metaMessages: [`URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`, `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`],\n            name: 'TimeoutError',\n        });\n    }\n}\n//# sourceMappingURL=request.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/request.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/rpc.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/errors/rpc.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AtomicReadyWalletRejectedUpgradeError: () => (/* binding */ AtomicReadyWalletRejectedUpgradeError),\n/* harmony export */   AtomicityNotSupportedError: () => (/* binding */ AtomicityNotSupportedError),\n/* harmony export */   BundleTooLargeError: () => (/* binding */ BundleTooLargeError),\n/* harmony export */   ChainDisconnectedError: () => (/* binding */ ChainDisconnectedError),\n/* harmony export */   DuplicateIdError: () => (/* binding */ DuplicateIdError),\n/* harmony export */   InternalRpcError: () => (/* binding */ InternalRpcError),\n/* harmony export */   InvalidInputRpcError: () => (/* binding */ InvalidInputRpcError),\n/* harmony export */   InvalidParamsRpcError: () => (/* binding */ InvalidParamsRpcError),\n/* harmony export */   InvalidRequestRpcError: () => (/* binding */ InvalidRequestRpcError),\n/* harmony export */   JsonRpcVersionUnsupportedError: () => (/* binding */ JsonRpcVersionUnsupportedError),\n/* harmony export */   LimitExceededRpcError: () => (/* binding */ LimitExceededRpcError),\n/* harmony export */   MethodNotFoundRpcError: () => (/* binding */ MethodNotFoundRpcError),\n/* harmony export */   MethodNotSupportedRpcError: () => (/* binding */ MethodNotSupportedRpcError),\n/* harmony export */   ParseRpcError: () => (/* binding */ ParseRpcError),\n/* harmony export */   ProviderDisconnectedError: () => (/* binding */ ProviderDisconnectedError),\n/* harmony export */   ProviderRpcError: () => (/* binding */ ProviderRpcError),\n/* harmony export */   ResourceNotFoundRpcError: () => (/* binding */ ResourceNotFoundRpcError),\n/* harmony export */   ResourceUnavailableRpcError: () => (/* binding */ ResourceUnavailableRpcError),\n/* harmony export */   RpcError: () => (/* binding */ RpcError),\n/* harmony export */   SwitchChainError: () => (/* binding */ SwitchChainError),\n/* harmony export */   TransactionRejectedRpcError: () => (/* binding */ TransactionRejectedRpcError),\n/* harmony export */   UnauthorizedProviderError: () => (/* binding */ UnauthorizedProviderError),\n/* harmony export */   UnknownBundleIdError: () => (/* binding */ UnknownBundleIdError),\n/* harmony export */   UnknownRpcError: () => (/* binding */ UnknownRpcError),\n/* harmony export */   UnsupportedChainIdError: () => (/* binding */ UnsupportedChainIdError),\n/* harmony export */   UnsupportedNonOptionalCapabilityError: () => (/* binding */ UnsupportedNonOptionalCapabilityError),\n/* harmony export */   UnsupportedProviderMethodError: () => (/* binding */ UnsupportedProviderMethodError),\n/* harmony export */   UserRejectedRequestError: () => (/* binding */ UserRejectedRequestError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n\n\nconst unknownErrorCode = -1;\nclass RpcError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(cause, { code, docsPath, metaMessages, name, shortMessage, }) {\n        super(shortMessage, {\n            cause,\n            docsPath,\n            metaMessages: metaMessages || cause?.metaMessages,\n            name: name || 'RpcError',\n        });\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = name || cause.name;\n        this.code = (cause instanceof _request_js__WEBPACK_IMPORTED_MODULE_1__.RpcRequestError ? cause.code : (code ?? unknownErrorCode));\n    }\n}\nclass ProviderRpcError extends RpcError {\n    constructor(cause, options) {\n        super(cause, options);\n        Object.defineProperty(this, \"data\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.data = options.data;\n    }\n}\nclass ParseRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ParseRpcError.code,\n            name: 'ParseRpcError',\n            shortMessage: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n        });\n    }\n}\nObject.defineProperty(ParseRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32700\n});\nclass InvalidRequestRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InvalidRequestRpcError.code,\n            name: 'InvalidRequestRpcError',\n            shortMessage: 'JSON is not a valid request object.',\n        });\n    }\n}\nObject.defineProperty(InvalidRequestRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32600\n});\nclass MethodNotFoundRpcError extends RpcError {\n    constructor(cause, { method } = {}) {\n        super(cause, {\n            code: MethodNotFoundRpcError.code,\n            name: 'MethodNotFoundRpcError',\n            shortMessage: `The method${method ? ` \"${method}\"` : ''} does not exist / is not available.`,\n        });\n    }\n}\nObject.defineProperty(MethodNotFoundRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32601\n});\nclass InvalidParamsRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InvalidParamsRpcError.code,\n            name: 'InvalidParamsRpcError',\n            shortMessage: [\n                'Invalid parameters were provided to the RPC method.',\n                'Double check you have provided the correct parameters.',\n            ].join('\\n'),\n        });\n    }\n}\nObject.defineProperty(InvalidParamsRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32602\n});\nclass InternalRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InternalRpcError.code,\n            name: 'InternalRpcError',\n            shortMessage: 'An internal error was received.',\n        });\n    }\n}\nObject.defineProperty(InternalRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32603\n});\nclass InvalidInputRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InvalidInputRpcError.code,\n            name: 'InvalidInputRpcError',\n            shortMessage: [\n                'Missing or invalid parameters.',\n                'Double check you have provided the correct parameters.',\n            ].join('\\n'),\n        });\n    }\n}\nObject.defineProperty(InvalidInputRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32000\n});\nclass ResourceNotFoundRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ResourceNotFoundRpcError.code,\n            name: 'ResourceNotFoundRpcError',\n            shortMessage: 'Requested resource not found.',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ResourceNotFoundRpcError'\n        });\n    }\n}\nObject.defineProperty(ResourceNotFoundRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32001\n});\nclass ResourceUnavailableRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ResourceUnavailableRpcError.code,\n            name: 'ResourceUnavailableRpcError',\n            shortMessage: 'Requested resource not available.',\n        });\n    }\n}\nObject.defineProperty(ResourceUnavailableRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32002\n});\nclass TransactionRejectedRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: TransactionRejectedRpcError.code,\n            name: 'TransactionRejectedRpcError',\n            shortMessage: 'Transaction creation failed.',\n        });\n    }\n}\nObject.defineProperty(TransactionRejectedRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32003\n});\nclass MethodNotSupportedRpcError extends RpcError {\n    constructor(cause, { method } = {}) {\n        super(cause, {\n            code: MethodNotSupportedRpcError.code,\n            name: 'MethodNotSupportedRpcError',\n            shortMessage: `Method${method ? ` \"${method}\"` : ''} is not supported.`,\n        });\n    }\n}\nObject.defineProperty(MethodNotSupportedRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32004\n});\nclass LimitExceededRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: LimitExceededRpcError.code,\n            name: 'LimitExceededRpcError',\n            shortMessage: 'Request exceeds defined limit.',\n        });\n    }\n}\nObject.defineProperty(LimitExceededRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32005\n});\nclass JsonRpcVersionUnsupportedError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: JsonRpcVersionUnsupportedError.code,\n            name: 'JsonRpcVersionUnsupportedError',\n            shortMessage: 'Version of JSON-RPC protocol is not supported.',\n        });\n    }\n}\nObject.defineProperty(JsonRpcVersionUnsupportedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32006\n});\nclass UserRejectedRequestError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UserRejectedRequestError.code,\n            name: 'UserRejectedRequestError',\n            shortMessage: 'User rejected the request.',\n        });\n    }\n}\nObject.defineProperty(UserRejectedRequestError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4001\n});\nclass UnauthorizedProviderError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnauthorizedProviderError.code,\n            name: 'UnauthorizedProviderError',\n            shortMessage: 'The requested method and/or account has not been authorized by the user.',\n        });\n    }\n}\nObject.defineProperty(UnauthorizedProviderError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4100\n});\nclass UnsupportedProviderMethodError extends ProviderRpcError {\n    constructor(cause, { method } = {}) {\n        super(cause, {\n            code: UnsupportedProviderMethodError.code,\n            name: 'UnsupportedProviderMethodError',\n            shortMessage: `The Provider does not support the requested method${method ? ` \" ${method}\"` : ''}.`,\n        });\n    }\n}\nObject.defineProperty(UnsupportedProviderMethodError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4200\n});\nclass ProviderDisconnectedError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ProviderDisconnectedError.code,\n            name: 'ProviderDisconnectedError',\n            shortMessage: 'The Provider is disconnected from all chains.',\n        });\n    }\n}\nObject.defineProperty(ProviderDisconnectedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4900\n});\nclass ChainDisconnectedError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ChainDisconnectedError.code,\n            name: 'ChainDisconnectedError',\n            shortMessage: 'The Provider is not connected to the requested chain.',\n        });\n    }\n}\nObject.defineProperty(ChainDisconnectedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4901\n});\nclass SwitchChainError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: SwitchChainError.code,\n            name: 'SwitchChainError',\n            shortMessage: 'An error occurred when attempting to switch chain.',\n        });\n    }\n}\nObject.defineProperty(SwitchChainError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4902\n});\nclass UnsupportedNonOptionalCapabilityError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnsupportedNonOptionalCapabilityError.code,\n            name: 'UnsupportedNonOptionalCapabilityError',\n            shortMessage: 'This Wallet does not support a capability that was not marked as optional.',\n        });\n    }\n}\nObject.defineProperty(UnsupportedNonOptionalCapabilityError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5700\n});\nclass UnsupportedChainIdError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnsupportedChainIdError.code,\n            name: 'UnsupportedChainIdError',\n            shortMessage: 'This Wallet does not support the requested chain ID.',\n        });\n    }\n}\nObject.defineProperty(UnsupportedChainIdError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5710\n});\nclass DuplicateIdError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: DuplicateIdError.code,\n            name: 'DuplicateIdError',\n            shortMessage: 'There is already a bundle submitted with this ID.',\n        });\n    }\n}\nObject.defineProperty(DuplicateIdError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5720\n});\nclass UnknownBundleIdError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnknownBundleIdError.code,\n            name: 'UnknownBundleIdError',\n            shortMessage: 'This bundle id is unknown / has not been submitted',\n        });\n    }\n}\nObject.defineProperty(UnknownBundleIdError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5730\n});\nclass BundleTooLargeError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: BundleTooLargeError.code,\n            name: 'BundleTooLargeError',\n            shortMessage: 'The call bundle is too large for the Wallet to process.',\n        });\n    }\n}\nObject.defineProperty(BundleTooLargeError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5740\n});\nclass AtomicReadyWalletRejectedUpgradeError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: AtomicReadyWalletRejectedUpgradeError.code,\n            name: 'AtomicReadyWalletRejectedUpgradeError',\n            shortMessage: 'The Wallet can support atomicity after an upgrade, but the user rejected the upgrade.',\n        });\n    }\n}\nObject.defineProperty(AtomicReadyWalletRejectedUpgradeError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5750\n});\nclass AtomicityNotSupportedError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: AtomicityNotSupportedError.code,\n            name: 'AtomicityNotSupportedError',\n            shortMessage: 'The wallet does not support atomic execution but the request requires it.',\n        });\n    }\n}\nObject.defineProperty(AtomicityNotSupportedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5760\n});\nclass UnknownRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            name: 'UnknownRpcError',\n            shortMessage: 'An unknown RPC error occurred.',\n        });\n    }\n}\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/rpc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/transport.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/errors/transport.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UrlRequiredError: () => (/* binding */ UrlRequiredError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass UrlRequiredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('No URL was provided to the Transport. Please provide a valid RPC URL to the Transport.', {\n            docsPath: '/docs/clients/intro',\n            name: 'UrlRequiredError',\n        });\n    }\n}\n//# sourceMappingURL=transport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy90cmFuc3BvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0M7QUFDL0IsK0JBQStCLCtDQUFTO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFx2aWVtXFxfZXNtXFxlcnJvcnNcXHRyYW5zcG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlRXJyb3IgfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNsYXNzIFVybFJlcXVpcmVkRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcignTm8gVVJMIHdhcyBwcm92aWRlZCB0byB0aGUgVHJhbnNwb3J0LiBQbGVhc2UgcHJvdmlkZSBhIHZhbGlkIFJQQyBVUkwgdG8gdGhlIFRyYW5zcG9ydC4nLCB7XG4gICAgICAgICAgICBkb2NzUGF0aDogJy9kb2NzL2NsaWVudHMvaW50cm8nLFxuICAgICAgICAgICAgbmFtZTogJ1VybFJlcXVpcmVkRXJyb3InLFxuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFuc3BvcnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/utils.js":
/*!************************************************!*\
  !*** ./node_modules/viem/_esm/errors/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getContractAddress: () => (/* binding */ getContractAddress),\n/* harmony export */   getUrl: () => (/* binding */ getUrl)\n/* harmony export */ });\nconst getContractAddress = (address) => address;\nconst getUrl = (url) => url;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFx2aWVtXFxfZXNtXFxlcnJvcnNcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRDb250cmFjdEFkZHJlc3MgPSAoYWRkcmVzcykgPT4gYWRkcmVzcztcbmV4cG9ydCBjb25zdCBnZXRVcmwgPSAodXJsKSA9PiB1cmw7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/version.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/errors/version.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.30.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXGVycm9yc1xcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjMwLjEnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js":
/*!************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/address/getAddress.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checksumAddress: () => (/* binding */ checksumAddress),\n/* harmony export */   getAddress: () => (/* binding */ getAddress)\n/* harmony export */ });\n/* harmony import */ var _errors_address_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../errors/address.js */ \"(ssr)/./node_modules/viem/_esm/errors/address.js\");\n/* harmony import */ var _encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../encoding/toBytes.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\");\n/* harmony import */ var _hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hash/keccak256.js */ \"(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var _lru_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lru.js */ \"(ssr)/./node_modules/viem/_esm/utils/lru.js\");\n/* harmony import */ var _isAddress_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./isAddress.js */ \"(ssr)/./node_modules/viem/_esm/utils/address/isAddress.js\");\n\n\n\n\n\nconst checksumAddressCache = /*#__PURE__*/ new _lru_js__WEBPACK_IMPORTED_MODULE_0__.LruMap(8192);\nfunction checksumAddress(address_, \n/**\n * Warning: EIP-1191 checksum addresses are generally not backwards compatible with the\n * wider Ethereum ecosystem, meaning it will break when validated against an application/tool\n * that relies on EIP-55 checksum encoding (checksum without chainId).\n *\n * It is highly recommended to not use this feature unless you\n * know what you are doing.\n *\n * See more: https://github.com/ethereum/EIPs/issues/1121\n */\nchainId) {\n    if (checksumAddressCache.has(`${address_}.${chainId}`))\n        return checksumAddressCache.get(`${address_}.${chainId}`);\n    const hexAddress = chainId\n        ? `${chainId}${address_.toLowerCase()}`\n        : address_.substring(2).toLowerCase();\n    const hash = (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)((0,_encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.stringToBytes)(hexAddress), 'bytes');\n    const address = (chainId ? hexAddress.substring(`${chainId}0x`.length) : hexAddress).split('');\n    for (let i = 0; i < 40; i += 2) {\n        if (hash[i >> 1] >> 4 >= 8 && address[i]) {\n            address[i] = address[i].toUpperCase();\n        }\n        if ((hash[i >> 1] & 0x0f) >= 8 && address[i + 1]) {\n            address[i + 1] = address[i + 1].toUpperCase();\n        }\n    }\n    const result = `0x${address.join('')}`;\n    checksumAddressCache.set(`${address_}.${chainId}`, result);\n    return result;\n}\nfunction getAddress(address, \n/**\n * Warning: EIP-1191 checksum addresses are generally not backwards compatible with the\n * wider Ethereum ecosystem, meaning it will break when validated against an application/tool\n * that relies on EIP-55 checksum encoding (checksum without chainId).\n *\n * It is highly recommended to not use this feature unless you\n * know what you are doing.\n *\n * See more: https://github.com/ethereum/EIPs/issues/1121\n */\nchainId) {\n    if (!(0,_isAddress_js__WEBPACK_IMPORTED_MODULE_3__.isAddress)(address, { strict: false }))\n        throw new _errors_address_js__WEBPACK_IMPORTED_MODULE_4__.InvalidAddressError({ address });\n    return checksumAddress(address, chainId);\n}\n//# sourceMappingURL=getAddress.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/address/isAddress.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/address/isAddress.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAddress: () => (/* binding */ isAddress),\n/* harmony export */   isAddressCache: () => (/* binding */ isAddressCache)\n/* harmony export */ });\n/* harmony import */ var _lru_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lru.js */ \"(ssr)/./node_modules/viem/_esm/utils/lru.js\");\n/* harmony import */ var _getAddress_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getAddress.js */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n\n\nconst addressRegex = /^0x[a-fA-F0-9]{40}$/;\n/** @internal */\nconst isAddressCache = /*#__PURE__*/ new _lru_js__WEBPACK_IMPORTED_MODULE_0__.LruMap(8192);\nfunction isAddress(address, options) {\n    const { strict = true } = options ?? {};\n    const cacheKey = `${address}.${strict}`;\n    if (isAddressCache.has(cacheKey))\n        return isAddressCache.get(cacheKey);\n    const result = (() => {\n        if (!addressRegex.test(address))\n            return false;\n        if (address.toLowerCase() === address)\n            return true;\n        if (strict)\n            return (0,_getAddress_js__WEBPACK_IMPORTED_MODULE_1__.checksumAddress)(address) === address;\n        return true;\n    })();\n    isAddressCache.set(cacheKey, result);\n    return result;\n}\n//# sourceMappingURL=isAddress.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2FkZHJlc3MvaXNBZGRyZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUM7QUFDZTtBQUNsRCxxQ0FBcUMsR0FBRztBQUN4QztBQUNPLHlDQUF5QywyQ0FBTTtBQUMvQztBQUNQLFlBQVksZ0JBQWdCO0FBQzVCLHdCQUF3QixRQUFRLEdBQUcsT0FBTztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLCtEQUFlO0FBQ2xDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxhZGRyZXNzXFxpc0FkZHJlc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTHJ1TWFwIH0gZnJvbSAnLi4vbHJ1LmpzJztcbmltcG9ydCB7IGNoZWNrc3VtQWRkcmVzcyB9IGZyb20gJy4vZ2V0QWRkcmVzcy5qcyc7XG5jb25zdCBhZGRyZXNzUmVnZXggPSAvXjB4W2EtZkEtRjAtOV17NDB9JC87XG4vKiogQGludGVybmFsICovXG5leHBvcnQgY29uc3QgaXNBZGRyZXNzQ2FjaGUgPSAvKiNfX1BVUkVfXyovIG5ldyBMcnVNYXAoODE5Mik7XG5leHBvcnQgZnVuY3Rpb24gaXNBZGRyZXNzKGFkZHJlc3MsIG9wdGlvbnMpIHtcbiAgICBjb25zdCB7IHN0cmljdCA9IHRydWUgfSA9IG9wdGlvbnMgPz8ge307XG4gICAgY29uc3QgY2FjaGVLZXkgPSBgJHthZGRyZXNzfS4ke3N0cmljdH1gO1xuICAgIGlmIChpc0FkZHJlc3NDYWNoZS5oYXMoY2FjaGVLZXkpKVxuICAgICAgICByZXR1cm4gaXNBZGRyZXNzQ2FjaGUuZ2V0KGNhY2hlS2V5KTtcbiAgICBjb25zdCByZXN1bHQgPSAoKCkgPT4ge1xuICAgICAgICBpZiAoIWFkZHJlc3NSZWdleC50ZXN0KGFkZHJlc3MpKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICBpZiAoYWRkcmVzcy50b0xvd2VyQ2FzZSgpID09PSBhZGRyZXNzKVxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIGlmIChzdHJpY3QpXG4gICAgICAgICAgICByZXR1cm4gY2hlY2tzdW1BZGRyZXNzKGFkZHJlc3MpID09PSBhZGRyZXNzO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KSgpO1xuICAgIGlzQWRkcmVzc0NhY2hlLnNldChjYWNoZUtleSwgcmVzdWx0KTtcbiAgICByZXR1cm4gcmVzdWx0O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXNBZGRyZXNzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/address/isAddress.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/buildRequest.js":
/*!******************************************************!*\
  !*** ./node_modules/viem/_esm/utils/buildRequest.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildRequest: () => (/* binding */ buildRequest),\n/* harmony export */   shouldRetry: () => (/* binding */ shouldRetry)\n/* harmony export */ });\n/* harmony import */ var _errors_base_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/rpc.js */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./encoding/toHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _promise_withDedupe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./promise/withDedupe.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withDedupe.js\");\n/* harmony import */ var _promise_withRetry_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./promise/withRetry.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\nfunction buildRequest(request, options = {}) {\n    return async (args, overrideOptions = {}) => {\n        const { dedupe = false, methods, retryDelay = 150, retryCount = 3, uid, } = {\n            ...options,\n            ...overrideOptions,\n        };\n        const { method } = args;\n        if (methods?.exclude?.includes(method))\n            throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError(new Error('method not supported'), {\n                method,\n            });\n        if (methods?.include && !methods.include.includes(method))\n            throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError(new Error('method not supported'), {\n                method,\n            });\n        const requestId = dedupe\n            ? (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_1__.stringToHex)(`${uid}.${(0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(args)}`)\n            : undefined;\n        return (0,_promise_withDedupe_js__WEBPACK_IMPORTED_MODULE_3__.withDedupe)(() => (0,_promise_withRetry_js__WEBPACK_IMPORTED_MODULE_4__.withRetry)(async () => {\n            try {\n                return await request(args);\n            }\n            catch (err_) {\n                const err = err_;\n                switch (err.code) {\n                    // -32700\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ParseRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ParseRpcError(err);\n                    // -32600\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidRequestRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidRequestRpcError(err);\n                    // -32601\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotFoundRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotFoundRpcError(err, { method: args.method });\n                    // -32602\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidParamsRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidParamsRpcError(err);\n                    // -32603\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InternalRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InternalRpcError(err);\n                    // -32000\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidInputRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidInputRpcError(err);\n                    // -32001\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceNotFoundRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceNotFoundRpcError(err);\n                    // -32002\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceUnavailableRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceUnavailableRpcError(err);\n                    // -32003\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.TransactionRejectedRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.TransactionRejectedRpcError(err);\n                    // -32004\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError(err, {\n                            method: args.method,\n                        });\n                    // -32005\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.LimitExceededRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.LimitExceededRpcError(err);\n                    // -32006\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.JsonRpcVersionUnsupportedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.JsonRpcVersionUnsupportedError(err);\n                    // 4001\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UserRejectedRequestError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UserRejectedRequestError(err);\n                    // 4100\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnauthorizedProviderError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnauthorizedProviderError(err);\n                    // 4200\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedProviderMethodError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedProviderMethodError(err);\n                    // 4900\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ProviderDisconnectedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ProviderDisconnectedError(err);\n                    // 4901\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ChainDisconnectedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ChainDisconnectedError(err);\n                    // 4902\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainError(err);\n                    // 5700\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedNonOptionalCapabilityError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedNonOptionalCapabilityError(err);\n                    // 5710\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedChainIdError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedChainIdError(err);\n                    // 5720\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.DuplicateIdError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.DuplicateIdError(err);\n                    // 5730\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnknownBundleIdError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnknownBundleIdError(err);\n                    // 5740\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.BundleTooLargeError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.BundleTooLargeError(err);\n                    // 5750\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicReadyWalletRejectedUpgradeError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicReadyWalletRejectedUpgradeError(err);\n                    // 5760\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicityNotSupportedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicityNotSupportedError(err);\n                    // CAIP-25: User Rejected Error\n                    // https://docs.walletconnect.com/2.0/specs/clients/sign/error-codes#rejected-caip-25\n                    case 5000:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UserRejectedRequestError(err);\n                    default:\n                        if (err_ instanceof _errors_base_js__WEBPACK_IMPORTED_MODULE_5__.BaseError)\n                            throw err_;\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnknownRpcError(err);\n                }\n            }\n        }, {\n            delay: ({ count, error }) => {\n                // If we find a Retry-After header, let's retry after the given time.\n                if (error && error instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_6__.HttpRequestError) {\n                    const retryAfter = error?.headers?.get('Retry-After');\n                    if (retryAfter?.match(/\\d/))\n                        return Number.parseInt(retryAfter) * 1000;\n                }\n                // Otherwise, let's retry with an exponential backoff.\n                return ~~(1 << count) * retryDelay;\n            },\n            retryCount,\n            shouldRetry: ({ error }) => shouldRetry(error),\n        }), { enabled: dedupe, id: requestId });\n    };\n}\n/** @internal */\nfunction shouldRetry(error) {\n    if ('code' in error && typeof error.code === 'number') {\n        if (error.code === -1)\n            return true; // Unknown error\n        if (error.code === _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.LimitExceededRpcError.code)\n            return true;\n        if (error.code === _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InternalRpcError.code)\n            return true;\n        return false;\n    }\n    if (error instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_6__.HttpRequestError && error.status) {\n        // Forbidden\n        if (error.status === 403)\n            return true;\n        // Request Timeout\n        if (error.status === 408)\n            return true;\n        // Request Entity Too Large\n        if (error.status === 413)\n            return true;\n        // Too Many Requests\n        if (error.status === 429)\n            return true;\n        // Internal Server Error\n        if (error.status === 500)\n            return true;\n        // Bad Gateway\n        if (error.status === 502)\n            return true;\n        // Service Unavailable\n        if (error.status === 503)\n            return true;\n        // Gateway Timeout\n        if (error.status === 504)\n            return true;\n        return false;\n    }\n    return true;\n}\n//# sourceMappingURL=buildRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/buildRequest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/chain/defineChain.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defineChain: () => (/* binding */ defineChain)\n/* harmony export */ });\nfunction defineChain(chain) {\n    return {\n        formatters: undefined,\n        fees: undefined,\n        serializers: undefined,\n        ...chain,\n    };\n}\n//# sourceMappingURL=defineChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2NoYWluL2RlZmluZUNoYWluLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcdXRpbHNcXGNoYWluXFxkZWZpbmVDaGFpbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGVmaW5lQ2hhaW4oY2hhaW4pIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBmb3JtYXR0ZXJzOiB1bmRlZmluZWQsXG4gICAgICAgIGZlZXM6IHVuZGVmaW5lZCxcbiAgICAgICAgc2VyaWFsaXplcnM6IHVuZGVmaW5lZCxcbiAgICAgICAgLi4uY2hhaW4sXG4gICAgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlZmluZUNoYWluLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/isHex.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/isHex.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isHex: () => (/* binding */ isHex)\n/* harmony export */ });\nfunction isHex(value, { strict = true } = {}) {\n    if (!value)\n        return false;\n    if (typeof value !== 'string')\n        return false;\n    return strict ? /^0x[0-9a-fA-F]*$/.test(value) : value.startsWith('0x');\n}\n//# sourceMappingURL=isHex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvaXNIZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLHdCQUF3QixnQkFBZ0IsSUFBSTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFx2aWVtXFxfZXNtXFx1dGlsc1xcZGF0YVxcaXNIZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGlzSGV4KHZhbHVlLCB7IHN0cmljdCA9IHRydWUgfSA9IHt9KSB7XG4gICAgaWYgKCF2YWx1ZSlcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdzdHJpbmcnKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHN0cmljdCA/IC9eMHhbMC05YS1mQS1GXSokLy50ZXN0KHZhbHVlKSA6IHZhbHVlLnN0YXJ0c1dpdGgoJzB4Jyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pc0hleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/pad.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/pad.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   padBytes: () => (/* binding */ padBytes),\n/* harmony export */   padHex: () => (/* binding */ padHex)\n/* harmony export */ });\n/* harmony import */ var _errors_data_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors/data.js */ \"(ssr)/./node_modules/viem/_esm/errors/data.js\");\n\nfunction pad(hexOrBytes, { dir, size = 32 } = {}) {\n    if (typeof hexOrBytes === 'string')\n        return padHex(hexOrBytes, { dir, size });\n    return padBytes(hexOrBytes, { dir, size });\n}\nfunction padHex(hex_, { dir, size = 32 } = {}) {\n    if (size === null)\n        return hex_;\n    const hex = hex_.replace('0x', '');\n    if (hex.length > size * 2)\n        throw new _errors_data_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: Math.ceil(hex.length / 2),\n            targetSize: size,\n            type: 'hex',\n        });\n    return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}`;\n}\nfunction padBytes(bytes, { dir, size = 32 } = {}) {\n    if (size === null)\n        return bytes;\n    if (bytes.length > size)\n        throw new _errors_data_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: bytes.length,\n            targetSize: size,\n            type: 'bytes',\n        });\n    const paddedBytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n        const padEnd = dir === 'right';\n        paddedBytes[padEnd ? i : size - i - 1] =\n            bytes[padEnd ? i : bytes.length - i - 1];\n    }\n    return paddedBytes;\n}\n//# sourceMappingURL=pad.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/pad.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/size.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/size.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _isHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\");\n\n/**\n * @description Retrieves the size of the value (in bytes).\n *\n * @param value The value (hex or byte array) to retrieve the size of.\n * @returns The size of the value (in bytes).\n */\nfunction size(value) {\n    if ((0,_isHex_js__WEBPACK_IMPORTED_MODULE_0__.isHex)(value, { strict: false }))\n        return Math.ceil((value.length - 2) / 2);\n    return value.length;\n}\n//# sourceMappingURL=size.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvc2l6ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFFBQVEsZ0RBQUssVUFBVSxlQUFlO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxkYXRhXFxzaXplLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzSGV4IH0gZnJvbSAnLi9pc0hleC5qcyc7XG4vKipcbiAqIEBkZXNjcmlwdGlvbiBSZXRyaWV2ZXMgdGhlIHNpemUgb2YgdGhlIHZhbHVlIChpbiBieXRlcykuXG4gKlxuICogQHBhcmFtIHZhbHVlIFRoZSB2YWx1ZSAoaGV4IG9yIGJ5dGUgYXJyYXkpIHRvIHJldHJpZXZlIHRoZSBzaXplIG9mLlxuICogQHJldHVybnMgVGhlIHNpemUgb2YgdGhlIHZhbHVlIChpbiBieXRlcykuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzaXplKHZhbHVlKSB7XG4gICAgaWYgKGlzSGV4KHZhbHVlLCB7IHN0cmljdDogZmFsc2UgfSkpXG4gICAgICAgIHJldHVybiBNYXRoLmNlaWwoKHZhbHVlLmxlbmd0aCAtIDIpIC8gMik7XG4gICAgcmV0dXJuIHZhbHVlLmxlbmd0aDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpemUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/size.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/trim.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/trim.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\nfunction trim(hexOrBytes, { dir = 'left' } = {}) {\n    let data = typeof hexOrBytes === 'string' ? hexOrBytes.replace('0x', '') : hexOrBytes;\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    if (typeof hexOrBytes === 'string') {\n        if (data.length === 1 && dir === 'right')\n            data = `${data}0`;\n        return `0x${data.length % 2 === 1 ? `0${data}` : data}`;\n    }\n    return data;\n}\n//# sourceMappingURL=trim.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvdHJpbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sNEJBQTRCLGVBQWUsSUFBSTtBQUN0RDtBQUNBO0FBQ0Esb0JBQW9CLHFCQUFxQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLEtBQUs7QUFDM0Isb0JBQW9CLDRCQUE0QixLQUFLLFNBQVM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcdXRpbHNcXGRhdGFcXHRyaW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHRyaW0oaGV4T3JCeXRlcywgeyBkaXIgPSAnbGVmdCcgfSA9IHt9KSB7XG4gICAgbGV0IGRhdGEgPSB0eXBlb2YgaGV4T3JCeXRlcyA9PT0gJ3N0cmluZycgPyBoZXhPckJ5dGVzLnJlcGxhY2UoJzB4JywgJycpIDogaGV4T3JCeXRlcztcbiAgICBsZXQgc2xpY2VMZW5ndGggPSAwO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGggLSAxOyBpKyspIHtcbiAgICAgICAgaWYgKGRhdGFbZGlyID09PSAnbGVmdCcgPyBpIDogZGF0YS5sZW5ndGggLSBpIC0gMV0udG9TdHJpbmcoKSA9PT0gJzAnKVxuICAgICAgICAgICAgc2xpY2VMZW5ndGgrKztcbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGRhdGEgPVxuICAgICAgICBkaXIgPT09ICdsZWZ0J1xuICAgICAgICAgICAgPyBkYXRhLnNsaWNlKHNsaWNlTGVuZ3RoKVxuICAgICAgICAgICAgOiBkYXRhLnNsaWNlKDAsIGRhdGEubGVuZ3RoIC0gc2xpY2VMZW5ndGgpO1xuICAgIGlmICh0eXBlb2YgaGV4T3JCeXRlcyA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgaWYgKGRhdGEubGVuZ3RoID09PSAxICYmIGRpciA9PT0gJ3JpZ2h0JylcbiAgICAgICAgICAgIGRhdGEgPSBgJHtkYXRhfTBgO1xuICAgICAgICByZXR1cm4gYDB4JHtkYXRhLmxlbmd0aCAlIDIgPT09IDEgPyBgMCR7ZGF0YX1gIDogZGF0YX1gO1xuICAgIH1cbiAgICByZXR1cm4gZGF0YTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyaW0uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/trim.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js":
/*!**********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/encoding/fromHex.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   hexToBigInt: () => (/* binding */ hexToBigInt),\n/* harmony export */   hexToBool: () => (/* binding */ hexToBool),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   hexToString: () => (/* binding */ hexToString)\n/* harmony export */ });\n/* harmony import */ var _errors_encoding_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/encoding.js */ \"(ssr)/./node_modules/viem/_esm/errors/encoding.js\");\n/* harmony import */ var _data_size_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/size.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/size.js\");\n/* harmony import */ var _data_trim_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../data/trim.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/trim.js\");\n/* harmony import */ var _toBytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./toBytes.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\");\n\n\n\n\nfunction assertSize(hexOrBytes, { size }) {\n    if ((0,_data_size_js__WEBPACK_IMPORTED_MODULE_0__.size)(hexOrBytes) > size)\n        throw new _errors_encoding_js__WEBPACK_IMPORTED_MODULE_1__.SizeOverflowError({\n            givenSize: (0,_data_size_js__WEBPACK_IMPORTED_MODULE_0__.size)(hexOrBytes),\n            maxSize: size,\n        });\n}\n/**\n * Decodes a hex string into a string, number, bigint, boolean, or byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex\n * - Example: https://viem.sh/docs/utilities/fromHex#usage\n *\n * @param hex Hex string to decode.\n * @param toOrOpts Type to convert to or options.\n * @returns Decoded value.\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x1a4', 'number')\n * // 420\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c6421', 'string')\n * // 'Hello world'\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *   size: 32,\n *   to: 'string'\n * })\n * // 'Hello world'\n */\nfunction fromHex(hex, toOrOpts) {\n    const opts = typeof toOrOpts === 'string' ? { to: toOrOpts } : toOrOpts;\n    const to = opts.to;\n    if (to === 'number')\n        return hexToNumber(hex, opts);\n    if (to === 'bigint')\n        return hexToBigInt(hex, opts);\n    if (to === 'string')\n        return hexToString(hex, opts);\n    if (to === 'boolean')\n        return hexToBool(hex, opts);\n    return (0,_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.hexToBytes)(hex, opts);\n}\n/**\n * Decodes a hex value into a bigint.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextobigint\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns BigInt value.\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x1a4', { signed: true })\n * // 420n\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420n\n */\nfunction hexToBigInt(hex, opts = {}) {\n    const { signed } = opts;\n    if (opts.size)\n        assertSize(hex, { size: opts.size });\n    const value = BigInt(hex);\n    if (!signed)\n        return value;\n    const size = (hex.length - 2) / 2;\n    const max = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n    if (value <= max)\n        return value;\n    return value - BigInt(`0x${'f'.padStart(size * 2, 'f')}`) - 1n;\n}\n/**\n * Decodes a hex value into a boolean.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextobool\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Boolean value.\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x01')\n * // true\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // true\n */\nfunction hexToBool(hex_, opts = {}) {\n    let hex = hex_;\n    if (opts.size) {\n        assertSize(hex, { size: opts.size });\n        hex = (0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(hex);\n    }\n    if ((0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(hex) === '0x00')\n        return false;\n    if ((0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(hex) === '0x01')\n        return true;\n    throw new _errors_encoding_js__WEBPACK_IMPORTED_MODULE_1__.InvalidHexBooleanError(hex);\n}\n/**\n * Decodes a hex string into a number.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextonumber\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Number value.\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToNumber('0x1a4')\n * // 420\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420\n */\nfunction hexToNumber(hex, opts = {}) {\n    return Number(hexToBigInt(hex, opts));\n}\n/**\n * Decodes a hex value into a UTF-8 string.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextostring\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns String value.\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c6421')\n * // 'Hello world!'\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // 'Hello world'\n */\nfunction hexToString(hex, opts = {}) {\n    let bytes = (0,_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.hexToBytes)(hex);\n    if (opts.size) {\n        assertSize(bytes, { size: opts.size });\n        bytes = (0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(bytes, { dir: 'right' });\n    }\n    return new TextDecoder().decode(bytes);\n}\n//# sourceMappingURL=fromHex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2VuY29kaW5nL2Zyb21IZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFzRjtBQUN0QztBQUNUO0FBQ0c7QUFDbkMsa0NBQWtDLE1BQU07QUFDL0MsUUFBUSxtREFBSztBQUNiLGtCQUFrQixrRUFBaUI7QUFDbkMsdUJBQXVCLG1EQUFLO0FBQzVCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNPO0FBQ1Asa0RBQWtELGVBQWU7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx1REFBVTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxjQUFjO0FBQzFCLHVDQUF1QyxjQUFjO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQixvR0FBb0csVUFBVTtBQUM5RztBQUNBO0FBQ08sbUNBQW1DO0FBQzFDLFlBQVksU0FBUztBQUNyQjtBQUNBLDBCQUEwQixpQkFBaUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsNEJBQTRCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFlBQVk7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFlBQVk7QUFDeEIsa0dBQWtHLFVBQVU7QUFDNUc7QUFDQTtBQUNPLGtDQUFrQztBQUN6QztBQUNBO0FBQ0EsMEJBQTBCLGlCQUFpQjtBQUMzQyxjQUFjLG1EQUFJO0FBQ2xCO0FBQ0EsUUFBUSxtREFBSTtBQUNaO0FBQ0EsUUFBUSxtREFBSTtBQUNaO0FBQ0EsY0FBYyx1RUFBc0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQixvR0FBb0csVUFBVTtBQUM5RztBQUNBO0FBQ08sbUNBQW1DO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDTyxtQ0FBbUM7QUFDMUMsZ0JBQWdCLHVEQUFVO0FBQzFCO0FBQ0EsNEJBQTRCLGlCQUFpQjtBQUM3QyxnQkFBZ0IsbURBQUksVUFBVSxjQUFjO0FBQzVDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxlbmNvZGluZ1xcZnJvbUhleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbnZhbGlkSGV4Qm9vbGVhbkVycm9yLCBTaXplT3ZlcmZsb3dFcnJvciwgfSBmcm9tICcuLi8uLi9lcnJvcnMvZW5jb2RpbmcuanMnO1xuaW1wb3J0IHsgc2l6ZSBhcyBzaXplXyB9IGZyb20gJy4uL2RhdGEvc2l6ZS5qcyc7XG5pbXBvcnQgeyB0cmltIH0gZnJvbSAnLi4vZGF0YS90cmltLmpzJztcbmltcG9ydCB7IGhleFRvQnl0ZXMgfSBmcm9tICcuL3RvQnl0ZXMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGFzc2VydFNpemUoaGV4T3JCeXRlcywgeyBzaXplIH0pIHtcbiAgICBpZiAoc2l6ZV8oaGV4T3JCeXRlcykgPiBzaXplKVxuICAgICAgICB0aHJvdyBuZXcgU2l6ZU92ZXJmbG93RXJyb3Ioe1xuICAgICAgICAgICAgZ2l2ZW5TaXplOiBzaXplXyhoZXhPckJ5dGVzKSxcbiAgICAgICAgICAgIG1heFNpemU6IHNpemUsXG4gICAgICAgIH0pO1xufVxuLyoqXG4gKiBEZWNvZGVzIGEgaGV4IHN0cmluZyBpbnRvIGEgc3RyaW5nLCBudW1iZXIsIGJpZ2ludCwgYm9vbGVhbiwgb3IgYnl0ZSBhcnJheS5cbiAqXG4gKiAtIERvY3M6IGh0dHBzOi8vdmllbS5zaC9kb2NzL3V0aWxpdGllcy9mcm9tSGV4XG4gKiAtIEV4YW1wbGU6IGh0dHBzOi8vdmllbS5zaC9kb2NzL3V0aWxpdGllcy9mcm9tSGV4I3VzYWdlXG4gKlxuICogQHBhcmFtIGhleCBIZXggc3RyaW5nIHRvIGRlY29kZS5cbiAqIEBwYXJhbSB0b09yT3B0cyBUeXBlIHRvIGNvbnZlcnQgdG8gb3Igb3B0aW9ucy5cbiAqIEByZXR1cm5zIERlY29kZWQgdmFsdWUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGZyb21IZXggfSBmcm9tICd2aWVtJ1xuICogY29uc3QgZGF0YSA9IGZyb21IZXgoJzB4MWE0JywgJ251bWJlcicpXG4gKiAvLyA0MjBcbiAqXG4gKiBAZXhhbXBsZVxuICogaW1wb3J0IHsgZnJvbUhleCB9IGZyb20gJ3ZpZW0nXG4gKiBjb25zdCBkYXRhID0gZnJvbUhleCgnMHg0ODY1NmM2YzZmMjA1NzZmNzI2YzY0MjEnLCAnc3RyaW5nJylcbiAqIC8vICdIZWxsbyB3b3JsZCdcbiAqXG4gKiBAZXhhbXBsZVxuICogaW1wb3J0IHsgZnJvbUhleCB9IGZyb20gJ3ZpZW0nXG4gKiBjb25zdCBkYXRhID0gZnJvbUhleCgnMHg0ODY1NmM2YzZmMjA1NzZmNzI2YzY0MjEwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwJywge1xuICogICBzaXplOiAzMixcbiAqICAgdG86ICdzdHJpbmcnXG4gKiB9KVxuICogLy8gJ0hlbGxvIHdvcmxkJ1xuICovXG5leHBvcnQgZnVuY3Rpb24gZnJvbUhleChoZXgsIHRvT3JPcHRzKSB7XG4gICAgY29uc3Qgb3B0cyA9IHR5cGVvZiB0b09yT3B0cyA9PT0gJ3N0cmluZycgPyB7IHRvOiB0b09yT3B0cyB9IDogdG9Pck9wdHM7XG4gICAgY29uc3QgdG8gPSBvcHRzLnRvO1xuICAgIGlmICh0byA9PT0gJ251bWJlcicpXG4gICAgICAgIHJldHVybiBoZXhUb051bWJlcihoZXgsIG9wdHMpO1xuICAgIGlmICh0byA9PT0gJ2JpZ2ludCcpXG4gICAgICAgIHJldHVybiBoZXhUb0JpZ0ludChoZXgsIG9wdHMpO1xuICAgIGlmICh0byA9PT0gJ3N0cmluZycpXG4gICAgICAgIHJldHVybiBoZXhUb1N0cmluZyhoZXgsIG9wdHMpO1xuICAgIGlmICh0byA9PT0gJ2Jvb2xlYW4nKVxuICAgICAgICByZXR1cm4gaGV4VG9Cb29sKGhleCwgb3B0cyk7XG4gICAgcmV0dXJuIGhleFRvQnl0ZXMoaGV4LCBvcHRzKTtcbn1cbi8qKlxuICogRGVjb2RlcyBhIGhleCB2YWx1ZSBpbnRvIGEgYmlnaW50LlxuICpcbiAqIC0gRG9jczogaHR0cHM6Ly92aWVtLnNoL2RvY3MvdXRpbGl0aWVzL2Zyb21IZXgjaGV4dG9iaWdpbnRcbiAqXG4gKiBAcGFyYW0gaGV4IEhleCB2YWx1ZSB0byBkZWNvZGUuXG4gKiBAcGFyYW0gb3B0cyBPcHRpb25zLlxuICogQHJldHVybnMgQmlnSW50IHZhbHVlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBpbXBvcnQgeyBoZXhUb0JpZ0ludCB9IGZyb20gJ3ZpZW0nXG4gKiBjb25zdCBkYXRhID0gaGV4VG9CaWdJbnQoJzB4MWE0JywgeyBzaWduZWQ6IHRydWUgfSlcbiAqIC8vIDQyMG5cbiAqXG4gKiBAZXhhbXBsZVxuICogaW1wb3J0IHsgaGV4VG9CaWdJbnQgfSBmcm9tICd2aWVtJ1xuICogY29uc3QgZGF0YSA9IGhleFRvQmlnSW50KCcweDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAxYTQnLCB7IHNpemU6IDMyIH0pXG4gKiAvLyA0MjBuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZXhUb0JpZ0ludChoZXgsIG9wdHMgPSB7fSkge1xuICAgIGNvbnN0IHsgc2lnbmVkIH0gPSBvcHRzO1xuICAgIGlmIChvcHRzLnNpemUpXG4gICAgICAgIGFzc2VydFNpemUoaGV4LCB7IHNpemU6IG9wdHMuc2l6ZSB9KTtcbiAgICBjb25zdCB2YWx1ZSA9IEJpZ0ludChoZXgpO1xuICAgIGlmICghc2lnbmVkKVxuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgY29uc3Qgc2l6ZSA9IChoZXgubGVuZ3RoIC0gMikgLyAyO1xuICAgIGNvbnN0IG1heCA9ICgxbiA8PCAoQmlnSW50KHNpemUpICogOG4gLSAxbikpIC0gMW47XG4gICAgaWYgKHZhbHVlIDw9IG1heClcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIHJldHVybiB2YWx1ZSAtIEJpZ0ludChgMHgkeydmJy5wYWRTdGFydChzaXplICogMiwgJ2YnKX1gKSAtIDFuO1xufVxuLyoqXG4gKiBEZWNvZGVzIGEgaGV4IHZhbHVlIGludG8gYSBib29sZWFuLlxuICpcbiAqIC0gRG9jczogaHR0cHM6Ly92aWVtLnNoL2RvY3MvdXRpbGl0aWVzL2Zyb21IZXgjaGV4dG9ib29sXG4gKlxuICogQHBhcmFtIGhleCBIZXggdmFsdWUgdG8gZGVjb2RlLlxuICogQHBhcmFtIG9wdHMgT3B0aW9ucy5cbiAqIEByZXR1cm5zIEJvb2xlYW4gdmFsdWUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGhleFRvQm9vbCB9IGZyb20gJ3ZpZW0nXG4gKiBjb25zdCBkYXRhID0gaGV4VG9Cb29sKCcweDAxJylcbiAqIC8vIHRydWVcbiAqXG4gKiBAZXhhbXBsZVxuICogaW1wb3J0IHsgaGV4VG9Cb29sIH0gZnJvbSAndmllbSdcbiAqIGNvbnN0IGRhdGEgPSBoZXhUb0Jvb2woJzB4MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMScsIHsgc2l6ZTogMzIgfSlcbiAqIC8vIHRydWVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhleFRvQm9vbChoZXhfLCBvcHRzID0ge30pIHtcbiAgICBsZXQgaGV4ID0gaGV4XztcbiAgICBpZiAob3B0cy5zaXplKSB7XG4gICAgICAgIGFzc2VydFNpemUoaGV4LCB7IHNpemU6IG9wdHMuc2l6ZSB9KTtcbiAgICAgICAgaGV4ID0gdHJpbShoZXgpO1xuICAgIH1cbiAgICBpZiAodHJpbShoZXgpID09PSAnMHgwMCcpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICBpZiAodHJpbShoZXgpID09PSAnMHgwMScpXG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIHRocm93IG5ldyBJbnZhbGlkSGV4Qm9vbGVhbkVycm9yKGhleCk7XG59XG4vKipcbiAqIERlY29kZXMgYSBoZXggc3RyaW5nIGludG8gYSBudW1iZXIuXG4gKlxuICogLSBEb2NzOiBodHRwczovL3ZpZW0uc2gvZG9jcy91dGlsaXRpZXMvZnJvbUhleCNoZXh0b251bWJlclxuICpcbiAqIEBwYXJhbSBoZXggSGV4IHZhbHVlIHRvIGRlY29kZS5cbiAqIEBwYXJhbSBvcHRzIE9wdGlvbnMuXG4gKiBAcmV0dXJucyBOdW1iZXIgdmFsdWUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGhleFRvTnVtYmVyIH0gZnJvbSAndmllbSdcbiAqIGNvbnN0IGRhdGEgPSBoZXhUb051bWJlcignMHgxYTQnKVxuICogLy8gNDIwXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGhleFRvTnVtYmVyIH0gZnJvbSAndmllbSdcbiAqIGNvbnN0IGRhdGEgPSBoZXhUb0JpZ0ludCgnMHgwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMWE0JywgeyBzaXplOiAzMiB9KVxuICogLy8gNDIwXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZXhUb051bWJlcihoZXgsIG9wdHMgPSB7fSkge1xuICAgIHJldHVybiBOdW1iZXIoaGV4VG9CaWdJbnQoaGV4LCBvcHRzKSk7XG59XG4vKipcbiAqIERlY29kZXMgYSBoZXggdmFsdWUgaW50byBhIFVURi04IHN0cmluZy5cbiAqXG4gKiAtIERvY3M6IGh0dHBzOi8vdmllbS5zaC9kb2NzL3V0aWxpdGllcy9mcm9tSGV4I2hleHRvc3RyaW5nXG4gKlxuICogQHBhcmFtIGhleCBIZXggdmFsdWUgdG8gZGVjb2RlLlxuICogQHBhcmFtIG9wdHMgT3B0aW9ucy5cbiAqIEByZXR1cm5zIFN0cmluZyB2YWx1ZS5cbiAqXG4gKiBAZXhhbXBsZVxuICogaW1wb3J0IHsgaGV4VG9TdHJpbmcgfSBmcm9tICd2aWVtJ1xuICogY29uc3QgZGF0YSA9IGhleFRvU3RyaW5nKCcweDQ4NjU2YzZjNmYyMDU3NmY3MjZjNjQyMScpXG4gKiAvLyAnSGVsbG8gd29ybGQhJ1xuICpcbiAqIEBleGFtcGxlXG4gKiBpbXBvcnQgeyBoZXhUb1N0cmluZyB9IGZyb20gJ3ZpZW0nXG4gKiBjb25zdCBkYXRhID0gaGV4VG9TdHJpbmcoJzB4NDg2NTZjNmM2ZjIwNTc2ZjcyNmM2NDIxMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMCcsIHtcbiAqICBzaXplOiAzMixcbiAqIH0pXG4gKiAvLyAnSGVsbG8gd29ybGQnXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZXhUb1N0cmluZyhoZXgsIG9wdHMgPSB7fSkge1xuICAgIGxldCBieXRlcyA9IGhleFRvQnl0ZXMoaGV4KTtcbiAgICBpZiAob3B0cy5zaXplKSB7XG4gICAgICAgIGFzc2VydFNpemUoYnl0ZXMsIHsgc2l6ZTogb3B0cy5zaXplIH0pO1xuICAgICAgICBieXRlcyA9IHRyaW0oYnl0ZXMsIHsgZGlyOiAncmlnaHQnIH0pO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IFRleHREZWNvZGVyKCkuZGVjb2RlKGJ5dGVzKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZyb21IZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js":
/*!**********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/encoding/toBytes.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolToBytes: () => (/* binding */ boolToBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   numberToBytes: () => (/* binding */ numberToBytes),\n/* harmony export */   stringToBytes: () => (/* binding */ stringToBytes),\n/* harmony export */   toBytes: () => (/* binding */ toBytes)\n/* harmony export */ });\n/* harmony import */ var _errors_base_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../errors/base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/isHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _data_pad_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../data/pad.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/pad.js\");\n/* harmony import */ var _fromHex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fromHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var _toHex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./toHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\n\n\n\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Encodes a UTF-8 string, hex value, bigint, number or boolean to a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes\n * - Example: https://viem.sh/docs/utilities/toBytes#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes('Hello world')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nfunction toBytes(value, opts = {}) {\n    if (typeof value === 'number' || typeof value === 'bigint')\n        return numberToBytes(value, opts);\n    if (typeof value === 'boolean')\n        return boolToBytes(value, opts);\n    if ((0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_0__.isHex)(value))\n        return hexToBytes(value, opts);\n    return stringToBytes(value, opts);\n}\n/**\n * Encodes a boolean into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#booltobytes\n *\n * @param value Boolean value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true)\n * // Uint8Array([1])\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true, { size: 32 })\n * // Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n */\nfunction boolToBytes(value, opts = {}) {\n    const bytes = new Uint8Array(1);\n    bytes[0] = Number(value);\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize)(bytes, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_2__.pad)(bytes, { size: opts.size });\n    }\n    return bytes;\n}\n// We use very optimized technique to convert hex string to byte array\nconst charCodeMap = {\n    zero: 48,\n    nine: 57,\n    A: 65,\n    F: 70,\n    a: 97,\n    f: 102,\n};\nfunction charCodeToBase16(char) {\n    if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n        return char - charCodeMap.zero;\n    if (char >= charCodeMap.A && char <= charCodeMap.F)\n        return char - (charCodeMap.A - 10);\n    if (char >= charCodeMap.a && char <= charCodeMap.f)\n        return char - (charCodeMap.a - 10);\n    return undefined;\n}\n/**\n * Encodes a hex string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#hextobytes\n *\n * @param hex Hex string to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nfunction hexToBytes(hex_, opts = {}) {\n    let hex = hex_;\n    if (opts.size) {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize)(hex, { size: opts.size });\n        hex = (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_2__.pad)(hex, { dir: 'right', size: opts.size });\n    }\n    let hexString = hex.slice(2);\n    if (hexString.length % 2)\n        hexString = `0${hexString}`;\n    const length = hexString.length / 2;\n    const bytes = new Uint8Array(length);\n    for (let index = 0, j = 0; index < length; index++) {\n        const nibbleLeft = charCodeToBase16(hexString.charCodeAt(j++));\n        const nibbleRight = charCodeToBase16(hexString.charCodeAt(j++));\n        if (nibbleLeft === undefined || nibbleRight === undefined) {\n            throw new _errors_base_js__WEBPACK_IMPORTED_MODULE_3__.BaseError(`Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`);\n        }\n        bytes[index] = nibbleLeft * 16 + nibbleRight;\n    }\n    return bytes;\n}\n/**\n * Encodes a number into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#numbertobytes\n *\n * @param value Number to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nfunction numberToBytes(value, opts) {\n    const hex = (0,_toHex_js__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(value, opts);\n    return hexToBytes(hex);\n}\n/**\n * Encodes a UTF-8 string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#stringtobytes\n *\n * @param value String to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nfunction stringToBytes(value, opts = {}) {\n    const bytes = encoder.encode(value);\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize)(bytes, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_2__.pad)(bytes, { dir: 'right', size: opts.size });\n    }\n    return bytes;\n}\n//# sourceMappingURL=toBytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js":
/*!********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/encoding/toHex.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolToHex: () => (/* binding */ boolToHex),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   numberToHex: () => (/* binding */ numberToHex),\n/* harmony export */   stringToHex: () => (/* binding */ stringToHex),\n/* harmony export */   toHex: () => (/* binding */ toHex)\n/* harmony export */ });\n/* harmony import */ var _errors_encoding_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../errors/encoding.js */ \"(ssr)/./node_modules/viem/_esm/errors/encoding.js\");\n/* harmony import */ var _data_pad_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/pad.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/pad.js\");\n/* harmony import */ var _fromHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) => i.toString(16).padStart(2, '0'));\n/**\n * Encodes a string, number, bigint, or ByteArray into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex\n * - Example: https://viem.sh/docs/utilities/toHex#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world')\n * // '0x48656c6c6f20776f726c6421'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex(420)\n * // '0x1a4'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world', { size: 32 })\n * // '0x48656c6c6f20776f726c64210000000000000000000000000000000000000000'\n */\nfunction toHex(value, opts = {}) {\n    if (typeof value === 'number' || typeof value === 'bigint')\n        return numberToHex(value, opts);\n    if (typeof value === 'string') {\n        return stringToHex(value, opts);\n    }\n    if (typeof value === 'boolean')\n        return boolToHex(value, opts);\n    return bytesToHex(value, opts);\n}\n/**\n * Encodes a boolean into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#booltohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true)\n * // '0x1'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(false)\n * // '0x0'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true, { size: 32 })\n * // '0x0000000000000000000000000000000000000000000000000000000000000001'\n */\nfunction boolToHex(value, opts = {}) {\n    const hex = `0x${Number(value)}`;\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize)(hex, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_1__.pad)(hex, { size: opts.size });\n    }\n    return hex;\n}\n/**\n * Encodes a bytes array into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#bytestohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]), { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nfunction bytesToHex(value, opts = {}) {\n    let string = '';\n    for (let i = 0; i < value.length; i++) {\n        string += hexes[value[i]];\n    }\n    const hex = `0x${string}`;\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize)(hex, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_1__.pad)(hex, { dir: 'right', size: opts.size });\n    }\n    return hex;\n}\n/**\n * Encodes a number or bigint into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#numbertohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420)\n * // '0x1a4'\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420, { size: 32 })\n * // '0x00000000000000000000000000000000000000000000000000000000000001a4'\n */\nfunction numberToHex(value_, opts = {}) {\n    const { signed, size } = opts;\n    const value = BigInt(value_);\n    let maxValue;\n    if (size) {\n        if (signed)\n            maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n        else\n            maxValue = 2n ** (BigInt(size) * 8n) - 1n;\n    }\n    else if (typeof value_ === 'number') {\n        maxValue = BigInt(Number.MAX_SAFE_INTEGER);\n    }\n    const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0;\n    if ((maxValue && value > maxValue) || value < minValue) {\n        const suffix = typeof value_ === 'bigint' ? 'n' : '';\n        throw new _errors_encoding_js__WEBPACK_IMPORTED_MODULE_2__.IntegerOutOfRangeError({\n            max: maxValue ? `${maxValue}${suffix}` : undefined,\n            min: `${minValue}${suffix}`,\n            signed,\n            size,\n            value: `${value_}${suffix}`,\n        });\n    }\n    const hex = `0x${(signed && value < 0 ? (1n << BigInt(size * 8)) + BigInt(value) : value).toString(16)}`;\n    if (size)\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_1__.pad)(hex, { size });\n    return hex;\n}\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Encodes a UTF-8 string into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#stringtohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nfunction stringToHex(value_, opts = {}) {\n    const value = encoder.encode(value_);\n    return bytesToHex(value, opts);\n}\n//# sourceMappingURL=toHex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js":
/*!********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/hash/keccak256.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   keccak256: () => (/* binding */ keccak256)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/sha3 */ \"(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/sha3.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/isHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../encoding/toBytes.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../encoding/toHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\n\n\nfunction keccak256(value, to_) {\n    const to = to_ || 'hex';\n    const bytes = (0,_noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_0__.keccak_256)((0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_1__.isHex)(value, { strict: false }) ? (0,_encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.toBytes)(value) : value);\n    if (to === 'bytes')\n        return bytes;\n    return (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_3__.toHex)(bytes);\n}\n//# sourceMappingURL=keccak256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2hhc2gva2VjY2FrMjU2LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWdEO0FBQ1A7QUFDUTtBQUNKO0FBQ3RDO0FBQ1A7QUFDQSxrQkFBa0IsOERBQVUsQ0FBQyxxREFBSyxVQUFVLGVBQWUsSUFBSSw2REFBTztBQUN0RTtBQUNBO0FBQ0EsV0FBVyx5REFBSztBQUNoQjtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxoYXNoXFxrZWNjYWsyNTYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsga2VjY2FrXzI1NiB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvc2hhMyc7XG5pbXBvcnQgeyBpc0hleCB9IGZyb20gJy4uL2RhdGEvaXNIZXguanMnO1xuaW1wb3J0IHsgdG9CeXRlcyB9IGZyb20gJy4uL2VuY29kaW5nL3RvQnl0ZXMuanMnO1xuaW1wb3J0IHsgdG9IZXggfSBmcm9tICcuLi9lbmNvZGluZy90b0hleC5qcyc7XG5leHBvcnQgZnVuY3Rpb24ga2VjY2FrMjU2KHZhbHVlLCB0b18pIHtcbiAgICBjb25zdCB0byA9IHRvXyB8fCAnaGV4JztcbiAgICBjb25zdCBieXRlcyA9IGtlY2Nha18yNTYoaXNIZXgodmFsdWUsIHsgc3RyaWN0OiBmYWxzZSB9KSA/IHRvQnl0ZXModmFsdWUpIDogdmFsdWUpO1xuICAgIGlmICh0byA9PT0gJ2J5dGVzJylcbiAgICAgICAgcmV0dXJuIGJ5dGVzO1xuICAgIHJldHVybiB0b0hleChieXRlcyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1rZWNjYWsyNTYuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/lru.js":
/*!*********************************************!*\
  !*** ./node_modules/viem/_esm/utils/lru.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LruMap: () => (/* binding */ LruMap)\n/* harmony export */ });\n/**\n * Map with a LRU (Least recently used) policy.\n *\n * @link https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nclass LruMap extends Map {\n    constructor(size) {\n        super();\n        Object.defineProperty(this, \"maxSize\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxSize = size;\n    }\n    get(key) {\n        const value = super.get(key);\n        if (super.has(key) && value !== undefined) {\n            this.delete(key);\n            super.set(key, value);\n        }\n        return value;\n    }\n    set(key, value) {\n        super.set(key, value);\n        if (this.maxSize && this.size > this.maxSize) {\n            const firstKey = this.keys().next().value;\n            if (firstKey)\n                this.delete(firstKey);\n        }\n        return this;\n    }\n}\n//# sourceMappingURL=lru.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2xydS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxscnUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNYXAgd2l0aCBhIExSVSAoTGVhc3QgcmVjZW50bHkgdXNlZCkgcG9saWN5LlxuICpcbiAqIEBsaW5rIGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0NhY2hlX3JlcGxhY2VtZW50X3BvbGljaWVzI0xSVVxuICovXG5leHBvcnQgY2xhc3MgTHJ1TWFwIGV4dGVuZHMgTWFwIHtcbiAgICBjb25zdHJ1Y3RvcihzaXplKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm1heFNpemVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5tYXhTaXplID0gc2l6ZTtcbiAgICB9XG4gICAgZ2V0KGtleSkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHN1cGVyLmdldChrZXkpO1xuICAgICAgICBpZiAoc3VwZXIuaGFzKGtleSkgJiYgdmFsdWUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5kZWxldGUoa2V5KTtcbiAgICAgICAgICAgIHN1cGVyLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHNldChrZXksIHZhbHVlKSB7XG4gICAgICAgIHN1cGVyLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgaWYgKHRoaXMubWF4U2l6ZSAmJiB0aGlzLnNpemUgPiB0aGlzLm1heFNpemUpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpcnN0S2V5ID0gdGhpcy5rZXlzKCkubmV4dCgpLnZhbHVlO1xuICAgICAgICAgICAgaWYgKGZpcnN0S2V5KVxuICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlKGZpcnN0S2V5KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1scnUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/lru.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js":
/*!**********************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/createBatchScheduler.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBatchScheduler: () => (/* binding */ createBatchScheduler)\n/* harmony export */ });\n/* harmony import */ var _withResolvers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./withResolvers.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withResolvers.js\");\n\nconst schedulerCache = /*#__PURE__*/ new Map();\n/** @internal */\nfunction createBatchScheduler({ fn, id, shouldSplitBatch, wait = 0, sort, }) {\n    const exec = async () => {\n        const scheduler = getScheduler();\n        flush();\n        const args = scheduler.map(({ args }) => args);\n        if (args.length === 0)\n            return;\n        fn(args)\n            .then((data) => {\n            if (sort && Array.isArray(data))\n                data.sort(sort);\n            for (let i = 0; i < scheduler.length; i++) {\n                const { resolve } = scheduler[i];\n                resolve?.([data[i], data]);\n            }\n        })\n            .catch((err) => {\n            for (let i = 0; i < scheduler.length; i++) {\n                const { reject } = scheduler[i];\n                reject?.(err);\n            }\n        });\n    };\n    const flush = () => schedulerCache.delete(id);\n    const getBatchedArgs = () => getScheduler().map(({ args }) => args);\n    const getScheduler = () => schedulerCache.get(id) || [];\n    const setScheduler = (item) => schedulerCache.set(id, [...getScheduler(), item]);\n    return {\n        flush,\n        async schedule(args) {\n            const { promise, resolve, reject } = (0,_withResolvers_js__WEBPACK_IMPORTED_MODULE_0__.withResolvers)();\n            const split = shouldSplitBatch?.([...getBatchedArgs(), args]);\n            if (split)\n                exec();\n            const hasActiveScheduler = getScheduler().length > 0;\n            if (hasActiveScheduler) {\n                setScheduler({ args, resolve, reject });\n                return promise;\n            }\n            setScheduler({ args, resolve, reject });\n            setTimeout(exec, wait);\n            return promise;\n        },\n    };\n}\n//# sourceMappingURL=createBatchScheduler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withDedupe.js":
/*!************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withDedupe.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   promiseCache: () => (/* binding */ promiseCache),\n/* harmony export */   withDedupe: () => (/* binding */ withDedupe)\n/* harmony export */ });\n/* harmony import */ var _lru_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lru.js */ \"(ssr)/./node_modules/viem/_esm/utils/lru.js\");\n\n/** @internal */\nconst promiseCache = /*#__PURE__*/ new _lru_js__WEBPACK_IMPORTED_MODULE_0__.LruMap(8192);\n/** Deduplicates in-flight promises. */\nfunction withDedupe(fn, { enabled = true, id }) {\n    if (!enabled || !id)\n        return fn();\n    if (promiseCache.get(id))\n        return promiseCache.get(id);\n    const promise = fn().finally(() => promiseCache.delete(id));\n    promiseCache.set(id, promise);\n    return promise;\n}\n//# sourceMappingURL=withDedupe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aERlZHVwZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDbkM7QUFDTyx1Q0FBdUMsMkNBQU07QUFDcEQ7QUFDTywwQkFBMEIsb0JBQW9CO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFx2aWVtXFxfZXNtXFx1dGlsc1xccHJvbWlzZVxcd2l0aERlZHVwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMcnVNYXAgfSBmcm9tICcuLi9scnUuanMnO1xuLyoqIEBpbnRlcm5hbCAqL1xuZXhwb3J0IGNvbnN0IHByb21pc2VDYWNoZSA9IC8qI19fUFVSRV9fKi8gbmV3IExydU1hcCg4MTkyKTtcbi8qKiBEZWR1cGxpY2F0ZXMgaW4tZmxpZ2h0IHByb21pc2VzLiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdpdGhEZWR1cGUoZm4sIHsgZW5hYmxlZCA9IHRydWUsIGlkIH0pIHtcbiAgICBpZiAoIWVuYWJsZWQgfHwgIWlkKVxuICAgICAgICByZXR1cm4gZm4oKTtcbiAgICBpZiAocHJvbWlzZUNhY2hlLmdldChpZCkpXG4gICAgICAgIHJldHVybiBwcm9taXNlQ2FjaGUuZ2V0KGlkKTtcbiAgICBjb25zdCBwcm9taXNlID0gZm4oKS5maW5hbGx5KCgpID0+IHByb21pc2VDYWNoZS5kZWxldGUoaWQpKTtcbiAgICBwcm9taXNlQ2FjaGUuc2V0KGlkLCBwcm9taXNlKTtcbiAgICByZXR1cm4gcHJvbWlzZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdpdGhEZWR1cGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withDedupe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withResolvers.js":
/*!***************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withResolvers.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withResolvers: () => (/* binding */ withResolvers)\n/* harmony export */ });\n/** @internal */\nfunction withResolvers() {\n    let resolve = () => undefined;\n    let reject = () => undefined;\n    const promise = new Promise((resolve_, reject_) => {\n        resolve = resolve_;\n        reject = reject_;\n    });\n    return { promise, resolve, reject };\n}\n//# sourceMappingURL=withResolvers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aFJlc29sdmVycy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcdXRpbHNcXHByb21pc2VcXHdpdGhSZXNvbHZlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIEBpbnRlcm5hbCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdpdGhSZXNvbHZlcnMoKSB7XG4gICAgbGV0IHJlc29sdmUgPSAoKSA9PiB1bmRlZmluZWQ7XG4gICAgbGV0IHJlamVjdCA9ICgpID0+IHVuZGVmaW5lZDtcbiAgICBjb25zdCBwcm9taXNlID0gbmV3IFByb21pc2UoKHJlc29sdmVfLCByZWplY3RfKSA9PiB7XG4gICAgICAgIHJlc29sdmUgPSByZXNvbHZlXztcbiAgICAgICAgcmVqZWN0ID0gcmVqZWN0XztcbiAgICB9KTtcbiAgICByZXR1cm4geyBwcm9taXNlLCByZXNvbHZlLCByZWplY3QgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdpdGhSZXNvbHZlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withResolvers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withRetry.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withRetry: () => (/* binding */ withRetry)\n/* harmony export */ });\n/* harmony import */ var _wait_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wait.js */ \"(ssr)/./node_modules/viem/_esm/utils/wait.js\");\n\nfunction withRetry(fn, { delay: delay_ = 100, retryCount = 2, shouldRetry = () => true, } = {}) {\n    return new Promise((resolve, reject) => {\n        const attemptRetry = async ({ count = 0 } = {}) => {\n            const retry = async ({ error }) => {\n                const delay = typeof delay_ === 'function' ? delay_({ count, error }) : delay_;\n                if (delay)\n                    await (0,_wait_js__WEBPACK_IMPORTED_MODULE_0__.wait)(delay);\n                attemptRetry({ count: count + 1 });\n            };\n            try {\n                const data = await fn();\n                resolve(data);\n            }\n            catch (err) {\n                if (count < retryCount &&\n                    (await shouldRetry({ count, error: err })))\n                    return retry({ error: err });\n                reject(err);\n            }\n        };\n        attemptRetry();\n    });\n}\n//# sourceMappingURL=withRetry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aFJldHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQzNCLHlCQUF5QixpRUFBaUUsSUFBSTtBQUNyRztBQUNBLHNDQUFzQyxZQUFZLElBQUk7QUFDdEQsbUNBQW1DLE9BQU87QUFDMUMsc0VBQXNFLGNBQWM7QUFDcEY7QUFDQSwwQkFBMEIsOENBQUk7QUFDOUIsK0JBQStCLGtCQUFrQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxtQkFBbUI7QUFDNUQsbUNBQW1DLFlBQVk7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcdG9rZW5kZXYtbmV3cm9vXFxhZG1pbi1wYW5lbFxcbm9kZV9tb2R1bGVzXFx2aWVtXFxfZXNtXFx1dGlsc1xccHJvbWlzZVxcd2l0aFJldHJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdhaXQgfSBmcm9tICcuLi93YWl0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiB3aXRoUmV0cnkoZm4sIHsgZGVsYXk6IGRlbGF5XyA9IDEwMCwgcmV0cnlDb3VudCA9IDIsIHNob3VsZFJldHJ5ID0gKCkgPT4gdHJ1ZSwgfSA9IHt9KSB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgY29uc3QgYXR0ZW1wdFJldHJ5ID0gYXN5bmMgKHsgY291bnQgPSAwIH0gPSB7fSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgcmV0cnkgPSBhc3luYyAoeyBlcnJvciB9KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGVsYXkgPSB0eXBlb2YgZGVsYXlfID09PSAnZnVuY3Rpb24nID8gZGVsYXlfKHsgY291bnQsIGVycm9yIH0pIDogZGVsYXlfO1xuICAgICAgICAgICAgICAgIGlmIChkZWxheSlcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgd2FpdChkZWxheSk7XG4gICAgICAgICAgICAgICAgYXR0ZW1wdFJldHJ5KHsgY291bnQ6IGNvdW50ICsgMSB9KTtcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBmbigpO1xuICAgICAgICAgICAgICAgIHJlc29sdmUoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgaWYgKGNvdW50IDwgcmV0cnlDb3VudCAmJlxuICAgICAgICAgICAgICAgICAgICAoYXdhaXQgc2hvdWxkUmV0cnkoeyBjb3VudCwgZXJyb3I6IGVyciB9KSkpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZXRyeSh7IGVycm9yOiBlcnIgfSk7XG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIGF0dGVtcHRSZXRyeSgpO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2l0aFJldHJ5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js":
/*!*************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withTimeout.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTimeout: () => (/* binding */ withTimeout)\n/* harmony export */ });\nfunction withTimeout(fn, { errorInstance = new Error('timed out'), timeout, signal, }) {\n    return new Promise((resolve, reject) => {\n        ;\n        (async () => {\n            let timeoutId;\n            try {\n                const controller = new AbortController();\n                if (timeout > 0) {\n                    timeoutId = setTimeout(() => {\n                        if (signal) {\n                            controller.abort();\n                        }\n                        else {\n                            reject(errorInstance);\n                        }\n                    }, timeout); // need to cast because bun globals.d.ts overrides @types/node\n                }\n                resolve(await fn({ signal: controller?.signal || null }));\n            }\n            catch (err) {\n                if (err?.name === 'AbortError')\n                    reject(errorInstance);\n                reject(err);\n            }\n            finally {\n                clearTimeout(timeoutId);\n            }\n        })();\n    });\n}\n//# sourceMappingURL=withTimeout.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/rpc/http.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/http.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpRpcClient: () => (/* binding */ getHttpRpcClient)\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../errors/request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../promise/withTimeout.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../stringify.js */ \"(ssr)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./id.js */ \"(ssr)/./node_modules/viem/_esm/utils/rpc/id.js\");\n\n\n\n\nfunction getHttpRpcClient(url, options = {}) {\n    return {\n        async request(params) {\n            const { body, onRequest = options.onRequest, onResponse = options.onResponse, timeout = options.timeout ?? 10_000, } = params;\n            const fetchOptions = {\n                ...(options.fetchOptions ?? {}),\n                ...(params.fetchOptions ?? {}),\n            };\n            const { headers, method, signal: signal_ } = fetchOptions;\n            try {\n                const response = await (0,_promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_0__.withTimeout)(async ({ signal }) => {\n                    const init = {\n                        ...fetchOptions,\n                        body: Array.isArray(body)\n                            ? (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.stringify)(body.map((body) => ({\n                                jsonrpc: '2.0',\n                                id: body.id ?? _id_js__WEBPACK_IMPORTED_MODULE_2__.idCache.take(),\n                                ...body,\n                            })))\n                            : (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.stringify)({\n                                jsonrpc: '2.0',\n                                id: body.id ?? _id_js__WEBPACK_IMPORTED_MODULE_2__.idCache.take(),\n                                ...body,\n                            }),\n                        headers: {\n                            'Content-Type': 'application/json',\n                            ...headers,\n                        },\n                        method: method || 'POST',\n                        signal: signal_ || (timeout > 0 ? signal : null),\n                    };\n                    const request = new Request(url, init);\n                    const args = (await onRequest?.(request, init)) ?? { ...init, url };\n                    const response = await fetch(args.url ?? url, args);\n                    return response;\n                }, {\n                    errorInstance: new _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.TimeoutError({ body, url }),\n                    timeout,\n                    signal: true,\n                });\n                if (onResponse)\n                    await onResponse(response);\n                let data;\n                if (response.headers.get('Content-Type')?.startsWith('application/json'))\n                    data = await response.json();\n                else {\n                    data = await response.text();\n                    try {\n                        data = JSON.parse(data || '{}');\n                    }\n                    catch (err) {\n                        if (response.ok)\n                            throw err;\n                        data = { error: data };\n                    }\n                }\n                if (!response.ok) {\n                    throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.HttpRequestError({\n                        body,\n                        details: (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.stringify)(data.error) || response.statusText,\n                        headers: response.headers,\n                        status: response.status,\n                        url,\n                    });\n                }\n                return data;\n            }\n            catch (err) {\n                if (err instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.HttpRequestError)\n                    throw err;\n                if (err instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.TimeoutError)\n                    throw err;\n                throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.HttpRequestError({\n                    body,\n                    cause: err,\n                    url,\n                });\n            }\n        },\n    };\n}\n//# sourceMappingURL=http.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/rpc/http.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/rpc/id.js":
/*!************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/id.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   idCache: () => (/* binding */ idCache)\n/* harmony export */ });\nfunction createIdStore() {\n    return {\n        current: 0,\n        take() {\n            return this.current++;\n        },\n        reset() {\n            this.current = 0;\n        },\n    };\n}\nconst idCache = /*#__PURE__*/ createIdStore();\n//# sourceMappingURL=id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3JwYy9pZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTztBQUNQIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxycGNcXGlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGNyZWF0ZUlkU3RvcmUoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY3VycmVudDogMCxcbiAgICAgICAgdGFrZSgpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnQrKztcbiAgICAgICAgfSxcbiAgICAgICAgcmVzZXQoKSB7XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnQgPSAwO1xuICAgICAgICB9LFxuICAgIH07XG59XG5leHBvcnQgY29uc3QgaWRDYWNoZSA9IC8qI19fUFVSRV9fKi8gY3JlYXRlSWRTdG9yZSgpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/rpc/id.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/stringify.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/stringify.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\nconst stringify = (value, replacer, space) => JSON.stringify(value, (key, value_) => {\n    const value = typeof value_ === 'bigint' ? value_.toString() : value_;\n    return typeof replacer === 'function' ? replacer(key, value) : value;\n}, space);\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3N0cmluZ2lmeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EsQ0FBQztBQUNEIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFxzdHJpbmdpZnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHN0cmluZ2lmeSA9ICh2YWx1ZSwgcmVwbGFjZXIsIHNwYWNlKSA9PiBKU09OLnN0cmluZ2lmeSh2YWx1ZSwgKGtleSwgdmFsdWVfKSA9PiB7XG4gICAgY29uc3QgdmFsdWUgPSB0eXBlb2YgdmFsdWVfID09PSAnYmlnaW50JyA/IHZhbHVlXy50b1N0cmluZygpIDogdmFsdWVfO1xuICAgIHJldHVybiB0eXBlb2YgcmVwbGFjZXIgPT09ICdmdW5jdGlvbicgPyByZXBsYWNlcihrZXksIHZhbHVlKSA6IHZhbHVlO1xufSwgc3BhY2UpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RyaW5naWZ5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/uid.js":
/*!*********************************************!*\
  !*** ./node_modules/viem/_esm/utils/uid.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3VpZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcX2VzbVxcdXRpbHNcXHVpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzaXplID0gMjU2O1xubGV0IGluZGV4ID0gc2l6ZTtcbmxldCBidWZmZXI7XG5leHBvcnQgZnVuY3Rpb24gdWlkKGxlbmd0aCA9IDExKSB7XG4gICAgaWYgKCFidWZmZXIgfHwgaW5kZXggKyBsZW5ndGggPiBzaXplICogMikge1xuICAgICAgICBidWZmZXIgPSAnJztcbiAgICAgICAgaW5kZXggPSAwO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNpemU7IGkrKykge1xuICAgICAgICAgICAgYnVmZmVyICs9ICgoMjU2ICsgTWF0aC5yYW5kb20oKSAqIDI1NikgfCAwKS50b1N0cmluZygxNikuc3Vic3RyaW5nKDEpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBidWZmZXIuc3Vic3RyaW5nKGluZGV4LCBpbmRleCsrICsgbGVuZ3RoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVpZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/wait.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/utils/wait.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wait: () => (/* binding */ wait)\n/* harmony export */ });\nasync function wait(time) {\n    return new Promise((res) => setTimeout(res, time));\n}\n//# sourceMappingURL=wait.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3dhaXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxub2RlX21vZHVsZXNcXHZpZW1cXF9lc21cXHV0aWxzXFx3YWl0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBhc3luYyBmdW5jdGlvbiB3YWl0KHRpbWUpIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlcykgPT4gc2V0VGltZW91dChyZXMsIHRpbWUpKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhaXQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/wait.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/_u64.js":
/*!******************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/hashes/esm/_u64.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/cryptoNode.js":
/*!************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n    ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto\n    : /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"randomBytes\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        ? /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2tDO0FBQzNCLGVBQWUsMk1BQUUsV0FBVywyTUFBRSxpQkFBaUIsME5BQWlCO0FBQ3ZFLE1BQU0sa0RBQVk7QUFDbEIsTUFBTSwyTUFBRSxXQUFXLDJNQUFFLGlCQUFpQiw0TkFBbUI7QUFDekQsVUFBVSwyTUFBRTtBQUNaO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXG5vZGVfbW9kdWxlc1xcdmllbVxcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcZXNtXFxjcnlwdG9Ob2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSW50ZXJuYWwgd2ViY3J5cHRvIGFsaWFzLlxuICogV2UgcHJlZmVyIFdlYkNyeXB0byBha2EgZ2xvYmFsVGhpcy5jcnlwdG8sIHdoaWNoIGV4aXN0cyBpbiBub2RlLmpzIDE2Ky5cbiAqIEZhbGxzIGJhY2sgdG8gTm9kZS5qcyBidWlsdC1pbiBjcnlwdG8gZm9yIE5vZGUuanMgPD12MTQuXG4gKiBTZWUgdXRpbHMudHMgZm9yIGRldGFpbHMuXG4gKiBAbW9kdWxlXG4gKi9cbi8vIEB0cy1pZ25vcmVcbmltcG9ydCAqIGFzIG5jIGZyb20gJ25vZGU6Y3J5cHRvJztcbmV4cG9ydCBjb25zdCBjcnlwdG8gPSBuYyAmJiB0eXBlb2YgbmMgPT09ICdvYmplY3QnICYmICd3ZWJjcnlwdG8nIGluIG5jXG4gICAgPyBuYy53ZWJjcnlwdG9cbiAgICA6IG5jICYmIHR5cGVvZiBuYyA9PT0gJ29iamVjdCcgJiYgJ3JhbmRvbUJ5dGVzJyBpbiBuY1xuICAgICAgICA/IG5jXG4gICAgICAgIDogdW5kZWZpbmVkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3J5cHRvTm9kZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/sha3.js":
/*!******************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/hashes/esm/sha3.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keccak: () => (/* binding */ Keccak),\n/* harmony export */   keccakP: () => (/* binding */ keccakP),\n/* harmony export */   keccak_224: () => (/* binding */ keccak_224),\n/* harmony export */   keccak_256: () => (/* binding */ keccak_256),\n/* harmony export */   keccak_384: () => (/* binding */ keccak_384),\n/* harmony export */   keccak_512: () => (/* binding */ keccak_512),\n/* harmony export */   sha3_224: () => (/* binding */ sha3_224),\n/* harmony export */   sha3_256: () => (/* binding */ sha3_256),\n/* harmony export */   sha3_384: () => (/* binding */ sha3_384),\n/* harmony export */   sha3_512: () => (/* binding */ sha3_512),\n/* harmony export */   shake128: () => (/* binding */ shake128),\n/* harmony export */   shake256: () => (/* binding */ shake256)\n/* harmony export */ });\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\n\n// prettier-ignore\n\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst IOTAS = (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.split)(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBH)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSH)(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBL)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSL)(h, l, s));\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(B);\n}\n/** Keccak sponge function. */\nclass Keccak extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        this.enableXOF = false;\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        // Can be passed from user as dkLen\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        // 0 < blockLen < 200\n        if (!(0 < blockLen && blockLen < 200))\n            throw new Error('only keccak-f1600 function is supported');\n        this.state = new Uint8Array(200);\n        this.state32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.state);\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    keccak() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(this.state32);\n        keccakP(this.state32, this.rounds);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(data);\n        const { blockLen, state } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this, false);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.state);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nconst sha3_224 = /* @__PURE__ */ (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nconst sha3_256 = /* @__PURE__ */ (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nconst sha3_384 = /* @__PURE__ */ (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nconst sha3_512 = /* @__PURE__ */ (() => gen(0x06, 72, 512 / 8))();\n/** keccak-224 hash function. */\nconst keccak_224 = /* @__PURE__ */ (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nconst keccak_256 = /* @__PURE__ */ (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nconst keccak_384 = /* @__PURE__ */ (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nconst keccak_512 = /* @__PURE__ */ (() => gen(0x01, 72, 512 / 8))();\nconst genShake = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createXOFer)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nconst shake128 = /* @__PURE__ */ (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nconst shake256 = /* @__PURE__ */ (() => genShake(0x1f, 136, 256 / 8))();\n//# sourceMappingURL=sha3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/sha3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/utils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/hashes/esm/utils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   aexists: () => (/* binding */ aexists),\n/* harmony export */   ahash: () => (/* binding */ ahash),\n/* harmony export */   anumber: () => (/* binding */ anumber),\n/* harmony export */   aoutput: () => (/* binding */ aoutput),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   byteSwap: () => (/* binding */ byteSwap),\n/* harmony export */   byteSwap32: () => (/* binding */ byteSwap32),\n/* harmony export */   byteSwapIfBE: () => (/* binding */ byteSwapIfBE),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   createOptHasher: () => (/* binding */ createOptHasher),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   createXOFer: () => (/* binding */ createXOFer),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   kdfInputToBytes: () => (/* binding */ kdfInputToBytes),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotl: () => (/* binding */ rotl),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   swap32IfBE: () => (/* binding */ swap32IfBE),\n/* harmony export */   swap8IfBE: () => (/* binding */ swap8IfBE),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/cryptoNode.js\");\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\n\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nconst isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nconst swap8IfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nconst byteSwapIfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nconst swap32IfBE = isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nconst wrapConstructor = createHasher;\nconst wrapConstructorWithOpts = createOptHasher;\nconst wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function') {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes === 'function') {\n        return Uint8Array.from(_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/node_modules/@noble/hashes/esm/utils.js\n");

/***/ })

};
;