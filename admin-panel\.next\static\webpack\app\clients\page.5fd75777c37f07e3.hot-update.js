"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/clients/page",{

/***/ "(app-pages-browser)/./src/components/ClientManagement.tsx":
/*!*********************************************!*\
  !*** ./src/components/ClientManagement.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ClientManagement() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [kycFilter, setKycFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistFilter, setWhitelistFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedClient, setSelectedClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // Fetch clients with automatic refresh\n    const { data: clientsData, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            'clients',\n            currentPage,\n            searchTerm,\n            kycFilter,\n            whitelistFilter\n        ],\n        queryFn: {\n            \"ClientManagement.useQuery\": async ()=>{\n                var _data_clients;\n                const params = new URLSearchParams({\n                    page: currentPage.toString(),\n                    limit: '10',\n                    ...searchTerm && {\n                        search: searchTerm\n                    },\n                    ...kycFilter && {\n                        kycStatus: kycFilter\n                    },\n                    ...whitelistFilter && {\n                        isWhitelisted: whitelistFilter\n                    }\n                });\n                console.log('Fetching clients with params:', params.toString());\n                const response = await fetch(\"/api/clients?\".concat(params));\n                if (!response.ok) {\n                    throw new Error('Failed to fetch clients');\n                }\n                const data = await response.json();\n                console.log('Clients data received:', (_data_clients = data.clients) === null || _data_clients === void 0 ? void 0 : _data_clients.length, 'clients');\n                return data;\n            }\n        }[\"ClientManagement.useQuery\"],\n        refetchInterval: 30000,\n        refetchOnWindowFocus: true,\n        refetchOnMount: true\n    });\n    // Update KYC status mutation\n    const updateKycMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"ClientManagement.useMutation[updateKycMutation]\": async (param)=>{\n                let { clientId, kycStatus, kycNotes } = param;\n                const response = await fetch(\"/api/clients/\".concat(clientId, \"/kyc\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        kycStatus,\n                        kycNotes\n                    })\n                });\n                if (!response.ok) throw new Error('Failed to update KYC status');\n                return response.json();\n            }\n        }[\"ClientManagement.useMutation[updateKycMutation]\"],\n        onSuccess: {\n            \"ClientManagement.useMutation[updateKycMutation]\": ()=>{\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'clients'\n                    ]\n                });\n            }\n        }[\"ClientManagement.useMutation[updateKycMutation]\"]\n    });\n    // Update whitelist status mutation\n    const updateWhitelistMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"ClientManagement.useMutation[updateWhitelistMutation]\": async (param)=>{\n                let { clientId, walletAddress, isWhitelisted } = param;\n                const response = await fetch(\"/api/clients/\".concat(clientId, \"/whitelist\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        walletAddress,\n                        isWhitelisted\n                    })\n                });\n                if (!response.ok) throw new Error('Failed to update whitelist status');\n                return response.json();\n            }\n        }[\"ClientManagement.useMutation[updateWhitelistMutation]\"],\n        onSuccess: {\n            \"ClientManagement.useMutation[updateWhitelistMutation]\": ()=>{\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        'clients'\n                    ]\n                });\n            }\n        }[\"ClientManagement.useMutation[updateWhitelistMutation]\"]\n    });\n    const getKycStatusColor = (status)=>{\n        switch(status){\n            case 'APPROVED':\n                return 'text-green-600 bg-green-100';\n            case 'REJECTED':\n                return 'text-red-600 bg-red-100';\n            case 'IN_REVIEW':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'PENDING':\n                return 'text-gray-600 bg-gray-100';\n            case 'EXPIRED':\n                return 'text-orange-600 bg-orange-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n            children: [\n                \"Error loading clients: \",\n                error.message\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"Client Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateForm(true),\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"Add New Client\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Search\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    placeholder: \"Search by name, email, phone...\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"KYC Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: kycFilter,\n                                    onChange: (e)=>setKycFilter(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All Statuses\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PENDING\",\n                                            children: \"Pending\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"IN_REVIEW\",\n                                            children: \"In Review\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"APPROVED\",\n                                            children: \"Approved\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"REJECTED\",\n                                            children: \"Rejected\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"EXPIRED\",\n                                            children: \"Expired\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Whitelist Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: whitelistFilter,\n                                    onChange: (e)=>setWhitelistFilter(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"true\",\n                                            children: \"Whitelisted\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"false\",\n                                            children: \"Not Whitelisted\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchTerm('');\n                                    setKycFilter('');\n                                    setWhitelistFilter('');\n                                    setCurrentPage(1);\n                                },\n                                className: \"w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Client\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"KYC Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Whitelist\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Agreement\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Qualification Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: clientsData === null || clientsData === void 0 ? void 0 : clientsData.clients.map((client)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    client.firstName,\n                                                                    \" \",\n                                                                    client.lastName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"ID: \",\n                                                                    client.id.slice(0, 8),\n                                                                    \"...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: client.email || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: client.phoneNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                    children: client.nationality\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getKycStatusColor(client.kycStatus)),\n                                                        children: client.kycStatus\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.isWhitelisted ? 'text-green-600 bg-green-100' : 'text-gray-600 bg-gray-100'),\n                                                        children: client.isWhitelisted ? 'Whitelisted' : 'Not Whitelisted'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.agreementAccepted ? 'text-green-600 bg-green-100' : 'text-gray-600 bg-gray-100'),\n                                                        children: client.agreementAccepted ? 'Accepted' : 'Not Accepted'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>router.push(\"/clients/\".concat(client.id)),\n                                                        className: \"text-blue-600 hover:text-blue-900 mr-3\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, client.id, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    clientsData && clientsData.pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex justify-between sm:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                        disabled: currentPage === 1,\n                                        className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage(Math.min(clientsData.pagination.totalPages, currentPage + 1)),\n                                        disabled: currentPage === clientsData.pagination.totalPages,\n                                        className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\",\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"Showing\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: (currentPage - 1) * 10 + 1\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this),\n                                                ' ',\n                                                \"to\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: Math.min(currentPage * 10, clientsData.pagination.total)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                ' ',\n                                                \"of\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: clientsData.pagination.total\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                ' ',\n                                                \"results\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                                    disabled: currentPage === 1,\n                                                    className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\",\n                                                    children: \"Previous\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                Array.from({\n                                                    length: Math.min(5, clientsData.pagination.totalPages)\n                                                }, (_, i)=>{\n                                                    const page = i + 1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(page),\n                                                        className: \"relative inline-flex items-center px-4 py-2 border text-sm font-medium \".concat(currentPage === page ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'),\n                                                        children: page\n                                                    }, page, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                }),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentPage(Math.min(clientsData.pagination.totalPages, currentPage + 1)),\n                                                    disabled: currentPage === clientsData.pagination.totalPages,\n                                                    className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\",\n                                                    children: \"Next\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ClientManagement.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientManagement, \"uAXmC5LJc9iBNvrYWjNuZWirHlw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation\n    ];\n});\n_c = ClientManagement;\nvar _c;\n$RefreshReg$(_c, \"ClientManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClientManagement.tsx\n"));

/***/ })

});