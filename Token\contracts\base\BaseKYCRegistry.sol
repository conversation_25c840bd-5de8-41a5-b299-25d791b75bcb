// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "../interfaces/IKYCRegistry.sol";

/**
 * @title BaseKYCRegistry
 * @dev Base implementation of the KYC registry functionality for ERC-3643 compliant tokens
 */
abstract contract BaseKYCRegistry is 
    Initializable, 
    AccessControlUpgradeable,
    IKYCRegistry 
{
    bytes32 public constant KYC_VERIFIER_ROLE = keccak256("KYC_VERIFIER_ROLE");
    
    // Mapping of KYC approved addresses
    mapping(address => bool) private _kycApproved;
    
    /**
     * @dev Initialize the KYC registry
     * @param admin The address to be granted KYC_VERIFIER_ROLE
     */
    function __BaseKYCRegistry_init(address admin) internal onlyInitializing {
        require(admin != address(0), "BaseKYCRegistry: admin cannot be zero address");
        _grantRole(KYC_VERIFIER_ROLE, admin);
    }
    
    /**
     * @dev Check if an address is KYC approved
     * @param account The address to check
     * @return bool True if the address is KYC approved, false otherwise
     */
    function isKycApproved(address account) public view virtual override returns (bool) {
        return _kycApproved[account];
    }
    
    /**
     * @dev Modifier for functions that can only be called by a KYC verifier
     */
    modifier onlyKYCVerifier() {
        require(
            hasRole(KYC_VERIFIER_ROLE, _msgSender()) || 
            hasRole(DEFAULT_ADMIN_ROLE, _msgSender()),
            "BaseKYCRegistry: caller is not a KYC verifier or admin"
        );
        _;
    }

    /**
     * @dev Internal function to approve KYC for an address
     * @param account The address to approve KYC for
     */
    function _approveKyc(address account) internal virtual {
        require(account != address(0), "BaseKYCRegistry: cannot approve KYC for zero address");
        require(!_kycApproved[account], "BaseKYCRegistry: address already KYC approved");
        
        _kycApproved[account] = true;
        emit KycApproved(account);
    }
    
    /**
     * @dev Internal function to revoke KYC approval for an address
     * @param account The address to revoke KYC approval from
     */
    function _revokeKyc(address account) internal virtual {
        require(account != address(0), "BaseKYCRegistry: cannot revoke KYC for zero address");
        require(_kycApproved[account], "BaseKYCRegistry: address not KYC approved");
        
        _kycApproved[account] = false;
        emit KycRevoked(account);
    }
    
    /**
     * @dev Approve KYC for an address
     * @param account The address to approve KYC for
     */
    function approveKyc(address account) public virtual override onlyKYCVerifier {
        _approveKyc(account);
    }
    
    /**
     * @dev Revoke KYC approval for an address
     * @param account The address to revoke KYC approval from
     */
    function revokeKyc(address account) public virtual override onlyKYCVerifier {
        _revokeKyc(account);
    }
    
    /**
     * @dev Batch approve KYC for addresses
     * @param accounts The addresses to approve KYC for
     */
    function batchApproveKyc(address[] calldata accounts) external virtual override onlyKYCVerifier {
        uint256 length = accounts.length;
        require(length > 0, "BaseKYCRegistry: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && !_kycApproved[addr]) {
                _kycApproved[addr] = true;
                emit KycApproved(addr);
            }
        }
    }
    
    /**
     * @dev Batch revoke KYC approval for addresses
     * @param accounts The addresses to revoke KYC approval from
     */
    function batchRevokeKyc(address[] calldata accounts) external virtual override onlyKYCVerifier {
        uint256 length = accounts.length;
        require(length > 0, "BaseKYCRegistry: empty addresses array");
        
        for (uint256 i = 0; i < length; i++) {
            address addr = accounts[i];
            if (addr != address(0) && _kycApproved[addr]) {
                _kycApproved[addr] = false;
                emit KycRevoked(addr);
            }
        }
    }
    
    /**
     * @dev Add a KYC verifier
     * @param verifier The address to grant KYC_VERIFIER_ROLE to
     */
    function addKycVerifier(address verifier) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(verifier != address(0), "BaseKYCRegistry: verifier cannot be zero address");
        grantRole(KYC_VERIFIER_ROLE, verifier);
    }
    
    /**
     * @dev Remove a KYC verifier
     * @param verifier The address to revoke KYC_VERIFIER_ROLE from
     */
    function removeKycVerifier(address verifier) external onlyRole(DEFAULT_ADMIN_ROLE) {
        revokeRole(KYC_VERIFIER_ROLE, verifier);
    }
}