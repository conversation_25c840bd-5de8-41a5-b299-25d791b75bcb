// Check if wallet ****************************************** exists in database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkExistingWallet() {
  console.log('=== Checking Existing Wallet ===');
  
  const targetWallet = '******************************************';
  
  try {
    // Check if client exists with this wallet
    const client = await prisma.client.findFirst({
      where: { 
        walletAddress: {
          equals: targetWallet,
          mode: 'insensitive'
        }
      },
      include: {
        tokenApprovals: {
          include: {
            token: {
              select: { address: true, symbol: true, name: true }
            }
          }
        }
      }
    });
    
    if (client) {
      console.log('✅ Client found with this wallet:');
      console.log(`   ID: ${client.id}`);
      console.log(`   Email: ${client.email}`);
      console.log(`   Name: ${client.firstName} ${client.lastName}`);
      console.log(`   Wallet: ${client.walletAddress}`);
      console.log(`   Global Whitelisted: ${client.isWhitelisted}`);
      console.log(`   KYC Status: ${client.kycStatus}`);
      console.log(`   Token Approvals: ${client.tokenApprovals.length}`);
      
      if (client.tokenApprovals.length > 0) {
        console.log('\n   Token whitelist status:');
        client.tokenApprovals.forEach(approval => {
          const status = approval.whitelistApproved ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
          console.log(`     - ${approval.token.symbol}: ${status}`);
        });
      } else {
        console.log('   No token approvals found');
      }
      
      return client;
    } else {
      console.log('❌ No client found with this wallet address');
      
      // Check if any client has a similar wallet address
      const allClients = await prisma.client.findMany({
        select: { id: true, email: true, walletAddress: true }
      });
      
      console.log(`\nFound ${allClients.length} total clients in database:`);
      allClients.forEach(c => {
        console.log(`   - ${c.email}: ${c.walletAddress || 'No wallet'}`);
      });
      
      return null;
    }
    
  } catch (error) {
    console.error('Error checking wallet:', error);
    return null;
  }
}

async function testWhitelistAPI(walletAddress) {
  console.log('\n=== Testing Whitelist API ===');
  
  try {
    const fetch = require('node-fetch');
    
    // Get all tokens
    const tokens = await prisma.token.findMany({
      select: { address: true, symbol: true }
    });

    if (tokens.length === 0) {
      console.log('No tokens to test');
      return;
    }

    const tokenAddresses = tokens.map(t => t.address);

    const response = await fetch('http://localhost:3000/api/whitelist/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        walletAddress: walletAddress,
        tokenAddresses: tokenAddresses
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Whitelist API Response:');
      console.log(`Wallet: ${data.walletAddress}`);
      console.log(`Global Whitelisted: ${data.globalWhitelisted}`);
      console.log(`KYC Status: ${data.kycStatus}`);
      console.log('\nToken-specific whitelist status:');
      
      data.tokens.forEach(token => {
        const tokenInfo = tokens.find(t => t.address.toLowerCase() === token.tokenAddress.toLowerCase());
        const symbol = tokenInfo?.symbol || 'UNKNOWN';
        const status = token.isWhitelisted ? '✅ WHITELISTED' : '❌ NOT WHITELISTED';
        console.log(`  ${symbol}: ${status}`);
      });
      
      const whitelistedCount = data.tokens.filter(t => t.isWhitelisted).length;
      console.log(`\nSummary: ${whitelistedCount}/${data.tokens.length} tokens whitelisted`);
      
    } else {
      console.log('❌ Whitelist API test failed:', response.status);
    }
  } catch (error) {
    console.error('Error testing whitelist API:', error);
  }
}

async function main() {
  const targetWallet = '******************************************';
  
  const existingClient = await checkExistingWallet();
  await testWhitelistAPI(targetWallet);
  
  if (!existingClient) {
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Create a client with this wallet address');
    console.log('2. Add token approvals for the tokens you want whitelisted');
    console.log('3. Test the whitelist tags in the client application');
  } else {
    console.log('\n🎯 READY TO TEST:');
    console.log('1. Login to client app with the email above');
    console.log('2. Connect wallet: ******************************************');
    console.log('3. Visit /offers page to see WHITELISTED tags');
  }
}

main().catch(console.error).finally(() => prisma.$disconnect());
