import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const tokenSymbol = searchParams.get('tokenSymbol');

    if (!tokenAddress && !tokenSymbol) {
      return NextResponse.json({ error: 'Token address or symbol required' }, { status: 400 });
    }

    // TODO: Query database for token-specific agreement acceptance
    // Example structure:
    // SELECT * FROM token_agreements 
    // WHERE user_email = ? AND (token_address = ? OR token_symbol = ?)

    const mockAgreementStatus = {
      accepted: false,
      acceptedAt: null,
      tokenAddress: tokenAddress,
      tokenSymbol: tokenSymbol,
      agreementVersion: '1.0',
    };

    return NextResponse.json(mockAgreementStatus);
  } catch (error) {
    console.error('Error fetching token agreement status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch token agreement status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      tokenAddress,
      tokenSymbol,
      accepted,
      agreementVersion = '1.0'
    } = body;

    if (!tokenAddress && !tokenSymbol) {
      return NextResponse.json({ error: 'Token address or symbol required' }, { status: 400 });
    }

    if (!accepted) {
      return NextResponse.json({ error: 'Agreement must be accepted' }, { status: 400 });
    }

    const acceptedAt = new Date().toISOString();
    const userEmail = session.user.email;

    // Store agreement acceptance (in production, this would be in database)
    const agreementKey = `token_agreement_${userEmail}_${tokenAddress || tokenSymbol}`;

    console.log('💾 Saving token agreement acceptance:', {
      userEmail,
      tokenAddress,
      tokenSymbol,
      accepted,
      acceptedAt,
      agreementVersion,
    });

    // TODO: Save token-specific agreement acceptance to database
    // Example structure:
    // INSERT INTO token_agreements
    // (user_email, token_address, token_symbol, accepted, accepted_at, agreement_version)
    // VALUES (?, ?, ?, ?, ?, ?)
    // ON DUPLICATE KEY UPDATE
    // accepted = VALUES(accepted),
    // accepted_at = VALUES(accepted_at),
    // agreement_version = VALUES(agreement_version)

    return NextResponse.json({
      success: true,
      message: 'Token agreement acceptance saved successfully',
      data: {
        accepted: true,
        acceptedAt,
        tokenAddress,
        tokenSymbol,
        agreementVersion,
      }
    });
  } catch (error) {
    console.error('Error saving token agreement acceptance:', error);
    return NextResponse.json(
      { error: 'Failed to save token agreement acceptance' },
      { status: 500 }
    );
  }
}
