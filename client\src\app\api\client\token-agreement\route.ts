import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const tokenSymbol = searchParams.get('tokenSymbol');

    if (!tokenAddress && !tokenSymbol) {
      return NextResponse.json({ error: 'Token address or symbol required' }, { status: 400 });
    }

    // TODO: Query database for token-specific agreement acceptance
    // Example structure:
    // SELECT * FROM token_agreements 
    // WHERE user_email = ? AND (token_address = ? OR token_symbol = ?)

    const mockAgreementStatus = {
      accepted: false,
      acceptedAt: null,
      tokenAddress: tokenAddress,
      tokenSymbol: tokenSymbol,
      agreementVersion: '1.0',
    };

    return NextResponse.json(mockAgreementStatus);
  } catch (error) {
    console.error('Error fetching token agreement status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch token agreement status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const userEmail = session.user.email;

    // Try to call admin panel API to save token agreement
    const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';

    const requestData = {
      ...body,
      clientEmail: userEmail,
    };

    try {
      const response = await fetch(`${adminApiUrl}/token-agreements`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const result = await response.json();

        console.log('💾 Saved token agreement via admin API:', {
          userEmail,
          tokenAddress: body.tokenAddress,
          tokenSymbol: body.tokenSymbol,
          accepted: body.accepted
        });

        return NextResponse.json(result);
      } else {
        console.warn('⚠️ Admin API not available for saving agreement, using fallback');
      }
    } catch (error) {
      console.warn('⚠️ Admin API error for saving agreement, using fallback:', error);
    }

    // Fallback: Log the agreement and return success
    const acceptedAt = new Date().toISOString();

    console.log('💾 Fallback: Logging token agreement acceptance:', {
      userEmail,
      tokenAddress: body.tokenAddress,
      tokenSymbol: body.tokenSymbol,
      accepted: body.accepted,
      acceptedAt,
      data: requestData
    });

    return NextResponse.json({
      success: true,
      message: 'Token agreement acceptance saved successfully (fallback mode)',
      data: {
        accepted: true,
        acceptedAt,
        tokenAddress: body.tokenAddress,
        tokenSymbol: body.tokenSymbol,
        agreementVersion: body.agreementVersion || '1.0',
      }
    });
  } catch (error) {
    console.error('Error saving token agreement acceptance:', error);
    return NextResponse.json(
      { error: 'Failed to save token agreement acceptance' },
      { status: 500 }
    );
  }
}
