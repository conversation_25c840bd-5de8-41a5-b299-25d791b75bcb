const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Debugging Factory Contract...");

  const [deployer] = await ethers.getSigners();
  console.log("Account:", deployer.address);
  console.log("Balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Factory address
  const factoryAddress = "******************************************";
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  console.log("Factory address:", factoryAddress);

  try {
    // Check if we have deployer role
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
    console.log("Has deployer role:", hasDeployerRole);

    if (!hasDeployerRole) {
      console.log("❌ Account does not have deployer role");
      
      // Check if we have admin role to grant deployer role
      const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
      const hasAdminRole = await factory.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
      console.log("Has admin role:", hasAdminRole);

      if (hasAdminRole) {
        console.log("🔧 Granting deployer role...");
        const grantTx = await factory.grantRole(DEPLOYER_ROLE, deployer.address);
        await grantTx.wait();
        console.log("✅ Deployer role granted");
      } else {
        console.log("❌ Account does not have admin role either");
        return;
      }
    }

    // Check current implementations
    console.log("\n🔍 Current implementations:");
    const tokenImpl = await factory.securityTokenImplementation();
    const whitelistImpl = await factory.whitelistImplementation();
    const whitelistKYCImpl = await factory.whitelistWithKYCImplementation();

    console.log("SecurityToken:", tokenImpl);
    console.log("Whitelist:", whitelistImpl);
    console.log("WhitelistWithKYC:", whitelistKYCImpl);

    // Try to estimate gas for a simple deployment
    console.log("\n⛽ Estimating gas for deployment...");
    try {
      const gasEstimate = await factory.deploySecurityToken.estimateGas(
        "Debug Test Token",
        "DBG001",
        0,
        ethers.parseUnits("1000", 0),
        deployer.address,
        "10 USD",
        "Tier 1: 5%",
        "Debug test token",
        ""
      );
      console.log("Gas estimate:", gasEstimate.toString());
    } catch (gasError) {
      console.log("❌ Gas estimation failed:", gasError.message);
    }

    // Try to estimate gas for deployment with options
    console.log("\n⛽ Estimating gas for deployment with options...");
    try {
      const gasEstimateOptions = await factory.deploySecurityTokenWithOptions.estimateGas(
        "Debug Test Token With Options",
        "DBG002",
        0,
        ethers.parseUnits("1000", 0),
        deployer.address,
        "10 USD",
        "Tier 1: 5%",
        "Debug test token with options",
        "",
        "https://example.com/agreement.pdf",
        true
      );
      console.log("Gas estimate with options:", gasEstimateOptions.toString());
    } catch (gasError) {
      console.log("❌ Gas estimation with options failed:", gasError.message);
    }

  } catch (error) {
    console.log("❌ Error:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Debug failed:", error);
    process.exit(1);
  });
