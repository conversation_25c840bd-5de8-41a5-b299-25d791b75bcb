const { ethers } = require('ethers');
const SecurityTokenFactoryABI = require('./src/contracts/SecurityTokenFactory.json').abi;
const SecurityTokenABI = require('./src/contracts/SecurityToken.json').abi;

// Configuration
const FACTORY_ADDRESS = "******************************************"; // Upgraded factory
const RPC_URL = "https://rpc-amoy.polygon.technology";
const KNOWN_TOKENS = [
  "******************************************", // Augment_019
  "******************************************", // Augment_01z
  "******************************************"  // Test Image Token
];

async function testFactoryConnection() {
  console.log("🔍 Testing Factory Contract Connection...");
  console.log("Factory Address:", FACTORY_ADDRESS);
  console.log("RPC URL:", RPC_URL);
  console.log("=" .repeat(50));

  try {
    // Create provider
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    console.log("✅ Provider created successfully");

    // Test basic connectivity
    const network = await provider.getNetwork();
    console.log("✅ Network connected:", network.name, "Chain ID:", network.chainId.toString());

    // Create factory contract instance
    const factory = new ethers.Contract(FACTORY_ADDRESS, SecurityTokenFactoryABI, provider);
    console.log("✅ Factory contract instance created");

    // Test 1: Check if factory supports enumeration
    console.log("\n📊 Testing Factory Enumeration Support...");
    try {
      const tokenCount = await factory.getTokenCount();
      console.log("✅ getTokenCount() works:", tokenCount.toString(), "tokens");

      if (tokenCount > 0) {
        try {
          const allTokens = await factory.getAllDeployedTokens();
          console.log("✅ getAllDeployedTokens() works:", allTokens.length, "addresses returned");
          console.log("Token addresses:", allTokens);
        } catch (error) {
          console.log("❌ getAllDeployedTokens() failed:", error.message);
        }
      } else {
        console.log("ℹ️  No tokens deployed in factory yet");
      }
    } catch (error) {
      console.log("❌ Factory enumeration not supported:", error.message);
    }

    // Test 2: Check known tokens directly
    console.log("\n🔍 Testing Known Token Contracts...");
    for (const tokenAddress of KNOWN_TOKENS) {
      try {
        console.log(`\nTesting token: ${tokenAddress}`);
        const tokenContract = new ethers.Contract(tokenAddress, SecurityTokenABI, provider);

        const [name, symbol, decimals, totalSupply] = await Promise.all([
          tokenContract.name(),
          tokenContract.symbol(),
          tokenContract.decimals(),
          tokenContract.totalSupply()
        ]);

        console.log(`✅ ${name} (${symbol})`);
        console.log(`   Decimals: ${decimals}`);
        console.log(`   Total Supply: ${totalSupply.toString()}`);

        // Try to get max supply
        try {
          const maxSupply = await tokenContract.maxSupply();
          console.log(`   Max Supply: ${maxSupply.toString()}`);
        } catch (error) {
          console.log(`   Max Supply: Not available (${error.message})`);
        }

        // Try to get image URL
        try {
          const imageUrl = await tokenContract.tokenImageUrl();
          console.log(`   Image URL: ${imageUrl || 'Not set'}`);
        } catch (error) {
          console.log(`   Image URL: Not supported (${error.message})`);
        }

      } catch (error) {
        console.log(`❌ Failed to load token ${tokenAddress}:`, error.message);
      }
    }

    // Test 3: Check factory events
    console.log("\n📅 Testing Factory Events...");
    try {
      const filter = factory.filters.TokenDeployed();
      const events = await factory.queryFilter(filter, -10000); // Last 10k blocks
      console.log(`✅ Found ${events.length} TokenDeployed events in last 10k blocks`);

      if (events.length > 0) {
        console.log("Recent deployments:");
        events.slice(-5).forEach((event, index) => {
          console.log(`  ${index + 1}. Token: ${event.args?.tokenAddress || 'Unknown'}`);
          console.log(`     Block: ${event.blockNumber}`);
        });
      }
    } catch (error) {
      console.log("❌ Failed to query events:", error.message);
    }

    console.log("\n" + "=" .repeat(50));
    console.log("🎉 Factory connection test completed!");

  } catch (error) {
    console.error("💥 Fatal error during testing:", error);
  }
}

// Run the test
testFactoryConnection().catch(console.error);
