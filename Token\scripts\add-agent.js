// <PERSON><PERSON><PERSON> to add an address to the AGENT_ROLE of a whitelist contract
const { ethers } = require("hardhat");

async function main() {
  try {
    // Get the token address from the environment variables
    const tokenAddress = process.env.TOKEN_ADDRESS;
    if (!tokenAddress) {
      throw new Error("TOKEN_ADDRESS environment variable not set");
    }

    // Get the agent address from the environment variables
    const agentAddress = process.env.AGENT_ADDRESS;
    if (!agentAddress) {
      throw new Error("AGENT_ADDRESS environment variable not set");
    }

    console.log(`Token address: ${tokenAddress}`);
    console.log(`Agent address to add: ${agentAddress}`);

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`Admin wallet: ${deployer.address}`);
    console.log(`Network: ${(await ethers.provider.getNetwork()).name}`);

    // First, connect to the token contract to get the whitelist address
    console.log("Connecting to token contract...");
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const tokenContract = await SecurityToken.attach(tokenAddress);
    
    console.log("Getting whitelist address from token contract...");
    const whitelistAddress = await tokenContract.whitelistAddress();
    console.log(`Whitelist contract address: ${whitelistAddress}`);
    
    if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
      throw new Error("Whitelist contract address not set on the token");
    }
    
    // Connect to the whitelist contract
    console.log("Connecting to whitelist contract...");
    const Whitelist = await ethers.getContractFactory("Whitelist");
    const whitelistContract = await Whitelist.attach(whitelistAddress);
    
    // Check if the executor has DEFAULT_ADMIN_ROLE
    const DEFAULT_ADMIN_ROLE = ethers.ZeroHash; // Default admin role is 0x00
    console.log("Checking if wallet has DEFAULT_ADMIN_ROLE...");
    const hasAdminRole = await whitelistContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    console.log(`Wallet has DEFAULT_ADMIN_ROLE: ${hasAdminRole}`);
    
    if (!hasAdminRole) {
      throw new Error(`Your wallet (${deployer.address}) does not have the DEFAULT_ADMIN_ROLE on the whitelist contract`);
    }
    
    // Check if address already has the AGENT_ROLE
    const AGENT_ROLE = ethers.keccak256(ethers.toUtf8Bytes("AGENT_ROLE"));
    console.log(`AGENT_ROLE hash: ${AGENT_ROLE}`);
    console.log("Checking if address already has AGENT_ROLE...");
    const alreadyHasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, agentAddress);
    console.log(`Address already has AGENT_ROLE: ${alreadyHasAgentRole}`);
    
    if (alreadyHasAgentRole) {
      console.log(`Address ${agentAddress} already has the AGENT_ROLE`);
      return;
    }
    
    // Add the agent to the whitelist contract
    console.log(`Adding ${agentAddress} as an agent...`);
    const tx = await whitelistContract.addAgent(agentAddress);
    console.log(`Transaction hash: ${tx.hash}`);
    
    // Wait for the transaction to be mined
    console.log("Waiting for transaction confirmation...");
    const receipt = await tx.wait();
    console.log(`Success! Transaction confirmed in block ${receipt.blockNumber}`);
    
    // Verify the agent was added
    console.log("Verifying AGENT_ROLE was granted...");
    const hasAgentRole = await whitelistContract.hasRole(AGENT_ROLE, agentAddress);
    console.log(`Address ${agentAddress} has AGENT_ROLE: ${hasAgentRole}`);
    
  } catch (error) {
    console.error("Error:", error.message);
    if (error.transaction) {
      console.error("Transaction that caused the error:", error.transaction);
    }
    if (error.data) {
      console.error("Error data:", error.data);
    }
    throw error;
  }
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 