// Test script to verify token API endpoints and data
const fetch = require('node-fetch');

const ADMIN_API_URL = 'http://localhost:3000/api';
const CLIENT_API_URL = 'http://localhost:3003/api';

async function testAdminTokensAPI() {
  console.log('=== Testing Admin Panel Tokens API ===');

  try {
    const response = await fetch(`${ADMIN_API_URL}/tokens?source=database`);
    const data = await response.json();

    console.log(`Status: ${response.status}`);
    console.log(`Number of tokens: ${data.length}`);

    if (data.length > 0) {
      console.log('\nFirst token data:');
      const token = data[0];
      console.log(`- Name: ${token.name}`);
      console.log(`- Symbol: ${token.symbol}`);
      console.log(`- Address: ${token.address}`);
      console.log(`- Decimals: ${token.decimals}`);
      console.log(`- Max Supply: ${token.maxSupply}`);
      console.log(`- Total Supply: ${token.totalSupply}`);
      console.log(`- Token Price: ${token.tokenPrice}`);
      console.log(`- Currency: ${token.currency}`);
      console.log(`- Token Type: ${token.tokenType}`);
    } else {
      console.log('No tokens found in database');
    }
  } catch (error) {
    console.error('Error testing admin API:', error.message);
  }
}

async function testClientTokensAPI() {
  console.log('\n=== Testing Client App Tokens API ===');

  try {
    const response = await fetch(`${CLIENT_API_URL}/tokens`);
    const data = await response.json();

    console.log(`Status: ${response.status}`);
    console.log(`Number of tokens: ${data.length}`);

    if (data.length > 0) {
      console.log('\nFirst token data (transformed for client):');
      const token = data[0];
      console.log(`- Name: ${token.name}`);
      console.log(`- Symbol: ${token.symbol}`);
      console.log(`- Address: ${token.address}`);
      console.log(`- Decimals: ${token.decimals}`);
      console.log(`- Max Supply: ${token.maxSupply}`);
      console.log(`- Total Supply: ${token.totalSupply}`);
      console.log(`- Price: ${token.price}`);
      console.log(`- Currency: ${token.currency}`);
      console.log(`- Category: ${token.category}`);
    } else {
      console.log('No tokens found');
    }
  } catch (error) {
    console.error('Error testing client API:', error.message);
  }
}

async function addTestToken() {
  console.log('\n=== Adding Test Token ===');

  const testToken = {
    address: '0xfccB88D208f5Ec7166ce2291138aaD5274C671dE',
    name: 'Augment_019',
    symbol: 'AUG019',
    decimals: 0,
    maxSupply: '1000000',
    totalSupply: '0',
    tokenType: 'commodity',
    tokenPrice: '10 USD',
    currency: 'USD',
    bonusTiers: 'Tier 1: 5%, Tier 2: 10%, Tier 3: 15%',
    whitelistAddress: '******************************************',
    adminAddress: '******************************************',
    hasKYC: true,
    network: 'amoy',
    deploymentNotes: 'Commodity token deployed via admin panel'
  };

  try {
    const response = await fetch(`${ADMIN_API_URL}/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testToken),
    });

    const data = await response.json();

    if (response.ok) {
      console.log('Test token added successfully!');
      console.log(`Token ID: ${data.id}`);
    } else {
      console.log(`Failed to add token: ${data.error}`);
    }
  } catch (error) {
    console.error('Error adding test token:', error.message);
  }
}

async function main() {
  await testAdminTokensAPI();
  await testClientTokensAPI();

  // Test again after adding token
  console.log('\n=== Testing After Adding Token ===');
  await testAdminTokensAPI();
  await testClientTokensAPI();
}

main().catch(console.error);
