require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  try {
    console.log("Deploying new SecurityTokenFactory with KYC support...");
    
    // Get network
    const network = process.env.NETWORK || "amoy";
    console.log(`Network: ${network}`);
    
    // Get admin address from environment or use deployer
    let adminAddress = process.env.ADMIN_ADDRESS;
    if (!adminAddress) {
      console.log("No ADMIN_ADDRESS specified, using deployer address");
    }
    
    // Get deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`Deployer address: ${deployer.address}`);
    
    // Use deployer address as admin if not specified
    if (!adminAddress) {
      adminAddress = deployer.address;
    }
    
    console.log(`Admin address: ${adminAddress}`);
    
    // Deploy the contract
    console.log("Deploying SecurityTokenFactory...");
    
    // Compile the contracts if needed
    const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    
    // Deploy with admin address
    console.log("Deploying with parameters:", [adminAddress]);
    const factory = await SecurityTokenFactory.deploy(adminAddress);
    
    console.log(`SecurityTokenFactory deployed to: ${factory.address}`);
    
    // Verify the contract is properly set up
    console.log("\nVerifying contract...");
    
    // Check token implementation address
    const securityTokenImplementation = await factory.securityTokenImplementation();
    console.log(`Security Token Implementation: ${securityTokenImplementation}`);
    
    // Check whitelist implementation address
    const whitelistImplementation = await factory.whitelistImplementation();
    console.log(`Whitelist Implementation: ${whitelistImplementation}`);
    
    // Check whitelist with KYC implementation address
    const whitelistWithKYCImplementation = await factory.whitelistWithKYCImplementation();
    console.log(`Whitelist With KYC Implementation: ${whitelistWithKYCImplementation}`);
    
    // Check that deployer has both admin and deployer roles
    const DEFAULT_ADMIN_ROLE = ethers.ZeroHash;
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    
    const hasAdminRole = await factory.hasRole(DEFAULT_ADMIN_ROLE, adminAddress);
    const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, adminAddress);
    
    console.log(`Admin has DEFAULT_ADMIN_ROLE: ${hasAdminRole}`);
    console.log(`Admin has DEPLOYER_ROLE: ${hasDeployerRole}`);
    
    console.log("\n✅ Deployment complete!");
    console.log(`\nUpdate your configuration to use the new factory address: ${factory.address}`);
    console.log(`For ${network}, update the factory address in your admin panel configuration.`);
    
  } catch (error) {
    console.error("Error deploying factory:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  }); 