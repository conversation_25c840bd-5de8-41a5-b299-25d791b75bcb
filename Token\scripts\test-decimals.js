// Test script to verify decimals functionality
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    // Get signers and network information
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Testing decimals functionality with account:", deployer.address);
    console.log("Network:", networkName);

    // Load factory address from deployment file
    const deploymentsDir = path.join(__dirname, "../deployments");
    const deploymentFile = path.join(deploymentsDir, `${networkName}.json`);

    if (!fs.existsSync(deploymentFile)) {
      console.error(`Deployment file not found for network ${networkName}`);
      console.error("Please run the factory deployment script first");
      process.exit(1);
    }

    const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));
    const factoryAddress = deploymentInfo.factory;

    console.log("Using factory at:", factoryAddress);

    // Load the factory contract
    const factory = await hre.ethers.getContractAt("SecurityTokenFactory", factoryAddress);

    // Test different decimal configurations
    const testCases = [
      { name: "Zero Decimals Token", symbol: "ZERO", decimals: 0 },
      { name: "Six Decimals Token", symbol: "SIX", decimals: 6 },
      { name: "Standard Token", symbol: "STD", decimals: 18 }
    ];

    for (const testCase of testCases) {
      console.log(`\n--- Testing ${testCase.name} (${testCase.decimals} decimals) ---`);

      // Deploy token
      const tx = await factory.deploySecurityToken(
        testCase.name,
        testCase.symbol,
        testCase.decimals,
        hre.ethers.parseEther("1000000"),
        deployer.address,
        "10 USD",
        "Tier 1: 5%",
        `Test token with ${testCase.decimals} decimals`
      );

      console.log("Transaction sent:", tx.hash);
      const receipt = await tx.wait();

      // Try to get token address from factory
      const tokenAddress = await factory.getTokenAddressBySymbol(testCase.symbol);

      if (tokenAddress && tokenAddress !== hre.ethers.ZeroAddress) {
        console.log("Token deployed at:", tokenAddress);

        // Get the token contract and verify decimals
        const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);
        const actualDecimals = await token.decimals();

        console.log("Expected decimals:", testCase.decimals);
        console.log("Actual decimals:", actualDecimals);
        console.log("✅ Decimals match:", actualDecimals === testCase.decimals);

        // Also verify other properties
        console.log("Token name:", await token.name());
        console.log("Token symbol:", await token.symbol());
        console.log("Max supply:", hre.ethers.formatUnits(await token.maxSupply(), actualDecimals));
      } else {
        console.error("❌ Token not found in factory registry");
      }
    }

    console.log("\n🎉 All decimal tests completed successfully!");

  } catch (error) {
    console.error("Error during testing:", error);
    process.exitCode = 1;
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
