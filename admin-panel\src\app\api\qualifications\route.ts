import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const tokenId = searchParams.get('tokenId');
    const clientId = searchParams.get('clientId');

    // Build where clause
    const whereClause: any = {};
    
    if (status && status !== 'ALL') {
      whereClause.qualificationStatus = status;
    }
    
    if (tokenId) {
      whereClause.tokenId = tokenId;
    }
    
    if (clientId) {
      whereClause.clientId = clientId;
    }

    // Fetch qualifications with related data
    const qualifications = await prisma.qualificationProgress.findMany({
      where: whereClause,
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            kycStatus: true,
            walletAddress: true,
            phoneNumber: true,
            nationality: true,
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
          }
        }
      },
      orderBy: [
        { qualificationStatus: 'asc' }, // PENDING first
        { updatedAt: 'desc' }
      ]
    });

    // Calculate summary statistics
    const summary = {
      total: qualifications.length,
      pending: qualifications.filter(q => q.qualificationStatus === 'PENDING').length,
      approved: qualifications.filter(q => q.qualificationStatus === 'APPROVED').length,
      rejected: qualifications.filter(q => q.qualificationStatus === 'REJECTED').length,
      forceApproved: qualifications.filter(q => q.qualificationStatus === 'FORCE_APPROVED').length,
    };

    return NextResponse.json({
      success: true,
      qualifications,
      summary,
    });

  } catch (error) {
    console.error('Error fetching qualifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualifications' },
      { status: 500 }
    );
  }
}

// Create a new qualification progress entry (for testing)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clientId,
      tokenId,
      countrySelected = false,
      countryValue,
      agreementAccepted = false,
      profileCompleted = false,
      walletConnected = false,
      kycCompleted = false,
      currentStep = 0,
      completedSteps = 0,
    } = body;

    if (!clientId) {
      return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });
    }

    // Check if qualification already exists
    const existing = await prisma.qualificationProgress.findFirst({
      where: {
        clientId,
        tokenId: tokenId || null,
      }
    });

    if (existing) {
      return NextResponse.json({ error: 'Qualification already exists for this client and token' }, { status: 400 });
    }

    // Create new qualification progress
    const qualification = await prisma.qualificationProgress.create({
      data: {
        clientId,
        tokenId,
        countrySelected,
        countryValue,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted,
        currentStep,
        completedSteps,
        qualificationStatus: 'PENDING',
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            kycStatus: true,
            walletAddress: true,
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
          }
        }
      }
    });

    console.log('Created qualification progress:', {
      id: qualification.id,
      clientEmail: qualification.client.email,
      tokenName: qualification.token?.name || 'Global',
      status: qualification.qualificationStatus,
    });

    return NextResponse.json({
      success: true,
      message: 'Qualification progress created successfully',
      qualification,
    });

  } catch (error) {
    console.error('Error creating qualification:', error);
    return NextResponse.json(
      { error: 'Failed to create qualification' },
      { status: 500 }
    );
  }
}
