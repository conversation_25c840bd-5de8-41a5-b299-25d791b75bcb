# ERC-3643 Compliant Security Token System

This repository contains a comprehensive implementation of ERC-3643 compliant security tokens with advanced compliance features, administrative tools, and client management capabilities.

## Project Structure

```
├── Token/                  # Blockchain smart contracts and infrastructure
│   ├── contracts/          # Smart contract source files
│   ├── scripts/           # Deployment and management scripts
│   ├── test/              # Contract test files
│   ├── artifacts/         # Compiled contract artifacts
│   └── README.md          # Token-specific documentation
├── admin-panel/           # Administrative web interface
├── client/                # Client portal application
└── docs/                  # Documentation
```

## Overview

This project implements a UUPS upgradeable, permissioned ERC-20 smart contract, modeled after the ERC-3643 framework. The contract includes on-chain compliance enforcement, is built using Solidity 0.8.20+, and is designed for Polygon-compatible networks.

## Features

- **Fully ERC-20 compliant** with minting & burning capabilities
- **UUPS Upgradeable pattern** for future improvements
- **On-chain compliance enforcement**:
  - Whitelist-based identity verification (KYC)
  - Address freezing/unfreezing functionality
  - Forced transfers for recovery and compliance
- **Access Control** with distinct roles:
  - `DEFAULT_ADMIN_ROLE`: Full authority over all settings and upgrades
  - `AGENT_ROLE`: Can mint tokens and manage whitelists
- **Pausable** functionality to halt all transfers when needed
- **Factory Deployment** system for easy token creation with custom parameters

## Contract Structure

The project consists of the following core contracts:

### 1. SecurityToken.sol

The main token contract that implements:
- ERC20 token standard
- Access control with roles
- Pausable functionality
- UUPS upgradeability
- Compliance enforcement through whitelist checks
- Direct whitelist management functions (addToWhitelist, batchAddToWhitelist, etc.)

### 2. Whitelist.sol

Manages the whitelist and frozen status of addresses:
- Add/remove addresses from whitelist
- Freeze/unfreeze addresses
- Batch operations
- Access control with roles
- UUPS upgradeability

### 3. SecurityTokenFactory.sol

Factory contract for deploying new tokens:
- Deploys new SecurityToken and Whitelist contracts
- Manages implementation contracts
- Access control for deployers

### 4. Admin Panel

A Next.js-based admin panel to manage tokens through a web interface:
- Create and deploy new tokens
- View token details
- Mint tokens and manage whitelists
- Pause/unpause tokens
- Upgrade contracts through the UI

## Setup Instructions

### Prerequisites

- Node.js and npm installed
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd erc3643
```

2. Install blockchain dependencies:
```bash
cd Token
npm install
```

3. Create a `.env.local` file in the Token directory and add your private key and API keys:
```
PRIVATE_KEY=your_private_key_here
POLYGONSCAN_API_KEY=your_polygonscan_api_key_here
```

4. Install admin panel dependencies (optional):
```bash
cd ../admin-panel
npm install
```

### Windows-Specific Instructions

If you're using Windows, please refer to the [Windows Whitelist Guide](WINDOWS_WHITELIST_GUIDE.md) for detailed instructions on setting environment variables in PowerShell and Command Prompt.

## Testing

Run the test suite to verify all functionality:

```bash
cd Token
npx hardhat test
```

For test coverage:

```bash
cd Token
npx hardhat coverage
```

## Deployment Instructions

**Note**: All deployment commands should be run from the `Token` directory.

### 1. Deploy the Factory

Deploy the SecurityTokenFactory contract:

```bash
cd Token
npx hardhat run scripts/01-deploy-factory.js --network mumbai
```

This will output the factory address that you'll need for the next steps.

### 2. Deploy a New Token

Deploy a new SecurityToken and Whitelist using the factory:

```bash
# For Unix/Linux/Mac:
cd Token
export FACTORY_ADDRESS=your_factory_address_here
export TOKEN_NAME="My Security Token"
export TOKEN_SYMBOL="MST"
export MAX_SUPPLY="1000000"
export TOKEN_PRICE="10 USD"
export BONUS_TIERS="Tier 1: 5%, Tier 2: 10%"

npx hardhat run scripts/02-deploy-token.js --network mumbai

# For Windows (Command Prompt):
cd Token
set FACTORY_ADDRESS=your_factory_address_here
set TOKEN_NAME="My Security Token"
set TOKEN_SYMBOL="MST"
set MAX_SUPPLY="1000000"
set TOKEN_PRICE="10 USD"
set BONUS_TIERS="Tier 1: 5%, Tier 2: 10%"

npx hardhat run scripts/02-deploy-token.js --network mumbai

# For Windows (PowerShell):
cd Token
$env:FACTORY_ADDRESS="your_factory_address_here"
$env:TOKEN_NAME="My Security Token"
$env:TOKEN_SYMBOL="MST"
$env:MAX_SUPPLY="1000000"
$env:TOKEN_PRICE="10 USD"
$env:BONUS_TIERS="Tier 1: 5%, Tier 2: 10%"

npx hardhat run scripts/02-deploy-token.js --network mumbai
```

### 3. Manage Whitelist

Add addresses to the whitelist:

```bash
# For Unix/Linux/Mac:
cd Token
export WHITELIST_ADDRESS=your_whitelist_address_here
export ACTION=batchAdd
export ADDRESSES=0xAddress1,0xAddress2,0xAddress3

npx hardhat run scripts/03-manage-whitelist.js --network mumbai

# For Windows (Command Prompt):
cd Token
set WHITELIST_ADDRESS=your_whitelist_address_here
set ACTION=batchAdd
set ADDRESSES=0xAddress1,0xAddress2,0xAddress3

npx hardhat run scripts/03-manage-whitelist.js --network mumbai

# For Windows (PowerShell):
cd Token
$env:WHITELIST_ADDRESS="your_whitelist_address_here"
$env:ACTION="batchAdd"
$env:ADDRESSES="0xAddress1,0xAddress2,0xAddress3"

npx hardhat run scripts/03-manage-whitelist.js --network mumbai
```

Freeze an address:

```bash
# For Unix/Linux/Mac:
export WHITELIST_ADDRESS=your_whitelist_address_here
export ACTION=freeze
export ADDRESS=0xAddress1

npx hardhat run scripts/03-manage-whitelist.js --network mumbai

# For Windows (Command Prompt):
set WHITELIST_ADDRESS=your_whitelist_address_here
set ACTION=freeze
set ADDRESS=0xAddress1

npx hardhat run scripts/03-manage-whitelist.js --network mumbai

# For Windows (PowerShell):
$env:WHITELIST_ADDRESS="your_whitelist_address_here"
$env:ACTION="freeze"
$env:ADDRESS="0xAddress1"

npx hardhat run scripts/03-manage-whitelist.js --network mumbai
```

### 4. Manage Token Operations

Mint tokens:

```bash
# For Unix/Linux/Mac:
export TOKEN_ADDRESS=your_token_address_here
export ACTION=mint
export TO_ADDRESS=0xRecipientAddress
export AMOUNT="1000"

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (Command Prompt):
set TOKEN_ADDRESS=your_token_address_here
set ACTION=mint
set TO_ADDRESS=0xRecipientAddress
set AMOUNT="1000"

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (PowerShell):
$env:TOKEN_ADDRESS="your_token_address_here"
$env:ACTION="mint"
$env:TO_ADDRESS="0xRecipientAddress"
$env:AMOUNT="1000"

npx hardhat run scripts/05-manage-token.js --network mumbai
```

Pause token transfers:

```bash
# For Unix/Linux/Mac:
export TOKEN_ADDRESS=your_token_address_here
export ACTION=pause

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (Command Prompt):
set TOKEN_ADDRESS=your_token_address_here
set ACTION=pause

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (PowerShell):
$env:TOKEN_ADDRESS="your_token_address_here"
$env:ACTION="pause"

npx hardhat run scripts/05-manage-token.js --network mumbai
```

Manage whitelist from token contract (after upgrading):

```bash
# For Unix/Linux/Mac:
export TOKEN_ADDRESS=your_token_address_here
export ACTION=addToWhitelist
export ADDRESS=0xAddress1

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (Command Prompt):
set TOKEN_ADDRESS=your_token_address_here
set ACTION=addToWhitelist
set ADDRESS=0xAddress1

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (PowerShell):
$env:TOKEN_ADDRESS="your_token_address_here"
$env:ACTION="addToWhitelist"
$env:ADDRESS="0xAddress1"

npx hardhat run scripts/05-manage-token.js --network mumbai
```

Batch whitelist addresses from token contract:

```bash
# For Unix/Linux/Mac:
export TOKEN_ADDRESS=your_token_address_here
export ACTION=batchAddToWhitelist
export ADDRESSES=0xAddress1,0xAddress2,0xAddress3

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (Command Prompt):
set TOKEN_ADDRESS=your_token_address_here
set ACTION=batchAddToWhitelist
set ADDRESSES=0xAddress1,0xAddress2,0xAddress3

npx hardhat run scripts/05-manage-token.js --network mumbai

# For Windows (PowerShell):
$env:TOKEN_ADDRESS="your_token_address_here"
$env:ACTION="batchAddToWhitelist"
$env:ADDRESSES="0xAddress1,0xAddress2,0xAddress3"

npx hardhat run scripts/05-manage-token.js --network mumbai
```

### 5. Upgrade Contracts

Upgrade a token contract:

```bash
# For Unix/Linux/Mac:
export CONTRACT_ADDRESS=your_token_address_here
export CONTRACT_TYPE=token

npx hardhat run scripts/04-upgrade-contracts.js --network mumbai

# For Windows (Command Prompt):
set CONTRACT_ADDRESS=your_token_address_here
set CONTRACT_TYPE=token

npx hardhat run scripts/04-upgrade-contracts.js --network mumbai

# For Windows (PowerShell):
$env:CONTRACT_ADDRESS="your_token_address_here"
$env:CONTRACT_TYPE="token"

npx hardhat run scripts/04-upgrade-contracts.js --network mumbai
```

Upgrade factory implementations:

```bash
# For Unix/Linux/Mac:
export CONTRACT_ADDRESS=your_factory_address_here
export CONTRACT_TYPE=factory

npx hardhat run scripts/04-upgrade-contracts.js --network mumbai

# For Windows (Command Prompt):
set CONTRACT_ADDRESS=your_factory_address_here
set CONTRACT_TYPE=factory

npx hardhat run scripts/04-upgrade-contracts.js --network mumbai

# For Windows (PowerShell):
$env:CONTRACT_ADDRESS="your_factory_address_here"
$env:CONTRACT_TYPE="factory"

npx hardhat run scripts/04-upgrade-contracts.js --network mumbai
```

## Helper Scripts

Several helper scripts are provided to assist with token management and troubleshooting:

### Token Info Helper

The token info helper script provides detailed information about a token and its whitelist:

```bash
# Unix/Linux/Mac:
export TOKEN_ADDRESS=<your-token-address>
npx hardhat run scripts/check-token-info.js --network amoy

# Windows PowerShell:
$env:TOKEN_ADDRESS="<your-token-address>"
npx hardhat run scripts/check-token-info.js --network amoy
```

This script:
- Shows comprehensive token information
- Displays the whitelist address if available
- Provides instructions for whitelist management
- Includes upgrade instructions if needed

## Admin Panel Setup

The admin panel provides a user-friendly interface for managing tokens and whitelists:

1. Navigate to the admin panel directory:
```bash
cd admin-panel
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
See `admin-panel/ENVIRONMENT_SETUP.md` for detailed instructions on setting up API keys and admin credentials.

4. Start the development server:
```bash
npm run dev
```

5. Open your browser and navigate to http://localhost:3000

### Admin Panel Features

- **Token Management**:
  - View token details including supply and whitelist status
  - Mint tokens to whitelisted addresses
  - Pause/unpause token transfers
  - Whitelist individual addresses directly from the token management page
  - Batch whitelist multiple addresses at once
  - Upgrade contracts to add new functionality

- **API Interface**:
  - RESTful API for programmatic token management
  - Token whitelist management endpoints
  - Contract upgrade endpoints

## Supported Networks

- **Polygon Mumbai Testnet**: For testing and development
- **Polygon Amoy Testnet**: For testing and development
- **Polygon Mainnet**: For production deployment

## Network Configuration

Network configuration is available in the `hardhat.config.js` file. You can modify network settings there.

## ABIs and Bytecode

After compiling, contract ABIs and bytecode can be found in the `artifacts/contracts/` directory.

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
