import React from 'react';

interface FactoryInfoProps {
  factoryAddress: string;
  network: string;
  hasDeployerRole: boolean;
}

/**
 * FactoryInfo Component
 * 
 * Displays information about the factory contract being used for token deployment
 */
const FactoryInfo: React.FC<FactoryInfoProps> = ({
  factoryAddress,
  network,
  hasDeployerRole
}) => {
  return (
    <div className="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6">
      <h3 className="font-bold mb-2">Using Factory Contract</h3>
      <p className="mb-2">
        Creating token using your deployed factory at: <span className="font-mono">{factoryAddress}</span>
      </p>
      {network === 'amoy' && (
        <p className="mb-2">
          This is your actual deployed factory contract on Amoy testnet.
        </p>
      )}
      {!hasDeployerRole && (
        <p className="mt-2 text-red-600 font-semibold">
          Warning: Your connected wallet may not have the DEPLOYER_ROLE required to create tokens.
        </p>
      )}
    </div>
  );
};

export default FactoryInfo;