@echo off
setlocal

REM Direct Transaction Executor for Amoy Testnet
echo ===== DIRECT TRANSACTION EXECUTOR FOR AMOY =====

REM Check if a parameter is provided
if "%~1"=="" (
  echo ERROR: No operation specified. Valid operations:
  echo   pause     - Pause the token
  echo   unpause   - Unpause the token
  echo   mint      - Mint new tokens
  echo Example usage: 
  echo   direct-tx pause 0xYourTokenAddress
  echo   direct-tx mint 0xYourTokenAddress 0xRecipientAddress 1000
  exit /b 1
)

REM Parse first parameter as operation
set OPERATION=%~1

REM Parse second parameter as token address
if "%~2"=="" (
  echo ERROR: Token address required
  exit /b 1
)
set TOKEN_ADDRESS=%~2

REM Default gas settings - very high to ensure success
set GAS_LIMIT=1000000
set GAS_PRICE=100

REM Check which operation
if /I "%OPERATION%"=="mint" (
  REM For mint, we need recipient and amount
  if "%~3"=="" (
    echo ERROR: Recipient address required for mint operation
    exit /b 1
  )
  if "%~4"=="" (
    echo ERROR: Token amount required for mint operation
    exit /b 1
  )
  
  set TO_ADDRESS=%~3
  set AMOUNT=%~4
  
  echo Operation: MINT
  echo Token Address: %TOKEN_ADDRESS%
  echo Recipient: %TO_ADDRESS%
  echo Amount: %AMOUNT%
) else if /I "%OPERATION%"=="pause" (
  echo Operation: PAUSE
  echo Token Address: %TOKEN_ADDRESS%
) else if /I "%OPERATION%"=="unpause" (
  echo Operation: UNPAUSE
  echo Token Address: %TOKEN_ADDRESS%
) else (
  echo ERROR: Invalid operation. Valid operations: pause, unpause, mint
  exit /b 1
)

echo Gas Limit: %GAS_LIMIT%
echo Gas Price: %GAS_PRICE% gwei

REM Optional parameters for gas
if not "%~5"=="" (
  set GAS_LIMIT=%~5
  echo Custom Gas Limit: %GAS_LIMIT%
)

if not "%~6"=="" (
  set GAS_PRICE=%~6
  echo Custom Gas Price: %GAS_PRICE% gwei
)

REM Set environment variables for script
set "OPERATION=%OPERATION%"
set "TOKEN_ADDRESS=%TOKEN_ADDRESS%"
set "GAS_LIMIT=%GAS_LIMIT%"
set "GAS_PRICE=%GAS_PRICE%"

REM For mint operation, set additional parameters
if /I "%OPERATION%"=="mint" (
  set "TO_ADDRESS=%TO_ADDRESS%"
  set "AMOUNT=%AMOUNT%"
)

echo.
echo Press any key to execute transaction (Ctrl+C to cancel)...
pause > nul

echo.
echo Executing transaction...
npx hardhat run scripts/direct-transaction.js --network amoy

echo.
echo Transaction attempt completed.
echo See above for results.

endlocal 