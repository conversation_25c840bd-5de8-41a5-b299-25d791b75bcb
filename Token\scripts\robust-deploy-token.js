// Robust Script to deploy a new SecurityToken using the factory
// This version handles Amoy testnet RPC issues better

const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("\n=== ROBUST TOKEN DEPLOYMENT FOR AMOY ===");

  try {
    // Get arguments from command line or set defaults
    const factoryAddress = process.env.FACTORY_ADDRESS;
    if (!factoryAddress) {
      throw new Error("FACTORY_ADDRESS environment variable not set");
    }

    // Token parameters - these can be customized or passed as env variables
    const tokenName = process.env.TOKEN_NAME || "Example Security Token";
    const tokenSymbol = process.env.TOKEN_SYMBOL || "EXST";
    const maxSupply = process.env.MAX_SUPPLY ? ethers.parseEther(process.env.MAX_SUPPLY) : ethers.parseEther("1000000");
    const tokenPrice = process.env.TOKEN_PRICE || "5 USD";
    const bonusTiers = process.env.BONUS_TIERS || "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%";
    
    // Get admin address - default to the deployer if not specified
    const adminAddressParam = process.env.ADMIN_ADDRESS;

    // Gas parameters - set explicitly high values
    const gasPrice = process.env.GAS_PRICE ? ethers.parseUnits(process.env.GAS_PRICE, "gwei") : ethers.parseUnits("100", "gwei");
    const gasLimit = process.env.GAS_LIMIT ? BigInt(process.env.GAS_LIMIT) : BigInt(2000000); // Default 2M gas

    console.log("Deploying a new SecurityToken with the following parameters:");
    console.log(`Factory Address: ${factoryAddress}`);
    console.log(`Name: ${tokenName}`);
    console.log(`Symbol: ${tokenSymbol}`);
    console.log(`Max Supply: ${ethers.formatEther(maxSupply)} tokens`);
    console.log(`Token Price: ${tokenPrice}`);
    console.log(`Bonus Tiers: ${bonusTiers}`);
    console.log(`Gas Limit: ${gasLimit.toString()}`);
    console.log(`Gas Price: ${ethers.formatUnits(gasPrice, "gwei")} gwei`);

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`Deployer: ${deployer.address}`);

    // Get the admin address (default to deployer if not specified)
    const adminAddress = adminAddressParam || deployer.address;
    console.log(`Admin: ${adminAddress}`);

    // Connect to the factory
    const factory = await ethers.getContractAt("SecurityTokenFactory", factoryAddress);
    
    console.log("Deploying token...");
    
    // Get current nonce
    const nonce = await deployer.getNonce();
    console.log(`Using nonce: ${nonce}`);
    
    // Create transaction object with explicit gas settings
    const deployTx = await factory.deploySecurityToken.populateTransaction(
      tokenName,
      tokenSymbol,
      maxSupply,
      adminAddress,
      tokenPrice,
      bonusTiers
    );
    
    // Add gas parameters
    deployTx.gasLimit = gasLimit;
    deployTx.gasPrice = gasPrice;
    deployTx.nonce = nonce;
    
    console.log("Sending transaction with explicit gas parameters...");
    
    // Send transaction with explicit gas parameters
    const tx = await deployer.sendTransaction(deployTx);
    console.log("Transaction hash:", tx.hash);
    
    // Save transaction details to a file for reference
    const txDetails = {
      operation: "deployToken",
      hash: tx.hash,
      to: factoryAddress,
      from: deployer.address,
      tokenName,
      tokenSymbol,
      maxSupply: ethers.formatEther(maxSupply),
      gasLimit: gasLimit.toString(),
      gasPrice: ethers.formatUnits(gasPrice, "gwei"),
      nonce: nonce,
      timestamp: new Date().toISOString()
    };

    const txLogPath = path.join(__dirname, '../transaction-logs.json');
    let txLogs = [];
    
    if (fs.existsSync(txLogPath)) {
      const fileContent = fs.readFileSync(txLogPath, 'utf8');
      try {
        txLogs = JSON.parse(fileContent);
      } catch (e) {
        console.warn("Couldn't parse existing transaction logs, creating new file");
      }
    }
    
    txLogs.push(txDetails);
    fs.writeFileSync(txLogPath, JSON.stringify(txLogs, null, 2));
    
    console.log("Waiting for transaction confirmation (this may take a while)...");
    
    // Wait for transaction to be mined with a timeout
    let receipt;
    try {
      // Wait with 120 second timeout
      const waitPromise = tx.wait();
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error("Transaction confirmation timed out after 120 seconds")), 120000)
      );
      
      receipt = await Promise.race([waitPromise, timeoutPromise]);
    } catch (error) {
      console.log(`Waiting for confirmation timed out or failed: ${error.message}`);
      console.log(`Transaction was submitted but confirmation status is unknown.`);
      console.log(`You can check the status manually at: https://amoy.polygonscan.com/tx/${tx.hash}`);
      
      // Try to continue anyway by querying for the token address
      console.log("Attempting to retrieve token address from factory...");
      
      try {
        // Give the transaction some time to be processed
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // Try to get the token address using the token symbol
        console.log(`Trying to get token address using symbol: ${tokenSymbol}`);
        const tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
        
        if (tokenAddress && tokenAddress !== ethers.ZeroAddress) {
          // Get the whitelist address from the token
          const token = await ethers.getContractAt("SecurityToken", tokenAddress);
          const whitelistAddress = await token.whitelistAddress();
          
          console.log(`SecurityToken deployed to: ${tokenAddress}`);
          console.log(`Whitelist deployed to: ${whitelistAddress}`);
          
          // Return the deployed contracts
          return {
            tokenAddress,
            whitelistAddress,
            deployer: deployer.address,
          };
        }
      } catch (lookupError) {
        console.log(`Could not retrieve token address: ${lookupError.message}`);
      }
      
      return {
        transactionHash: tx.hash,
        status: 'unknown',
        error: 'Transaction confirmation timed out'
      };
    }
    
    console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
    
    // Find the TokenDeployed event
    console.log("Looking for TokenDeployed event in logs...");
    
    // Debug log information
    console.log(`Receipt has ${receipt.logs.length} logs`);
    
    // Find the event
    let tokenAddress, whitelistAddress;
    
    const event = receipt.logs.find(
      log => log.fragment && log.fragment.name === 'TokenDeployed'
    );
    
    if (event && event.args) {
      console.log("TokenDeployed event found!");
      tokenAddress = event.args.tokenAddress;
      whitelistAddress = event.args.whitelistAddress;
    } else {
      // If we can't find the event or args, try to extract addresses from factory
      console.log("TokenDeployed event not found in standard format, attempting alternative extraction...");
      
      // Try to get the token address using the token symbol
      console.log(`Trying to get token address using symbol: ${tokenSymbol}`);
      
      tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
      console.log(`Found token address from getTokenAddressBySymbol: ${tokenAddress}`);
      
      if (tokenAddress && tokenAddress !== ethers.ZeroAddress) {
        // Get the whitelist address from the token
        const token = await ethers.getContractAt("SecurityToken", tokenAddress);
        whitelistAddress = await token.whitelistAddress();
        console.log(`Found whitelist address from token: ${whitelistAddress}`);
      } else {
        console.error("Could not find deployed token information");
        return {
          transactionHash: tx.hash,
          status: 'failed',
          error: 'Could not find token address after deployment'
        };
      }
    }
    
    console.log(`SecurityToken deployed to: ${tokenAddress}`);
    console.log(`Whitelist deployed to: ${whitelistAddress}`);
    
    // Connect to the deployed contracts
    const token = await ethers.getContractAt("SecurityToken", tokenAddress);
    const whitelist = await ethers.getContractAt("Whitelist", whitelistAddress);
    
    // Log some information about the deployed contracts
    console.log(`Token name: ${await token.name()}`);
    console.log(`Token symbol: ${await token.symbol()}`);
    console.log(`Token max supply: ${ethers.formatEther(await token.maxSupply())} tokens`);
    console.log(`Token totalSupply: ${ethers.formatEther(await token.totalSupply())} tokens`);
    console.log(`Token whitelist: ${await token.whitelistAddress()}`);
    
    console.log("Deployment completed successfully!");
    
    // Return the deployed contracts
    return {
      tokenAddress,
      whitelistAddress,
      deployer: deployer.address,
      transactionHash: tx.hash,
      status: 'success'
    };
  } catch (error) {
    console.error("\n❌ ERROR during token deployment:");
    if (error.reason) console.error(`Reason: ${error.reason}`);
    console.error(error);
    
    console.log("\n=== TROUBLESHOOTING SUGGESTIONS ===");
    console.log("1. Try with even higher gas values:");
    console.log(`   $env:GAS_LIMIT="3000000"`);
    console.log(`   $env:GAS_PRICE="200"`);
    console.log("2. Try a different RPC endpoint:");
    console.log(`   $env:AMOY_RPC_URL="https://polygon-amoy.blockpi.network/v1/rpc/public"`);
    console.log("3. Check if your private key has enough funds for gas fees");
    console.log("4. Make sure the token symbol is unique");
    console.log("5. The Amoy testnet may be experiencing issues - try again later");
    
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then((result) => {
      console.log("Deployment result:", result);
      process.exit(0);
    })
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
} else {
  // Export for use in other scripts
  module.exports = main;
} 