const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Testing Agreement Features in New Implementation...");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  // Factory address
  const factoryAddress = "******************************************";
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = SecurityTokenFactory.attach(factoryAddress);

  // Test deployment with agreement URL
  console.log("\n🚀 Deploying test token with agreement URL...");
  
  const agreementUrl = "https://example.com/investment-agreement.pdf";
  const testSymbol = "AGR" + Date.now().toString().slice(-4); // Unique symbol
  
  try {
    const deployTx = await factory.deploySecurityTokenWithOptions(
      "Agreement Test Token",
      testSymbol,
      0, // decimals
      ethers.parseUnits("1000000", 0), // maxSupply
      deployer.address, // admin
      "100 USD", // price
      "Tier 1: 5%", // bonus tiers
      "Test token with agreement functionality", // details
      "", // image URL
      agreementUrl, // agreement URL
      true // with KYC
    );

    console.log("Deployment transaction:", deployTx.hash);
    const receipt = await deployTx.wait();

    // Find the TokenDeployed event
    const tokenDeployedEvent = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (tokenDeployedEvent) {
      const parsed = factory.interface.parseLog(tokenDeployedEvent);
      const tokenAddress = parsed.args.tokenAddress;
      console.log("✅ Token deployed successfully:", tokenAddress);

      // Test agreement functions
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token = SecurityToken.attach(tokenAddress);

      console.log("\n🔍 Testing agreement functions...");

      // Test getAgreementUrl
      try {
        const retrievedUrl = await token.getAgreementUrl();
        console.log("✅ Agreement URL retrieved:", retrievedUrl);
        
        if (retrievedUrl === agreementUrl) {
          console.log("✅ Agreement URL matches expected value");
        } else {
          console.log("❌ Agreement URL mismatch. Expected:", agreementUrl, "Got:", retrievedUrl);
        }
      } catch (error) {
        console.log("❌ getAgreementUrl failed:", error.message);
      }

      // Test hasAcceptedAgreement (should be false initially)
      try {
        const hasAccepted = await token.hasAcceptedAgreement(deployer.address);
        console.log("✅ hasAcceptedAgreement:", hasAccepted);
        
        if (!hasAccepted) {
          console.log("✅ Initial agreement status is correctly false");
        } else {
          console.log("❌ Initial agreement status should be false");
        }
      } catch (error) {
        console.log("❌ hasAcceptedAgreement failed:", error.message);
      }

      // Test acceptAgreement
      try {
        console.log("\n📝 Accepting agreement...");
        const acceptTx = await token.acceptAgreement();
        console.log("Accept transaction:", acceptTx.hash);
        await acceptTx.wait();
        console.log("✅ Agreement accepted successfully");

        // Verify acceptance
        const hasAcceptedAfter = await token.hasAcceptedAgreement(deployer.address);
        console.log("✅ Agreement status after acceptance:", hasAcceptedAfter);

        if (hasAcceptedAfter) {
          console.log("✅ Agreement acceptance verified");
        } else {
          console.log("❌ Agreement acceptance not recorded");
        }

        // Get acceptance timestamp
        const timestamp = await token.getAgreementAcceptanceTimestamp(deployer.address);
        console.log("✅ Agreement acceptance timestamp:", timestamp.toString());

        if (timestamp > 0) {
          console.log("✅ Timestamp recorded correctly");
        } else {
          console.log("❌ Timestamp not recorded");
        }

      } catch (error) {
        console.log("❌ acceptAgreement failed:", error.message);
      }

      // Test trying to accept again (should fail)
      try {
        console.log("\n🔄 Testing duplicate acceptance (should fail)...");
        await token.acceptAgreement();
        console.log("❌ Duplicate acceptance should have failed");
      } catch (error) {
        if (error.message.includes("agreement already accepted")) {
          console.log("✅ Duplicate acceptance correctly prevented");
        } else {
          console.log("❌ Unexpected error:", error.message);
        }
      }

      console.log("\n🎉 AGREEMENT FEATURES TEST SUMMARY");
      console.log("==================================");
      console.log("Token Address:", tokenAddress);
      console.log("Agreement URL:", agreementUrl);
      console.log("✅ All agreement functions working correctly!");
      console.log("");
      console.log("🌐 Test this token in admin panel:");
      console.log(`http://localhost:3000/clients/`);
      console.log("");
      console.log("🌐 Test client portal with agreements:");
      console.log(`http://localhost:7788/`);

    } else {
      console.log("❌ Could not find TokenDeployed event");
    }

  } catch (error) {
    console.log("❌ Deployment failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
