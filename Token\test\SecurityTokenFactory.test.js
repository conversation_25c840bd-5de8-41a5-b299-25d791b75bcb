const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("SecurityTokenFactory", function () {
  let SecurityTokenFactory;
  let factory;
  let owner;
  let deployer;
  let admin;
  let user;

  beforeEach(async function () {
    // Get signers
    [owner, deployer, admin, user] = await ethers.getSigners();

    // Deploy factory
    SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
    factory = await SecurityTokenFactory.deploy(owner.address);
    await factory.waitForDeployment();

    // Add deployer role
    await factory.connect(owner).addDeployer(deployer.address);
  });

  describe("Deployment", function () {
    it("Should set the right owner", async function () {
      expect(await factory.hasRole(await factory.DEFAULT_ADMIN_ROLE(), owner.address)).to.equal(true);
    });

    it("Should set the correct implementations", async function () {
      expect(await factory.securityTokenImplementation()).to.not.equal(ethers.ZeroAddress);
      expect(await factory.whitelistImplementation()).to.not.equal(ethers.ZeroAddress);
      expect(await factory.whitelistWithKYCImplementation()).to.not.equal(ethers.ZeroAddress);
    });

    it("Should assign the deployer role correctly", async function () {
      expect(await factory.hasRole(await factory.DEPLOYER_ROLE(), deployer.address)).to.equal(true);
    });
  });

  describe("Token Deployment", function () {
    it("Should deploy a token with basic whitelist", async function () {
      const tx = await factory.connect(deployer).deploySecurityToken(
        "Test Token",
        "TEST",
        18,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%, Tier 2: 10%",
        "Test token details"
      );

      const receipt = await tx.wait();
      const event = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === "TokenDeployed";
        } catch {
          return false;
        }
      });
      expect(event).to.not.be.undefined;

      const parsedEvent = factory.interface.parseLog(event);
      const tokenAddress = parsedEvent.args.tokenAddress;
      const identityRegistryAddress = parsedEvent.args.identityRegistryAddress;

      // Get token contract
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token = await SecurityToken.attach(tokenAddress);

      // Get whitelist contract
      const Whitelist = await ethers.getContractFactory("Whitelist");
      const whitelist = await Whitelist.attach(identityRegistryAddress);

      // Verify token properties
      expect(await token.name()).to.equal("Test Token");
      expect(await token.symbol()).to.equal("TEST");
      expect(await token.decimals()).to.equal(18);
      expect(await token.maxSupply()).to.equal(1000000);
      expect(await token.identityRegistry()).to.equal(identityRegistryAddress);
      expect(await token.tokenPrice()).to.equal("10 USD");
      expect(await token.bonusTiers()).to.equal("Tier 1: 5%, Tier 2: 10%");
      expect(await token.tokenDetails()).to.equal("Test token details");

      // Verify admin has correct roles
      expect(await token.hasRole(await token.DEFAULT_ADMIN_ROLE(), admin.address)).to.equal(true);
      expect(await token.hasRole(await token.AGENT_ROLE(), admin.address)).to.equal(true);
      expect(await whitelist.hasRole(await whitelist.DEFAULT_ADMIN_ROLE(), admin.address)).to.equal(true);
      expect(await whitelist.hasRole(await whitelist.AGENT_ROLE(), admin.address)).to.equal(true);

      // Check token in registry
      expect(await factory.getTokenAddressBySymbol("TEST")).to.equal(tokenAddress);
    });

    it("Should deploy a token with KYC whitelist", async function () {
      const tx = await factory.connect(deployer).deploySecurityTokenWithOptions(
        "KYC Token",
        "KYC",
        6,
        1000000,
        admin.address,
        "20 USD",
        "Tier 1: 10%, Tier 2: 15%",
        "KYC token details",
        true
      );

      const receipt = await tx.wait();
      const event = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === "TokenDeployed";
        } catch {
          return false;
        }
      });
      expect(event).to.not.be.undefined;

      const parsedEvent = factory.interface.parseLog(event);
      const tokenAddress = parsedEvent.args.tokenAddress;
      const identityRegistryAddress = parsedEvent.args.identityRegistryAddress;

      // Get token contract
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token = await SecurityToken.attach(tokenAddress);

      // Get whitelist with KYC contract
      const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
      const whitelistWithKYC = await WhitelistWithKYC.attach(identityRegistryAddress);

      // Verify token properties
      expect(await token.name()).to.equal("KYC Token");
      expect(await token.symbol()).to.equal("KYC");
      expect(await token.decimals()).to.equal(6);
      expect(await token.identityRegistry()).to.equal(identityRegistryAddress);

      // Verify admin has correct roles
      expect(await token.hasRole(await token.DEFAULT_ADMIN_ROLE(), admin.address)).to.equal(true);
      expect(await whitelistWithKYC.hasRole(await whitelistWithKYC.DEFAULT_ADMIN_ROLE(), admin.address)).to.equal(true);
      expect(await whitelistWithKYC.hasRole(await whitelistWithKYC.AGENT_ROLE(), admin.address)).to.equal(true);

      // Check if we can use KYC functions
      await whitelistWithKYC.connect(admin).approveKyc(user.address);
      expect(await whitelistWithKYC.isKycApproved(user.address)).to.equal(true);
      expect(await whitelistWithKYC.isWhitelisted(user.address)).to.equal(true);
    });

    it("Should not allow duplicate token symbols", async function () {
      await factory.connect(deployer).deploySecurityToken(
        "First Token",
        "DUP",
        18,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%",
        "First token"
      );

      await expect(factory.connect(deployer).deploySecurityToken(
        "Second Token",
        "DUP",
        18,
        2000000,
        admin.address,
        "20 USD",
        "Tier 1: 10%",
        "Second token"
      )).to.be.revertedWith("SecurityTokenFactory: token with this symbol already exists");
    });

    it("Should not allow non-deployer to deploy tokens", async function () {
      await expect(factory.connect(user).deploySecurityToken(
        "Test Token",
        "TEST",
        18,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%",
        "Test token"
      )).to.be.reverted;
    });

    it("Should deploy tokens with custom decimals", async function () {
      // Test with 0 decimals
      const tx0 = await factory.connect(deployer).deploySecurityToken(
        "Zero Decimals Token",
        "ZERO",
        0,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%",
        "Zero decimals token"
      );

      const receipt0 = await tx0.wait();
      const event0 = receipt0.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === "TokenDeployed";
        } catch {
          return false;
        }
      });
      const parsedEvent0 = factory.interface.parseLog(event0);
      const tokenAddress0 = parsedEvent0.args.tokenAddress;

      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token0 = await SecurityToken.attach(tokenAddress0);
      expect(await token0.decimals()).to.equal(0);

      // Test with 6 decimals
      const tx6 = await factory.connect(deployer).deploySecurityToken(
        "Six Decimals Token",
        "SIX",
        6,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%",
        "Six decimals token"
      );

      const receipt6 = await tx6.wait();
      const event6 = receipt6.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === "TokenDeployed";
        } catch {
          return false;
        }
      });
      const parsedEvent6 = factory.interface.parseLog(event6);
      const tokenAddress6 = parsedEvent6.args.tokenAddress;

      const token6 = await SecurityToken.attach(tokenAddress6);
      expect(await token6.decimals()).to.equal(6);
    });

    it("Should not allow decimals greater than 18", async function () {
      await expect(factory.connect(deployer).deploySecurityToken(
        "Invalid Token",
        "INVALID",
        19,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%",
        "Invalid decimals token"
      )).to.be.reverted;
    });
  });

  describe("Implementation Updates", function () {
    it("Should allow admin to update implementations", async function () {
      // Deploy new implementations
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const newTokenImpl = await SecurityToken.deploy();
      await newTokenImpl.waitForDeployment();

      const Whitelist = await ethers.getContractFactory("Whitelist");
      const newWhitelistImpl = await Whitelist.deploy();
      await newWhitelistImpl.waitForDeployment();

      const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
      const newWhitelistWithKYCImpl = await WhitelistWithKYC.deploy();
      await newWhitelistWithKYCImpl.waitForDeployment();

      // Get current implementations
      const oldTokenImpl = await factory.securityTokenImplementation();
      const oldWhitelistImpl = await factory.whitelistImplementation();
      const oldWhitelistWithKYCImpl = await factory.whitelistWithKYCImplementation();

      // Update implementations
      await factory.connect(owner).updateImplementations(
        await newTokenImpl.getAddress(),
        await newWhitelistImpl.getAddress(),
        await newWhitelistWithKYCImpl.getAddress()
      );

      // Verify implementations were updated
      expect(await factory.securityTokenImplementation()).to.equal(await newTokenImpl.getAddress());
      expect(await factory.whitelistImplementation()).to.equal(await newWhitelistImpl.getAddress());
      expect(await factory.whitelistWithKYCImplementation()).to.equal(await newWhitelistWithKYCImpl.getAddress());

      // Verify implementations are different from old ones
      expect(await factory.securityTokenImplementation()).to.not.equal(oldTokenImpl);
      expect(await factory.whitelistImplementation()).to.not.equal(oldWhitelistImpl);
      expect(await factory.whitelistWithKYCImplementation()).to.not.equal(oldWhitelistWithKYCImpl);
    });

    it("Should not allow non-admin to update implementations", async function () {
      // Deploy new implementations
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const newTokenImpl = await SecurityToken.deploy();
      await newTokenImpl.waitForDeployment();

      const Whitelist = await ethers.getContractFactory("Whitelist");
      const newWhitelistImpl = await Whitelist.deploy();
      await newWhitelistImpl.waitForDeployment();

      const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
      const newWhitelistWithKYCImpl = await WhitelistWithKYC.deploy();
      await newWhitelistWithKYCImpl.waitForDeployment();

      // Try to update implementations
      await expect(factory.connect(user).updateImplementations(
        await newTokenImpl.getAddress(),
        await newWhitelistImpl.getAddress(),
        await newWhitelistWithKYCImpl.getAddress()
      )).to.be.reverted;
    });
  });

  describe("Deployer Management", function () {
    it("Should allow admin to add and remove deployers", async function () {
      await factory.connect(owner).addDeployer(user.address);
      expect(await factory.hasRole(await factory.DEPLOYER_ROLE(), user.address)).to.equal(true);

      await factory.connect(owner).removeDeployer(user.address);
      expect(await factory.hasRole(await factory.DEPLOYER_ROLE(), user.address)).to.equal(false);
    });

    it("Should not allow non-admin to add or remove deployers", async function () {
      await expect(factory.connect(user).addDeployer(user.address)).to.be.reverted;
      await expect(factory.connect(user).removeDeployer(deployer.address)).to.be.reverted;
    });
  });

  describe("Token Lookup", function () {
    it("Should correctly lookup tokens by symbol", async function () {
      // Deploy a token
      const tx = await factory.connect(deployer).deploySecurityToken(
        "Lookup Token",
        "LOOK",
        18,
        1000000,
        admin.address,
        "10 USD",
        "Tier 1: 5%",
        "Lookup test token"
      );

      const receipt = await tx.wait();
      const event = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === "TokenDeployed";
        } catch {
          return false;
        }
      });
      const parsedEvent = factory.interface.parseLog(event);
      const tokenAddress = parsedEvent.args.tokenAddress;

      // Lookup the token
      expect(await factory.getTokenAddressBySymbol("LOOK")).to.equal(tokenAddress);

      // Non-existent token should return zero address
      expect(await factory.getTokenAddressBySymbol("NONE")).to.equal(ethers.ZeroAddress);
    });
  });

  describe("Full Token Deployment Flow", function () {
    it("Should deploy a token and allow operations", async function () {
      // Deploy token with KYC
      const tx = await factory.connect(deployer).deploySecurityTokenWithOptions(
        "Flow Token",
        "FLOW",
        18,
        1000000,
        admin.address,
        "10 USD",
        "Tiers",
        "Flow test token",
        true
      );

      const receipt = await tx.wait();
      const event = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === "TokenDeployed";
        } catch {
          return false;
        }
      });
      const parsedEvent = factory.interface.parseLog(event);
      const tokenAddress = parsedEvent.args.tokenAddress;
      const identityRegistryAddress = parsedEvent.args.identityRegistryAddress;

      // Get contracts
      const SecurityToken = await ethers.getContractFactory("SecurityToken");
      const token = await SecurityToken.attach(tokenAddress);

      const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
      const whitelist = await WhitelistWithKYC.attach(identityRegistryAddress);

      // Perform operations
      // Add user to whitelist and approve KYC
      await whitelist.connect(admin).approveKyc(user.address);

      // Mint tokens
      await token.connect(admin).mint(user.address, 1000);
      expect(await token.balanceOf(user.address)).to.equal(1000);

      // Transfer tokens (should work since user is whitelisted and KYC approved)
      await whitelist.connect(admin).addToWhitelist(deployer.address);
      await whitelist.connect(admin).approveKyc(deployer.address);

      await token.connect(user).transfer(deployer.address, 500);
      expect(await token.balanceOf(user.address)).to.equal(500);
      expect(await token.balanceOf(deployer.address)).to.equal(500);

      // Freeze user
      await whitelist.connect(admin).freezeAddress(user.address);

      // Transfer should fail after freezing
      await expect(token.connect(user).transfer(deployer.address, 100)).to.be.revertedWith("SecurityToken: sender frozen");

      // Force transfer should work
      await token.connect(admin).forcedTransfer(user.address, deployer.address, 100);
      expect(await token.balanceOf(user.address)).to.equal(400);
      expect(await token.balanceOf(deployer.address)).to.equal(600);
    });
  });
});