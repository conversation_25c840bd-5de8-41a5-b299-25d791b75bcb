import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { OrderStatus } from '@prisma/client';

// GET /api/orders/[id] - Get a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const order = await prisma.order.findUnique({
      where: { id },
      select: {
        id: true,
        status: true,
        tokensOrdered: true,
        tokensConfirmed: true,
        amountToPay: true,
        confirmedPayment: true,
        tokenPrice: true,
        paymentReference: true,
        transactionHash: true,
        blockNumber: true,
        createdAt: true,
        updatedAt: true,
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
            tokenPrice: true,
            currency: true
          }
        },
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        }
      }
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      order
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch order' },
      { status: 500 }
    );
  }
}

// PUT /api/orders/[id] - Update an order
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const {
      status,
      tokensConfirmed,
      confirmedPayment
    } = body;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id }
    });

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (status && Object.values(OrderStatus).includes(status)) {
      updateData.status = status;
    }

    if (tokensConfirmed !== undefined) {
      updateData.tokensConfirmed = tokensConfirmed.toString();
    }

    if (confirmedPayment !== undefined) {
      updateData.confirmedPayment = confirmedPayment.toString();
    }

    // Update the order
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        status: true,
        tokensOrdered: true,
        tokensConfirmed: true,
        amountToPay: true,
        confirmedPayment: true,
        tokenPrice: true,
        paymentReference: true,
        transactionHash: true,
        blockNumber: true,
        createdAt: true,
        updatedAt: true,
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
            tokenPrice: true,
            currency: true
          }
        },
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      order: updatedOrder
    });
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

// DELETE /api/orders/[id] - Delete an order
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id }
    });

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    // Delete the order
    await prisma.order.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Order deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete order' },
      { status: 500 }
    );
  }
}
