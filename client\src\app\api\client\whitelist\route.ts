import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { createAuthHeaders } from '@/lib/jwt';

// GET /api/client/whitelist - Get whitelist status
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create authenticated headers for admin panel API
    const userSub = session.user.sub;
    if (!userSub) {
      console.error('User sub is missing in session');
      return NextResponse.json({ error: 'User identifier missing' }, { status: 500 });
    }
    const authHeaders = createAuthHeaders({ 
      sub: userSub,
      email: session.user.email || undefined 
    });

    // Find client by email first
    const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
    const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(session.user.email || '')}`, {
      headers: authHeaders,
    });

    if (!searchResponse.ok) {
      throw new Error('Failed to find client profile');
    }

    const searchData = await searchResponse.json();
    const client = searchData.clients?.[0];

    if (!client) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Get whitelist status
    const whitelistResponse = await fetch(`${adminApiUrl}/clients/${client.id}/whitelist`, {
      headers: authHeaders,
    });

    if (!whitelistResponse.ok) {
      throw new Error('Failed to fetch whitelist status');
    }

    const whitelistData = await whitelistResponse.json();
    return NextResponse.json({
      isWhitelisted: whitelistData.isWhitelisted,
      walletAddress: whitelistData.walletAddress,
      whitelistedAt: whitelistData.whitelistedAt,
    });
  } catch (error) {
    console.error('Error fetching whitelist status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch whitelist status' },
      { status: 500 }
    );
  }
}
