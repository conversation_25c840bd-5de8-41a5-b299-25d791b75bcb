@echo off
setlocal

REM Direct Token Deployment Tool for Amoy Testnet
echo ===== ULTRA TOKEN DEPLOYMENT FOR AMOY =====

REM Required params - factory address
if "%~1"=="" (
  set FACTORY_ADDRESS=0x1d3b71176345004b14ab24229576d99115914541
  echo Using default factory address: %FACTORY_ADDRESS%
) else (
  set FACTORY_ADDRESS=%~1
)

REM Required params - token name, symbol, max supply
if "%~2"=="" (
  set TOKEN_NAME=Nova
  echo Using default token name: %TOKEN_NAME%
) else (
  set TOKEN_NAME=%~2
)

if "%~3"=="" (
  set TOKEN_SYMBOL=NNN
  echo Using default token symbol: %TOKEN_SYMBOL%
) else (
  set TOKEN_SYMBOL=%~3
)

if "%~4"=="" (
  set MAX_SUPPLY=1500000
  echo Using default max supply: %MAX_SUPPLY%
) else (
  set MAX_SUPPLY=%~4
)

REM Optional params
if not "%~5"=="" (
  set TOKEN_PRICE=%~5
) else (
  set TOKEN_PRICE=3
)

if not "%~6"=="" (
  set BONUS_TIERS=%~6
) else (
  set BONUS_TIERS=Tier 1: 5%%, Tier 2: 10%%, Tier 3: 15%%
)

REM Gas settings (ultra high by default)
if not "%~7"=="" (
  set GAS_LIMIT=%~7
) else (
  set GAS_LIMIT=8000000
)

if not "%~8"=="" (
  set GAS_PRICE=%~8
) else (
  set GAS_PRICE=300
)

REM Admin address (optional)
if not "%~9"=="" (
  set ADMIN_ADDRESS=%~9
)

REM Display configuration
echo.
echo Factory Address: %FACTORY_ADDRESS%
echo Token Name: %TOKEN_NAME%
echo Token Symbol: %TOKEN_SYMBOL%
echo Max Supply: %MAX_SUPPLY%
echo Token Price: %TOKEN_PRICE%
echo Bonus Tiers: %BONUS_TIERS%
echo Gas Limit: %GAS_LIMIT%
echo Gas Price: %GAS_PRICE% gwei
if defined ADMIN_ADDRESS (
  echo Admin Address: %ADMIN_ADDRESS%
) else (
  echo Admin Address: [Will use deployer address]
)

REM Set environment variables for script
set "FACTORY_ADDRESS=%FACTORY_ADDRESS%"
set "TOKEN_NAME=%TOKEN_NAME%"
set "TOKEN_SYMBOL=%TOKEN_SYMBOL%"
set "MAX_SUPPLY=%MAX_SUPPLY%"
set "TOKEN_PRICE=%TOKEN_PRICE%"
set "BONUS_TIERS=%BONUS_TIERS%"
set "GAS_LIMIT=%GAS_LIMIT%"
set "GAS_PRICE=%GAS_PRICE%"
if defined ADMIN_ADDRESS (
  set "ADMIN_ADDRESS=%ADMIN_ADDRESS%"
)

REM Try alternative RPC URL
set "AMOY_RPC_URL=https://polygon-amoy.blockpi.network/v1/rpc/public"

echo.
echo Press any key to execute token deployment (Ctrl+C to cancel)...
pause > nul

echo.
echo Executing token deployment...
npx hardhat run scripts/extreme-deploy-token.js --network amoy

echo.
echo Deployment attempt completed.
echo See above for results.

endlocal 