'use client';

import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    category: 'Getting Started',
    question: 'What is TokenDev?',
    answer: 'TokenDev is a secure token management and KYC compliance platform that allows you to invest in security tokens. We provide a compliant environment for token issuance and investment with full regulatory compliance.'
  },
  {
    category: 'Getting Started',
    question: 'How do I get started?',
    answer: 'To get started, you need to: 1) Create an account and sign in, 2) Complete the KYC qualification process, 3) Connect your wallet, 4) Browse available tokens and make investments.'
  },
  {
    category: 'KYC & Compliance',
    question: 'What is KYC and why is it required?',
    answer: 'KYC (Know Your Customer) is a regulatory requirement that helps us verify your identity and ensure compliance with financial regulations. This process is mandatory for all users who want to invest in security tokens.'
  },
  {
    category: 'KYC & Compliance',
    question: 'What documents do I need for KYC?',
    answer: 'You will need: 1) Government-issued photo ID (passport, driver\'s license, or national ID), 2) Proof of address (utility bill or bank statement less than 3 months old), 3) Financial information and source of funds documentation.'
  },
  {
    category: 'KYC & Compliance',
    question: 'How long does KYC approval take?',
    answer: 'KYC approval typically takes 1-3 business days. You will receive email notifications about the status of your application. In some cases, additional documentation may be requested.'
  },
  {
    category: 'Token Investment',
    question: 'How do I invest in tokens?',
    answer: 'Once your KYC is approved and you\'ve connected your wallet, you can browse available tokens on the Dashboard. Click "Invest Now" on any qualified token to place an order. Orders are subject to admin approval.'
  },
  {
    category: 'Token Investment',
    question: 'What types of tokens are available?',
    answer: 'We offer various types of security tokens including commodities, real estate, equity, debt/bonds, funds, and other securities. Each token represents a different investment opportunity with its own risk profile.'
  },
  {
    category: 'Token Investment',
    question: 'Are there minimum investment amounts?',
    answer: 'Minimum investment amounts vary by token. You can see the price per token and available supply on each token\'s details. Some tokens may have specific qualification requirements.'
  },
  {
    category: 'Orders & Payments',
    question: 'How do orders work?',
    answer: 'When you place an order, it goes to our admin team for approval. Once approved, you\'ll receive payment instructions. After payment confirmation, tokens will be minted to your wallet address.'
  },
  {
    category: 'Orders & Payments',
    question: 'Can I cancel my order?',
    answer: 'Orders can typically be cancelled before they are confirmed by our admin team. Once an order is confirmed and payment is requested, cancellation may not be possible. Contact support for assistance.'
  },
  {
    category: 'Wallet & Technical',
    question: 'What wallets are supported?',
    answer: 'We support various Web3 wallets including MetaMask, WalletConnect-compatible wallets, and other popular options. Make sure your wallet is connected to the correct network.'
  },
  {
    category: 'Wallet & Technical',
    question: 'Is my information secure?',
    answer: 'Yes, we use industry-standard security measures including encryption, secure data storage, and compliance with data protection regulations. Your personal and financial information is protected.'
  }
];

const categories = Array.from(new Set(faqData.map(item => item.category)));

export default function HelpPage() {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const filteredFAQ = selectedCategory === 'All' 
    ? faqData 
    : faqData.filter(item => item.category === selectedCategory);

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Help & FAQ</h1>
          <p className="text-lg text-gray-600">
            Find answers to common questions about TokenDev, KYC, and token investment
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 justify-center">
            <button
              onClick={() => setSelectedCategory('All')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === 'All'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All
            </button>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQ.map((item, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200">
              <button
                onClick={() => toggleItem(index)}
                className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
              >
                <div>
                  <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full mr-3">
                    {item.category}
                  </span>
                  <span className="text-lg font-medium text-gray-900">
                    {item.question}
                  </span>
                </div>
                {openItems.includes(index) ? (
                  <ChevronUpIcon className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDownIcon className="h-5 w-5 text-gray-500" />
                )}
              </button>
              {openItems.includes(index) && (
                <div className="px-6 pb-4">
                  <p className="text-gray-700 leading-relaxed">
                    {item.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact Support */}
        <div className="mt-12 text-center bg-blue-50 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Still need help?</h2>
          <p className="text-gray-600 mb-6">
            Can't find the answer you're looking for? Our support team is here to help.
          </p>
          <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Email Support
            </a>
            <a
              href="/qualification"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Complete Qualification
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
