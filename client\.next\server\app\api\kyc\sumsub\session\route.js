/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kyc/sumsub/session/route";
exports.ids = ["app/api/kyc/sumsub/session/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&page=%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&page=%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_kyc_sumsub_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kyc/sumsub/session/route.ts */ \"(rsc)/./src/app/api/kyc/sumsub/session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kyc/sumsub/session/route\",\n        pathname: \"/api/kyc/sumsub/session\",\n        filename: \"route\",\n        bundlePath: \"app/api/kyc/sumsub/session/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\kyc\\\\sumsub\\\\session\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_kyc_sumsub_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&page=%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kyc/sumsub/session/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/kyc/sumsub/session/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\n * Sumsub KYC Session Management\n * Creates and retrieves verification sessions with Sumsub\n */ const SUMSUB_BASE_URL = process.env.SUMSUB_BASE_URL;\nconst SUMSUB_TOKEN = process.env.SUMSUB_TOKEN;\nconst SUMSUB_SECRET = process.env.SUMSUB_SECRET;\n/**\n * Generate Sumsub API signature\n * Format: timestamp + method + url + body (body only if present)\n */ function generateSignature(method, url, body, timestamp) {\n    // For token requests (POST with no body), don't include body in signature\n    const signatureSource = timestamp.toString() + method.toUpperCase() + url + body;\n    const signature = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHmac('sha256', SUMSUB_SECRET).update(signatureSource).digest('hex');\n    console.log('Signature generation:', {\n        timestamp,\n        method: method.toUpperCase(),\n        url,\n        bodyLength: body.length,\n        signatureSource: signatureSource.substring(0, 100) + '...',\n        signature\n    });\n    return signature;\n}\n/**\n * Map common country names to ISO 3166-1 alpha-3 codes\n */ function mapCountryToAlpha3(country) {\n    if (!country) return 'USA'; // Default fallback\n    const countryMap = {\n        'united states': 'USA',\n        'usa': 'USA',\n        'us': 'USA',\n        'america': 'USA',\n        'united kingdom': 'GBR',\n        'uk': 'GBR',\n        'britain': 'GBR',\n        'england': 'GBR',\n        'germany': 'DEU',\n        'deutschland': 'DEU',\n        'france': 'FRA',\n        'canada': 'CAN',\n        'australia': 'AUS',\n        'japan': 'JPN',\n        'china': 'CHN',\n        'india': 'IND',\n        'brazil': 'BRA',\n        'russia': 'RUS',\n        'italy': 'ITA',\n        'spain': 'ESP',\n        'netherlands': 'NLD',\n        'sweden': 'SWE',\n        'norway': 'NOR',\n        'denmark': 'DNK',\n        'finland': 'FIN',\n        'poland': 'POL',\n        'switzerland': 'CHE',\n        'austria': 'AUT',\n        'belgium': 'BEL',\n        'portugal': 'PRT',\n        'ireland': 'IRL',\n        'greece': 'GRC',\n        'turkey': 'TUR',\n        'south korea': 'KOR',\n        'korea': 'KOR',\n        'mexico': 'MEX',\n        'argentina': 'ARG',\n        'chile': 'CHL',\n        'colombia': 'COL',\n        'peru': 'PER',\n        'venezuela': 'VEN',\n        'south africa': 'ZAF',\n        'egypt': 'EGY',\n        'israel': 'ISR',\n        'saudi arabia': 'SAU',\n        'uae': 'ARE',\n        'united arab emirates': 'ARE',\n        'singapore': 'SGP',\n        'malaysia': 'MYS',\n        'thailand': 'THA',\n        'indonesia': 'IDN',\n        'philippines': 'PHL',\n        'vietnam': 'VNM',\n        'new zealand': 'NZL'\n    };\n    const normalized = country.toLowerCase().trim();\n    // Check if it's already a 3-letter code\n    if (normalized.length === 3 && /^[A-Z]{3}$/i.test(normalized)) {\n        return normalized.toUpperCase();\n    }\n    // Look up in mapping\n    const mapped = countryMap[normalized];\n    if (mapped) {\n        return mapped;\n    }\n    // If no mapping found, return USA as fallback\n    console.warn(`Unknown country: ${country}, using USA as fallback`);\n    return 'USA';\n}\n/**\n * Get detailed applicant data from Sumsub\n */ async function getSumsubApplicantData(applicantId) {\n    try {\n        const url = `/resources/applicants/${applicantId}/one`;\n        const headers = createSumsubHeaders('GET', url);\n        const response = await fetch(`${SUMSUB_BASE_URL}${url}`, {\n            method: 'GET',\n            headers: headers\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Failed to get Sumsub applicant data:', errorText);\n            return null;\n        }\n        return await response.json();\n    } catch (error) {\n        console.error('Error getting Sumsub applicant data:', error);\n        return null;\n    }\n}\n/**\n * Get applicant documents from Sumsub\n */ async function getSumsubApplicantDocuments(applicantId) {\n    try {\n        const url = `/resources/applicants/${applicantId}/info/idDoc`;\n        const headers = createSumsubHeaders('GET', url);\n        const response = await fetch(`${SUMSUB_BASE_URL}${url}`, {\n            method: 'GET',\n            headers: headers\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Failed to get Sumsub applicant documents:', errorText);\n            return null;\n        }\n        return await response.json();\n    } catch (error) {\n        console.error('Error getting Sumsub applicant documents:', error);\n        return null;\n    }\n}\n/**\n * Create Sumsub headers with authentication\n */ function createSumsubHeaders(method, url, body = '') {\n    const timestamp = Math.floor(Date.now() / 1000);\n    const signature = generateSignature(method, url, body, timestamp);\n    const headers = {\n        'Accept': 'application/json',\n        'X-App-Token': SUMSUB_TOKEN,\n        'X-App-Access-Sig': signature,\n        'X-App-Access-Ts': timestamp.toString()\n    };\n    // Only add Content-Type for requests with body\n    if (body && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {\n        headers['Content-Type'] = 'application/json';\n    }\n    return headers;\n}\nasync function POST(request) {\n    try {\n        // Check authentication\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}));\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { callback } = await request.json();\n        // Get user profile for comparison data\n        const profileResponse = await fetch(`${process.env.AUTH0_BASE_URL}/api/client/profile`, {\n            headers: {\n                'Cookie': request.headers.get('cookie') || ''\n            }\n        });\n        let userProfile = null;\n        if (profileResponse.ok) {\n            userProfile = await profileResponse.json();\n        }\n        // Step 1: Create applicant with user data\n        const applicantData = {\n            externalUserId: session.user.sub,\n            info: userProfile ? {\n                firstName: userProfile.firstName,\n                lastName: userProfile.lastName,\n                dob: userProfile.birthday,\n                country: mapCountryToAlpha3(userProfile.nationality),\n                phone: userProfile.phoneNumber,\n                ...userProfile.email && {\n                    email: userProfile.email\n                }\n            } : {\n                firstName: session.user.given_name || '',\n                lastName: session.user.family_name || '',\n                country: 'USA',\n                ...session.user.email && {\n                    email: session.user.email\n                }\n            },\n            metadata: [\n                {\n                    key: 'qualification_data',\n                    value: JSON.stringify({\n                        user_id: session.user.sub,\n                        email: session.user.email,\n                        expected_data: userProfile ? {\n                            first_name: userProfile.firstName,\n                            last_name: userProfile.lastName,\n                            date_of_birth: userProfile.birthday,\n                            nationality: userProfile.nationality,\n                            document_type: userProfile.identificationType,\n                            document_number: userProfile.passportNumber || userProfile.idCardNumber,\n                            document_expiration: userProfile.documentExpiration,\n                            phone_number: userProfile.phoneNumber\n                        } : null\n                    })\n                }\n            ]\n        };\n        // Use the same level name for consistency\n        const levelName = process.env.SUMSUB_LEVEL_NAME || 'basic-kyc-level';\n        const applicantUrl = `/resources/applicants?levelName=${encodeURIComponent(levelName)}`;\n        const applicantBody = JSON.stringify(applicantData);\n        const applicantHeaders = createSumsubHeaders('POST', applicantUrl, applicantBody);\n        const applicantResponse = await fetch(`${SUMSUB_BASE_URL}${applicantUrl}`, {\n            method: 'POST',\n            headers: applicantHeaders,\n            body: applicantBody\n        });\n        let applicantResult;\n        let applicantId;\n        if (!applicantResponse.ok) {\n            const errorText = await applicantResponse.text();\n            let errorData;\n            try {\n                errorData = JSON.parse(errorText);\n            } catch  {\n                console.error('Sumsub applicant creation failed:', errorText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to create applicant',\n                    details: errorText\n                }, {\n                    status: applicantResponse.status\n                });\n            }\n            // Handle case where applicant already exists (409 conflict)\n            if (errorData.code === 409 && errorData.description?.includes('already exists')) {\n                console.log('Applicant already exists, retrieving existing applicant...');\n                // Extract applicant ID from error message\n                const existingIdMatch = errorData.description.match(/already exists: ([a-f0-9]+)/);\n                if (existingIdMatch) {\n                    applicantId = existingIdMatch[1];\n                    // Get existing applicant data\n                    const getApplicantUrl = `/resources/applicants/${applicantId}/one`;\n                    const getApplicantHeaders = createSumsubHeaders('GET', getApplicantUrl);\n                    const getApplicantResponse = await fetch(`${SUMSUB_BASE_URL}${getApplicantUrl}`, {\n                        method: 'GET',\n                        headers: getApplicantHeaders\n                    });\n                    if (getApplicantResponse.ok) {\n                        applicantResult = await getApplicantResponse.json();\n                        console.log('Retrieved existing applicant:', applicantId);\n                    } else {\n                        const getErrorText = await getApplicantResponse.text();\n                        console.error('Failed to retrieve existing applicant:', getErrorText);\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: 'Failed to retrieve existing applicant',\n                            details: getErrorText\n                        }, {\n                            status: getApplicantResponse.status\n                        });\n                    }\n                } else {\n                    console.error('Could not extract applicant ID from error:', errorData.description);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Applicant exists but could not retrieve ID',\n                        details: errorText\n                    }, {\n                        status: 409\n                    });\n                }\n            } else {\n                // Handle other errors\n                console.error('Sumsub applicant creation failed:', errorData);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to create applicant',\n                    details: errorData.description || errorText,\n                    correlationId: errorData.correlationId\n                }, {\n                    status: applicantResponse.status\n                });\n            }\n        } else {\n            // Successful creation\n            applicantResult = await applicantResponse.json();\n            applicantId = applicantResult.id;\n            console.log('Created new applicant:', applicantId);\n        }\n        // Step 2: Generate access token for the applicant\n        console.log('Using Sumsub level name:', levelName);\n        const tokenUrl = `/resources/accessTokens?userId=${encodeURIComponent(session.user.sub)}&levelName=${encodeURIComponent(levelName)}&ttlInSecs=600`;\n        const tokenHeaders = createSumsubHeaders('POST', tokenUrl, ''); // Empty body for token request\n        const tokenResponse = await fetch(`${SUMSUB_BASE_URL}${tokenUrl}`, {\n            method: 'POST',\n            headers: tokenHeaders,\n            body: null\n        });\n        if (!tokenResponse.ok) {\n            const errorText = await tokenResponse.text();\n            let errorData;\n            try {\n                errorData = JSON.parse(errorText);\n                console.error('Sumsub token generation failed:', errorData);\n                // Provide helpful error message for level not found\n                let errorMessage = errorData.description || errorText;\n                if (errorData.description?.includes('not found')) {\n                    errorMessage = `Level '${levelName}' not found. Please create this verification level in your Sumsub Dashboard at https://cockpit.sumsub.com or set SUMSUB_LEVEL_NAME environment variable to an existing level name.`;\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to generate access token',\n                    details: errorMessage,\n                    correlationId: errorData.correlationId,\n                    levelName: levelName\n                }, {\n                    status: tokenResponse.status\n                });\n            } catch  {\n                console.error('Sumsub token generation failed:', errorText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to generate access token',\n                    details: errorText\n                }, {\n                    status: tokenResponse.status\n                });\n            }\n        }\n        const tokenResult = await tokenResponse.json();\n        // Return session data with applicant information\n        const sessionData = {\n            session_id: applicantId,\n            session_token: tokenResult.token,\n            status: 'created',\n            applicant_id: applicantId,\n            user_id: session.user.sub,\n            level_name: levelName,\n            applicant_data: {\n                id: applicantResult.id,\n                externalUserId: applicantResult.externalUserId,\n                info: applicantResult.info,\n                review: applicantResult.review,\n                createdAt: applicantResult.createdAt,\n                type: applicantResult.type\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: sessionData\n        });\n    } catch (error) {\n        console.error('Sumsub session creation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Check authentication\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}));\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const sessionId = searchParams.get('sessionId');\n        if (!sessionId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Session ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get applicant status from Sumsub\n        const statusUrl = `/resources/applicants/${sessionId}/one`;\n        const statusHeaders = createSumsubHeaders('GET', statusUrl);\n        const statusResponse = await fetch(`${SUMSUB_BASE_URL}${statusUrl}`, {\n            method: 'GET',\n            headers: statusHeaders\n        });\n        if (!statusResponse.ok) {\n            const errorText = await statusResponse.text();\n            let errorData;\n            try {\n                errorData = JSON.parse(errorText);\n                console.error('Sumsub status check failed:', errorData);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to check status',\n                    details: errorData.description || errorText,\n                    correlationId: errorData.correlationId\n                }, {\n                    status: statusResponse.status\n                });\n            } catch  {\n                console.error('Sumsub status check failed:', errorText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to check status',\n                    details: errorText\n                }, {\n                    status: statusResponse.status\n                });\n            }\n        }\n        const statusResult = await statusResponse.json();\n        // Map Sumsub status to our internal status\n        const mapSumsubStatus = (review)=>{\n            if (!review) return 'pending';\n            switch(review.reviewStatus){\n                case 'completed':\n                    return review.reviewResult?.reviewAnswer === 'GREEN' ? 'approved' : 'rejected';\n                case 'pending':\n                    return 'pending';\n                case 'init':\n                    return 'pending';\n                default:\n                    return 'pending';\n            }\n        };\n        const sessionData = {\n            session_id: sessionId,\n            status: mapSumsubStatus(statusResult.review),\n            applicant_data: statusResult,\n            review_status: statusResult.review?.reviewStatus,\n            review_result: statusResult.review?.reviewResult\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: sessionData\n        });\n    } catch (error) {\n        console.error('Sumsub session status error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kyc/sumsub/session/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&page=%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fsumsub%2Fsession%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();