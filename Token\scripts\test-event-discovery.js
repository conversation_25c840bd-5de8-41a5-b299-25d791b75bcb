const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Testing event-based token discovery...");
  
  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  // Current factory address from config
  const FACTORY_ADDRESS = "******************************************";
  
  try {
    // Connect to the current factory
    const factory = await ethers.getContractAt("SecurityTokenFactory", FACTORY_ADDRESS);
    console.log("✅ Connected to factory at:", FACTORY_ADDRESS);

    // Get current block number
    const currentBlock = await ethers.provider.getBlockNumber();
    console.log("Current block:", currentBlock);

    // Search for TokenDeployed events
    console.log("\n🔍 Searching for TokenDeployed events...");
    
    try {
      // Try different block ranges to find events
      const ranges = [
        { from: -10000, to: "latest", desc: "Last 10,000 blocks" },
        { from: -50000, to: "latest", desc: "Last 50,000 blocks" },
        { from: 0, to: "latest", desc: "All blocks (may be slow)" }
      ];

      let allEvents = [];
      
      for (const range of ranges) {
        try {
          console.log(`\n📊 Checking ${range.desc}...`);
          
          const filter = factory.filters.TokenDeployed();
          const events = await factory.queryFilter(filter, range.from, range.to);
          
          console.log(`Found ${events.length} TokenDeployed events in ${range.desc}`);
          
          if (events.length > 0) {
            allEvents = events;
            
            // Display event details
            console.log("\n📋 Token deployment events:");
            events.forEach((event, index) => {
              console.log(`\n${index + 1}. Block ${event.blockNumber}:`);
              console.log(`   Token Address: ${event.args?.tokenAddress}`);
              console.log(`   Whitelist Address: ${event.args?.identityRegistryAddress}`);
              console.log(`   Name: ${event.args?.name}`);
              console.log(`   Symbol: ${event.args?.symbol}`);
              console.log(`   Decimals: ${event.args?.decimals}`);
              console.log(`   Max Supply: ${event.args?.maxSupply?.toString()}`);
              console.log(`   Admin: ${event.args?.admin}`);
              console.log(`   Has KYC: ${event.args?.hasKYC}`);
            });
            
            break; // Found events, no need to check other ranges
          }
        } catch (error) {
          console.log(`❌ Error checking ${range.desc}:`, error.message);
        }
      }

      if (allEvents.length === 0) {
        console.log("\n📝 No TokenDeployed events found in any range");
        console.log("💡 This means no tokens have been deployed by this factory yet");
        console.log("🚀 Create a token using the admin panel to test the discovery");
        return;
      }

      // Extract unique token addresses
      const tokenAddresses = [...new Set(allEvents.map(event => event.args?.tokenAddress).filter(Boolean))];
      console.log(`\n🎯 Found ${tokenAddresses.length} unique token addresses:`);
      
      // Test loading token details
      console.log("\n📄 Loading token details...");
      
      for (let i = 0; i < tokenAddresses.length; i++) {
        const address = tokenAddresses[i];
        console.log(`\n${i + 1}. Testing token at ${address}:`);
        
        try {
          const token = await ethers.getContractAt("SecurityToken", address);
          
          const name = await token.name();
          const symbol = await token.symbol();
          const decimals = await token.decimals();
          const totalSupply = await token.totalSupply();
          const maxSupply = await token.maxSupply();
          
          console.log(`   ✅ ${name} (${symbol})`);
          console.log(`   📊 Decimals: ${decimals}`);
          console.log(`   📈 Total Supply: ${totalSupply.toString()}`);
          console.log(`   📊 Max Supply: ${maxSupply.toString()}`);
          
          // Try to get whitelist address
          try {
            const whitelistAddress = await token.identityRegistry();
            console.log(`   🔐 Whitelist: ${whitelistAddress}`);
          } catch (err) {
            try {
              const whitelistAddress = await token.whitelistAddress();
              console.log(`   🔐 Whitelist: ${whitelistAddress}`);
            } catch (err2) {
              console.log(`   ⚠️ Could not read whitelist address`);
            }
          }
          
        } catch (error) {
          console.log(`   ❌ Error reading token details: ${error.message}`);
        }
      }

      console.log("\n🎉 Event-based discovery test completed!");
      console.log("\n📋 Summary:");
      console.log(`   • Found ${allEvents.length} deployment events`);
      console.log(`   • Discovered ${tokenAddresses.length} unique tokens`);
      console.log(`   • Dashboard will now automatically load these tokens`);

    } catch (error) {
      console.log("❌ Error searching for events:", error.message);
    }

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
