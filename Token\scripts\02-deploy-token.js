// We require the Hardhat Runtime Environment explicitly here. This is optional
// but useful for running the script in a standalone fashion through `node <script>`.
//
// When running the script with `npx hardhat run <script>` you'll find the Hardhat
// Runtime Environment's members available in the global scope.
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    // Get signers and network information
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Deploying token with the account:", deployer.address);
    console.log("Network:", networkName);

    // Load factory address from deployment file
    const deploymentsDir = path.join(__dirname, "../deployments");
    const deploymentFile = path.join(deploymentsDir, `${networkName}.json`);

    if (!fs.existsSync(deploymentFile)) {
      console.error(`Deployment file not found for network ${networkName}`);
      console.error("Please run the factory deployment script first");
      process.exit(1);
    }

    const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));
    const factoryAddress = deploymentInfo.factory;

    console.log("Using factory at:", factoryAddress);

    // Load the factory contract
    // In ethers v6, we use getContractAt instead of attach
    const factory = await hre.ethers.getContractAt("SecurityTokenFactory", factoryAddress);

    // Token parameters
    const tokenName = process.env.TOKEN_NAME || "Test Security Token3";
    const tokenSymbol = process.env.TOKEN_SYMBOL || "TST4";
    const tokenDecimals = process.env.TOKEN_DECIMALS ? parseInt(process.env.TOKEN_DECIMALS) : 0;

    // Validate decimals
    if (tokenDecimals < 0 || tokenDecimals > 18) {
      console.error("Error: TOKEN_DECIMALS must be between 0 and 18");
      process.exit(1);
    }

    // Parse maxSupply based on decimals
    const maxSupplyString = process.env.MAX_SUPPLY || "1000000";
    const maxSupply = tokenDecimals === 0
      ? BigInt(maxSupplyString)
      : hre.ethers.parseUnits(maxSupplyString, tokenDecimals);

    const adminAddress = process.env.ADMIN_ADDRESS || deployer.address;
    const tokenPrice = process.env.TOKEN_PRICE || "10 USD";
    const bonusTiers = process.env.BONUS_TIERS || "Tier 1: 5%, Tier 2: 10%, Tier 3: 15%";
    const tokenDetails = process.env.TOKEN_DETAILS || "Test security token for development";
    const tokenImageUrl = process.env.TOKEN_IMAGE_URL || "";
    const withKYC = process.env.WITH_KYC === "true" || true;

    console.log("Deploying token with parameters:");
    console.log("Name:", tokenName);
    console.log("Symbol:", tokenSymbol);
    console.log("Decimals:", tokenDecimals);
    console.log("Max Supply:", maxSupply.toString());
    console.log("Admin:", adminAddress);
    console.log("With KYC:", withKYC);

    // In ethers v6, try/catch this call since it's failing
    try {
      const existingToken = await factory.getTokenAddressBySymbol(tokenSymbol);
      if (existingToken !== hre.ethers.ZeroAddress) {
        console.error(`Token with symbol ${tokenSymbol} already exists at ${existingToken}`);
        process.exit(1);
      }
    } catch (error) {
      console.log("No existing token found with this symbol, proceeding with deployment...");
    }

    // Deploy the token
    console.log("Deploying token...");
    let tx;

    if (withKYC) {
      tx = await factory.deploySecurityTokenWithOptions(
        tokenName,
        tokenSymbol,
        tokenDecimals,
        maxSupply,
        adminAddress,
        tokenPrice,
        bonusTiers,
        tokenDetails,
        tokenImageUrl,
        true
      );
    } else {
      tx = await factory.deploySecurityToken(
        tokenName,
        tokenSymbol,
        tokenDecimals,
        maxSupply,
        adminAddress,
        tokenPrice,
        bonusTiers,
        tokenDetails,
        tokenImageUrl
      );
    }

    console.log("Transaction sent:", tx.hash);
    console.log("Waiting for confirmation...");

    // In ethers v6, we wait for transaction receipts differently
    const receipt = await tx.wait();

    // In ethers v6, the deploySecurityToken and deploySecurityTokenWithOptions return the token and whitelist addresses
    // directly, so we can try to parse them from the transaction result
    console.log("Extracting token and identity registry addresses...");

    let tokenAddress, identityRegistryAddress;

    // Try to get the addresses from the receipt directly
    for (const log of receipt.logs) {
      try {
        // Try to decode as TokenDeployed event
        const eventSignature = "TokenDeployed(address,address,string,string,uint8,uint256,address,bool)";
        const eventTopic = hre.ethers.id(eventSignature);

        if (log.topics[0] === eventTopic) {
          console.log("Found TokenDeployed event");

          // The event is:
          // TokenDeployed(address indexed tokenAddress, address indexed identityRegistryAddress, string name, string symbol, uint8 decimals, uint256 maxSupply, address admin, bool hasKYC)

          // The first and second indexed parameters (tokenAddress and identityRegistryAddress) are in topics
          tokenAddress = hre.ethers.getAddress("0x" + log.topics[1].slice(26));
          identityRegistryAddress = hre.ethers.getAddress("0x" + log.topics[2].slice(26));

          console.log("Decoded from event topics:");
          console.log("- Token address:", tokenAddress);
          console.log("- Identity Registry address:", identityRegistryAddress);
          break;
        }
      } catch (error) {
        console.log("Error decoding event:", error.message);
        continue;
      }
    }

    // If we couldn't get the addresses from the events, try to get them by calling the contract
    if (!tokenAddress || !identityRegistryAddress) {
      console.log("Could not decode event, trying to get addresses by calling the contract...");
      tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);

      // If we have the token address, get the identity registry address from the token
      if (tokenAddress && tokenAddress !== hre.ethers.ZeroAddress) {
        const token = await hre.ethers.getContractAt("SecurityToken", tokenAddress);
        identityRegistryAddress = await token.identityRegistry();
      } else {
        throw new Error("Failed to get token address");
      }
    }

    if (!tokenAddress || !identityRegistryAddress) {
      throw new Error("Failed to get token or identity registry address");
    }

    console.log("Token deployed successfully:");
    console.log("Token address:", tokenAddress);
    console.log("Identity Registry address:", identityRegistryAddress);

    // Save token deployment information
    const tokenInfo = {
      network: networkName,
      name: tokenName,
      symbol: tokenSymbol,
      decimals: tokenDecimals,
      address: tokenAddress,
      identityRegistry: identityRegistryAddress,
      admin: adminAddress,
      maxSupply: maxSupply.toString(),
      withKYC: withKYC,
      deploymentTx: tx.hash,
      timestamp: new Date().toISOString()
    };

    // Create a tokens directory if it doesn't exist
    const tokensDir = path.join(__dirname, "../tokens");
    if (!fs.existsSync(tokensDir)) {
      fs.mkdirSync(tokensDir);
    }

    // Save to a file named by the token symbol
    const tokenFile = path.join(tokensDir, `${tokenSymbol}.json`);
    fs.writeFileSync(
      tokenFile,
      JSON.stringify(tokenInfo, null, 2)
    );

    console.log(`Token information saved to ${tokenFile}`);

    // Update an index file with all tokens
    const indexFile = path.join(tokensDir, "index.json");
    let tokenIndex = {};

    if (fs.existsSync(indexFile)) {
      tokenIndex = JSON.parse(fs.readFileSync(indexFile, "utf8"));
    }

    tokenIndex[tokenSymbol] = {
      name: tokenName,
      decimals: tokenDecimals,
      address: tokenAddress,
      identityRegistry: identityRegistryAddress,
      network: networkName,
      withKYC: withKYC
    };

    fs.writeFileSync(
      indexFile,
      JSON.stringify(tokenIndex, null, 2)
    );

    console.log("Token index updated");
    console.log("Deployment completed successfully");

  } catch (error) {
    console.error("Error during token deployment:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });