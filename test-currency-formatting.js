// Test currency formatting function
function formatPrice(price, currency) {
  const numPrice = parseFloat(price);

  // Handle crypto currencies that don't have standard currency codes
  const cryptoCurrencies = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI'];
  if (cryptoCurrencies.includes(currency.toUpperCase())) {
    return `${numPrice} ${currency.toUpperCase()}`;
  }

  // Handle standard fiat currencies
  const supportedCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
  const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(numPrice);
}

// Test cases
const testCases = [
  { price: '1', currency: 'BTC' },
  { price: '10', currency: 'USD' },
  { price: '100', currency: 'EUR' },
  { price: '0.5', currency: 'ETH' },
  { price: '25', currency: 'UNKNOWN' }, // Should default to USD
];

console.log('Currency formatting test results:');
testCases.forEach(test => {
  const formatted = formatPrice(test.price, test.currency);
  console.log(`${test.price} ${test.currency} → ${formatted}`);
});

// Test with actual token data
const fetch = require('node-fetch');

async function testWithRealData() {
  try {
    console.log('\nTesting with real token data:');
    const response = await fetch('http://localhost:3003/api/tokens');
    const tokens = await response.json();
    
    tokens.slice(0, 5).forEach(token => {
      const formatted = formatPrice(token.price, token.currency);
      console.log(`${token.symbol}: ${token.price} ${token.currency} → ${formatted}`);
    });
    
  } catch (error) {
    console.error('Error fetching real data:', error.message);
  }
}

testWithRealData();
