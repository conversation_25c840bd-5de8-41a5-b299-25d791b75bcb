import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { createAuthHeaders } from '@/lib/jwt';

// GET /api/client/kyc - Get KYC status
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create authenticated headers for admin panel API
    const authHeaders = createAuthHeaders(session.user);

    // Find client by email first
    const adminApiUrl = process.env.ADMIN_API_BASE_URL || 'http://localhost:3000/api';
    const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(session.user.email || '')}`, {
      headers: authHeaders,
    });

    if (!searchResponse.ok) {
      throw new Error('Failed to find client profile');
    }

    const searchData = await searchResponse.json();
    const client = searchData.clients?.[0];

    if (!client) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Get KYC details
    const kycResponse = await fetch(`${adminApiUrl}/clients/${client.id}/kyc`, {
      headers: authHeaders,
    });

    if (!kycResponse.ok) {
      throw new Error('Failed to fetch KYC status');
    }

    const kycData = await kycResponse.json();
    return NextResponse.json({
      status: kycData.kycStatus,
      notes: kycData.kycNotes,
      submittedAt: kycData.updatedAt,
      completedAt: kycData.kycCompletedAt,
    });
  } catch (error) {
    console.error('Error fetching KYC status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch KYC status' },
      { status: 500 }
    );
  }
}

// POST /api/client/kyc - Submit KYC application
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Create authenticated headers for admin panel API
    const authHeaders = createAuthHeaders(session.user);

    // Find client by email first
    const adminApiUrl = process.env.ADMIN_API_BASE_URL || 'http://localhost:3000/api';
    const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(session.user.email || '')}`, {
      headers: authHeaders,
    });

    if (!searchResponse.ok) {
      throw new Error('Failed to find client profile');
    }

    const searchData = await searchResponse.json();
    const client = searchData.clients?.[0];

    if (!client) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Update KYC status to IN_REVIEW when submitted
    const kycResponse = await fetch(`${adminApiUrl}/clients/${client.id}/kyc`, {
      method: 'PUT',
      headers: authHeaders,
      body: JSON.stringify({
        kycStatus: 'IN_REVIEW',
        kycNotes: 'KYC application submitted by client',
      }),
    });

    if (!kycResponse.ok) {
      throw new Error('Failed to submit KYC application');
    }

    return NextResponse.json({
      success: true,
      message: 'KYC application submitted successfully. It will be reviewed within 2-3 business days.',
    });
  } catch (error) {
    console.error('Error submitting KYC application:', error);
    return NextResponse.json(
      { error: 'Failed to submit KYC application' },
      { status: 500 }
    );
  }
}
