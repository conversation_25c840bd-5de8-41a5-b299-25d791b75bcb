# Token Minting API Integration Guide

## Overview

This guide explains how to integrate the optimized token minting functionality with your admin panel. We've created specialized scripts to handle token minting on the Amoy testnet that are resilient against RPC issues.

## Minting Scripts Overview

We've created three different solutions:

1. `direct-mint.js` - Core script with optimized gas values and multiple RPC fallbacks
2. `direct-mint.bat` - Windows batch file for easy command-line execution
3. `api-mint.js` - API-friendly wrapper for integration with backend services

## Integration Options

### Option 1: Direct API Integration (Recommended)

Your backend can execute the script via Node.js's child process:

```javascript
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

async function mintTokens(tokenAddress, recipientAddress, amount) {
  // Set environment variables for the script
  process.env.TOKEN_ADDRESS = tokenAddress;
  process.env.TO_ADDRESS = recipientAddress;
  process.env.AMOUNT = amount;
  process.env.GAS_LIMIT = "5000000";  // Optimized gas limit
  process.env.GAS_PRICE = "50";       // 50 gwei
  
  return new Promise((resolve, reject) => {
    // Execute the script
    exec('npx hardhat run scripts/api-mint.js --network amoy', 
      { maxBuffer: 1024 * 1024 }, // 1MB buffer
      (error, stdout, stderr) => {
        console.log(stdout);
        if (stderr) console.error(stderr);
        
        if (error) {
          // Check if the transaction was actually submitted
          try {
            const resultsPath = path.join(__dirname, 'minting-results.json');
            if (fs.existsSync(resultsPath)) {
              const mintings = JSON.parse(fs.readFileSync(resultsPath));
              const latest = mintings[mintings.length - 1];
              
              // If we have a transaction hash, return it even if there was an error
              if (latest.transactionHash) {
                resolve({
                  ...latest,
                  status: 'submitted',
                  message: 'Transaction submitted but confirmation status unknown'
                });
                return;
              }
            }
          } catch (e) {
            // Ignore errors reading the file
          }
          
          reject(new Error(`Token minting failed: ${error.message}`));
          return;
        }
        
        // Read the minting result
        try {
          const resultsPath = path.join(__dirname, 'minting-results.json');
          const mintings = JSON.parse(fs.readFileSync(resultsPath));
          const latest = mintings[mintings.length - 1];
          resolve(latest);
        } catch (readError) {
          reject(new Error(`Minting may have succeeded but couldn't read result: ${readError.message}`));
        }
      }
    );
  });
}
```

### Option 2: Import as Module

The script can also be imported as a module:

```javascript
const mintTokens = require('./scripts/api-mint');

async function handleMintRequest(req, res) {
  try {
    // Set environment variables
    process.env.TOKEN_ADDRESS = req.body.tokenAddress;
    process.env.TO_ADDRESS = req.body.recipientAddress;
    process.env.AMOUNT = req.body.amount;
    // Use optimized gas settings
    process.env.GAS_LIMIT = "5000000";
    process.env.GAS_PRICE = "50";
    
    // Execute minting
    const result = await mintTokens();
    res.json({ success: true, mint: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
}
```

## Frontend Implementation

In your admin panel frontend, implement a form with these fields:
- Token address (or dropdown of deployed tokens)
- Recipient address
- Amount to mint

Example React component:

```jsx
function MintForm({ onSubmit, isLoading }) {
  const [tokenAddress, setTokenAddress] = useState('');
  const [recipient, setRecipient] = useState('');
  const [amount, setAmount] = useState('');
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ tokenAddress, recipient, amount });
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div className="form-group">
        <label>Token Address:</label>
        <input 
          type="text" 
          value={tokenAddress} 
          onChange={(e) => setTokenAddress(e.target.value)} 
          required 
        />
      </div>
      
      <div className="form-group">
        <label>Recipient Address:</label>
        <input 
          type="text" 
          value={recipient} 
          onChange={(e) => setRecipient(e.target.value)} 
          required 
        />
      </div>
      
      <div className="form-group">
        <label>Amount:</label>
        <input 
          type="text" 
          value={amount} 
          onChange={(e) => setAmount(e.target.value)} 
          required 
        />
      </div>
      
      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Minting...' : 'Mint Tokens'}
      </button>
    </form>
  );
}
```

## HTTP API Endpoint

Create an endpoint in your admin panel:

```
POST /api/tokens/mint

Request Body:
{
  "tokenAddress": "0x324ab4526d55630bf8afa86f479697b23c6792a4",
  "recipientAddress": "0x56f3726c92b8b92a6ab71983886f91718540d888",
  "amount": "100"
}

Response:
{
  "success": true,
  "mint": {
    "tokenAddress": "0x324ab4526d55630bf8afa86f479697b23c6792a4",
    "recipientAddress": "0x56f3726c92b8b92a6ab71983886f91718540d888",
    "amount": "100",
    "transactionHash": "0x...",
    "status": "success",
    "confirmed": true,
    "blockNumber": 12345678
  }
}
```

## Error Handling

The API script handles errors extensively and creates log files:

- `minting-results.json` - Successful minting operations
- `minting-errors.json` - Failed minting operations with error details

Your API should check these files if the script execution fails to determine if the operation actually succeeded or not.

## Testing

To test minting manually from the command line:

```bash
# Windows - using batch file
direct-mint.bat 0x324ab4526d55630bf8afa86f479697b23c6792a4 0x56f3726c92b8b92a6ab71983886f91718540d888 100

# PowerShell with custom parameters
$env:TOKEN_ADDRESS="0x324ab4526d55630bf8afa86f479697b23c6792a4"; $env:TO_ADDRESS="0x56f3726c92b8b92a6ab71983886f91718540d888"; $env:AMOUNT="100"; npx hardhat run scripts/direct-mint.js --network amoy
``` 