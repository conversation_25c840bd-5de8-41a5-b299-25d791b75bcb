const { ethers, upgrades } = require("hardhat");

async function main() {
  const proxyAddress = "******************************************";
  
  // Get the implementation address
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
  console.log("Implementation contract address: ", implementationAddress);
  
  // Verify the implementation contract
  try {
    await hre.run("verify:verify", {
      address: implementationAddress,
      // Add constructor arguments here if your contract has any
      // constructorArguments: [],
    });
  } catch (error) {
    console.error("Verification error:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 