import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

/**
 * Update KYC status in admin panel from client portal
 * POST /api/client/kyc/update-status
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { kycStatus, kycNotes } = body;

    if (!kycStatus) {
      return NextResponse.json({ error: 'KYC status is required' }, { status: 400 });
    }

    console.log('Updating KYC status for user:', {
      userEmail: session.user.email,
      kycStatus,
      kycNotes
    });

    // Find client by email in admin panel
    const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
    
    try {
      const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(session.user.email || '')}&limit=1`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!searchResponse.ok) {
        console.error('Failed to search for client:', searchResponse.status);
        return NextResponse.json({ error: 'Failed to find client profile' }, { status: 404 });
      }

      const searchData = await searchResponse.json();
      const client = searchData.clients?.[0];

      if (!client) {
        console.error('Client not found for email:', session.user.email);
        return NextResponse.json({ error: 'Client profile not found' }, { status: 404 });
      }

      console.log('Found client for KYC update:', {
        clientId: client.id,
        email: client.email,
        currentKycStatus: client.kycStatus
      });

      // Update the client's KYC status
      const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}/kyc`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          kycStatus,
          kycNotes: kycNotes || 'KYC status updated via client portal',
        }),
      });

      if (!updateResponse.ok) {
        const errorText = await updateResponse.text();
        console.error('Failed to update KYC status:', errorText);
        return NextResponse.json({ error: 'Failed to update KYC status' }, { status: 500 });
      }

      const updatedClient = await updateResponse.json();
      console.log('Successfully updated client KYC status:', {
        clientId: updatedClient.id,
        newKycStatus: updatedClient.kycStatus,
        kycCompletedAt: updatedClient.kycCompletedAt
      });

      return NextResponse.json({
        success: true,
        message: 'KYC status updated successfully',
        client: {
          id: updatedClient.id,
          kycStatus: updatedClient.kycStatus,
          kycCompletedAt: updatedClient.kycCompletedAt,
          kycNotes: updatedClient.kycNotes
        }
      });

    } catch (error) {
      // If admin panel is not running, return graceful error
      console.log('Admin panel not available for KYC update:', error);
      return NextResponse.json({ 
        success: false,
        message: 'KYC status will be updated automatically via webhook' 
      }, { status: 202 });
    }

  } catch (error) {
    console.error('Error updating KYC status:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
