const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  try {
    const [deployer] = await hre.ethers.getSigners();
    const networkName = hre.network.name;

    console.log("Checking token symbol with the account:", deployer.address);
    console.log("Network:", networkName);

    // Load deployment information
    const deploymentFile = path.join(__dirname, "../deployments", `${networkName}.json`);
    if (!fs.existsSync(deploymentFile)) {
      console.error(`Deployment file not found: ${deploymentFile}`);
      process.exit(1);
    }

    const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));
    const factoryAddress = deploymentInfo.factory;

    if (!factoryAddress) {
      console.error("Factory address not found in deployment file");
      process.exit(1);
    }

    console.log("Using factory at:", factoryAddress);

    // Connect to the factory contract
    const factory = await hre.ethers.getContractAt("SecurityTokenFactory", factoryAddress);

    // Get the symbol to check
    const symbol = process.env.TOKEN_SYMBOL || "TEST";
    console.log("Checking symbol:", symbol);

    // Check if token with this symbol already exists
    const existingTokenAddress = await factory.getTokenAddressBySymbol(symbol);
    console.log("Existing token address for symbol:", existingTokenAddress);

    if (existingTokenAddress === hre.ethers.ZeroAddress) {
      console.log("✅ Symbol is available for use");
    } else {
      console.log("❌ Symbol already exists at address:", existingTokenAddress);
      
      // Try to get more info about the existing token
      try {
        const token = await hre.ethers.getContractAt("SecurityToken", existingTokenAddress);
        const name = await token.name();
        const decimals = await token.decimals();
        const maxSupply = await token.maxSupply();
        
        console.log("Existing token details:");
        console.log("- Name:", name);
        console.log("- Symbol:", symbol);
        console.log("- Decimals:", decimals.toString());
        console.log("- Max Supply:", maxSupply.toString());
      } catch (err) {
        console.log("Could not read existing token details:", err.message);
      }
    }

  } catch (error) {
    console.error("Error checking token symbol:", error);
    process.exitCode = 1;
  }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
