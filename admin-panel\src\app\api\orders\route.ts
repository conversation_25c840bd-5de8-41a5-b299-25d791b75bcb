import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { OrderStatus } from '@prisma/client';

// Generate a random payment reference
function generatePaymentReference(): string {
  const prefix = 'ORD';
  const timestamp = Date.now().toString(36).toUpperCase();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}-${timestamp}-${random}`;
}

// GET /api/orders - Get all orders with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenId = searchParams.get('tokenId');
    const clientId = searchParams.get('clientId');
    const status = searchParams.get('status') as OrderStatus | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (tokenId) where.tokenId = tokenId;
    if (clientId) where.clientId = clientId;
    if (status) where.status = status;

    // Get orders with related data
    const orders = await prisma.order.findMany({
      where,
      select: {
        id: true,
        status: true,
        tokensOrdered: true,
        tokensConfirmed: true,
        amountToPay: true,
        confirmedPayment: true,
        tokenPrice: true,
        paymentReference: true,
        transactionHash: true,
        blockNumber: true,
        createdAt: true,
        updatedAt: true,
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
            tokenPrice: true,
            currency: true
          }
        },
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await prisma.order.count({ where });

    return NextResponse.json({
      success: true,
      orders,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create a new order
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tokenId,
      clientId,
      tokensOrdered,
    } = body;

    // Validate required fields
    if (!tokenId || !clientId || !tokensOrdered) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: tokenId, clientId, tokensOrdered' },
        { status: 400 }
      );
    }

    // Validate that token and client exist
    const token = await prisma.token.findUnique({
      where: { id: tokenId }
    });

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token not found' },
        { status: 404 }
      );
    }

    const currentTokenPrice = token.tokenPrice;

    const client = await prisma.client.findUnique({
      where: { id: clientId }
    });

    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Calculate amount to pay
    const tokensOrderedNum = parseFloat(tokensOrdered);
    const tokenPriceNum = parseFloat(currentTokenPrice.replace(/[^\d.-]/g, ''));
    const amountToPay = (tokensOrderedNum * tokenPriceNum).toString();

    // Generate payment reference
    const paymentReference = generatePaymentReference();

    // Create the order
    const order = await prisma.order.create({
      data: {
        tokenId,
        clientId,
        tokensOrdered: tokensOrdered.toString(),
        tokenPrice: currentTokenPrice,
        amountToPay,
        paymentReference,
      },
      include: {
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
            tokenPrice: true,
            currency: true
          }
        },
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      order
    });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
