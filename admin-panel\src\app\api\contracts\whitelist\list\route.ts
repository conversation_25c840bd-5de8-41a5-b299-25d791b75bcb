import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import WhitelistABI from '../../../../../contracts/Whitelist.json';
import SecurityTokenABI from '../../../../../contracts/SecurityToken.json';
import { networkConfig } from '../../../../../config';

const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002,
};

// Vault addresses where frozen tokens are stored
const VAULT_ADDRESSES = {
  amoy: process.env.AMOY_VAULT_ADDRESS || '******************************************', // Default to admin address
  polygon: process.env.POLYGON_VAULT_ADDRESS || '******************************************',
  unknown: process.env.AMOY_VAULT_ADDRESS || '******************************************'
};

// We need to track partial freeze transfers to identify frozen tokens per wallet
// This would ideally be handled by an escrow contract, but for now we'll use event logs
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const whitelistAddress = searchParams.get('whitelistAddress');
    const tokenAddress = searchParams.get('tokenAddress'); // Added token address parameter
    const network = searchParams.get('network') || 'amoy';
    
    if (!whitelistAddress) {
      return NextResponse.json(
        { error: 'Whitelist address is required' },
        { status: 400 }
      );
    }

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;
    const vaultAddress = VAULT_ADDRESSES[actualNetwork as keyof typeof VAULT_ADDRESSES] || VAULT_ADDRESSES.amoy;
    
    // Connect to the network with explicit chainId
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });
    
    // Connect to the whitelist contract
    const whitelistContract = new ethers.Contract(
      whitelistAddress,
      WhitelistABI.abi,
      provider
    );

    // Connect to the token contract
    const tokenContract = new ethers.Contract(
      tokenAddress,
      SecurityTokenABI.abi,
      provider
    );
    
    // Get token decimals
    const decimals = await tokenContract.decimals();
    
    // Get all AddedToWhitelist events
    const addedFilter = whitelistContract.filters.AddedToWhitelist();
    const removedFilter = whitelistContract.filters.RemovedFromWhitelist();
    const frozenFilter = whitelistContract.filters.AddressFrozen();
    const unfrozenFilter = whitelistContract.filters.AddressUnfrozen();
    
    const fromBlock = 0; // Ideally would be the contract creation block, but we'll start from 0 for simplicity
    
    // Get all events
    const addedEvents = await whitelistContract.queryFilter(addedFilter, fromBlock);
    const removedEvents = await whitelistContract.queryFilter(removedFilter, fromBlock);
    const frozenEvents = await whitelistContract.queryFilter(frozenFilter, fromBlock);
    const unfrozenEvents = await whitelistContract.queryFilter(unfrozenFilter, fromBlock);
    
    // Track whitelisted and frozen addresses
    const whitelistedAddresses = new Map();
    
    // Process added events
    for (const event of addedEvents) {
      const log = event as unknown as { args?: Array<string> };
      if (log && log.args && log.args.length > 0) {
        const address = log.args[0];
        whitelistedAddresses.set(address, { 
          whitelisted: true, 
          frozen: false 
        });
      }
    }
    
    // Process removed events
    for (const event of removedEvents) {
      const log = event as unknown as { args?: Array<string> };
      if (log && log.args && log.args.length > 0) {
        const address = log.args[0];
        if (whitelistedAddresses.has(address)) {
          whitelistedAddresses.set(address, { 
            ...whitelistedAddresses.get(address),
            whitelisted: false 
          });
        }
      }
    }
    
    // Process frozen events
    for (const event of frozenEvents) {
      const log = event as unknown as { args?: Array<string> };
      if (log && log.args && log.args.length > 0) {
        const address = log.args[0];
        if (whitelistedAddresses.has(address)) {
          whitelistedAddresses.set(address, { 
            ...whitelistedAddresses.get(address),
            frozen: true 
          });
        }
      }
    }
    
    // Process unfrozen events
    for (const event of unfrozenEvents) {
      const log = event as unknown as { args?: Array<string> };
      if (log && log.args && log.args.length > 0) {
        const address = log.args[0];
        if (whitelistedAddresses.has(address)) {
          whitelistedAddresses.set(address, { 
            ...whitelistedAddresses.get(address),
            frozen: false 
          });
        }
      }
    }
    
    // Filter only currently whitelisted addresses
    const whitelistedAddressList = Array.from(whitelistedAddresses.entries())
      .filter(([_, state]) => state.whitelisted)
      .map(([address, state]) => ({
        address,
        frozen: state.frozen
      }));
    
    // Get transfer events from users to vault (partial freeze) and from vault to users (partial unfreeze)
    // We only need to consider transfers from the adminTransfer function to track partial freezing
    const transferFilter = tokenContract.filters.ForcedTransfer();
    const transferEvents = await tokenContract.queryFilter(transferFilter, fromBlock);
    
    // Create a map to track frozen tokens per address
    const frozenTokensMap = new Map();
    
    // Process admin transfer events (ForcedTransfer)
    for (const event of transferEvents) {
      const log = event as unknown as { args?: Array<any> };
      if (log && log.args && log.args.length >= 3) {
        const from = log.args[0] as string;
        const to = log.args[1] as string;
        const amount = log.args[2] as bigint;
        
        if (to.toLowerCase() === vaultAddress.toLowerCase()) {
          // This is a freeze operation: from user to vault
          const currentFrozen = frozenTokensMap.get(from) || BigInt(0);
          frozenTokensMap.set(from, currentFrozen + amount);
        } else if (from.toLowerCase() === vaultAddress.toLowerCase()) {
          // This is an unfreeze operation: from vault to user
          const currentFrozen = frozenTokensMap.get(to) || BigInt(0);
          if (currentFrozen >= amount) {
            frozenTokensMap.set(to, currentFrozen - amount);
          }
        }
      }
    }
    
    // Fetch token balances for each whitelisted address
    const addressesWithBalances = await Promise.all(
      whitelistedAddressList.map(async (item) => {
        try {
          const balanceRaw = await tokenContract.balanceOf(item.address);
          const balance = ethers.formatUnits(balanceRaw, decimals);
          
          // Get the frozen tokens for this address
          const frozenTokensRaw = frozenTokensMap.get(item.address) || BigInt(0);
          const frozenTokens = ethers.formatUnits(frozenTokensRaw, decimals);
          
          return {
            ...item,
            balance,
            frozenTokens
          };
        } catch (error) {
          console.error(`Error fetching balance for ${item.address}:`, error);
          return {
            ...item,
            balance: '0',
            frozenTokens: '0'
          };
        }
      })
    );
    
    return NextResponse.json({
      success: true,
      whitelistedAddresses: addressesWithBalances,
      vaultAddress
    });
    
  } catch (error: any) {
    console.error('Error getting whitelisted addresses:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
} 