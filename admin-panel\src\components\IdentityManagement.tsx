'use client';

import { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon, UserGroupIcon, ShieldCheckIcon, DocumentCheckIcon } from '@heroicons/react/24/outline';

interface IdentityStatus {
  address: string;
  blockchain: {
    isVerified: boolean;
    isWhitelisted: boolean;
    isKycApproved: boolean;
    country: string;
    isFrozen: boolean;
    claims: number;
  };
  database: {
    exists: boolean;
    kycStatus: string;
    isWhitelisted: boolean;
    nationality?: string;
  };
  client: any;
}

interface IdentityManagementProps {
  clientId?: string;
  walletAddress?: string;
  onStatusUpdate?: () => void;
}

export default function IdentityManagement({ 
  clientId, 
  walletAddress: initialAddress, 
  onStatusUpdate 
}: IdentityManagementProps) {
  const [address, setAddress] = useState(initialAddress || '');
  const [status, setStatus] = useState<IdentityStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const fetchIdentityStatus = async (addr: string) => {
    if (!addr) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/identity?address=${addr}`);
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        throw new Error('Failed to fetch identity status');
      }
    } catch (error) {
      console.error('Error fetching identity status:', error);
      setMessage({ type: 'error', text: 'Failed to fetch identity status' });
    } finally {
      setLoading(false);
    }
  };

  const performAction = async (action: string, country?: string) => {
    if (!address) return;

    setActionLoading(action);
    setMessage(null);

    try {
      const response = await fetch('/api/identity', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          address,
          country,
          clientId
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: `${action.replace('_', ' ')} completed successfully. Tx: ${data.txHash}` 
        });
        
        // Refresh status
        await fetchIdentityStatus(address);
        
        // Notify parent component
        if (onStatusUpdate) {
          onStatusUpdate();
        }
      } else {
        throw new Error(data.error || 'Action failed');
      }
    } catch (error) {
      console.error(`Error performing ${action}:`, error);
      setMessage({ type: 'error', text: `Failed to ${action.replace('_', ' ')}: ${error.message}` });
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    if (initialAddress) {
      fetchIdentityStatus(initialAddress);
    }
  }, [initialAddress]);

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ml-2 ${
        condition
          ? 'text-green-600 bg-green-100'
          : 'text-gray-600 bg-gray-100'
      }`}>
        {condition ? (
          <><CheckCircleIcon className="w-3 h-3 mr-1" /> {trueText}</>
        ) : (
          <><XCircleIcon className="w-3 h-3 mr-1" /> {falseText}</>
        )}
      </span>
    );
  };

  const getCountryName = (code: string) => {
    const countries: { [key: string]: string } = {
      '840': 'United States',
      '124': 'Canada',
      '826': 'United Kingdom',
      '276': 'Germany',
      '250': 'France',
      '380': 'Italy',
      '724': 'Spain',
      '528': 'Netherlands',
      '756': 'Switzerland',
      '36': 'Australia',
      '392': 'Japan',
      '702': 'Singapore'
    };
    return countries[code] || `Country ${code}`;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <ShieldCheckIcon className="w-5 h-5 mr-2 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">ERC-3643 Identity Management</h3>
        </div>

        <div className="space-y-4">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Enter wallet address (0x...)"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={() => fetchIdentityStatus(address)}
              disabled={!address || loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                'Check Status'
              )}
            </button>
          </div>

          {message && (
            <div className={`p-4 rounded-md ${
              message.type === 'error'
                ? 'bg-red-100 border border-red-400 text-red-700'
                : 'bg-green-100 border border-green-400 text-green-700'
            }`}>
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                <span>{message.text}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {status && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Blockchain Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <UserGroupIcon className="w-5 h-5 mr-2 text-blue-600" />
              <h4 className="text-lg font-semibold text-gray-900">Blockchain Status</h4>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Identity Verified:</span>
                {getStatusBadge(status.blockchain.isVerified, 'Verified', 'Not Verified')}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Whitelisted:</span>
                {getStatusBadge(status.blockchain.isWhitelisted, 'Whitelisted', 'Not Whitelisted')}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">KYC Approved:</span>
                {getStatusBadge(status.blockchain.isKycApproved, 'Approved', 'Not Approved')}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Country:</span>
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-blue-600 bg-blue-100">
                  {getCountryName(status.blockchain.country)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Frozen:</span>
                {getStatusBadge(!status.blockchain.isFrozen, 'Active', 'Frozen')}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Claims:</span>
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-600 bg-gray-100">
                  {status.blockchain.claims} claims
                </span>
              </div>
            </div>
          </div>

          {/* Database Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <DocumentCheckIcon className="w-5 h-5 mr-2 text-blue-600" />
              <h4 className="text-lg font-semibold text-gray-900">Database Status</h4>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Client Exists:</span>
                {getStatusBadge(status.database.exists, 'Found', 'Not Found')}
              </div>

              {status.database.exists && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">KYC Status:</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      status.database.kycStatus === 'APPROVED' ? 'text-green-600 bg-green-100' :
                      status.database.kycStatus === 'REJECTED' ? 'text-red-600 bg-red-100' :
                      'text-gray-600 bg-gray-100'
                    }`}>
                      {status.database.kycStatus}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">DB Whitelisted:</span>
                    {getStatusBadge(status.database.isWhitelisted, 'Yes', 'No')}
                  </div>

                  {status.database.nationality && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">Nationality:</span>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-blue-600 bg-blue-100">
                        {status.database.nationality}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {status && (
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Actions</h4>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {!status.blockchain.isVerified && (
              <button
                onClick={() => performAction('register', status.database.nationality || 'US')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'register' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                ) : (
                  'Register Identity'
                )}
              </button>
            )}

            {status.blockchain.isVerified && !status.blockchain.isWhitelisted && (
              <button
                onClick={() => performAction('whitelist')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'whitelist' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                ) : (
                  'Add to Whitelist'
                )}
              </button>
            )}

            {status.blockchain.isWhitelisted && (
              <button
                onClick={() => performAction('unwhitelist')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'unwhitelist' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                ) : (
                  'Remove from Whitelist'
                )}
              </button>
            )}

            {status.blockchain.isVerified && !status.blockchain.isKycApproved && (
              <button
                onClick={() => performAction('approve_kyc')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'approve_kyc' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                ) : (
                  'Approve KYC'
                )}
              </button>
            )}

            {status.blockchain.isKycApproved && (
              <button
                onClick={() => performAction('revoke_kyc')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'revoke_kyc' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                ) : (
                  'Revoke KYC'
                )}
              </button>
            )}

            {status.blockchain.isVerified && !status.blockchain.isFrozen && (
              <button
                onClick={() => performAction('freeze')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'freeze' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mx-auto"></div>
                ) : (
                  'Freeze Address'
                )}
              </button>
            )}

            {status.blockchain.isFrozen && (
              <button
                onClick={() => performAction('unfreeze')}
                disabled={!!actionLoading}
                className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {actionLoading === 'unfreeze' ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                ) : (
                  'Unfreeze Address'
                )}
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
