"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx":
/*!************************************************************!*\
  !*** ./src/components/qualification/QualificationFlow.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QualificationFlow: () => (/* binding */ QualificationFlow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _CountrySelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CountrySelection */ \"(app-pages-browser)/./src/components/qualification/CountrySelection.tsx\");\n/* harmony import */ var _TokenAgreement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenAgreement */ \"(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\");\n/* harmony import */ var _QualificationForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../QualificationForm */ \"(app-pages-browser)/./src/components/QualificationForm.tsx\");\n/* harmony import */ var _WalletConnection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../WalletConnection */ \"(app-pages-browser)/./src/components/WalletConnection.tsx\");\n/* harmony import */ var _AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../AutomaticKYC */ \"(app-pages-browser)/./src/components/AutomaticKYC.tsx\");\n/* __next_internal_client_entry_do_not_use__ QualificationFlow auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QualificationFlow(param) {\n    let { tokenAddress, tokenName, tokenSymbol } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [stepData, setStepData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        country: '',\n        agreementAccepted: false,\n        profileCompleted: false,\n        walletConnected: false,\n        kycCompleted: false\n    });\n    const [kycStatus, setKycStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [kycError, setKycError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch existing qualification progress\n    const { data: qualificationProgress, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'qualification-progress',\n            tokenAddress\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const params = new URLSearchParams();\n                if (tokenAddress) params.append('tokenAddress', tokenAddress);\n                const response = await fetch(\"/api/client/qualification-progress?\".concat(params.toString()));\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch client profile\n    const { data: profile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/profile');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Fetch wallet status\n    const { data: walletStatus } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'wallet-status'\n        ],\n        queryFn: {\n            \"QualificationFlow.useQuery\": async ()=>{\n                const response = await fetch('/api/client/wallet');\n                if (response.ok) {\n                    return response.json();\n                }\n                return null;\n            }\n        }[\"QualificationFlow.useQuery\"]\n    });\n    // Update step data based on fetched progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QualificationFlow.useEffect\": ()=>{\n            if (qualificationProgress) {\n                // Try to get more recent data from localStorage first\n                const storageKey = \"qualification_progress_\".concat(tokenAddress);\n                let localProgress = null;\n                try {\n                    const stored = localStorage.getItem(storageKey);\n                    if (stored) {\n                        localProgress = JSON.parse(stored);\n                        console.log('📱 Found localStorage progress:', localProgress);\n                    }\n                } catch (error) {\n                    console.error('Error reading localStorage:', error);\n                }\n                // Use localStorage data if it's more recent, otherwise use API data\n                const progressToUse = localProgress || qualificationProgress;\n                const newStepData = {\n                    country: progressToUse.country || '',\n                    agreementAccepted: progressToUse.agreementAccepted || false,\n                    profileCompleted: progressToUse.profileCompleted || !!profile,\n                    walletConnected: progressToUse.walletConnected || !!(walletStatus === null || walletStatus === void 0 ? void 0 : walletStatus.verified),\n                    kycCompleted: progressToUse.kycCompleted || (profile === null || profile === void 0 ? void 0 : profile.kycStatus) === 'APPROVED'\n                };\n                setStepData(newStepData);\n                // Set current step based on saved progress or calculate from completion status\n                let calculatedStep = progressToUse.currentStep || 0;\n                // Allow users to progress through all steps without blocking\n                // Only auto-advance to next incomplete step if current step is completed\n                if (calculatedStep === 0 && newStepData.country) {\n                    calculatedStep = 1; // Move to agreement if country is selected\n                } else if (calculatedStep === 1 && newStepData.agreementAccepted) {\n                    calculatedStep = 2; // Move to profile if agreement is accepted\n                } else if (calculatedStep === 2 && newStepData.profileCompleted) {\n                    calculatedStep = 3; // Move to wallet if profile is completed\n                } else if (calculatedStep === 3 && newStepData.walletConnected) {\n                    calculatedStep = 4; // Move to KYC if wallet is connected\n                } else if (calculatedStep === 4 && newStepData.kycCompleted) {\n                    calculatedStep = 5; // All completed\n                }\n                setCurrentStep(calculatedStep);\n                console.log('🔄 Restored qualification state:', {\n                    stepData: newStepData,\n                    currentStep: calculatedStep,\n                    savedProgress: progressToUse,\n                    source: localProgress ? 'localStorage' : 'API'\n                });\n            }\n        }\n    }[\"QualificationFlow.useEffect\"], [\n        qualificationProgress,\n        profile,\n        walletStatus,\n        tokenAddress\n    ]);\n    const steps = [\n        {\n            id: 'country',\n            title: 'Country Selection',\n            description: 'Select your country of residence for compliance',\n            status: stepData.country ? 'completed' : currentStep === 0 ? 'current' : 'pending'\n        },\n        {\n            id: 'agreement',\n            title: 'Token Agreement',\n            description: \"Accept the \".concat(tokenName || 'token', \" specific investment agreement\"),\n            status: stepData.agreementAccepted ? 'completed' : currentStep === 1 ? 'current' : 'pending'\n        },\n        {\n            id: 'profile',\n            title: 'Main Information',\n            description: 'Complete your personal and financial information',\n            status: stepData.profileCompleted ? 'completed' : currentStep === 2 ? 'current' : 'pending'\n        },\n        {\n            id: 'wallet',\n            title: 'Wallet Connection',\n            description: 'Connect and verify your cryptocurrency wallet',\n            status: stepData.walletConnected ? 'completed' : currentStep === 3 ? 'current' : 'pending'\n        },\n        {\n            id: 'kyc',\n            title: 'KYC Verification',\n            description: 'Complete identity verification using Sumsub',\n            status: stepData.kycCompleted ? 'completed' : kycStatus === 'failed' ? 'error' : currentStep === 4 ? 'current' : 'pending'\n        }\n    ];\n    const getStepIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case 'current':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 rounded-full border-2 border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStepColor = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'current':\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    // Function to save qualification progress\n    const saveProgress = async (updatedStepData, newCurrentStep)=>{\n        try {\n            // Calculate completed steps based on actual step completion flags\n            const stepCompletionFlags = [\n                updatedStepData.countryCompleted || updatedStepData.country && updatedStepData.country !== '',\n                updatedStepData.agreementAccepted,\n                updatedStepData.profileCompleted,\n                updatedStepData.walletConnected,\n                updatedStepData.kycCompleted\n            ];\n            const actualCompletedSteps = stepCompletionFlags.filter(Boolean).length;\n            const progressData = {\n                ...updatedStepData,\n                tokenAddress,\n                currentStep: newCurrentStep,\n                completedSteps: actualCompletedSteps\n            };\n            console.log('💾 Saving progress to database:', progressData);\n            // Save to backend database via admin panel API\n            const response = await fetch('/api/client/qualification-progress', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(progressData)\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save progress');\n            }\n            const result = await response.json();\n            console.log('✅ Progress saved successfully to database:', result);\n        } catch (error) {\n            console.error('❌ Error saving progress:', error);\n        // Don't block the user flow if saving fails\n        }\n    };\n    // Step completion handlers\n    const handleCountryComplete = async (country)=>{\n        const updatedStepData = {\n            ...stepData,\n            country,\n            countryCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(1);\n        // Save progress\n        await saveProgress(updatedStepData, 1);\n    };\n    const handleAgreementComplete = async ()=>{\n        // First save the token agreement\n        try {\n            const response = await fetch('/api/client/token-agreement', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress,\n                    tokenSymbol,\n                    accepted: true\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to save agreement');\n            }\n            console.log('✅ Token agreement saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving token agreement:', error);\n        }\n        // Update step data and progress\n        const updatedStepData = {\n            ...stepData,\n            agreementAccepted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(2);\n        // Save progress\n        await saveProgress(updatedStepData, 2);\n    };\n    const handleProfileComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            profileCompleted: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(3);\n        // Save progress\n        await saveProgress(updatedStepData, 3);\n    };\n    const handleWalletComplete = async ()=>{\n        const updatedStepData = {\n            ...stepData,\n            walletConnected: true\n        };\n        setStepData(updatedStepData);\n        setCurrentStep(4);\n        // Save progress\n        await saveProgress(updatedStepData, 4);\n    };\n    const handleKYCStatusChange = async (status, error)=>{\n        setKycStatus(status);\n        if (error) {\n            setKycError(error);\n        } else {\n            setKycError(null);\n        }\n        if (status === 'completed') {\n            const updatedStepData = {\n                ...stepData,\n                kycCompleted: true\n            };\n            setStepData(updatedStepData);\n            setCurrentStep(5);\n            // Save progress\n            await saveProgress(updatedStepData, 5);\n        }\n    };\n    // Step navigation functions\n    const canNavigateToStep = (stepIndex)=>{\n        // Users can always navigate to completed steps or the next incomplete step\n        if (stepIndex === 0) return true; // Country selection always available\n        if (stepIndex === 1) return stepData.country !== ''; // Agreement if country selected\n        if (stepIndex === 2) return stepData.agreementAccepted; // Profile if agreement accepted\n        if (stepIndex === 3) return stepData.profileCompleted; // Wallet if profile completed\n        if (stepIndex === 4) return stepData.walletConnected; // KYC if wallet connected\n        if (stepIndex === 5) return stepData.kycCompleted; // Completion if KYC done\n        return false;\n    };\n    const handleStepClick = (stepIndex)=>{\n        if (canNavigateToStep(stepIndex)) {\n            setCurrentStep(stepIndex);\n            // Save the current step navigation\n            saveProgress(stepData, stepIndex);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 331,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n            lineNumber: 330,\n            columnNumber: 7\n        }, this);\n    }\n    const completedSteps = steps.filter((step)=>step.status === 'completed').length;\n    const totalSteps = steps.length;\n    const progressPercentage = completedSteps / totalSteps * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: tokenName ? \"\".concat(tokenName, \" Qualification\") : 'Token Qualification'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 mb-6\",\n                        children: [\n                            \"Complete the following steps to qualify for \",\n                            tokenName || 'token',\n                            \" investment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(progressPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            completedSteps,\n                            \" of \",\n                            totalSteps,\n                            \" steps completed\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleStepClick(index),\n                        className: \"p-4 rounded-lg border text-center transition-all duration-200 \".concat(getStepColor(step.status), \" \").concat(canNavigateToStep(index) ? 'cursor-pointer hover:shadow-md hover:scale-105' : 'cursor-not-allowed opacity-60'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-2\",\n                                children: getStepIcon(step.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold mb-1\",\n                                children: step.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: step.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            canNavigateToStep(index) && index !== currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 font-medium\",\n                                children: \"Click to navigate\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountrySelection__WEBPACK_IMPORTED_MODULE_2__.CountrySelection, {\n                        onComplete: handleCountryComplete,\n                        selectedCountry: stepData.country,\n                        isCompleted: stepData.country !== ''\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAgreement__WEBPACK_IMPORTED_MODULE_3__.TokenAgreement, {\n                        onComplete: handleAgreementComplete,\n                        tokenName: tokenName,\n                        tokenSymbol: tokenSymbol,\n                        isCompleted: stepData.agreementAccepted\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Main Information\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Please provide your complete personal and financial information.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QualificationForm__WEBPACK_IMPORTED_MODULE_4__.QualificationForm, {\n                                onComplete: handleProfileComplete,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"Wallet Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Connect your cryptocurrency wallet using Reown (WalletConnect).\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnection__WEBPACK_IMPORTED_MODULE_5__.WalletConnection, {\n                                onWalletConnected: handleWalletComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: \"KYC Verification\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Complete your identity verification using Sumsub.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomaticKYC__WEBPACK_IMPORTED_MODULE_6__.AutomaticKYC, {\n                                onStatusChange: handleKYCStatusChange,\n                                existingProfile: profile\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Qualification Submitted!\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: [\n                                    \"You have successfully completed all qualification steps for \",\n                                    tokenName || 'this token',\n                                    \". Your application is now pending admin review and approval.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-yellow-800\",\n                                                    children: \"Pending Admin Approval\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-yellow-700\",\n                                                    children: \"An administrator will review your qualification and approve you for token investment.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/',\n                                className: \"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Return to Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\QualificationFlow.tsx\",\n        lineNumber: 341,\n        columnNumber: 5\n    }, this);\n}\n_s(QualificationFlow, \"pBQqYfVaJc8Pfcz8udfxQdEIGfk=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = QualificationFlow;\nvar _c;\n$RefreshReg$(_c, \"QualificationFlow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/QualificationFlow.tsx\n"));

/***/ })

});