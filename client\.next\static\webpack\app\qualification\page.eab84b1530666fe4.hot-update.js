"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/qualification/page",{

/***/ "(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx":
/*!*********************************************************!*\
  !*** ./src/components/qualification/TokenAgreement.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenAgreement: () => (/* binding */ TokenAgreement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* __next_internal_client_entry_do_not_use__ TokenAgreement auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TokenAgreement(param) {\n    let { onComplete, tokenName = 'Security Token', tokenSymbol = 'TOKEN', isCompleted, agreementText } = param;\n    _s();\n    const [checkboxChecked, setCheckboxChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAcceptAgreement = async ()=>{\n        if (!checkboxChecked) {\n            alert('Please check the agreement checkbox before confirming.');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log('📄 Accepting token agreement for:', tokenName, tokenSymbol);\n            // Call the completion handler (which will save the agreement)\n            await onComplete();\n            console.log('✅ Token agreement accepted successfully');\n        } catch (error) {\n            console.error('❌ Error accepting agreement:', error);\n            alert('Failed to accept agreement. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // If already completed, show completion status\n    if (isCompleted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-green-50 border border-green-200 rounded-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-6 w-6 text-green-600 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800\",\n                                children: \"Agreement Accepted\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-700\",\n                                children: [\n                                    \"You have successfully accepted the \",\n                                    tokenName,\n                                    \" (\",\n                                    tokenSymbol,\n                                    \") investment agreement.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-12 w-12 text-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: [\n                            tokenName,\n                            \" Investment Agreement\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Please review and accept the specific terms for investing in \",\n                            tokenName,\n                            \" (\",\n                            tokenSymbol,\n                            \").\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-8 w-8 text-blue-600 mt-1 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: [\n                                        tokenName,\n                                        \" (\",\n                                        tokenSymbol,\n                                        \") Investment Terms\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                                    children: agreementText ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-700 whitespace-pre-wrap\",\n                                        children: agreementText\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-700 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: [\n                                                        \"INVESTMENT AGREEMENT FOR \",\n                                                        tokenName.toUpperCase(),\n                                                        \" (\",\n                                                        tokenSymbol,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 22\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"This agreement governs your investment in \",\n                                                    tokenName,\n                                                    \" security tokens. By proceeding, you acknowledge and agree to the following terms:\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"1. Token Details:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 24\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside ml-4 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"Token Name: \",\n                                                                    tokenName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"Token Symbol: \",\n                                                                    tokenSymbol\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Token Type: Security Token\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"2. Investment Terms:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 24\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside ml-4 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"This is a security token offering subject to applicable securities laws\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Tokens are restricted securities and may not be freely transferable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Investment involves significant risk and may result in total loss\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"No guarantee of returns or liquidity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"3. Compliance Requirements:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 24\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside ml-4 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"You must be an accredited investor or qualified purchaser\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"You must complete KYC/AML verification\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"You must comply with transfer restrictions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-4\",\n                                                children: \"This is a simplified version. The complete agreement will be provided upon investment confirmation.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Agreement Acceptance\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" This agreement is specific to \",\n                                        tokenName,\n                                        \" (\",\n                                        tokenSymbol,\n                                        \"). By accepting, you agree to the terms and conditions for this particular token investment.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"token-agreement-checkbox\",\n                                        checked: checkboxChecked,\n                                        onChange: (e)=>setCheckboxChecked(e.target.checked),\n                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1\",\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"token-agreement-checkbox\",\n                                        className: \"text-sm text-gray-700 leading-relaxed\",\n                                        children: [\n                                            \"I have read, understood, and agree to the \",\n                                            tokenName,\n                                            \" (\",\n                                            tokenSymbol,\n                                            \") investment agreement terms and conditions. I acknowledge that this agreement is legally binding and governs my investment in this specific security token.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAcceptAgreement,\n                                        disabled: !checkboxChecked || isSubmitting,\n                                        className: \"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors \".concat(checkboxChecked && !isSubmitting ? 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500' : 'bg-gray-300 cursor-not-allowed'),\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Processing Agreement...\"\n                                            ]\n                                        }, void 0, true) : \"Accept \".concat(tokenName, \" Agreement\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    !checkboxChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-xs text-gray-500 text-center\",\n                                        children: \"Please check the agreement checkbox above to enable confirmation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\components\\\\qualification\\\\TokenAgreement.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenAgreement, \"ywFypMe+D57CnltBiKE/BAqbYTY=\");\n_c = TokenAgreement;\nvar _c;\n$RefreshReg$(_c, \"TokenAgreement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/qualification/TokenAgreement.tsx\n"));

/***/ })

});