const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 Whitelisting Wallet for Claim_002 Token...");
  console.log("=" .repeat(60));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Using account:", deployer.address);

  try {
    const tokenAddress = "******************************************"; // Claim_002
    const walletToWhitelist = "******************************************";

    console.log("\n1️⃣ Getting Token's IdentityRegistry...");
    
    // Connect to the token to get its identity registry
    const tokenABI = ['function identityRegistry() external view returns (address)'];
    const token = new ethers.Contract(tokenAddress, tokenABI, deployer);
    
    const identityRegistryAddress = await token.identityRegistry();
    console.log("   Token Address:", tokenAddress);
    console.log("   Identity Registry:", identityRegistryAddress);

    console.log("\n2️⃣ Connecting to IdentityRegistry...");
    
    // Connect to identity registry
    const identityABI = [
      'function isVerified(address account) external view returns (bool)',
      'function isWhitelisted(address account) external view returns (bool)',
      'function isFrozen(address account) external view returns (bool)',
      'function registerIdentity(address userAddress, uint16 country) external',
      'function addToWhitelist(address account) external'
    ];
    
    const identityRegistry = new ethers.Contract(identityRegistryAddress, identityABI, deployer);

    console.log("\n3️⃣ Checking Current Status...");
    
    try {
      // Check current status
      const isVerified = await identityRegistry.isVerified(walletToWhitelist);
      const isWhitelisted = await identityRegistry.isWhitelisted(walletToWhitelist);
      const isFrozen = await identityRegistry.isFrozen(walletToWhitelist);
      
      console.log("   Current Status for:", walletToWhitelist);
      console.log("   Verified:", isVerified);
      console.log("   Whitelisted:", isWhitelisted);
      console.log("   Frozen:", isFrozen);
      
      // Register identity if not verified
      if (!isVerified) {
        console.log("\n4️⃣ Registering Identity...");
        const registerTx = await identityRegistry.registerIdentity(walletToWhitelist, 840, {
          gasLimit: 500000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        });
        console.log("   Register transaction sent:", registerTx.hash);
        await registerTx.wait();
        console.log("   ✅ Identity registered successfully!");
      } else {
        console.log("\n4️⃣ Identity already registered ✅");
      }
      
      // Add to whitelist if not whitelisted
      if (!isWhitelisted) {
        console.log("\n5️⃣ Adding to Whitelist...");
        const whitelistTx = await identityRegistry.addToWhitelist(walletToWhitelist, {
          gasLimit: 300000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        });
        console.log("   Whitelist transaction sent:", whitelistTx.hash);
        await whitelistTx.wait();
        console.log("   ✅ Added to whitelist successfully!");
      } else {
        console.log("\n5️⃣ Already whitelisted ✅");
      }
      
      // Final verification
      console.log("\n6️⃣ Final Verification...");
      const finalVerified = await identityRegistry.isVerified(walletToWhitelist);
      const finalWhitelisted = await identityRegistry.isWhitelisted(walletToWhitelist);
      const finalFrozen = await identityRegistry.isFrozen(walletToWhitelist);
      
      console.log("   Final Status:");
      console.log("   Verified:", finalVerified);
      console.log("   Whitelisted:", finalWhitelisted);
      console.log("   Frozen:", finalFrozen);
      console.log("   Can Access Token:", finalVerified && finalWhitelisted && !finalFrozen);
      
    } catch (statusError) {
      console.log("   Error checking status:", statusError.message);
      
      // If status check fails, try to register first
      console.log("\n4️⃣ Attempting to Register Identity (status check failed)...");
      try {
        const registerTx = await identityRegistry.registerIdentity(walletToWhitelist, 840, {
          gasLimit: 500000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        });
        console.log("   Register transaction sent:", registerTx.hash);
        await registerTx.wait();
        console.log("   ✅ Identity registered successfully!");
        
        console.log("\n5️⃣ Adding to Whitelist...");
        const whitelistTx = await identityRegistry.addToWhitelist(walletToWhitelist, {
          gasLimit: 300000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei'),
          maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
        });
        console.log("   Whitelist transaction sent:", whitelistTx.hash);
        await whitelistTx.wait();
        console.log("   ✅ Added to whitelist successfully!");
        
      } catch (registerError) {
        console.log("   Error during registration:", registerError.message);
      }
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 WHITELIST PROCESS COMPLETE!");
    console.log("=" .repeat(60));
    console.log("📋 Summary:");
    console.log("   Token: Claim_002");
    console.log("   Token Address:", tokenAddress);
    console.log("   Identity Registry:", identityRegistryAddress);
    console.log("   Wallet:", walletToWhitelist);
    console.log("\n🔧 Next Steps:");
    console.log("   1. Try accessing the token again");
    console.log("   2. The investment should now work");
    console.log("   3. Check the admin panel for confirmation");

  } catch (error) {
    console.error("❌ Failed to whitelist wallet:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
