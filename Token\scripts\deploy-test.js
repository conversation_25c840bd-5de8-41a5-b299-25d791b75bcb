// Simple test script for deploying tokens
const { ethers } = require("hardhat");

async function main() {
  console.log("Starting deployment test...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log(`Deployer: ${deployer.address}`);
  
  // Step 1: Deploy the SecurityTokenFactory
  console.log("\nStep 1: Deploying SecurityTokenFactory...");
  const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
  const factory = await SecurityTokenFactory.deploy(deployer.address);
  await factory.waitForDeployment();
  
  const factoryAddress = await factory.getAddress();
  console.log(`SecurityTokenFactory deployed to: ${factoryAddress}`);
  console.log(`SecurityToken implementation: ${await factory.securityTokenImplementation()}`);
  console.log(`Whitelist implementation: ${await factory.whitelistImplementation()}`);
  
  // Step 2: Deploy a SecurityToken
  console.log("\nStep 2: Deploying a SecurityToken...");
  
  const tokenName = "Test Security Token";
  const tokenSymbol = "TST";
  const maxSupply = ethers.parseEther("1000000");
  const tokenPrice = "10 USD";
  const bonusTiers = "Tier 1: 5%, Tier 2: 10%";
  
  // Deploy token using the factory
  const deployTx = await factory.deploySecurityToken(
    tokenName,
    tokenSymbol,
    maxSupply,
    deployer.address,
    tokenPrice,
    bonusTiers
  );
  
  console.log(`Transaction hash: ${deployTx.hash}`);
  
  // Wait for the transaction to be mined
  const receipt = await deployTx.wait();
  console.log(`Transaction was mined in block: ${receipt.blockNumber}`);
  
  // Try to get the token address from the event
  console.log("Looking for event in transaction logs...");
  console.log(`Receipt has ${receipt.logs.length} logs`);
  
  for (let i = 0; i < receipt.logs.length; i++) {
    const log = receipt.logs[i];
    console.log(`Log ${i}: ${log.topics ? log.topics.length : 0} topics`);
    
    if (log.fragment && log.fragment.name) {
      console.log(`  Event name: ${log.fragment.name}`);
    }
  }
  
  // Check if token was deployed using getTokenAddressBySymbol
  console.log("\nChecking if token was deployed by using getTokenAddressBySymbol...");
  const tokenAddress = await factory.getTokenAddressBySymbol(tokenSymbol);
  console.log(`Token address for symbol ${tokenSymbol}: ${tokenAddress}`);
  
  if (tokenAddress && tokenAddress !== ethers.ZeroAddress) {
    console.log("Token was successfully deployed!");
    
    // Connect to the deployed token
    const token = await ethers.getContractAt("SecurityToken", tokenAddress);
    console.log(`\nToken Information:`);
    console.log(`Name: ${await token.name()}`);
    console.log(`Symbol: ${await token.symbol()}`);
    console.log(`Max Supply: ${ethers.formatEther(await token.maxSupply())} tokens`);
    console.log(`Whitelist Address: ${await token.whitelistAddress()}`);
    
    // Connect to the whitelist
    const whitelistAddress = await token.whitelistAddress();
    const whitelist = await ethers.getContractAt("Whitelist", whitelistAddress);
    
    // Step 3: Test whitelist functionality
    console.log("\nStep 3: Testing whitelist functionality...");
    
    // Add deployer to whitelist
    console.log("Adding deployer to whitelist...");
    await whitelist.addToWhitelist(deployer.address);
    console.log(`Is deployer whitelisted: ${await whitelist.isWhitelisted(deployer.address)}`);
    
    // Step 4: Test token operations
    console.log("\nStep 4: Testing token operations...");
    
    // Mint tokens
    console.log("Minting 1000 tokens to deployer...");
    await token.mint(deployer.address, ethers.parseEther("1000"));
    console.log(`Deployer balance: ${ethers.formatEther(await token.balanceOf(deployer.address))} tokens`);
    
    console.log("\nDeployment test completed successfully!");
  } else {
    console.error("Failed to deploy token.");
  }
}

// Execute the script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });