const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("SecurityToken", function () {
  let SecurityToken;
  let Whitelist;
  let securityToken;
  let whitelist;
  let owner;
  let agent;
  let user1;
  let user2;
  let nonWhitelisted;

  beforeEach(async function () {
    // Get signers
    [owner, agent, user1, user2, nonWhitelisted] = await ethers.getSigners();

    // Deploy whitelist
    Whitelist = await ethers.getContractFactory("Whitelist");
    whitelist = await upgrades.deployProxy(Whitelist, [owner.address], { initializer: "initialize" });
    await whitelist.deployed();

    // Grant agent role to agent
    await whitelist.addAgent(agent.address);

    // Deploy security token
    SecurityToken = await ethers.getContractFactory("SecurityToken");
    securityToken = await upgrades.deployProxy(
      SecurityToken,
      [
        "Security Token",
        "SEC",
        1000000,
        whitelist.address,
        owner.address,
        "10 USD",
        "Tier 1: 5%, Tier 2: 10%",
        "Security token for testing",
      ],
      { initializer: "initialize" }
    );
    await securityToken.deployed();

    // Setup whitelist
    await whitelist.connect(agent).addToWhitelist(user1.address);
    await whitelist.connect(agent).addToWhitelist(user2.address);
    await whitelist.connect(agent).approveKyc(user1.address);
    await whitelist.connect(agent).approveKyc(user2.address);
  });

  describe("Deployment", function () {
    it("Should set the right owner", async function () {
      expect(await securityToken.hasRole(await securityToken.DEFAULT_ADMIN_ROLE(), owner.address)).to.equal(true);
    });

    it("Should set the right name and symbol", async function () {
      expect(await securityToken.name()).to.equal("Security Token");
      expect(await securityToken.symbol()).to.equal("SEC");
    });

    it("Should set the right max supply", async function () {
      expect(await securityToken.maxSupply()).to.equal(1000000);
    });

    it("Should set the right identity registry", async function () {
      expect(await securityToken.identityRegistry()).to.equal(whitelist.address);
    });

    it("Should set the right token metadata", async function () {
      expect(await securityToken.tokenPrice()).to.equal("10 USD");
      expect(await securityToken.bonusTiers()).to.equal("Tier 1: 5%, Tier 2: 10%");
      expect(await securityToken.tokenDetails()).to.equal("Security token for testing");
    });
  });

  describe("Minting", function () {
    it("Should allow agent to mint tokens", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).mint(user1.address, 100);
      expect(await securityToken.balanceOf(user1.address)).to.equal(100);
    });

    it("Should not allow non-agent to mint tokens", async function () {
      await expect(securityToken.connect(user1).mint(user1.address, 100)).to.be.reverted;
    });

    it("Should not allow minting to non-whitelisted addresses", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await expect(securityToken.connect(agent).mint(nonWhitelisted.address, 100)).to.be.revertedWith("SecurityToken: recipient not whitelisted");
    });

    it("Should not allow minting to frozen addresses", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await whitelist.connect(agent).freezeAddress(user1.address);
      await expect(securityToken.connect(agent).mint(user1.address, 100)).to.be.revertedWith("SecurityToken: recipient frozen");
    });

    it("Should not allow minting more than max supply", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await expect(securityToken.connect(agent).mint(user1.address, 1000001)).to.be.revertedWith("SecurityToken: exceeds max supply");
    });
  });

  describe("Transfers", function () {
    beforeEach(async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).mint(user1.address, 1000);
    });

    it("Should allow transfers between whitelisted addresses", async function () {
      await securityToken.connect(user1).transfer(user2.address, 100);
      expect(await securityToken.balanceOf(user1.address)).to.equal(900);
      expect(await securityToken.balanceOf(user2.address)).to.equal(100);
    });

    it("Should not allow transfers to non-whitelisted addresses", async function () {
      await expect(securityToken.connect(user1).transfer(nonWhitelisted.address, 100)).to.be.revertedWith("SecurityToken: recipient not whitelisted");
    });

    it("Should not allow transfers from frozen addresses", async function () {
      await whitelist.connect(agent).freezeAddress(user1.address);
      await expect(securityToken.connect(user1).transfer(user2.address, 100)).to.be.revertedWith("SecurityToken: sender frozen");
    });

    it("Should not allow transfers to frozen addresses", async function () {
      await whitelist.connect(agent).freezeAddress(user2.address);
      await expect(securityToken.connect(user1).transfer(user2.address, 100)).to.be.revertedWith("SecurityToken: recipient frozen");
    });

    it("Should not allow transfers when paused", async function () {
      await securityToken.connect(owner).pause();
      await expect(securityToken.connect(user1).transfer(user2.address, 100)).to.be.revertedWith("Pausable: paused");
    });

    it("Should check canTransfer function correctly", async function () {
      expect(await securityToken.canTransfer(user1.address, user2.address, 100)).to.equal(true);
      expect(await securityToken.canTransfer(user1.address, nonWhitelisted.address, 100)).to.equal(false);
      await whitelist.connect(agent).freezeAddress(user1.address);
      expect(await securityToken.canTransfer(user1.address, user2.address, 100)).to.equal(false);
    });
  });

  describe("Forced Transfers", function () {
    beforeEach(async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).mint(user1.address, 1000);
    });

    it("Should allow transfer manager to force transfer from frozen address", async function () {
      await whitelist.connect(agent).freezeAddress(user1.address);
      await securityToken.connect(owner).grantRole(await securityToken.TRANSFER_MANAGER_ROLE(), agent.address);
      await securityToken.connect(agent).forcedTransfer(user1.address, user2.address, 100);
      expect(await securityToken.balanceOf(user1.address)).to.equal(900);
      expect(await securityToken.balanceOf(user2.address)).to.equal(100);
    });

    it("Should not allow non-transfer manager to force transfer", async function () {
      await expect(securityToken.connect(user1).forcedTransfer(user1.address, user2.address, 100)).to.be.reverted;
    });

    it("Should not allow force transfer to non-whitelisted addresses", async function () {
      await securityToken.connect(owner).grantRole(await securityToken.TRANSFER_MANAGER_ROLE(), agent.address);
      await expect(securityToken.connect(agent).forcedTransfer(user1.address, nonWhitelisted.address, 100)).to.be.revertedWith("SecurityToken: recipient not whitelisted");
    });
  });

  describe("Whitelist Management", function () {
    it("Should allow agent to add to whitelist through token", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).addToWhitelist(nonWhitelisted.address);
      expect(await whitelist.isWhitelisted(nonWhitelisted.address)).to.equal(true);
    });

    it("Should allow agent to remove from whitelist through token", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).removeFromWhitelist(user1.address);
      expect(await whitelist.isWhitelisted(user1.address)).to.equal(false);
    });

    it("Should allow agent to freeze address through token", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).freezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(true);
    });

    it("Should allow agent to unfreeze address through token", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).freezeAddress(user1.address);
      await securityToken.connect(agent).unfreezeAddress(user1.address);
      expect(await whitelist.isFrozen(user1.address)).to.equal(false);
    });
  });

  describe("Token Metadata", function () {
    it("Should allow admin to update token metadata", async function () {
      await securityToken.connect(owner).updateTokenMetadata("20 USD", "New tiers", "Updated details");
      expect(await securityToken.tokenPrice()).to.equal("20 USD");
      expect(await securityToken.bonusTiers()).to.equal("New tiers");
      expect(await securityToken.tokenDetails()).to.equal("Updated details");
    });

    it("Should not allow non-admin to update token metadata", async function () {
      await expect(securityToken.connect(user1).updateTokenMetadata("20 USD", "New tiers", "Updated details")).to.be.reverted;
    });
  });

  describe("Max Supply", function () {
    it("Should allow admin to update max supply", async function () {
      await securityToken.connect(owner).updateMaxSupply(2000000);
      expect(await securityToken.maxSupply()).to.equal(2000000);
    });

    it("Should not allow non-admin to update max supply", async function () {
      await expect(securityToken.connect(user1).updateMaxSupply(2000000)).to.be.reverted;
    });

    it("Should not allow updating max supply below current total supply", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(agent).mint(user1.address, 500000);
      await expect(securityToken.connect(owner).updateMaxSupply(400000)).to.be.revertedWith("SecurityToken: new max supply below current total supply");
    });
  });

  describe("Identity Registry", function () {
    it("Should allow admin to update identity registry", async function () {
      const NewWhitelist = await ethers.getContractFactory("Whitelist");
      const newWhitelist = await upgrades.deployProxy(NewWhitelist, [owner.address], { initializer: "initialize" });
      await newWhitelist.deployed();

      await securityToken.connect(owner).updateIdentityRegistry(newWhitelist.address);
      expect(await securityToken.identityRegistry()).to.equal(newWhitelist.address);
    });

    it("Should not allow non-admin to update identity registry", async function () {
      const NewWhitelist = await ethers.getContractFactory("Whitelist");
      const newWhitelist = await upgrades.deployProxy(NewWhitelist, [owner.address], { initializer: "initialize" });
      await newWhitelist.deployed();

      await expect(securityToken.connect(user1).updateIdentityRegistry(newWhitelist.address)).to.be.reverted;
    });
  });

  describe("Agent Management", function () {
    it("Should allow admin to add an agent", async function () {
      await securityToken.connect(owner).addAgent(user1.address);
      expect(await securityToken.isAgent(user1.address)).to.equal(true);
      expect(await securityToken.getAgentCount()).to.equal(2); // owner + user1
    });

    it("Should allow admin to remove an agent", async function () {
      await securityToken.connect(owner).addAgent(agent.address);
      await securityToken.connect(owner).removeAgent(agent.address);
      expect(await securityToken.isAgent(agent.address)).to.equal(false);
      expect(await securityToken.getAgentCount()).to.equal(1); // only owner
    });

    it("Should not allow non-admin to add or remove agents", async function () {
      await expect(securityToken.connect(user1).addAgent(user2.address)).to.be.reverted;
      await expect(securityToken.connect(user1).removeAgent(owner.address)).to.be.reverted;
    });

    it("Should correctly enumerate agents", async function () {
      await securityToken.connect(owner).addAgent(user1.address);
      await securityToken.connect(owner).addAgent(user2.address);
      
      expect(await securityToken.getAgentCount()).to.equal(3);
      expect(await securityToken.getAllAgents()).to.have.lengthOf(3);
      
      // Check that we can get agents by index
      const agent0 = await securityToken.getAgentAt(0);
      const agent1 = await securityToken.getAgentAt(1);
      const agent2 = await securityToken.getAgentAt(2);
      
      // Verify the three agents are owner, user1, and user2 (order may vary)
      const agents = [agent0, agent1, agent2];
      expect(agents).to.include.members([owner.address, user1.address, user2.address]);
    });
  });
});