const { ethers } = require('ethers');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function testMintingSetup() {
  console.log('🧪 Testing Minting Setup');
  console.log('========================');

  try {
    // 1. Check environment variables
    console.log('\n1️⃣ Checking environment variables...');

    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    console.log(`   RPC URL: ${rpcUrl ? '✅ Set' : '❌ Missing'}`);
    console.log(`   Private Key: ${privateKey ? '✅ Set' : '❌ Missing'}`);

    if (!rpcUrl || !privateKey) {
      console.log('\n❌ Missing required environment variables');
      return;
    }

    // 2. Test blockchain connection
    console.log('\n2️⃣ Testing blockchain connection...');

    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const wallet = new ethers.Wallet(privateKey, provider);

    console.log(`   Admin wallet: ${wallet.address}`);

    const network = await provider.getNetwork();
    console.log(`   Network: ${network.name} (Chain ID: ${network.chainId})`);

    const balance = await provider.getBalance(wallet.address);
    console.log(`   Balance: ${ethers.formatEther(balance)} ETH`);

    if (parseFloat(ethers.formatEther(balance)) < 0.01) {
      console.log('   ⚠️  Low balance - may not be enough for gas fees');
    }

    // 3. Test token contract access
    console.log('\n3️⃣ Testing token contract access...');

    // Use the Order_01 token address from your system
    const TOKEN_ADDRESS = '******************************************';

    try {
      const tokenABI = require('../Token/artifacts/contracts/SecurityToken.sol/SecurityToken.json');
      const tokenContract = new ethers.Contract(TOKEN_ADDRESS, tokenABI.abi, wallet);

      const name = await tokenContract.name();
      const symbol = await tokenContract.symbol();
      const decimals = await tokenContract.decimals();
      const totalSupply = await tokenContract.totalSupply();

      console.log(`   Token: ${name} (${symbol})`);
      console.log(`   Decimals: ${decimals}`);
      console.log(`   Total Supply: ${ethers.formatUnits(totalSupply, decimals)}`);

      // Check minting permissions
      try {
        const AGENT_ROLE = await tokenContract.AGENT_ROLE();
        const hasAgentRole = await tokenContract.hasRole(AGENT_ROLE, wallet.address);
        console.log(`   Admin has AGENT_ROLE: ${hasAgentRole ? '✅ YES' : '❌ NO'}`);

        const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();
        const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);
        console.log(`   Admin has DEFAULT_ADMIN_ROLE: ${hasAdminRole ? '✅ YES' : '❌ NO'}`);

        if (!hasAgentRole && !hasAdminRole) {
          console.log('   💡 Admin needs AGENT_ROLE or DEFAULT_ADMIN_ROLE to mint tokens');
          console.log('   💡 Grant role with: tokenContract.grantRole(AGENT_ROLE, adminAddress)');
        }
      } catch (error) {
        console.log('   ⚠️  Could not check roles:', error.message);
      }

    } catch (error) {
      console.log(`   ❌ Token contract error: ${error.message}`);
    }

    // 4. Test database connection
    console.log('\n4️⃣ Testing database connection...');

    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      // Get a sample order
      const orders = await prisma.order.findMany({
        take: 1,
        include: {
          client: true,
          token: true
        }
      });

      if (orders.length > 0) {
        const order = orders[0];
        console.log(`   ✅ Database connected`);
        console.log(`   Sample order: ${order.token.name} for ${order.client.firstName} ${order.client.lastName}`);
        console.log(`   Order status: ${order.status}`);
        console.log(`   Client wallet: ${order.client.walletAddress || 'Not set'}`);
      } else {
        console.log(`   ✅ Database connected (no orders found)`);
      }

      await prisma.$disconnect();
    } catch (error) {
      console.log(`   ❌ Database error: ${error.message}`);
    }

    console.log('\n✅ Minting setup test completed!');
    console.log('\n💡 Next steps:');
    console.log('1. Make sure admin wallet has MINTER_ROLE on token contracts');
    console.log('2. Ensure admin wallet has sufficient ETH for gas fees');
    console.log('3. Verify clients have wallet addresses registered');
    console.log('4. Test minting from the admin panel orders page');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testMintingSetup().catch(console.error);
