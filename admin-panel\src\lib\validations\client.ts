import { z } from 'zod';

// Enums
export const GenderEnum = z.enum(['MALE', 'FEMALE', 'OTHER', 'PREFER_NOT_TO_SAY']);
export const IdentificationTypeEnum = z.enum(['PASSPORT', 'ID_CARD', 'DRIVERS_LICENSE', 'OTHER']);
export const PEPStatusEnum = z.enum(['NOT_PEP', 'DOMESTIC_PEP', 'FOREIGN_PEP', 'INTERNATIONAL_ORG_PEP', 'FAMILY_MEMBER', 'CLOSE_ASSOCIATE']);
export const KYCStatusEnum = z.enum(['PENDING', 'IN_REVIEW', 'APPROVED', 'REJECTED', 'EXPIRED']);

// Client creation schema
export const createClientSchema = z.object({
  // Personal Information
  firstName: z.string().min(1, 'First name is required').max(100),
  lastName: z.string().min(1, 'Last name is required').max(100),
  gender: GenderEnum,
  nationality: z.string().min(1, 'Nationality is required').max(100),
  birthday: z.string().refine((date) => {
    const parsedDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - parsedDate.getFullYear();
    return age >= 18 && age <= 120;
  }, 'Must be between 18 and 120 years old'),
  birthPlace: z.string().min(1, 'Birth place is required').max(200),

  // Identification
  identificationType: IdentificationTypeEnum,
  passportNumber: z.string().optional(),
  idCardNumber: z.string().optional(),
  documentExpiration: z.string().refine((date) => {
    const parsedDate = new Date(date);
    const today = new Date();
    return parsedDate > today;
  }, 'Document expiration must be in the future'),

  // Contact Information
  phoneNumber: z.string().min(1, 'Phone number is required').regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format'),
  email: z.string().email('Invalid email format').optional(),

  // Professional Information
  occupation: z.string().min(1, 'Occupation is required').max(200),
  sectorOfActivity: z.string().min(1, 'Sector of activity is required').max(200),
  pepStatus: PEPStatusEnum,
  pepDetails: z.string().max(1000).optional(),

  // Address Information
  street: z.string().min(1, 'Street is required').max(200),
  buildingNumber: z.string().min(1, 'Building number is required').max(50),
  city: z.string().min(1, 'City is required').max(100),
  state: z.string().max(100).optional(),
  country: z.string().min(1, 'Country is required').max(100),
  zipCode: z.string().min(1, 'Zip code is required').max(20),

  // Financial Information
  sourceOfWealth: z.string().min(1, 'Source of wealth is required').max(500),
  bankAccountNumber: z.string().min(1, 'Bank account number is required').max(50),
  sourceOfFunds: z.string().min(1, 'Source of funds is required').max(500),
  taxIdentificationNumber: z.string().min(1, 'Tax identification number is required').max(50),
}).refine((data) => {
  // Ensure at least one identification document is provided
  if (data.identificationType === 'PASSPORT' && !data.passportNumber) {
    return false;
  }
  if ((data.identificationType === 'ID_CARD' || data.identificationType === 'DRIVERS_LICENSE') && !data.idCardNumber) {
    return false;
  }
  return true;
}, {
  message: 'Identification document number is required based on the selected type',
  path: ['passportNumber', 'idCardNumber']
});

// Client update schema (all fields optional except id)
export const updateClientSchema = z.object({
  id: z.string().cuid(),
  // Personal Information (all optional for updates)
  firstName: z.string().min(1).max(100).optional(),
  lastName: z.string().min(1).max(100).optional(),
  gender: GenderEnum.optional(),
  nationality: z.string().min(1).max(100).optional(),
  birthday: z.string().optional(),
  birthPlace: z.string().min(1).max(200).optional(),

  // Identification (optional for updates)
  identificationType: IdentificationTypeEnum.optional(),
  passportNumber: z.string().optional(),
  idCardNumber: z.string().optional(),
  documentExpiration: z.string().optional(),

  // Contact Information (optional for updates)
  phoneNumber: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format').optional(),
  email: z.string().email('Invalid email format').optional(),

  // Professional Information (optional for updates)
  occupation: z.string().min(1).max(200).optional(),
  sectorOfActivity: z.string().min(1).max(200).optional(),
  pepStatus: PEPStatusEnum.optional(),
  pepDetails: z.string().max(1000).optional(),

  // Address Information (optional for updates)
  street: z.string().min(1).max(200).optional(),
  buildingNumber: z.string().min(1).max(50).optional(),
  city: z.string().min(1).max(100).optional(),
  state: z.string().max(100).optional(),
  country: z.string().min(1).max(100).optional(),
  zipCode: z.string().min(1).max(20).optional(),

  // Financial Information (optional for updates)
  sourceOfWealth: z.string().min(1).max(500).optional(),
  bankAccountNumber: z.string().min(1).max(50).optional(),
  sourceOfFunds: z.string().min(1).max(500).optional(),
  taxIdentificationNumber: z.string().min(1).max(50).optional(),

  // KYC and Blockchain fields
  kycStatus: KYCStatusEnum.optional(),
  kycNotes: z.string().max(1000).optional(),
  walletAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address').optional(),
  isWhitelisted: z.boolean().optional(),

  // Agreement fields
  agreementAccepted: z.boolean().optional(),
  agreementAcceptedAt: z.string().optional(),
});

// Client query schema
export const clientQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('10'),
  search: z.string().optional(),
  kycStatus: KYCStatusEnum.optional(),
  isWhitelisted: z.boolean().optional(),
  sortBy: z.enum(['createdAt', 'firstName', 'lastName', 'kycStatus']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// KYC update schema
export const updateKYCSchema = z.object({
  clientId: z.string().cuid(),
  kycStatus: KYCStatusEnum,
  kycNotes: z.string().max(1000).optional(),
});

// Whitelist update schema
export const updateWhitelistSchema = z.object({
  clientId: z.string().cuid(),
  walletAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address'),
  isWhitelisted: z.boolean(),
});

export type CreateClientInput = z.infer<typeof createClientSchema>;
export type UpdateClientInput = z.infer<typeof updateClientSchema>;
export type ClientQueryInput = z.infer<typeof clientQuerySchema>;
export type UpdateKYCInput = z.infer<typeof updateKYCSchema>;
export type UpdateWhitelistInput = z.infer<typeof updateWhitelistSchema>;
