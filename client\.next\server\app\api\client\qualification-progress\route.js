/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/qualification-progress/route";
exports.ids = ["app/api/client/qualification-progress/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/qualification-progress/route.ts */ \"(rsc)/./src/app/api/client/qualification-progress/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/qualification-progress/route\",\n        pathname: \"/api/client/qualification-progress\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/qualification-progress/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\qualification-progress\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/qualification-progress/route.ts":
/*!************************************************************!*\
  !*** ./src/app/api/client/qualification-progress/route.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)();\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const tokenAddress = searchParams.get('tokenAddress');\n        const userEmail = session.user.email;\n        // Get stored qualification progress from localStorage simulation\n        // In a real implementation, this would query the database\n        const storageKey = `qualification_progress_${userEmail}_${tokenAddress}`;\n        // For now, we'll use a simple in-memory storage simulation\n        // In production, this should query the actual database\n        const defaultProgress = {\n            country: '',\n            countryCompleted: false,\n            agreementAccepted: false,\n            profileCompleted: false,\n            walletConnected: false,\n            kycCompleted: false,\n            currentStep: 0,\n            completedSteps: 0,\n            tokenAddress: tokenAddress,\n            userEmail: userEmail,\n            lastUpdated: new Date().toISOString()\n        };\n        // Check if user has existing profile (affects profileCompleted status)\n        try {\n            const profileResponse = await fetch(`${process.env.ADMIN_API_URL || 'http://localhost:6677/api'}/clients?search=${encodeURIComponent(userEmail)}&limit=1`);\n            if (profileResponse.ok) {\n                const profileData = await profileResponse.json();\n                if (profileData.clients && profileData.clients.length > 0) {\n                    const client = profileData.clients[0];\n                    defaultProgress.profileCompleted = true;\n                    defaultProgress.walletConnected = !!client.walletAddress;\n                    defaultProgress.kycCompleted = client.kycStatus === 'APPROVED';\n                    // Update current step based on completion status\n                    if (defaultProgress.kycCompleted) {\n                        defaultProgress.currentStep = 5; // All completed\n                        defaultProgress.completedSteps = 5;\n                    } else if (defaultProgress.walletConnected) {\n                        defaultProgress.currentStep = 4; // On KYC step\n                        defaultProgress.completedSteps = 4;\n                    } else if (defaultProgress.profileCompleted) {\n                        defaultProgress.currentStep = 3; // On wallet step\n                        defaultProgress.completedSteps = 3;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error checking profile status:', error);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(defaultProgress);\n    } catch (error) {\n        console.error('Error fetching qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)();\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { country, countryCompleted, agreementAccepted, profileCompleted, walletConnected, kycCompleted, tokenAddress, currentStep, completedSteps } = body;\n        const userEmail = session.user.email;\n        const storageKey = `qualification_progress_${userEmail}_${tokenAddress}`;\n        // Create progress object\n        const progressData = {\n            country: country || '',\n            countryCompleted: countryCompleted || false,\n            agreementAccepted: agreementAccepted || false,\n            profileCompleted: profileCompleted || false,\n            walletConnected: walletConnected || false,\n            kycCompleted: kycCompleted || false,\n            currentStep: currentStep || 0,\n            completedSteps: completedSteps || 0,\n            tokenAddress: tokenAddress,\n            userEmail: userEmail,\n            lastUpdated: new Date().toISOString()\n        };\n        // In a real implementation, this would save to database\n        // For now, we'll log the progress and simulate saving\n        console.log('💾 Saving qualification progress:', progressData);\n        // TODO: Implement actual database save\n        // Example structure:\n        // INSERT INTO qualification_progress\n        // (user_email, token_address, country, country_completed, agreement_accepted,\n        //  profile_completed, wallet_connected, kyc_completed, current_step, completed_steps, updated_at)\n        // VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n        // ON DUPLICATE KEY UPDATE ...\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Qualification progress saved successfully',\n            data: progressData\n        });\n    } catch (error) {\n        console.error('Error saving qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jbGllbnQvcXVhbGlmaWNhdGlvbi1wcm9ncmVzcy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RDtBQUNQO0FBRTFDLGVBQWVFLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNQyxVQUFVLE1BQU1ILCtEQUFVQTtRQUNoQyxJQUFJLENBQUNHLFNBQVNDLE1BQU07WUFDbEIsT0FBT0wscURBQVlBLENBQUNNLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFlLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNwRTtRQUVBLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSVAsUUFBUVEsR0FBRztRQUM1QyxNQUFNQyxlQUFlSCxhQUFhSSxHQUFHLENBQUM7UUFDdEMsTUFBTUMsWUFBWVYsUUFBUUMsSUFBSSxDQUFDVSxLQUFLO1FBRXBDLGlFQUFpRTtRQUNqRSwwREFBMEQ7UUFDMUQsTUFBTUMsYUFBYSxDQUFDLHVCQUF1QixFQUFFRixVQUFVLENBQUMsRUFBRUYsY0FBYztRQUV4RSwyREFBMkQ7UUFDM0QsdURBQXVEO1FBQ3ZELE1BQU1LLGtCQUFrQjtZQUN0QkMsU0FBUztZQUNUQyxrQkFBa0I7WUFDbEJDLG1CQUFtQjtZQUNuQkMsa0JBQWtCO1lBQ2xCQyxpQkFBaUI7WUFDakJDLGNBQWM7WUFDZEMsYUFBYTtZQUNiQyxnQkFBZ0I7WUFDaEJiLGNBQWNBO1lBQ2RFLFdBQVdBO1lBQ1hZLGFBQWEsSUFBSUMsT0FBT0MsV0FBVztRQUNyQztRQUVBLHVFQUF1RTtRQUN2RSxJQUFJO1lBQ0YsTUFBTUMsa0JBQWtCLE1BQU1DLE1BQU0sR0FBR0MsUUFBUUMsR0FBRyxDQUFDQyxhQUFhLElBQUksNEJBQTRCLGdCQUFnQixFQUFFQyxtQkFBbUJwQixXQUFXLFFBQVEsQ0FBQztZQUN6SixJQUFJZSxnQkFBZ0JNLEVBQUUsRUFBRTtnQkFDdEIsTUFBTUMsY0FBYyxNQUFNUCxnQkFBZ0J2QixJQUFJO2dCQUM5QyxJQUFJOEIsWUFBWUMsT0FBTyxJQUFJRCxZQUFZQyxPQUFPLENBQUNDLE1BQU0sR0FBRyxHQUFHO29CQUN6RCxNQUFNQyxTQUFTSCxZQUFZQyxPQUFPLENBQUMsRUFBRTtvQkFDckNwQixnQkFBZ0JJLGdCQUFnQixHQUFHO29CQUNuQ0osZ0JBQWdCSyxlQUFlLEdBQUcsQ0FBQyxDQUFDaUIsT0FBT0MsYUFBYTtvQkFDeER2QixnQkFBZ0JNLFlBQVksR0FBR2dCLE9BQU9FLFNBQVMsS0FBSztvQkFFcEQsaURBQWlEO29CQUNqRCxJQUFJeEIsZ0JBQWdCTSxZQUFZLEVBQUU7d0JBQ2hDTixnQkFBZ0JPLFdBQVcsR0FBRyxHQUFHLGdCQUFnQjt3QkFDakRQLGdCQUFnQlEsY0FBYyxHQUFHO29CQUNuQyxPQUFPLElBQUlSLGdCQUFnQkssZUFBZSxFQUFFO3dCQUMxQ0wsZ0JBQWdCTyxXQUFXLEdBQUcsR0FBRyxjQUFjO3dCQUMvQ1AsZ0JBQWdCUSxjQUFjLEdBQUc7b0JBQ25DLE9BQU8sSUFBSVIsZ0JBQWdCSSxnQkFBZ0IsRUFBRTt3QkFDM0NKLGdCQUFnQk8sV0FBVyxHQUFHLEdBQUcsaUJBQWlCO3dCQUNsRFAsZ0JBQWdCUSxjQUFjLEdBQUc7b0JBQ25DO2dCQUNGO1lBQ0Y7UUFDRixFQUFFLE9BQU9sQixPQUFPO1lBQ2RtQyxRQUFRbkMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDbEQ7UUFFQSxPQUFPUCxxREFBWUEsQ0FBQ00sSUFBSSxDQUFDVztJQUMzQixFQUFFLE9BQU9WLE9BQU87UUFDZG1DLFFBQVFuQyxLQUFLLENBQUMsMENBQTBDQTtRQUN4RCxPQUFPUCxxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXlDLEdBQ2xEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZW1DLEtBQUt4QyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNSCwrREFBVUE7UUFDaEMsSUFBSSxDQUFDRyxTQUFTQyxNQUFNO1lBQ2xCLE9BQU9MLHFEQUFZQSxDQUFDTSxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZSxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNb0MsT0FBTyxNQUFNekMsUUFBUUcsSUFBSTtRQUMvQixNQUFNLEVBQ0pZLE9BQU8sRUFDUEMsZ0JBQWdCLEVBQ2hCQyxpQkFBaUIsRUFDakJDLGdCQUFnQixFQUNoQkMsZUFBZSxFQUNmQyxZQUFZLEVBQ1pYLFlBQVksRUFDWlksV0FBVyxFQUNYQyxjQUFjLEVBQ2YsR0FBR21CO1FBRUosTUFBTTlCLFlBQVlWLFFBQVFDLElBQUksQ0FBQ1UsS0FBSztRQUNwQyxNQUFNQyxhQUFhLENBQUMsdUJBQXVCLEVBQUVGLFVBQVUsQ0FBQyxFQUFFRixjQUFjO1FBRXhFLHlCQUF5QjtRQUN6QixNQUFNaUMsZUFBZTtZQUNuQjNCLFNBQVNBLFdBQVc7WUFDcEJDLGtCQUFrQkEsb0JBQW9CO1lBQ3RDQyxtQkFBbUJBLHFCQUFxQjtZQUN4Q0Msa0JBQWtCQSxvQkFBb0I7WUFDdENDLGlCQUFpQkEsbUJBQW1CO1lBQ3BDQyxjQUFjQSxnQkFBZ0I7WUFDOUJDLGFBQWFBLGVBQWU7WUFDNUJDLGdCQUFnQkEsa0JBQWtCO1lBQ2xDYixjQUFjQTtZQUNkRSxXQUFXQTtZQUNYWSxhQUFhLElBQUlDLE9BQU9DLFdBQVc7UUFDckM7UUFFQSx3REFBd0Q7UUFDeEQsc0RBQXNEO1FBQ3REYyxRQUFRSSxHQUFHLENBQUMscUNBQXFDRDtRQUVqRCx1Q0FBdUM7UUFDdkMscUJBQXFCO1FBQ3JCLHFDQUFxQztRQUNyQyw4RUFBOEU7UUFDOUUsa0dBQWtHO1FBQ2xHLDJDQUEyQztRQUMzQyw4QkFBOEI7UUFFOUIsT0FBTzdDLHFEQUFZQSxDQUFDTSxJQUFJLENBQUM7WUFDdkJ5QyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsTUFBTUo7UUFDUjtJQUNGLEVBQUUsT0FBT3RDLE9BQU87UUFDZG1DLFFBQVFuQyxLQUFLLENBQUMsd0NBQXdDQTtRQUN0RCxPQUFPUCxxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXdDLEdBQ2pEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGNsaWVudFxcc3JjXFxhcHBcXGFwaVxcY2xpZW50XFxxdWFsaWZpY2F0aW9uLXByb2dyZXNzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgZ2V0U2Vzc2lvbiB9IGZyb20gJ0BhdXRoMC9uZXh0anMtYXV0aDAnO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlc3Npb24oKTtcbiAgICBpZiAoIXNlc3Npb24/LnVzZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pO1xuICAgIH1cblxuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCB0b2tlbkFkZHJlc3MgPSBzZWFyY2hQYXJhbXMuZ2V0KCd0b2tlbkFkZHJlc3MnKTtcbiAgICBjb25zdCB1c2VyRW1haWwgPSBzZXNzaW9uLnVzZXIuZW1haWw7XG5cbiAgICAvLyBHZXQgc3RvcmVkIHF1YWxpZmljYXRpb24gcHJvZ3Jlc3MgZnJvbSBsb2NhbFN0b3JhZ2Ugc2ltdWxhdGlvblxuICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBxdWVyeSB0aGUgZGF0YWJhc2VcbiAgICBjb25zdCBzdG9yYWdlS2V5ID0gYHF1YWxpZmljYXRpb25fcHJvZ3Jlc3NfJHt1c2VyRW1haWx9XyR7dG9rZW5BZGRyZXNzfWA7XG5cbiAgICAvLyBGb3Igbm93LCB3ZSdsbCB1c2UgYSBzaW1wbGUgaW4tbWVtb3J5IHN0b3JhZ2Ugc2ltdWxhdGlvblxuICAgIC8vIEluIHByb2R1Y3Rpb24sIHRoaXMgc2hvdWxkIHF1ZXJ5IHRoZSBhY3R1YWwgZGF0YWJhc2VcbiAgICBjb25zdCBkZWZhdWx0UHJvZ3Jlc3MgPSB7XG4gICAgICBjb3VudHJ5OiAnJyxcbiAgICAgIGNvdW50cnlDb21wbGV0ZWQ6IGZhbHNlLFxuICAgICAgYWdyZWVtZW50QWNjZXB0ZWQ6IGZhbHNlLFxuICAgICAgcHJvZmlsZUNvbXBsZXRlZDogZmFsc2UsXG4gICAgICB3YWxsZXRDb25uZWN0ZWQ6IGZhbHNlLFxuICAgICAga3ljQ29tcGxldGVkOiBmYWxzZSxcbiAgICAgIGN1cnJlbnRTdGVwOiAwLFxuICAgICAgY29tcGxldGVkU3RlcHM6IDAsXG4gICAgICB0b2tlbkFkZHJlc3M6IHRva2VuQWRkcmVzcyxcbiAgICAgIHVzZXJFbWFpbDogdXNlckVtYWlsLFxuICAgICAgbGFzdFVwZGF0ZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB9O1xuXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgZXhpc3RpbmcgcHJvZmlsZSAoYWZmZWN0cyBwcm9maWxlQ29tcGxldGVkIHN0YXR1cylcbiAgICB0cnkge1xuICAgICAgY29uc3QgcHJvZmlsZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuQURNSU5fQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo2Njc3L2FwaSd9L2NsaWVudHM/c2VhcmNoPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHVzZXJFbWFpbCl9JmxpbWl0PTFgKTtcbiAgICAgIGlmIChwcm9maWxlUmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgcHJvZmlsZURhdGEgPSBhd2FpdCBwcm9maWxlUmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAocHJvZmlsZURhdGEuY2xpZW50cyAmJiBwcm9maWxlRGF0YS5jbGllbnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zdCBjbGllbnQgPSBwcm9maWxlRGF0YS5jbGllbnRzWzBdO1xuICAgICAgICAgIGRlZmF1bHRQcm9ncmVzcy5wcm9maWxlQ29tcGxldGVkID0gdHJ1ZTtcbiAgICAgICAgICBkZWZhdWx0UHJvZ3Jlc3Mud2FsbGV0Q29ubmVjdGVkID0gISFjbGllbnQud2FsbGV0QWRkcmVzcztcbiAgICAgICAgICBkZWZhdWx0UHJvZ3Jlc3Mua3ljQ29tcGxldGVkID0gY2xpZW50Lmt5Y1N0YXR1cyA9PT0gJ0FQUFJPVkVEJztcblxuICAgICAgICAgIC8vIFVwZGF0ZSBjdXJyZW50IHN0ZXAgYmFzZWQgb24gY29tcGxldGlvbiBzdGF0dXNcbiAgICAgICAgICBpZiAoZGVmYXVsdFByb2dyZXNzLmt5Y0NvbXBsZXRlZCkge1xuICAgICAgICAgICAgZGVmYXVsdFByb2dyZXNzLmN1cnJlbnRTdGVwID0gNTsgLy8gQWxsIGNvbXBsZXRlZFxuICAgICAgICAgICAgZGVmYXVsdFByb2dyZXNzLmNvbXBsZXRlZFN0ZXBzID0gNTtcbiAgICAgICAgICB9IGVsc2UgaWYgKGRlZmF1bHRQcm9ncmVzcy53YWxsZXRDb25uZWN0ZWQpIHtcbiAgICAgICAgICAgIGRlZmF1bHRQcm9ncmVzcy5jdXJyZW50U3RlcCA9IDQ7IC8vIE9uIEtZQyBzdGVwXG4gICAgICAgICAgICBkZWZhdWx0UHJvZ3Jlc3MuY29tcGxldGVkU3RlcHMgPSA0O1xuICAgICAgICAgIH0gZWxzZSBpZiAoZGVmYXVsdFByb2dyZXNzLnByb2ZpbGVDb21wbGV0ZWQpIHtcbiAgICAgICAgICAgIGRlZmF1bHRQcm9ncmVzcy5jdXJyZW50U3RlcCA9IDM7IC8vIE9uIHdhbGxldCBzdGVwXG4gICAgICAgICAgICBkZWZhdWx0UHJvZ3Jlc3MuY29tcGxldGVkU3RlcHMgPSAzO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBwcm9maWxlIHN0YXR1czonLCBlcnJvcik7XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGRlZmF1bHRQcm9ncmVzcyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcXVhbGlmaWNhdGlvbiBwcm9ncmVzczonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBxdWFsaWZpY2F0aW9uIHByb2dyZXNzJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXNzaW9uKCk7XG4gICAgaWYgKCFzZXNzaW9uPy51c2VyKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KTtcbiAgICB9XG5cbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgY29uc3Qge1xuICAgICAgY291bnRyeSxcbiAgICAgIGNvdW50cnlDb21wbGV0ZWQsXG4gICAgICBhZ3JlZW1lbnRBY2NlcHRlZCxcbiAgICAgIHByb2ZpbGVDb21wbGV0ZWQsXG4gICAgICB3YWxsZXRDb25uZWN0ZWQsXG4gICAgICBreWNDb21wbGV0ZWQsXG4gICAgICB0b2tlbkFkZHJlc3MsXG4gICAgICBjdXJyZW50U3RlcCxcbiAgICAgIGNvbXBsZXRlZFN0ZXBzXG4gICAgfSA9IGJvZHk7XG5cbiAgICBjb25zdCB1c2VyRW1haWwgPSBzZXNzaW9uLnVzZXIuZW1haWw7XG4gICAgY29uc3Qgc3RvcmFnZUtleSA9IGBxdWFsaWZpY2F0aW9uX3Byb2dyZXNzXyR7dXNlckVtYWlsfV8ke3Rva2VuQWRkcmVzc31gO1xuXG4gICAgLy8gQ3JlYXRlIHByb2dyZXNzIG9iamVjdFxuICAgIGNvbnN0IHByb2dyZXNzRGF0YSA9IHtcbiAgICAgIGNvdW50cnk6IGNvdW50cnkgfHwgJycsXG4gICAgICBjb3VudHJ5Q29tcGxldGVkOiBjb3VudHJ5Q29tcGxldGVkIHx8IGZhbHNlLFxuICAgICAgYWdyZWVtZW50QWNjZXB0ZWQ6IGFncmVlbWVudEFjY2VwdGVkIHx8IGZhbHNlLFxuICAgICAgcHJvZmlsZUNvbXBsZXRlZDogcHJvZmlsZUNvbXBsZXRlZCB8fCBmYWxzZSxcbiAgICAgIHdhbGxldENvbm5lY3RlZDogd2FsbGV0Q29ubmVjdGVkIHx8IGZhbHNlLFxuICAgICAga3ljQ29tcGxldGVkOiBreWNDb21wbGV0ZWQgfHwgZmFsc2UsXG4gICAgICBjdXJyZW50U3RlcDogY3VycmVudFN0ZXAgfHwgMCxcbiAgICAgIGNvbXBsZXRlZFN0ZXBzOiBjb21wbGV0ZWRTdGVwcyB8fCAwLFxuICAgICAgdG9rZW5BZGRyZXNzOiB0b2tlbkFkZHJlc3MsXG4gICAgICB1c2VyRW1haWw6IHVzZXJFbWFpbCxcbiAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfTtcblxuICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBzYXZlIHRvIGRhdGFiYXNlXG4gICAgLy8gRm9yIG5vdywgd2UnbGwgbG9nIHRoZSBwcm9ncmVzcyBhbmQgc2ltdWxhdGUgc2F2aW5nXG4gICAgY29uc29sZS5sb2coJ/Cfkr4gU2F2aW5nIHF1YWxpZmljYXRpb24gcHJvZ3Jlc3M6JywgcHJvZ3Jlc3NEYXRhKTtcblxuICAgIC8vIFRPRE86IEltcGxlbWVudCBhY3R1YWwgZGF0YWJhc2Ugc2F2ZVxuICAgIC8vIEV4YW1wbGUgc3RydWN0dXJlOlxuICAgIC8vIElOU0VSVCBJTlRPIHF1YWxpZmljYXRpb25fcHJvZ3Jlc3NcbiAgICAvLyAodXNlcl9lbWFpbCwgdG9rZW5fYWRkcmVzcywgY291bnRyeSwgY291bnRyeV9jb21wbGV0ZWQsIGFncmVlbWVudF9hY2NlcHRlZCxcbiAgICAvLyAgcHJvZmlsZV9jb21wbGV0ZWQsIHdhbGxldF9jb25uZWN0ZWQsIGt5Y19jb21wbGV0ZWQsIGN1cnJlbnRfc3RlcCwgY29tcGxldGVkX3N0ZXBzLCB1cGRhdGVkX2F0KVxuICAgIC8vIFZBTFVFUyAoPywgPywgPywgPywgPywgPywgPywgPywgPywgPywgPylcbiAgICAvLyBPTiBEVVBMSUNBVEUgS0VZIFVQREFURSAuLi5cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgbWVzc2FnZTogJ1F1YWxpZmljYXRpb24gcHJvZ3Jlc3Mgc2F2ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgIGRhdGE6IHByb2dyZXNzRGF0YVxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBxdWFsaWZpY2F0aW9uIHByb2dyZXNzOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIHNhdmUgcXVhbGlmaWNhdGlvbiBwcm9ncmVzcycgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXRTZXNzaW9uIiwiR0VUIiwicmVxdWVzdCIsInNlc3Npb24iLCJ1c2VyIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwidG9rZW5BZGRyZXNzIiwiZ2V0IiwidXNlckVtYWlsIiwiZW1haWwiLCJzdG9yYWdlS2V5IiwiZGVmYXVsdFByb2dyZXNzIiwiY291bnRyeSIsImNvdW50cnlDb21wbGV0ZWQiLCJhZ3JlZW1lbnRBY2NlcHRlZCIsInByb2ZpbGVDb21wbGV0ZWQiLCJ3YWxsZXRDb25uZWN0ZWQiLCJreWNDb21wbGV0ZWQiLCJjdXJyZW50U3RlcCIsImNvbXBsZXRlZFN0ZXBzIiwibGFzdFVwZGF0ZWQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJwcm9maWxlUmVzcG9uc2UiLCJmZXRjaCIsInByb2Nlc3MiLCJlbnYiLCJBRE1JTl9BUElfVVJMIiwiZW5jb2RlVVJJQ29tcG9uZW50Iiwib2siLCJwcm9maWxlRGF0YSIsImNsaWVudHMiLCJsZW5ndGgiLCJjbGllbnQiLCJ3YWxsZXRBZGRyZXNzIiwia3ljU3RhdHVzIiwiY29uc29sZSIsIlBPU1QiLCJib2R5IiwicHJvZ3Jlc3NEYXRhIiwibG9nIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJkYXRhIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/qualification-progress/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();