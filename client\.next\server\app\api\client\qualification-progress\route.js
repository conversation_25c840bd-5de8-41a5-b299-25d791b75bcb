/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/qualification-progress/route";
exports.ids = ["app/api/client/qualification-progress/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/qualification-progress/route.ts */ \"(rsc)/./src/app/api/client/qualification-progress/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/qualification-progress/route\",\n        pathname: \"/api/client/qualification-progress\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/qualification-progress/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\qualification-progress\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/qualification-progress/route.ts":
/*!************************************************************!*\
  !*** ./src/app/api/client/qualification-progress/route.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const tokenAddress = searchParams.get('tokenAddress');\n        const userEmail = session.user.email;\n        // Try to call admin panel API, fallback to default if it fails\n        const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';\n        const params = new URLSearchParams({\n            clientEmail: userEmail\n        });\n        if (tokenAddress) {\n            params.append('tokenAddress', tokenAddress);\n        }\n        try {\n            const response = await fetch(`${adminApiUrl}/qualification-progress?${params.toString()}`);\n            if (response.ok) {\n                const progressData = await response.json();\n                console.log('📊 Retrieved qualification progress from admin API:', {\n                    userEmail,\n                    tokenAddress,\n                    currentStep: progressData.currentStep,\n                    completedSteps: progressData.completedSteps\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(progressData);\n            } else {\n                console.warn('⚠️ Admin API not available, using fallback');\n            }\n        } catch (error) {\n            console.warn('⚠️ Admin API error, using fallback:', error);\n        }\n        // Fallback: Check if user has existing profile and create default progress\n        const defaultProgress = {\n            country: '',\n            countryCompleted: false,\n            agreementAccepted: false,\n            profileCompleted: false,\n            walletConnected: false,\n            kycCompleted: false,\n            currentStep: 0,\n            completedSteps: 0,\n            tokenAddress: tokenAddress,\n            clientEmail: userEmail,\n            lastUpdated: new Date().toISOString()\n        };\n        // Check if user has existing profile (affects profileCompleted status)\n        try {\n            const profileResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`);\n            if (profileResponse.ok) {\n                const profileData = await profileResponse.json();\n                if (profileData.clients && profileData.clients.length > 0) {\n                    const client = profileData.clients[0];\n                    defaultProgress.profileCompleted = true;\n                    defaultProgress.walletConnected = !!client.walletAddress;\n                    defaultProgress.kycCompleted = client.kycStatus === 'APPROVED';\n                    // Update current step based on completion status\n                    if (defaultProgress.kycCompleted) {\n                        defaultProgress.currentStep = 5; // All completed\n                        defaultProgress.completedSteps = 5;\n                    } else if (defaultProgress.walletConnected) {\n                        defaultProgress.currentStep = 4; // On KYC step\n                        defaultProgress.completedSteps = 4;\n                    } else if (defaultProgress.profileCompleted) {\n                        defaultProgress.currentStep = 3; // On wallet step\n                        defaultProgress.completedSteps = 3;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error checking profile status:', error);\n        }\n        console.log('📊 Using fallback qualification progress:', defaultProgress);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(defaultProgress);\n    } catch (error) {\n        console.error('Error fetching qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const userEmail = session.user.email;\n        // Try to call admin panel API to save qualification progress\n        const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';\n        const requestData = {\n            ...body,\n            clientEmail: userEmail\n        };\n        try {\n            const response = await fetch(`${adminApiUrl}/qualification-progress`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log('💾 Saved qualification progress via admin API:', {\n                    userEmail,\n                    tokenAddress: body.tokenAddress,\n                    currentStep: body.currentStep,\n                    completedSteps: body.completedSteps\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n            } else {\n                console.warn('⚠️ Admin API not available for saving, using fallback');\n            }\n        } catch (error) {\n            console.warn('⚠️ Admin API error for saving, using fallback:', error);\n        }\n        // Fallback: Log the progress and return success\n        console.log('💾 Fallback: Logging qualification progress:', {\n            userEmail,\n            tokenAddress: body.tokenAddress,\n            currentStep: body.currentStep,\n            completedSteps: body.completedSteps,\n            data: requestData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Qualification progress saved successfully (fallback mode)',\n            data: requestData\n        });\n    } catch (error) {\n        console.error('Error saving qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT endpoint to fix qualification progress flags\nasync function PUT(request) {\n    try {\n        const { userEmail, tokenAddress } = await request.json();\n        if (!userEmail || !tokenAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🔧 Fixing qualification progress flags via client API for:', {\n            userEmail,\n            tokenAddress\n        });\n        // Forward the request to the admin API\n        const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';\n        const response = await fetch(`${adminApiUrl}/qualification-progress`, {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userEmail,\n                tokenAddress\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Admin API responded with status: ${response.status}`);\n        }\n        const result = await response.json();\n        console.log('✅ Qualification progress flags fixed successfully via admin API:', result);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('❌ Error fixing qualification progress flags:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/qualification-progress/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();