/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client/qualification-progress/route";
exports.ids = ["app/api/client/qualification-progress/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_client_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/client/qualification-progress/route.ts */ \"(rsc)/./src/app/api/client/qualification-progress/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client/qualification-progress/route\",\n        pathname: \"/api/client/qualification-progress\",\n        filename: \"route\",\n        bundlePath: \"app/api/client/qualification-progress/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\client\\\\qualification-progress\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_client_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client/qualification-progress/route.ts":
/*!************************************************************!*\
  !*** ./src/app/api/client/qualification-progress/route.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const tokenAddress = searchParams.get('tokenAddress');\n        const userEmail = session.user.email;\n        // Try to call admin panel API, fallback to default if it fails\n        const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';\n        const params = new URLSearchParams({\n            clientEmail: userEmail\n        });\n        if (tokenAddress) {\n            params.append('tokenAddress', tokenAddress);\n        }\n        try {\n            const response = await fetch(`${adminApiUrl}/qualification-progress?${params.toString()}`);\n            if (response.ok) {\n                const progressData = await response.json();\n                console.log('📊 Retrieved qualification progress from admin API:', {\n                    userEmail,\n                    tokenAddress,\n                    currentStep: progressData.currentStep,\n                    completedSteps: progressData.completedSteps\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(progressData);\n            } else {\n                console.warn('⚠️ Admin API not available, using fallback');\n            }\n        } catch (error) {\n            console.warn('⚠️ Admin API error, using fallback:', error);\n        }\n        // Fallback: Check if user has existing profile and create default progress\n        const defaultProgress = {\n            country: '',\n            countryCompleted: false,\n            agreementAccepted: false,\n            profileCompleted: false,\n            walletConnected: false,\n            kycCompleted: false,\n            currentStep: 0,\n            completedSteps: 0,\n            tokenAddress: tokenAddress,\n            clientEmail: userEmail,\n            lastUpdated: new Date().toISOString()\n        };\n        // Check if user has existing profile (affects profileCompleted status)\n        try {\n            const profileResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`);\n            if (profileResponse.ok) {\n                const profileData = await profileResponse.json();\n                if (profileData.clients && profileData.clients.length > 0) {\n                    const client = profileData.clients[0];\n                    defaultProgress.profileCompleted = true;\n                    defaultProgress.walletConnected = !!client.walletAddress;\n                    defaultProgress.kycCompleted = client.kycStatus === 'APPROVED';\n                    // Update current step based on completion status\n                    if (defaultProgress.kycCompleted) {\n                        defaultProgress.currentStep = 5; // All completed\n                        defaultProgress.completedSteps = 5;\n                    } else if (defaultProgress.walletConnected) {\n                        defaultProgress.currentStep = 4; // On KYC step\n                        defaultProgress.completedSteps = 4;\n                    } else if (defaultProgress.profileCompleted) {\n                        defaultProgress.currentStep = 3; // On wallet step\n                        defaultProgress.completedSteps = 3;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error checking profile status:', error);\n        }\n        console.log('📊 Using fallback qualification progress:', defaultProgress);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(defaultProgress);\n    } catch (error) {\n        console.error('Error fetching qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const userEmail = session.user.email;\n        // Try to call admin panel API to save qualification progress\n        const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';\n        const requestData = {\n            ...body,\n            clientEmail: userEmail\n        };\n        try {\n            const response = await fetch(`${adminApiUrl}/qualification-progress`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log('💾 Saved qualification progress via admin API:', {\n                    userEmail,\n                    tokenAddress: body.tokenAddress,\n                    currentStep: body.currentStep,\n                    completedSteps: body.completedSteps\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n            } else {\n                console.warn('⚠️ Admin API not available for saving, using fallback');\n            }\n        } catch (error) {\n            console.warn('⚠️ Admin API error for saving, using fallback:', error);\n        }\n        // Fallback: Log the progress and return success\n        console.log('💾 Fallback: Logging qualification progress:', {\n            userEmail,\n            tokenAddress: body.tokenAddress,\n            currentStep: body.currentStep,\n            completedSteps: body.completedSteps,\n            data: requestData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Qualification progress saved successfully (fallback mode)',\n            data: requestData\n        });\n    } catch (error) {\n        console.error('Error saving qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client/qualification-progress/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient%2Fqualification-progress%2Froute&page=%2Fapi%2Fclient%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();