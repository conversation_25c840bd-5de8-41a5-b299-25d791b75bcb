import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

// GET /api/client/profile - Get current user's client profile
export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userEmail = session.user.email;
    console.log('Fetching profile for user email:', userEmail);

    // Forward request to admin panel API to get client by email
    const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
    console.log('Using admin API URL:', adminApiUrl);

    let response;
    try {
      const searchUrl = `${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`;
      console.log('Fetching from URL:', searchUrl);

      response = await fetch(searchUrl, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Admin API response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          console.log('Admin API returned 404 - no client found');
          return NextResponse.json(null, { status: 404 });
        }
        throw new Error(`Admin API error: ${response.status}`);
      }
    } catch (error) {
      // If admin panel is not running, return 404 (no profile found)
      console.log('Admin panel not available, error:', error);
      return NextResponse.json(null, { status: 404 });
    }

    const data = await response.json();
    console.log('Admin API response data:', data);

    const client = data.clients?.[0];
    console.log('Found client:', client ? 'yes' : 'no');

    if (!client) {
      console.log('No client found in response');
      return NextResponse.json(null, { status: 404 });
    }

    // Transform admin panel client data to match client app expectations
    const clientProfile = {
      id: client.id,
      email: client.email,
      firstName: client.firstName,
      lastName: client.lastName,
      gender: client.gender,
      nationality: client.nationality,
      birthday: client.birthday,
      birthPlace: client.birthPlace,
      identificationType: client.identificationType,
      passportNumber: client.passportNumber,
      idCardNumber: client.idCardNumber,
      documentExpiration: client.documentExpiration,
      phoneNumber: client.phoneNumber,
      occupation: client.occupation,
      sectorOfActivity: client.sectorOfActivity,
      pepStatus: client.pepStatus,
      pepDetails: client.pepDetails,
      street: client.street,
      buildingNumber: client.buildingNumber,
      city: client.city,
      state: client.state,
      country: client.country,
      zipCode: client.zipCode,
      sourceOfWealth: client.sourceOfWealth,
      bankAccountNumber: client.bankAccountNumber,
      sourceOfFunds: client.sourceOfFunds,
      taxIdentificationNumber: client.taxIdentificationNumber,
      kycStatus: client.kycStatus,
      kycCompletedAt: client.kycCompletedAt,
      kycNotes: client.kycNotes,
      isWhitelisted: client.isWhitelisted,
      walletAddress: client.walletAddress,
      walletVerifiedAt: client.walletVerifiedAt,
      createdAt: client.createdAt,
      updatedAt: client.updatedAt,
    };

    return NextResponse.json(clientProfile);
  } catch (error) {
    console.error('Error fetching client profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch client profile' },
      { status: 500 }
    );
  }
}

// POST /api/client/profile - Create or update client profile
export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const userEmail = session.user.email;

    const profileData = {
      ...body,
      email: userEmail,
    };
    console.log('[CLIENT PROFILE API] Creating/updating client profile with data:', profileData);

    const adminApiUrl = process.env.ADMIN_API_BASE_URL;
    if (!adminApiUrl) {
      console.error('[CLIENT PROFILE API] ADMIN_API_BASE_URL is not defined.');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }
    console.log('[CLIENT PROFILE API] Using admin API URL:', adminApiUrl);

    // First, check if client already exists
    console.log('[CLIENT PROFILE API] Checking if client already exists...');
    let existingClient = null;
    try {
      const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        existingClient = searchData.clients?.[0];
        console.log('[CLIENT PROFILE API] Existing client found:', existingClient ? 'yes' : 'no');
      }
    } catch (error) {
      console.log('[CLIENT PROFILE API] Error checking for existing client:', error);
    }

    let adminApiResponse;
    try {
      if (existingClient) {
        // Update existing client
        console.log('[CLIENT PROFILE API] Updating existing client:', existingClient.id);
        adminApiResponse = await fetch(`${adminApiUrl}/clients/${existingClient.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(profileData),
        });
      } else {
        // Create new client
        console.log('[CLIENT PROFILE API] Creating new client');
        adminApiResponse = await fetch(`${adminApiUrl}/clients`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(profileData),
        });
      }
      console.log('[CLIENT PROFILE API] Admin panel response status:', adminApiResponse.status);

      const responseDataText = await adminApiResponse.text(); // Read response as text first for better error diagnosis
      console.log('[CLIENT PROFILE API] Admin panel response text:', responseDataText);

      if (!adminApiResponse.ok) {
        let errorDetail = `Admin API error: ${adminApiResponse.status} ${adminApiResponse.statusText}`;
        try {
          const errorJson = JSON.parse(responseDataText);
          errorDetail = errorJson.error || errorDetail;
          console.error('[CLIENT PROFILE API] Admin panel API error JSON:', errorJson);
        } catch (e) {
          console.error('[CLIENT PROFILE API] Failed to parse admin error response as JSON.');
        }
        throw new Error(errorDetail);
      }

      const clientData = JSON.parse(responseDataText); // Parse text to JSON if response is OK
      console.log('[CLIENT PROFILE API] Successfully processed client in admin panel:', clientData);

      const clientProfile = {
        id: clientData.id,
        email: clientData.email,
        firstName: clientData.firstName,
        lastName: clientData.lastName,
        gender: clientData.gender,
        nationality: clientData.nationality,
        birthday: clientData.birthday,
        birthPlace: clientData.birthPlace,
        identificationType: clientData.identificationType,
        passportNumber: clientData.passportNumber,
        idCardNumber: clientData.idCardNumber,
        documentExpiration: clientData.documentExpiration,
        phoneNumber: clientData.phoneNumber,
        occupation: clientData.occupation,
        sectorOfActivity: clientData.sectorOfActivity,
        pepStatus: clientData.pepStatus,
        pepDetails: clientData.pepDetails,
        street: clientData.street,
        buildingNumber: clientData.buildingNumber,
        city: clientData.city,
        state: clientData.state,
        country: clientData.country,
        zipCode: clientData.zipCode,
        sourceOfWealth: clientData.sourceOfWealth,
        bankAccountNumber: clientData.bankAccountNumber,
        sourceOfFunds: clientData.sourceOfFunds,
        taxIdentificationNumber: clientData.taxIdentificationNumber,
        kycStatus: clientData.kycStatus,
        kycCompletedAt: clientData.kycCompletedAt,
        kycNotes: clientData.kycNotes,
        isWhitelisted: clientData.isWhitelisted,
        walletAddress: clientData.walletAddress,
        walletVerifiedAt: clientData.walletVerifiedAt,
        createdAt: clientData.createdAt,
        updatedAt: clientData.updatedAt,
      };

      // Return 200 for updates, 201 for creates
      const statusCode = existingClient ? 200 : 201;
      return NextResponse.json(clientProfile, { status: statusCode });

    } catch (fetchError) {
      console.error('[CLIENT PROFILE API] Error during fetch to admin panel or processing its response:', fetchError);
      const message = fetchError instanceof Error ? fetchError.message : 'Failed to communicate with admin service';
      return NextResponse.json({ error: 'Admin service communication error', details: message }, { status: 502 }); // Bad Gateway
    }

  } catch (error) {
    console.error('[CLIENT PROFILE API] General error in POST /api/client/profile:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: 'Failed to create client profile', details: message }, { status: 500 });
  }
}

// PUT /api/client/profile - Update client profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const userEmail = session.user.email;

    console.log('Updating client profile:', body);

    // First, get the client ID by email
    const adminApiUrl = process.env.ADMIN_API_BASE_URL!;
    const searchResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!searchResponse.ok) {
      throw new Error(`Failed to find client: ${searchResponse.status}`);
    }

    const searchData = await searchResponse.json();
    const client = searchData.clients?.[0];

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Update the client via admin panel API
    const updateResponse = await fetch(`${adminApiUrl}/clients/${client.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!updateResponse.ok) {
      const errorData = await updateResponse.json().catch(() => ({}));
      throw new Error(errorData.error || `Admin API error: ${updateResponse.status}`);
    }

    const updatedClient = await updateResponse.json();

    // Transform response to match client app expectations
    const clientProfile = {
      id: updatedClient.id,
      email: updatedClient.email,
      firstName: updatedClient.firstName,
      lastName: updatedClient.lastName,
      gender: updatedClient.gender,
      nationality: updatedClient.nationality,
      birthday: updatedClient.birthday,
      birthPlace: updatedClient.birthPlace,
      identificationType: updatedClient.identificationType,
      passportNumber: updatedClient.passportNumber,
      idCardNumber: updatedClient.idCardNumber,
      documentExpiration: updatedClient.documentExpiration,
      phoneNumber: updatedClient.phoneNumber,
      occupation: updatedClient.occupation,
      sectorOfActivity: updatedClient.sectorOfActivity,
      pepStatus: updatedClient.pepStatus,
      pepDetails: updatedClient.pepDetails,
      street: updatedClient.street,
      buildingNumber: updatedClient.buildingNumber,
      city: updatedClient.city,
      state: updatedClient.state,
      country: updatedClient.country,
      zipCode: updatedClient.zipCode,
      sourceOfWealth: updatedClient.sourceOfWealth,
      bankAccountNumber: updatedClient.bankAccountNumber,
      sourceOfFunds: updatedClient.sourceOfFunds,
      taxIdentificationNumber: updatedClient.taxIdentificationNumber,
      kycStatus: updatedClient.kycStatus,
      kycCompletedAt: updatedClient.kycCompletedAt,
      kycNotes: updatedClient.kycNotes,
      isWhitelisted: updatedClient.isWhitelisted,
      walletAddress: updatedClient.walletAddress,
      walletVerifiedAt: updatedClient.walletVerifiedAt,
      createdAt: updatedClient.createdAt,
      updatedAt: updatedClient.updatedAt,
    };

    return NextResponse.json(clientProfile);
  } catch (error) {
    console.error('Error updating client profile:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update client profile' },
      { status: 500 }
    );
  }
}
