require("dotenv").config();
const { ethers } = require("hardhat");
const factoryABI = require("../admin-panel/src/contracts/SecurityTokenFactory.json").abi;

async function main() {
  try {
    // Get network
    const network = process.env.NETWORK || "amoy";
    console.log(`Network: ${network}`);
    
    // Get factory address from environment
    const factoryAddress = process.env.FACTORY_ADDRESS;
    if (!factoryAddress) {
      console.error("Please set the FACTORY_ADDRESS environment variable");
      process.exit(1);
    }
    
    console.log(`Factory address to update: ${factoryAddress}`);
    
    // Get deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`Deployer address: ${deployer.address}`);
    
    // Connect to the existing factory
    console.log("Connecting to factory...");
    const existingFactory = new ethers.Contract(
      factoryAddress,
      factoryABI,
      deployer
    );
    
    // Check if deployer has admin role
    const DEFAULT_ADMIN_ROLE = ethers.ZeroHash;
    const hasAdminRole = await existingFactory.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    if (!hasAdminRole) {
      console.error(`Deployer ${deployer.address} does not have DEFAULT_ADMIN_ROLE and cannot update implementations`);
      process.exit(1);
    }
    
    console.log("Deployer has admin permissions.");
    
    // Deploy new implementation contracts
    console.log("\nDeploying new implementation contracts...");
    
    // Deploy SecurityToken implementation
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const securityTokenImpl = await SecurityToken.deploy();
    console.log(`New SecurityToken implementation: ${securityTokenImpl.address}`);
    
    // Deploy Whitelist implementation
    const Whitelist = await ethers.getContractFactory("Whitelist");
    const whitelistImpl = await Whitelist.deploy();
    console.log(`New Whitelist implementation: ${whitelistImpl.address}`);
    
    // Deploy WhitelistWithKYC implementation
    const WhitelistWithKYC = await ethers.getContractFactory("WhitelistWithKYC");
    const whitelistWithKYCImpl = await WhitelistWithKYC.deploy();
    console.log(`New WhitelistWithKYC implementation: ${whitelistWithKYCImpl.address}`);
    
    // Get current implementations for logs
    const currentTokenImpl = await existingFactory.securityTokenImplementation();
    const currentWhitelistImpl = await existingFactory.whitelistImplementation();
    let currentKYCImpl;
    try {
      currentKYCImpl = await existingFactory.whitelistWithKYCImplementation();
    } catch (err) {
      currentKYCImpl = "Not implemented";
    }
    
    console.log("\nCurrent implementations:");
    console.log(`- SecurityToken: ${currentTokenImpl}`);
    console.log(`- Whitelist: ${currentWhitelistImpl}`);
    console.log(`- WhitelistWithKYC: ${currentKYCImpl}`);
    
    console.log("\nNew implementations:");
    console.log(`- SecurityToken: ${securityTokenImpl.address}`);
    console.log(`- Whitelist: ${whitelistImpl.address}`);
    console.log(`- WhitelistWithKYC: ${whitelistWithKYCImpl.address}`);
    
    // Update the implementations
    console.log("\nUpdating implementations...");
    
    const tx = await existingFactory.updateImplementations(
      securityTokenImpl.address,
      whitelistImpl.address,
      whitelistWithKYCImpl.address
    );
    
    console.log(`Transaction sent: ${tx.hash}`);
    console.log("Waiting for confirmation...");
    
    // Wait for the transaction to be mined
    const receipt = await tx.wait();
    console.log(`Transaction confirmed in block ${receipt.blockNumber}`);
    
    // Verify the update was successful
    const updatedTokenImpl = await existingFactory.securityTokenImplementation();
    const updatedWhitelistImpl = await existingFactory.whitelistImplementation();
    const updatedKYCImpl = await existingFactory.whitelistWithKYCImplementation();
    
    console.log("\nVerifying updated implementations:");
    console.log(`- SecurityToken: ${updatedTokenImpl}`);
    console.log(`- Whitelist: ${updatedWhitelistImpl}`);
    console.log(`- WhitelistWithKYC: ${updatedKYCImpl}`);
    
    const success = (
      updatedTokenImpl === securityTokenImpl.address &&
      updatedWhitelistImpl === whitelistImpl.address &&
      updatedKYCImpl === whitelistWithKYCImpl.address
    );
    
    if (success) {
      console.log("\n✅ Update successful! Factory now supports KYC functionality.");
    } else {
      console.log("\n❌ Update verification failed. Some implementations may not have been updated correctly.");
    }
    
  } catch (error) {
    console.error("Error updating factory implementations:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  }); 