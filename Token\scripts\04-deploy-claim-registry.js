const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Deploying ClaimRegistry Contract...");
  console.log("=====================================");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  try {
    // 1. Deploy ClaimRegistry
    console.log("\n📋 Deploying ClaimRegistry...");
    const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
    
    const claimRegistry = await upgrades.deployProxy(
      ClaimRegistry,
      [deployer.address], // admin address
      { 
        initializer: "initialize",
        kind: "uups"
      }
    );

    await claimRegistry.waitForDeployment();
    const claimRegistryAddress = await claimRegistry.getAddress();
    
    console.log("✅ ClaimRegistry deployed to:", claimRegistryAddress);

    // 2. Verify deployment
    console.log("\n🔍 Verifying deployment...");
    
    // Check admin role
    const DEFAULT_ADMIN_ROLE = await claimRegistry.DEFAULT_ADMIN_ROLE();
    const hasAdminRole = await claimRegistry.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    console.log("Admin role assigned:", hasAdminRole);

    // Check claim issuer role
    const CLAIM_ISSUER_ROLE = await claimRegistry.CLAIM_ISSUER_ROLE();
    const hasIssuerRole = await claimRegistry.hasRole(CLAIM_ISSUER_ROLE, deployer.address);
    console.log("Claim issuer role assigned:", hasIssuerRole);

    // Check claim types
    const KYC_CLAIM = await claimRegistry.KYC_CLAIM();
    const QUALIFICATION_CLAIM = await claimRegistry.QUALIFICATION_CLAIM();
    console.log("KYC claim type:", KYC_CLAIM.toString());
    console.log("Qualification claim type:", QUALIFICATION_CLAIM.toString());

    // 3. Test basic functionality
    console.log("\n🧪 Testing basic functionality...");
    
    // Test issuing a claim
    const testAddress = "******************************************";
    const testData = ethers.AbiCoder.defaultAbiCoder().encode(
      ['string', 'uint256'],
      ['TEST_CLAIM', Math.floor(Date.now() / 1000)]
    );

    console.log("Issuing test KYC claim...");
    const issueTx = await claimRegistry.issueClaim(
      testAddress,
      KYC_CLAIM,
      "0x", // empty signature
      testData,
      "test://kyc-claim", // uri
      0 // never expires
    );
    
    const issueReceipt = await issueTx.wait();
    console.log("✅ Test claim issued, tx:", issueReceipt.transactionHash);

    // Verify the claim
    const hasValidClaim = await claimRegistry.hasValidClaim(testAddress, KYC_CLAIM);
    console.log("Test address has valid KYC claim:", hasValidClaim);

    // Get claim IDs
    const claimIds = await claimRegistry.getClaimIds(testAddress, KYC_CLAIM);
    console.log("Number of claims for test address:", claimIds.length);

    if (claimIds.length > 0) {
      const claim = await claimRegistry.getClaim(testAddress, KYC_CLAIM, claimIds[0]);
      console.log("First claim issuer:", claim.issuer);
      console.log("First claim revoked:", claim.revoked);
    }

    // 4. Save deployment info
    console.log("\n💾 Saving deployment information...");
    
    const deploymentInfo = {
      network: "polygon-amoy",
      claimRegistry: {
        address: claimRegistryAddress,
        deployer: deployer.address,
        deployedAt: new Date().toISOString(),
        transactionHash: claimRegistry.deploymentTransaction()?.hash,
        blockNumber: await deployer.provider.getBlockNumber()
      },
      claimTypes: {
        KYC_CLAIM: KYC_CLAIM.toString(),
        ACCREDITED_INVESTOR_CLAIM: "2",
        JURISDICTION_CLAIM: "3",
        QUALIFICATION_CLAIM: QUALIFICATION_CLAIM.toString()
      },
      roles: {
        DEFAULT_ADMIN_ROLE: DEFAULT_ADMIN_ROLE,
        CLAIM_ISSUER_ROLE: CLAIM_ISSUER_ROLE,
        CLAIM_VERIFIER_ROLE: await claimRegistry.CLAIM_VERIFIER_ROLE()
      }
    };

    // Save to file
    const fs = require('fs');
    const path = require('path');
    
    const deploymentsDir = path.join(__dirname, '../deployments');
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    const deploymentFile = path.join(deploymentsDir, 'claim-registry-deployment.json');
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("✅ Deployment info saved to:", deploymentFile);

    // 5. Environment variables
    console.log("\n🔧 Environment Variables to Add:");
    console.log("================================");
    console.log(`CLAIM_REGISTRY_ADDRESS=${claimRegistryAddress}`);
    console.log(`CLAIM_REGISTRY_ADMIN=${deployer.address}`);
    console.log("");
    console.log("Add these to your .env files:");
    console.log("- admin-panel/.env.local");
    console.log("- client/.env.local");

    // 6. Next steps
    console.log("\n🎯 Next Steps:");
    console.log("==============");
    console.log("1. Add environment variables to .env files");
    console.log("2. Update admin panel to include claim management UI");
    console.log("3. Integrate claim issuance with existing approval workflows");
    console.log("4. Test claim functionality in admin panel");

    console.log("\n🎉 ClaimRegistry deployment completed successfully!");
    
    return {
      claimRegistry: claimRegistryAddress,
      deployer: deployer.address,
      network: "polygon-amoy"
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
