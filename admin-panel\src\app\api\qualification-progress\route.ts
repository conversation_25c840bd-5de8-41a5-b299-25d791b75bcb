import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientEmail = searchParams.get('clientEmail');
    const tokenAddress = searchParams.get('tokenAddress');
    const clientId = searchParams.get('clientId');

    if (!clientEmail && !clientId) {
      return NextResponse.json({ error: 'Client email or ID required' }, { status: 400 });
    }

    // Find client first
    let client;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else {
      client = await prisma.client.findFirst({
        where: { email: clientEmail }
      });
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find qualification progress
    const whereClause: any = {
      clientId: client.id
    };

    if (tokenAddress) {
      // Find token first
      const token = await prisma.token.findFirst({
        where: { address: tokenAddress }
      });
      
      if (token) {
        whereClause.tokenId = token.id;
      } else {
        // If token not found, return default progress
        return NextResponse.json({
          country: '',
          countryCompleted: false,
          agreementAccepted: false,
          profileCompleted: !!client,
          walletConnected: !!client.walletAddress,
          kycCompleted: client.kycStatus === 'APPROVED',
          currentStep: 0,
          completedSteps: 0,
          tokenAddress: tokenAddress,
          clientEmail: client.email,
          lastUpdated: new Date().toISOString(),
        });
      }
    }

    const progress = await prisma.qualificationProgress.findFirst({
      where: whereClause,
      include: {
        client: true,
        token: true
      }
    });

    if (!progress) {
      // Return default progress based on client status
      const defaultProgress = {
        country: '',
        countryCompleted: false,
        agreementAccepted: false,
        profileCompleted: !!client,
        walletConnected: !!client.walletAddress,
        kycCompleted: client.kycStatus === 'APPROVED',
        currentStep: 0,
        completedSteps: 0,
        tokenAddress: tokenAddress,
        clientEmail: client.email,
        lastUpdated: new Date().toISOString(),
      };

      // Calculate current step based on completion status
      if (defaultProgress.kycCompleted) {
        defaultProgress.currentStep = 5;
        defaultProgress.completedSteps = 5;
      } else if (defaultProgress.walletConnected) {
        defaultProgress.currentStep = 4;
        defaultProgress.completedSteps = 4;
      } else if (defaultProgress.profileCompleted) {
        defaultProgress.currentStep = 3;
        defaultProgress.completedSteps = 3;
      }

      return NextResponse.json(defaultProgress);
    }

    // Return existing progress
    return NextResponse.json({
      country: progress.countryValue || '',
      countryCompleted: progress.countrySelected,
      agreementAccepted: progress.agreementAccepted,
      profileCompleted: progress.profileCompleted,
      walletConnected: progress.walletConnected,
      kycCompleted: progress.kycCompleted,
      currentStep: progress.currentStep,
      completedSteps: progress.completedSteps,
      tokenAddress: progress.token?.address || tokenAddress,
      clientEmail: progress.client.email,
      lastUpdated: progress.updatedAt.toISOString(),
    });

  } catch (error) {
    console.error('Error fetching qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualification progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clientEmail,
      clientId,
      tokenAddress,
      country,
      countryCompleted,
      agreementAccepted,
      profileCompleted,
      walletConnected,
      kycCompleted,
      currentStep,
      completedSteps
    } = body;

    if (!clientEmail && !clientId) {
      return NextResponse.json({ error: 'Client email or ID required' }, { status: 400 });
    }

    // Find client
    let client;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else {
      client = await prisma.client.findFirst({
        where: { email: clientEmail }
      });
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find token if provided
    let token = null;
    if (tokenAddress) {
      token = await prisma.token.findFirst({
        where: { address: tokenAddress }
      });
    }

    // Create or update qualification progress
    const progressData = {
      clientId: client.id,
      tokenId: token?.id,
      countrySelected: countryCompleted || false,
      countryValue: country || '',
      agreementAccepted: agreementAccepted || false,
      profileCompleted: profileCompleted || false,
      walletConnected: walletConnected || false,
      kycCompleted: kycCompleted || false,
      currentStep: currentStep || 0,
      completedSteps: completedSteps || 0,
      // Set completion timestamps
      countryCompletedAt: countryCompleted ? new Date() : null,
      agreementCompletedAt: agreementAccepted ? new Date() : null,
      profileCompletedAt: profileCompleted ? new Date() : null,
      walletCompletedAt: walletConnected ? new Date() : null,
      kycCompletedAt: kycCompleted ? new Date() : null,
    };

    const whereClause: any = {
      clientId: client.id
    };

    if (token) {
      whereClause.tokenId = token.id;
    } else if (!tokenAddress) {
      // For global progress (no specific token)
      whereClause.tokenId = null;
    }

    const progress = await prisma.qualificationProgress.upsert({
      where: {
        clientId_tokenId: whereClause
      },
      update: progressData,
      create: progressData,
      include: {
        client: true,
        token: true
      }
    });

    console.log('💾 Saved qualification progress to database:', {
      clientEmail: client.email,
      tokenAddress: token?.address || 'global',
      currentStep: progressData.currentStep,
      completedSteps: progressData.completedSteps
    });

    return NextResponse.json({
      success: true,
      message: 'Qualification progress saved successfully',
      data: {
        id: progress.id,
        currentStep: progress.currentStep,
        completedSteps: progress.completedSteps,
        lastUpdated: progress.updatedAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error saving qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to save qualification progress' },
      { status: 500 }
    );
  }
}
