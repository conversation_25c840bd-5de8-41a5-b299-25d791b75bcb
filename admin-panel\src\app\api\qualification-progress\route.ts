import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientEmail = searchParams.get('clientEmail');
    const tokenAddress = searchParams.get('tokenAddress');
    const clientId = searchParams.get('clientId');

    if (!clientEmail && !clientId) {
      return NextResponse.json({ error: 'Client email or ID required' }, { status: 400 });
    }

    // Find client first
    let client;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else {
      client = await prisma.client.findFirst({
        where: { email: clientEmail }
      });
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find qualification progress
    const whereClause: any = {
      clientId: client.id
    };

    if (tokenAddress) {
      // Find token first
      const token = await prisma.token.findFirst({
        where: { address: tokenAddress }
      });
      
      if (token) {
        whereClause.tokenId = token.id;
      } else {
        // If token not found, return default progress
        return NextResponse.json({
          country: '',
          countryCompleted: false,
          agreementAccepted: false,
          profileCompleted: !!client,
          walletConnected: !!client.walletAddress,
          kycCompleted: client.kycStatus === 'APPROVED',
          currentStep: 0,
          completedSteps: 0,
          tokenAddress: tokenAddress,
          clientEmail: client.email,
          lastUpdated: new Date().toISOString(),
        });
      }
    }

    const progress = await prisma.qualificationProgress.findFirst({
      where: whereClause,
      include: {
        client: true,
        token: true
      }
    });

    if (!progress) {
      // Return default progress based on client status
      const defaultProgress = {
        country: '',
        countryCompleted: false,
        agreementAccepted: false,
        profileCompleted: !!client,
        walletConnected: !!client.walletAddress,
        kycCompleted: client.kycStatus === 'APPROVED',
        currentStep: 0,
        completedSteps: 0,
        tokenAddress: tokenAddress,
        clientEmail: client.email,
        lastUpdated: new Date().toISOString(),
      };

      // Calculate current step based on completion status
      if (defaultProgress.kycCompleted) {
        defaultProgress.currentStep = 5;
        defaultProgress.completedSteps = 5;
      } else if (defaultProgress.walletConnected) {
        defaultProgress.currentStep = 4;
        defaultProgress.completedSteps = 4;
      } else if (defaultProgress.profileCompleted) {
        defaultProgress.currentStep = 3;
        defaultProgress.completedSteps = 3;
      }

      return NextResponse.json(defaultProgress);
    }

    // Return existing progress
    return NextResponse.json({
      country: progress.countryValue || '',
      countryCompleted: progress.countrySelected,
      agreementAccepted: progress.agreementAccepted,
      profileCompleted: progress.profileCompleted,
      walletConnected: progress.walletConnected,
      kycCompleted: progress.kycCompleted,
      currentStep: progress.currentStep,
      completedSteps: progress.completedSteps,
      tokenAddress: progress.token?.address || tokenAddress,
      clientEmail: progress.client.email,
      lastUpdated: progress.updatedAt.toISOString(),
    });

  } catch (error) {
    console.error('Error fetching qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualification progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clientEmail,
      clientId,
      tokenAddress,
      country,
      countryCompleted,
      agreementAccepted,
      profileCompleted,
      walletConnected,
      kycCompleted,
      currentStep,
      completedSteps
    } = body;

    if (!clientEmail && !clientId) {
      return NextResponse.json({ error: 'Client email or ID required' }, { status: 400 });
    }

    // Find client
    let client;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else {
      client = await prisma.client.findFirst({
        where: { email: clientEmail }
      });
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find token if provided
    let token = null;
    if (tokenAddress) {
      token = await prisma.token.findFirst({
        where: { address: tokenAddress }
      });
    }

    // Create or update qualification progress
    const progressData = {
      clientId: client.id,
      tokenId: token?.id,
      countrySelected: countryCompleted || false,
      countryValue: country || '',
      agreementAccepted: agreementAccepted || false,
      profileCompleted: profileCompleted || false,
      walletConnected: walletConnected || false,
      kycCompleted: kycCompleted || false,
      currentStep: currentStep || 0,
      completedSteps: completedSteps || 0,
      // Set completion timestamps
      countryCompletedAt: countryCompleted ? new Date() : null,
      agreementCompletedAt: agreementAccepted ? new Date() : null,
      profileCompletedAt: profileCompleted ? new Date() : null,
      walletCompletedAt: walletConnected ? new Date() : null,
      kycCompletedAt: kycCompleted ? new Date() : null,
    };

    const whereClause: any = {
      clientId: client.id
    };

    if (token) {
      whereClause.tokenId = token.id;
    } else if (!tokenAddress) {
      // For global progress (no specific token)
      whereClause.tokenId = null;
    }

    const progress = await prisma.qualificationProgress.upsert({
      where: {
        clientId_tokenId: whereClause
      },
      update: progressData,
      create: progressData,
      include: {
        client: true,
        token: true
      }
    });

    console.log('💾 Saved qualification progress to database:', {
      clientEmail: client.email,
      tokenAddress: token?.address || 'global',
      currentStep: progressData.currentStep,
      completedSteps: progressData.completedSteps,
      flags: {
        countrySelected: progressData.countrySelected,
        agreementAccepted: progressData.agreementAccepted,
        profileCompleted: progressData.profileCompleted,
        walletConnected: progressData.walletConnected,
        kycCompleted: progressData.kycCompleted
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Qualification progress saved successfully',
      data: {
        id: progress.id,
        currentStep: progress.currentStep,
        completedSteps: progress.completedSteps,
        lastUpdated: progress.updatedAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error saving qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to save qualification progress' },
      { status: 500 }
    );
  }
}

// PUT endpoint to fix qualification progress flags
export async function PUT(request: Request) {
  try {
    const { userEmail, tokenAddress } = await request.json();

    if (!userEmail || !tokenAddress) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Find the client
    const client = await prisma.client.findUnique({
      where: { email: userEmail }
    });

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // Find existing qualification progress
    const existingProgress = await prisma.qualificationProgress.findFirst({
      where: {
        clientId: client.id,
        tokenAddress: tokenAddress
      }
    });

    if (!existingProgress) {
      return NextResponse.json({ error: 'Qualification progress not found' }, { status: 404 });
    }

    // Calculate correct completion flags based on current state
    const countrySelected = existingProgress.completedSteps >= 1 || !!existingProgress.countryValue;
    const agreementAccepted = existingProgress.completedSteps >= 2;
    const profileCompleted = existingProgress.completedSteps >= 3 || !!client.firstName;
    const walletConnected = existingProgress.completedSteps >= 4 || !!client.walletAddress;
    const kycCompleted = existingProgress.completedSteps >= 5 || client.kycStatus === 'APPROVED';

    // Update the qualification progress with correct flags
    const updatedProgress = await prisma.qualificationProgress.update({
      where: { id: existingProgress.id },
      data: {
        countrySelected,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted,
        // Set country value if not set
        countryValue: existingProgress.countryValue || (countrySelected ? 'Fixed' : ''),
      }
    });

    console.log('🔧 Fixed qualification progress flags:', {
      clientEmail: client.email,
      tokenAddress,
      before: {
        countrySelected: existingProgress.countrySelected,
        agreementAccepted: existingProgress.agreementAccepted,
        profileCompleted: existingProgress.profileCompleted,
        walletConnected: existingProgress.walletConnected,
        kycCompleted: existingProgress.kycCompleted
      },
      after: {
        countrySelected,
        agreementAccepted,
        profileCompleted,
        walletConnected,
        kycCompleted
      }
    });

    return NextResponse.json({
      message: 'Qualification progress flags fixed successfully',
      progress: updatedProgress
    });

  } catch (error) {
    console.error('Error fixing qualification progress:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
